name: Dev
# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  #push:
  #  branches: [develop]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
jobs:
  build:
    name: Build and push image to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Check Out Repo
        uses: actions/checkout@v2

      - name: create .env file
        run: |
          echo "FIREBASE_API_KEY=${{ vars.FIREBASE_API_KEY }}" >> .env
          echo "FIREBASE_AUTH_DOMAIN=${{ vars.FIREBASE_AUTH_DOMAIN }}" >> .env
          echo "FIREBASE_PROJECT_ID=${{ vars.FIREBASE_PROJECT_ID }}" >> .env
          echo "FIREBASE_STORAGE_BUCKET=${{ vars.FIREBASE_STORAGE_BUCKET }}" >> .env
          echo "FIREBASE_MESSAGING_SENDER_ID=${{ vars.FIREBASE_MESSAGING_SENDER_ID }}" >> .env
          echo "FIREBASE_APP_ID=${{ vars.FIREBASE_APP_ID }}" >> .env
          echo "NEXT_PUBLIC_BASE_URL=${{ vars.NEXT_PUBLIC_BASE_URL }}" >> .env
          echo "FIREBASE_CREDENTIALS=${{ vars.FIREBASE_CREDENTIALS }}" >> .env
          echo "API_URL=${{ vars.API_URL }}" >> .env
          cat .env

      # Login docker hub
      - name: Login Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v1
        id: buildx

      - name: Cache Docker Image Layers
        uses: actions/cache@v2
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        id: docker_build
        with:
          context: ./
          file: ./Dockerfile
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          tags: ${{ secrets.DOCKER_USER }}/industrii-admin:latest
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Verify
        run: |
          echo ${{ steps.docker_build.outputs.digest }}
          ls -la
          cat .env


  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [build]

    steps:
      - name: SSH and Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEV_SSH_HOST }}
          username: ${{ secrets.DEV_SSH_USER }}
          key: ${{ secrets.DEV_SSH_KEY }}
          script: |
            docker stack deploy -c ~/data/industrii/stack.yml --with-registry-auth industrii
