{"name": "industrii-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build:babel": "rm -rf .dist; babel ./src --out-dir .dist --extensions .ts", "build": "yarn swagger && next build", "start": "yarn migration:run && next start", "lint": "next lint", "lint:check": "eslint \"./src/**/*.{js,ts,jsx,tsx}\"", "lint:fix": "eslint --fix \"./src/**/*.{js,ts,jsx,tsx}\"", "format": "prettier --check \"./**/*.{js,ts,jsx,tsx,json}\"", "format:fix": "prettier --write \"./**/*.{js,ts,jsx,tsx,json}\"", "migration:run": "yarn build:babel && typeorm migration:run -d ./.dist/backend/database/data-source.js", "migration:revert": "yarn build:babel && typeorm migration:revert -d ./.dist/backend/database/data-source.js", "migration:generate": "yarn build:babel && typeorm migration:generate -d ./.dist/backend/database/data-source.js ./src/backend/database/migrations/migrations", "migration:create": "typeorm migration:create ./src/backend/database/migrations/migrations", "prepare": "husky", "swagger": "yarn next-swagger-doc-cli next-swagger-doc.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.550.0", "@babel/cli": "^7.24.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.4", "@hubspot/api-client": "^11.1.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.25.0", "@tanstack/react-query-devtools": "^5.28.10", "@tanstack/react-table": "^8.13.2", "@tanstack/react-virtual": "^3.2.1", "@vercel/blob": "^0.23.2", "@vercel/functions": "^2.0.0", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.4", "csv-parse": "^5.5.5", "csv-stringify": "^6.4.6", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "firebase": "^10.8.1", "firebase-admin": "^12.0.0", "focus-trap-react": "^10.2.3", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.354.0", "next": "14.1.3", "next-route-handler-pipe": "^2.0.0", "next-swagger-doc": "^0.4.0", "nodemailer": "^6.9.13", "nuqs": "^2.4.3", "pg": "^8.11.3", "pino": "^9.2.0", "pino-pretty": "^11.2.1", "plaid": "^22.0.1", "pluralize": "^8.0.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "react": "^18", "react-aria": "^3.32.1", "react-day-picker": "^8.10.0", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.0.6", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.4.1", "react-image-file-resizer": "^0.4.8", "react-input-mask": "^2.0.4", "react-paginate": "^8.2.0", "react-stately": "^3.30.1", "referral-codes": "^3.0.0", "reflect-metadata": "^0.2.1", "swagger-ui-react": "5.3.2", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "typeorm": "^0.3.20", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.24.3", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-transform-private-methods": "^7.24.1", "@types/lodash": "^4.17.1", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/pluralize": "^0.0.33", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-input-mask": "^3.0.5", "@types/swagger-ui-react": "^4.18.3", "@typescript-eslint/eslint-plugin": "7.2.0", "@typescript-eslint/parser": "7.2.0", "autoprefixer": "^10.0.1", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8", "eslint-config-next": "14.1.3", "eslint-config-prettier": "^9.1.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "^8", "prettier": "^3.2.5", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write", "yarn run eslint --fix"], "*.{md,json}": ["prettier --write"]}}