{"parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "tsconfigRootDir": ".", "sourceType": "module"}, "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "eslint:recommended", "prettier"], "plugins": ["@typescript-eslint/eslint-plugin"], "ignorePatterns": [".eslintrc.js", "next.config.mjs"], "globals": {"React": true}, "rules": {"no-var": "error", "no-console": "warn", "no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "space-before-blocks": "error", "brace-style": "error"}}