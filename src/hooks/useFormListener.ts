import { useSurveyStore } from '@/components/sections/surveys/hooks/useSurveyStore';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { create } from 'zustand';
import { useShallow } from 'zustand/react/shallow';

interface FormStore {
  isFormDirty: boolean;
  setFormDirty: (dirty: boolean) => void;
}

export const useFormDirtyStore = create<FormStore>(set => ({
  isFormDirty: false,
  setFormDirty: dirty => set(() => ({ isFormDirty: dirty })),
}));

export const useFormListener = () => {
  const formContext = useFormContext();

  const { currentStep } = useSurveyStore(
    useShallow(state => ({
      currentStep: state.currentStep,
    })),
  );
  const { setFormDirty } = useFormDirtyStore();

  useEffect(() => {
    if (!formContext) {
      setFormDirty(false);
    }

    const {
      formState: { dirtyFields },
    } = formContext;
    const formIsDirty = Object.keys(dirtyFields).length > 0;

    setFormDirty(formIsDirty);
  }, [formContext, setFormDirty, currentStep]);

  useEffect(() => {
    return () => {
      setFormDirty(false);
    };
  }, [setFormDirty]);

  return null;
};
