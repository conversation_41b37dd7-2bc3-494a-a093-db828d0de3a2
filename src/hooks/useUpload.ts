import { DeleteImgPayload } from '@/backend/aws/validations/upload';
import { api } from '@/lib/http';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useUpload() {
  const { mutateAsync, isPending, isError } = useMutation({
    mutationFn: (body: FormData) => api.upload.uploadImage(body),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  const { mutateAsync: deleteFile, isPending: isDeleting } = useMutation({
    mutationFn: (body: DeleteImgPayload) => api.upload.deleteImage(body),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    uploadFile: mutateAsync,
    isUploading: isPending,
    isUploadError: isError,
    deleteFile,
    isDeleting,
  };
}
