import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';

export function usePagination() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const page = searchParams.get('page') ? Number(searchParams.get('page')) : 1;
  const pageSize = searchParams.get('pageSize') ? Number(searchParams.get('pageSize')) : 10;

  const goToPage = useCallback(
    (page: number) => {
      const params = new URLSearchParams(Array.from(searchParams.entries()));
      params.set('page', page.toString());
      const paramsString = params.toString();
      router.push(`${pathname}?${paramsString}`, { scroll: false });
    },
    [pathname, router, searchParams],
  );

  const nextPage = useCallback(() => {
    goToPage(Number(page) + 1);
  }, [goToPage, page]);

  const previousPage = useCallback(() => {
    goToPage(Number(page) - 1);
  }, [goToPage, page]);

  const changePageSize = useCallback(
    (pageSize: number) => {
      const params = new URLSearchParams(Array.from(searchParams.entries()));
      params.set('pageSize', pageSize.toString());
      params.set('page', '1');
      const paramsString = params.toString();
      router.push(`${pathname}?${paramsString}`, { scroll: false });
    },
    [pathname, router, searchParams],
  );

  return {
    page,
    pageSize,
    goToPage,
    nextPage,
    previousPage,
    changePageSize,
  };
}
