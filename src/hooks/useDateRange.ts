import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';

type Props = {
  from: string;
  to: string;
};

export function useDateRange() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  const changeDateRange = useCallback(
    ({ from, to }: Props) => {
      const params = new URLSearchParams(Array.from(searchParams.entries()));
      params.set('from', from);
      params.set('to', to);

      const paramsString = params.toString();
      router.push(`${pathname}?${paramsString}`);
    },
    [pathname, router, searchParams],
  );

  return { changeDateRange };
}
