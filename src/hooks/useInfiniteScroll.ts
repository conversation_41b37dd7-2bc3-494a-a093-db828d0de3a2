import React from 'react';

interface UseInfiniteScrollProps {
  hasNextPage?: boolean;
  fetchNextPage?: () => void;
  threshold?: number;
  isFetchingNextPage?: boolean;
}

export function useInfiniteScroll({
  hasNextPage,
  fetchNextPage,
  threshold = 0.1,
  isFetchingNextPage,
}: UseInfiniteScrollProps) {
  const loadMoreRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        const first = entries[0];
        if (first.isIntersecting && hasNextPage && fetchNextPage && !isFetchingNextPage) {
          fetchNextPage();
        }
      },
      { threshold },
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [fetchNextPage, hasNextPage, threshold, isFetchingNextPage]);

  return loadMoreRef;
}
