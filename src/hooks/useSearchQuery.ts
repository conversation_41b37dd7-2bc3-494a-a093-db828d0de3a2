import { useCallback, useEffect, useState } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

export function useSearchQuery() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const search = searchParams.get('search') ?? undefined;
  const [searchString, setSearchString] = useState(search);
  const searchDebounce = useDebounce(searchString, 500);

  const onSearchChange = useCallback(() => {
    const params = new URLSearchParams(Array.from(searchParams.entries()));
    if (searchDebounce === undefined) return;
    if (searchDebounce.length === 0) {
      params.delete('search');
    } else {
      params.set('search', searchDebounce);
    }
    params.set('page', '1');
    const paramsString = params.toString();
    router.push(`${pathname}?${paramsString}`);
  }, [pathname, router, searchDebounce, searchParams]);

  const changeSearch = useCallback(
    (search: string) => {
      setSearchString(() => search);
    },
    [setSearchString],
  );

  useEffect(() => {
    onSearchChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchDebounce]);

  return {
    search,
    changeSearch,
  };
}
