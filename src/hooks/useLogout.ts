'use client';
import { useRouter } from 'next/navigation';
import { useMutation } from '@tanstack/react-query';
import { api } from '@/lib/http';

export function useLogout() {
  const router = useRouter();
  const { mutateAsync } = useMutation({
    mutationFn: async () => api.auth.logout(),
    onSuccess: () => {
      router.replace('/login');
    },
  });

  return {
    logout: mutateAsync,
  };
}
