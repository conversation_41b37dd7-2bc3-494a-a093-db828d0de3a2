'use client';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';

export function useCurrentUser() {
  const { data, isLoading } = useQuery({
    queryKey: ['currentUser'],
    queryFn: async () => {
      return await api.users.me();
    },
  });

  return {
    user: data?.data as UserAdmin | undefined,
    isLoadingCurrentUser: isLoading,
  };
}
