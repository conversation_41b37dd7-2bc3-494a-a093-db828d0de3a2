import { useCallback, useState } from 'react';

export function useLocalPagination() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const goToPage = useCallback((page: number) => {
    setPage(page);
  }, []);

  const nextPage = useCallback(() => {
    setPage(page + 1);
  }, [page]);

  const previousPage = useCallback(() => {
    if (page > 1) {
      setPage(page - 1);
    }
  }, [page]);

  const changePageSize = useCallback((pageSize: number) => {
    setPageSize(pageSize);
  }, []);

  return {
    page,
    pageSize,
    goToPage,
    nextPage,
    previousPage,
    changePageSize,
  };
}
