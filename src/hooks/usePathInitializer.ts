'use client';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { create } from 'zustand';

interface AsPathStoreType {
  prevAsPath: string | undefined;
  currentAsPath: string | undefined;
  pathHistory: { [key: string]: string };
  setPathHistory: (newMap: { [key: string]: string }) => void;
}

export const useAsPathStore = create<AsPathStoreType>(set => ({
  prevAsPath: undefined,
  currentAsPath: undefined,
  pathHistory: {},
  setPathHistory: (newMap: { [key: string]: string }) =>
    set(state => {
      let updatedPathHistory = { ...state.pathHistory, ...newMap };
      const keys = Object.keys(updatedPathHistory);
      if (keys.length > 3) {
        const oldestKey = keys[0];
        const { [oldestKey]: _, ...rest } = updatedPathHistory;
        updatedPathHistory = rest;
      }
      return {
        pathHistory: updatedPathHistory,
      };
    }),
}));

export const useAsPath = () => useAsPathStore(state => state);

export const getAsPath = () => useAsPathStore.getState();

export const getBasePath = (path: string | undefined) => path?.split('?')[0]?.split('/')[1];

export const usePathInitializer = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { currentAsPath, setPathHistory } = useAsPath();

  const params = new URLSearchParams(Array.from(searchParams.entries()));
  const paramsString = params.toString();

  useEffect(() => {
    const basePathname = getBasePath(pathname);
    const baseCurrentAsPath = getBasePath(currentAsPath || pathname);

    const newPath = `${pathname}?${paramsString}`;

    if (currentAsPath !== newPath && !newPath.includes('editId')) {
      setPathHistory({
        [`${basePathname}-${baseCurrentAsPath}`]: currentAsPath || `/${basePathname}`,
      });
      useAsPathStore.setState(state => ({
        ...state,
        currentAsPath: newPath,
        prevAsPath: currentAsPath,
      }));
    }
  }, [pathname, currentAsPath, paramsString, setPathHistory]);

  return null;
};
