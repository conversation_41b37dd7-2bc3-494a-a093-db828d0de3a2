import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect } from 'react';

type UseSortQueryHookParams = {
  sortBy?: string;
  sortOrder?: string;
};

export function useSortQuery({ sortBy: defaultSortBy, sortOrder: defaultSortOrder }: UseSortQueryHookParams = {}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const sortBy = searchParams.get('sortBy') ?? defaultSortBy ?? 'id';
  const sortOrder = searchParams.get('sortOrder') ?? defaultSortOrder ?? 'ASC';

  useEffect(() => {
    if (!defaultSortBy) {
      const params = new URLSearchParams(Array.from(searchParams.entries()));
      params.delete('sortBy');
      params.delete('sortOrder');
      const paramsString = params.toString();
      router.replace(`${pathname}?${paramsString}`);
      return;
    }
    changeSort(defaultSortBy, defaultSortOrder);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const changeSort = useCallback(
    (sortBy?: string, sortOrder?: string) => {
      const params = new URLSearchParams(Array.from(searchParams.entries()));
      if (sortBy) {
        params.set('sortBy', sortBy);
      }

      if (sortOrder) {
        params.set('sortOrder', sortOrder);
      }
      const paramsString = params.toString();
      router.replace(`${pathname}?${paramsString}`);
    },
    [pathname, router, searchParams],
  );

  return {
    sortBy,
    sortOrder,
    changeSort,
  };
}
