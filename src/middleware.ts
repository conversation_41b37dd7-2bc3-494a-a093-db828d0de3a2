import { NextRequest, NextResponse } from 'next/server';

const authApiRoutes = ['/api/auth/login', '/api/auth/signup', '/api/auth/logout'];
const authRoutes = ['/login', '/forgot-password', '/reset-password'];

const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',')?.map(origin => origin.trim()) || [];

export async function middleware(request: NextRequest) {
  const {
    url,
    nextUrl: { pathname, origin },
  } = request;
  const accessToken = request.cookies.get('accessToken');
  const refreshToken = request.cookies.get('refreshToken');

  const isAuthApiRoute = authApiRoutes.includes(pathname);
  const isAuthRoute = authRoutes.includes(pathname);
  const withoutTokens = !accessToken && !refreshToken;

  // Handle CORS
  const requestOrigin = request.headers.get('origin');
  const response = NextResponse.next();

  // Handle CORS for API routes
  if (pathname.startsWith('/api')) {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      const preflightResponse = new NextResponse(null, { status: 204 });
      applySecurityHeaders(preflightResponse, requestOrigin);
      return preflightResponse;
    }

    // Check if origin is allowed
    if (requestOrigin && allowedOrigins.length > 0) {
      if (!allowedOrigins.includes(requestOrigin)) {
        return NextResponse.json({ message: 'Origin not allowed' }, { status: 403 });
      }
      applySecurityHeaders(response, requestOrigin);
    }
  }

  // Handle CORS for remaining routes
  if (requestOrigin && allowedOrigins.length > 0) {
    if (!allowedOrigins.includes(requestOrigin)) {
      return NextResponse.json({ message: 'Origin not allowed' }, { status: 403 });
    }
    applySecurityHeaders(response, requestOrigin);
  }

  // Allow API routes and auth routes to process next if no access token
  if (pathname.includes('/api') || (withoutTokens && isAuthRoute)) {
    const response = NextResponse.next({
      request: {
        ...request,
        headers: request.headers,
      },
    });

    return response;
  }

  // Handle authentication redirects
  if (withoutTokens && !isAuthApiRoute && !isAuthRoute && pathname !== '/login') {
    return NextResponse.redirect(new URL('/login', url));
  }

  const responseAPI = await fetch(`${origin}/api/auth`, {
    headers: {
      Cookie: `accessToken=${accessToken?.value}; refreshToken=${refreshToken?.value}`,
    },
  });

  if (!isAuthApiRoute && pathname.includes('/api')) {
    return responseAPI;
  }

  if (responseAPI.status === 200 && isAuthRoute) {
    return NextResponse.redirect(new URL('/', url));
  }

  if (responseAPI.status !== 200) {
    return isAuthRoute ? NextResponse.next() : NextResponse.redirect(new URL('/login', url));
  }

  return response;
}

function applySecurityHeaders(response: NextResponse, origin: string | null) {
  // If origin is allowed, reflect it back, otherwise use the first allowed origin
  const allowedOrigin = origin && allowedOrigins.includes(origin) ? origin : allowedOrigins[0] || '*';

  response.headers.set('Access-Control-Allow-Origin', allowedOrigin);
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PATCH, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|images|favicon.ico).*)'],
};
