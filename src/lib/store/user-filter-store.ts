import { create } from 'zustand';

// Types
export interface FilterItem {
  field: string;
  operator: string;
  value: string;
}

interface UserFilterState {
  filters: FilterItem[];
  setFilters: (filters: FilterItem[]) => void;
  addFilter: (filter: FilterItem) => void;
  removeFilter: (index: number) => void;
  clearFilters: () => void;
}

export const useUserFilterStore = create<UserFilterState>()(set => ({
  filters: [],

  setFilters: (filters: FilterItem[]) => set({ filters }),

  addFilter: (filter: FilterItem) =>
    set((state: UserFilterState) => {
      // Check if a filter with this field already exists
      const existingFilterIndex = state.filters.findIndex((f: FilterItem) => f.field === filter.field);

      // If it exists, replace it; otherwise add a new one
      if (existingFilterIndex !== -1) {
        const newFilters = [...state.filters];
        newFilters[existingFilterIndex] = filter;
        return { filters: newFilters };
      }

      return { filters: [...state.filters, filter] };
    }),

  removeFilter: (index: number) =>
    set((state: UserFilterState) => ({
      filters: state.filters.filter((_, i) => i !== index),
    })),

  clearFilters: () => set({ filters: [] }),
}));
