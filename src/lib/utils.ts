import { CreateSurveyQuestionPayload } from '@/components/sections/surveys/validations';
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { ObjectLiteral } from 'typeorm';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const isEmpty = <T>(object: T): boolean => {
  return !object || (object && Object.keys(object).length === 0 && object.constructor === Object);
};

export const createPayloadUserIncluded = <T extends object>(data: T): Partial<T> => {
  const payload: Partial<T> = {};
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const value = data[key as keyof T];
      if (value !== undefined && (!Array.isArray(value) || (Array.isArray(value) && value.length))) {
        payload[key] = value as T[Extract<keyof T, string>];
      }
    }
  }
  return payload;
};

export const createPayloadModel = <T extends object, K extends string>(data: T, keys: K[]): Partial<T> => {
  const payload: Partial<T> = {};
  for (const key of keys) {
    const k = key as unknown as keyof T;
    if (data[k] !== undefined) {
      payload[k] = data[k];
    }
  }
  return payload;
};

export const reduces = <T extends { id: number }>(data: T[]): Partial<T[]> => {
  if (!data || !data.length) return [];

  const payload = data.reduce((acc: T[], curr: T) => {
    const find = acc.find(item => item.id === curr.id);
    if (!find) {
      acc.push(curr);
    }
    return acc;
  }, []);

  return payload;
};

export const destructuringModel = <T extends ObjectLiteral>(data: T): Partial<T> => {
  const payload = { ...data };
  delete payload.id;
  delete payload.createdAt;
  delete payload.updatedAt;
  delete payload.deletedAt;
  if (payload.company) {
    delete payload.company;
  }
  if (payload.surveyId) {
    delete payload.surveyId;
  }
  return payload;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const get = (from: any, selector: string, defaultValue: any = undefined) =>
  selector
    .replace(/\[([^[\]]*)\]/g, '.$1.')
    .split('.')
    .filter(t => t !== '')
    .reduce((prev, cur) => prev && prev[cur], from) || defaultValue;

export const removeFakeIdQuestions = (surveyLists: CreateSurveyQuestionPayload[]) => {
  return surveyLists.map((survey: CreateSurveyQuestionPayload) => {
    if (typeof survey?.id === 'string' && survey?.id?.startsWith('FAKE_')) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { id, ...rest } = survey;
      return rest;
    }
    return survey;
  });
};

export const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const formatNumberToDollar = (number: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(number);
};
