import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { BaseApiResponse } from '@/types/base-response';

export type Params = Record<string, unknown>;

export class HttpClient {
  public axiosInstance: AxiosInstance;
  private handler: AxiosResponseHandler;
  private static _instance: HttpClient;

  // Using Singleton Pattern to persist the instance across all HttpApi instances
  public static instance(): HttpClient {
    if (!HttpClient._instance) {
      HttpClient._instance = new HttpClient();
    }
    return HttpClient._instance;
  }

  public constructor() {
    this.axiosInstance = axios.create({
      baseURL: '/api',
      withCredentials: true,
    });

    this.axiosInstance.interceptors.response.use(
      response => response,
      async error => {
        const originalRequest = error.config;
        if (error.response.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          try {
            await this.axiosInstance.post('/auth');
            return this.axiosInstance(originalRequest);
          } catch (refreshError) {
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      },
    );

    this.handler = new AxiosResponseHandler();
  }

  public async get<T>(uri: string): Promise<T> {
    try {
      const r = await this.axiosInstance.get(uri);
      return this.handler.success<T>(r);
    } catch (e) {
      throw this.handler.error(e as AxiosError);
    }
  }

  public async post<T>(uri: string, params: Params | FormData = {}, config: AxiosRequestConfig = {}): Promise<T> {
    try {
      const r = await this.axiosInstance.post(uri, params, config);
      return this.handler.success<T>(r);
    } catch (e) {
      throw this.handler.error(e as AxiosError);
    }
  }

  public async put<T>(uri: string, params: Params = {}): Promise<T> {
    try {
      const r = await this.axiosInstance.put(uri, params);
      return this.handler.success<T>(r);
    } catch (e) {
      throw this.handler.error(e as AxiosError);
    }
  }

  public async patch<T>(uri: string, params: Params = {}): Promise<T> {
    try {
      const r = await this.axiosInstance.patch(uri, params);
      return this.handler.success<T>(r);
    } catch (e) {
      throw this.handler.error(e as AxiosError);
    }
  }

  public async delete<T>(uri: string, params: Params = {}): Promise<T> {
    try {
      const r = await this.axiosInstance.delete(uri, params);
      return this.handler.success<T>(r);
    } catch (e) {
      throw this.handler.error(e as AxiosError);
    }
  }
}

class AxiosResponseHandler {
  public success<TData>(res: AxiosResponse): TData {
    return res.data as TData;
  }

  public error(err: AxiosError) {
    if (err.response && err.response.data !== undefined && (err.response.data as unknown as Error).message) {
      const message = (err.response.data && (err.response.data as unknown as Error).message) || err.message;

      return new Error(message);
    }

    if (err.response?.data) {
      return new Error((err.response.data as BaseApiResponse<object, Error>)?.errors?.[0]?.message);
    }
    return new Error(err.message);
  }
}
