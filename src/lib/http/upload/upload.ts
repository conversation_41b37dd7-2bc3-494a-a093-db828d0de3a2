import { DeleteImgPayload } from '@/backend/aws/validations/upload';
import Base<PERSON>pi from '@/lib/http/base';
import { HttpClient } from '@/lib/http/client';
import { UploadResponse } from '../types';
import { BaseApiResponse } from '@/types/base-response';

export class UploadApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/uploads');
  }

  async deleteImage(data: DeleteImgPayload): Promise<void> {
    return this.http.put(`${this.baseRoute}`, data);
  }

  async uploadImage(data: FormData): Promise<UploadResponse> {
    const response: BaseApiResponse<UploadResponse> = await this.http.post(`${this.baseRoute}`, data);
    return response.data;
  }
}
