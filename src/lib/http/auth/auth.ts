import BaseApi from '@/lib/http/base';
import { HttpClient } from '@/lib/http/client';
import { LoginResponse } from '@/lib/http/auth/types';
import { LoginPayload } from '@/backend/auth/validations/login';
import { AdminForgotPasswordPayload } from '@/backend/auth/validations/admin-forgot-password';
import { AdminResetPasswordPayload } from '@/backend/auth/validations/admin-reset-password';

export class AuthApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/auth');
  }

  async login(params: LoginPayload): Promise<LoginResponse> {
    return this.http.post(`${this.baseRoute}/login`, params);
  }

  async logout(): Promise<void> {
    return this.http.post(`${this.baseRoute}/logout`);
  }

  async forgotPassword(params: AdminForgotPasswordPayload): Promise<void> {
    return this.http.post(`${this.baseRoute}/forgot-password`, params);
  }

  async verifyAdminPasswordResetCode(code: string): Promise<{ isResetPassword: boolean }> {
    return this.http.get(`${this.baseRoute}/reset-password/code-verification?code=${code}`);
  }

  async resetPassword(params: AdminResetPasswordPayload): Promise<void> {
    return this.http.post(`${this.baseRoute}/reset-password`, params);
  }
}
