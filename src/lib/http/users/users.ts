import { HttpClient } from '@/lib/http/client';
import { PaginationPayload, PaginationResponse } from '@/types/pagination';
import { User, UserType } from '@/backend/users/entities/User';
import BaseApi from '@/lib/http/base';
import { CreateUserPayload } from '@/backend/users/validations/create-user';
import { BaseApiResponse } from '@/types/base-response';
import { UserTransaction } from '@/backend/users/entities/UserTransaction';
import { UpdateUserPayload } from '@/backend/users/validations/update-user';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import { UserImportValidated } from '@/backend/users/types/import-user';
import { ImportUsersPayload } from '@/backend/users/validations/import-user';
import { VerifyUserPayload } from '@/backend/users/validations/verify-user';
import { City } from '@/backend/users/entities/City';
import { Province } from '@/backend/users/entities/Province';
import { ExportUsersPayload } from '@/backend/users/validations/export-users';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UpdateUserBalancePayload } from '@/backend/users/validations/update-user-balance';
import { Audience } from '@/backend/audiences/entities/Audience';
import { EditUserInAudiencesPayload } from '@/backend/users/validations/edit-user-in-audiences';
import { RequestUserInfoPayload } from '@/backend/users/validations/request-user-info';

export class UsersApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/users');
  }

  async getUsers(
    payload: PaginationPayload<User> = {},
    filtersQueryString?: string,
  ): Promise<PaginationResponse<User>> {
    const queryParams = this.generateQueryParams(payload);
    const url = `${this.baseRoute}?${queryParams}${filtersQueryString ? `&${filtersQueryString}` : ''}`;
    return this.http.get(url);
  }

  async getPrioritizedUsers(payload: PaginationPayload<User> = {}, ids: number[]): Promise<PaginationResponse<User>> {
    return this.http.post(`${this.baseRoute}/prioritized?${this.generateQueryParams(payload)}`, { ids });
  }

  async deleteUser(id: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${id}`);
  }

  async exportUsers(data: ExportUsersPayload): Promise<string> {
    return this.http.post(`${this.baseRoute}/export`, data);
  }

  async addUser(data: CreateUserPayload): Promise<User> {
    return this.http.post(`${this.baseRoute}`, data);
  }

  async getUser(id: number): Promise<BaseApiResponse<User>> {
    return this.http.get(`${this.baseRoute}/${id}`);
  }

  async getUserCompletedSurveys(id: number): Promise<BaseApiResponse<UserSurvey[]>> {
    return this.http.get(`${this.baseRoute}/${id}/completed-surveys`);
  }

  async getUserTransactions(id: number): Promise<BaseApiResponse<UserTransaction[]>> {
    return this.http.get(`${this.baseRoute}/${id}/transactions`);
  }

  async updateUser(id: number, data: UpdateUserPayload): Promise<User> {
    return this.http.patch(`${this.baseRoute}/${id}`, data);
  }

  async validateCsvImported(data: FormData): Promise<BaseApiResponse<UserImportValidated[]>> {
    return this.http.post(`${this.baseRoute}/import-users/validate-csv`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  }

  async importUsers(data: ImportUsersPayload): Promise<BaseApiResponse<boolean>> {
    return this.http.post(`${this.baseRoute}/import-users`, data);
  }

  async userVerification(data: Partial<VerifyUserPayload> & { id?: number }): Promise<BaseApiResponse<void>> {
    const userId = data.id;
    delete data.id;
    return this.http.patch(`${this.baseRoute}/${userId}/verification`, data);
  }

  async getCities(): Promise<BaseApiResponse<City[]>> {
    return this.http.get(`${this.baseRoute}/cities`);
  }

  async getProvinces(): Promise<BaseApiResponse<Province[]>> {
    return this.http.get(`${this.baseRoute}/provinces`);
  }

  async me(): Promise<BaseApiResponse<User | UserAdmin>> {
    return this.http.get(`${this.baseRoute}/me`);
  }

  async updateBalance(data: UpdateUserBalancePayload & { id: number }) {
    return this.http.post(`${this.baseRoute}/${data.id}/balance`, data);
  }

  async getUserAudiences(id: number): Promise<BaseApiResponse<Audience[]>> {
    return this.http.get(`${this.baseRoute}/${id}/audiences`);
  }

  async addUserToAudiences(id: number, data: EditUserInAudiencesPayload): Promise<BaseApiResponse<void>> {
    return this.http.post(`${this.baseRoute}/${id}/audiences/add`, data);
  }

  async removeUserFromAudiences(id: number, data: EditUserInAudiencesPayload): Promise<BaseApiResponse<void>> {
    return this.http.post(`${this.baseRoute}/${id}/audiences/remove`, data);
  }

  async requestInfo(id: number, payload: RequestUserInfoPayload): Promise<BaseApiResponse<void>> {
    return this.http.patch(`${this.baseRoute}/${id}/request-info`, payload);
  }

  async updateUserNote(id: number, note: string | null): Promise<BaseApiResponse<void>> {
    return this.http.patch(`${this.baseRoute}/${id}/notes`, { note });
  }

  async assignUserRole(id: number, data: UserType): Promise<BaseApiResponse<void>> {
    return this.http.post(`${this.baseRoute}/${id}/assign-user-type`, { userType: data });
  }
}
