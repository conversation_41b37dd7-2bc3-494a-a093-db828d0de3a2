import BaseApi from '@/lib/http/base';
import { HttpClient } from '@/lib/http/client';
import {
  BaseAddressResponse,
  LocationResult,
  RetrievePayload,
  SearchPayload,
  SearchResult,
} from '@/backend/canadapost/validations/canadapost';
import { BaseApiResponse } from '@/types/base-response';

export class CanadaPostApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/canadapost');
  }

  async searchAddress(searchPayload: SearchPayload): Promise<BaseApiResponse<BaseAddressResponse<SearchResult>>> {
    return this.http.get(`${this.baseRoute}/search?${this.generateQueryParams(searchPayload)}`);
  }

  async retrieveAddress(
    addressPayload: RetrievePayload,
  ): Promise<BaseApiResponse<BaseAddressResponse<LocationResult>>> {
    return this.http.get(`${this.baseRoute}/retrieve?${this.generateQueryParams(addressPayload)}`);
  }
}
