import { Audience } from '@/backend/audiences/entities/Audience';
import { Survey } from '@/backend/surveys/entities/Survey';
import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import { QuestionUserAnswers } from '@/backend/surveys/types/question-user-answers';
import { PublishSurveyPayload } from '@/backend/surveys/validations/publish-survey';
import { UpdateQuestionOrderPayload } from '@/backend/surveys/validations/update-survey-question';
import { User } from '@/backend/users/entities/User';
import {
  CreateSurveyInfoPayload,
  CreateSurveyPayload,
  CreateSurveyQuestionPayload,
  PublishAudiencePayload,
} from '@/components/sections/surveys/validations';
import BaseApi from '@/lib/http/base';
import { HttpClient } from '@/lib/http/client';
import { BaseApiResponse } from '@/types/base-response';
import { PaginationPayload, PaginationResponse } from '@/types/pagination';
import { AudienceUsersResponse } from './types';

export class SurveysApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/surveys');
  }

  async getSurveys(payload: PaginationPayload<Survey> = {}): Promise<PaginationResponse<Survey>> {
    return this.http.get(`${this.baseRoute}?${this.generateQueryParams(payload)}`);
  }

  async getSurvey(id: number): Promise<BaseApiResponse<Survey>> {
    return this.http.get(`${this.baseRoute}/${id}`);
  }

  async getSurveyQuestions(id: number): Promise<BaseApiResponse<SurveyQuestion[]>> {
    return this.http.get(`${this.baseRoute}/${id}/questions`);
  }

  async getSurveyAudiences(id: number): Promise<BaseApiResponse<Audience[]>> {
    return this.http.get(`${this.baseRoute}/${id}/audiences`);
  }

  async getPublishedSurveys(): Promise<BaseApiResponse<Survey[]>> {
    return this.http.get(`${this.baseRoute}/published-surveys`);
  }

  async createSurvey(data: CreateSurveyPayload): Promise<Survey> {
    const response: BaseApiResponse<Survey> = await this.http.post(`${this.baseRoute}`, data);
    return response.data;
  }

  async updateSurvey(surveyId: number, data: Partial<CreateSurveyInfoPayload>): Promise<Survey | SurveyQuestion> {
    const response: BaseApiResponse<Survey> = await this.http.patch(`${this.baseRoute}/${surveyId}`, data);
    return response.data;
  }

  async updateSurveyQuestions(
    surveyId: number,
    data: Partial<CreateSurveyQuestionPayload>,
  ): Promise<Survey | SurveyQuestion> {
    const response: BaseApiResponse<Survey> = await this.http.patch(`${this.baseRoute}/${surveyId}/questions`, data);
    return response.data;
  }

  async createSurveyQuestions(data: CreateSurveyQuestionPayload): Promise<Survey | SurveyQuestion> {
    const response: BaseApiResponse<Survey> = await this.http.post(
      `${this.baseRoute}/${data.surveyId}/questions`,
      data,
    );
    return response.data;
  }

  async publishAudiences(data: PublishAudiencePayload & { surveyId: number }): Promise<void> {
    return this.http.post(`${this.baseRoute}/${data.surveyId}/publish`, data);
  }

  async publishPublicSurvey(surveyId: number): Promise<void> {
    return this.http.post(`${this.baseRoute}/${surveyId}/publish`);
  }

  public async saveDraftAudience(data: PublishSurveyPayload & { surveyId: number }): Promise<void> {
    return this.http.post(`${this.baseRoute}/${data.surveyId}/audiences`, data);
  }

  async updateQuestionOrder(surveyId: number, data: UpdateQuestionOrderPayload): Promise<void> {
    return await this.http.patch(`${this.baseRoute}/${surveyId}/questions/order`, data);
  }

  async duplicateSurvey(id: number): Promise<BaseApiResponse<Survey>> {
    return this.http.post(`${this.baseRoute}/${id}/duplicate`);
  }

  async exportSurvey(id: number): Promise<string> {
    return this.http.get(`${this.baseRoute}/${id}/exports`);
  }

  async getSurveyResults(id: number): Promise<BaseApiResponse<QuestionUserAnswers[]>> {
    return this.http.get(`${this.baseRoute}/${id}/results`);
  }

  async deleteSurveyQuestion(surveyId: number, questionId: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${surveyId}/questions/${questionId}`);
  }

  async deleteSurvey(id: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${id}`);
  }
  async unPublishSurvey(surveyId: number): Promise<void> {
    return this.http.post(`${this.baseRoute}/${surveyId}/unpublish`);
  }

  async getSurveyParticipants(surveyId: number, payload: PaginationPayload<User> = {}): Promise<AudienceUsersResponse> {
    return this.http.get(`${this.baseRoute}/${surveyId}/users?${this.generateQueryParams(payload)}`);
  }
}
