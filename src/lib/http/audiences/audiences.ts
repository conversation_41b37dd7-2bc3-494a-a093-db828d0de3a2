import { HttpClient } from '@/lib/http/client';
import { PaginationPayload, PaginationResponse } from '@/types/pagination';
import { Audience } from '@/backend/audiences/entities/Audience';
import BaseApi from '@/lib/http/base';
import { BaseApiResponse } from '@/types/base-response';
import {
  CreateAudiencePayload,
  UpdateAudiencePayload,
  UsersIncludedPayload,
} from '@/backend/audiences/validations/add-update-audience';
import { Survey } from '@/backend/surveys/entities/Survey';
import { AudienceItem } from '@/lib/http/audiences/types';
import { User } from '@/backend/users/entities/User';

export class AudiencesApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/audiences');
  }

  async getAudiences(payload: PaginationPayload<Audience> = {}): Promise<PaginationResponse<AudienceItem>> {
    return this.http.get(`${this.baseRoute}?${this.generateQueryParams(payload)}`);
  }

  async deleteAudience(id: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${id}`);
  }

  async addAudience(data: CreateAudiencePayload): Promise<void> {
    return this.http.post(`${this.baseRoute}`, data);
  }

  async getAudience(id: number): Promise<BaseApiResponse<Audience>> {
    return this.http.get(`${this.baseRoute}/${id}`);
  }

  async updateAudience(id: number, data: UpdateAudiencePayload): Promise<BaseApiResponse<void>> {
    return this.http.patch(`${this.baseRoute}/${id}`, data);
  }

  async duplicateAudience(id: number): Promise<BaseApiResponse<Audience>> {
    return this.http.post(`${this.baseRoute}/${id}/duplicate`);
  }

  async checkNameExist(name: string): Promise<BaseApiResponse<{ isNameExist: boolean }>> {
    return this.http.get(`${this.baseRoute}/name-verification?name=${name}`);
  }

  async countUsersIncluded(data: UsersIncludedPayload): Promise<BaseApiResponse<{ usersIncluded: number }>> {
    return this.http.post(`${this.baseRoute}/users-included`, data);
  }

  async getAudienceSurveys(id: number): Promise<BaseApiResponse<Survey[]>> {
    return this.http.get(`${this.baseRoute}/${id}/surveys`);
  }

  async getAudienceUsers(id: number, payload: PaginationPayload<User> = {}): Promise<PaginationResponse<User[]>> {
    return this.http.get(`${this.baseRoute}/${id}/users?${this.generateQueryParams(payload)}`);
  }

  async removeUserFromAudience(id: number, payload: { userIds: number[] }): Promise<BaseApiResponse<User[]>> {
    return this.http.post(`${this.baseRoute}/${id}/users/remove`, payload);
  }

  async exportAudienceUsers(id: number): Promise<string> {
    return this.http.post(`${this.baseRoute}/${id}/exports`);
  }

  async exportAudienceSurveys(id: number): Promise<string> {
    return this.http.post(`${this.baseRoute}/${id}/export-surveys`);
  }
}
