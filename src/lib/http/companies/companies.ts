import { HttpClient } from '@/lib/http/client';
import { PaginationPayload, PaginationResponse } from '@/types/pagination';
import { Company } from '@/backend/companies/entities/Company';
import BaseApi from '@/lib/http/base';
import { BaseApiResponse } from '@/types/base-response';
import { AddUpdateCompanyPayload } from '@/backend/companies/validations/add-update-company';
import { Survey } from '@/backend/surveys/entities/Survey';

export class CompaniesApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/companies');
  }

  async getCompanies(payload: PaginationPayload<Company> = {}): Promise<PaginationResponse<Company>> {
    return this.http.get(`${this.baseRoute}?${this.generateQueryParams(payload)}`);
  }

  async deleteCompany(id: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${id}`);
  }

  async addCompany(data: AddUpdateCompanyPayload): Promise<void> {
    return this.http.post(`${this.baseRoute}`, data);
  }

  async getCompany(id: number): Promise<BaseApiResponse<Company>> {
    return this.http.get(`${this.baseRoute}/${id}`);
  }

  async updateCompany(id: number, data: AddUpdateCompanyPayload): Promise<BaseApiResponse<void>> {
    return this.http.patch(`${this.baseRoute}/${id}`, data);
  }

  async checkNameExist(name: string): Promise<BaseApiResponse<{ isNameExist: boolean }>> {
    return this.http.get(`${this.baseRoute}/name-verification?name=${name}`);
  }

  async getCompanySurveys(id: number): Promise<BaseApiResponse<Survey[]>> {
    return this.http.get(`${this.baseRoute}/${id}/surveys`);
  }
}
