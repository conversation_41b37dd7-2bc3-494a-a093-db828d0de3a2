import { HttpClient } from '@/lib/http/client';
import Base<PERSON><PERSON> from '@/lib/http/base';
import { AddAdminPayload, UpdateAdminPayload } from '@/backend/users/validations/add-update-admin';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { AdminChangePasswordPayload } from '@/backend/auth/validations/admin-change-password';
import { BaseApiResponse } from '@/types/base-response';

export class UserAdminApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/user-admin');
  }

  async getDashboardUsers(): Promise<BaseApiResponse<UserAdmin[]>> {
    return this.http.get(`${this.baseRoute}`);
  }

  async addUserAdmin(data: AddAdminPayload): Promise<UserAdmin> {
    return this.http.post(`${this.baseRoute}`, data);
  }

  async getUserAdmin(id: number): Promise<UserAdmin> {
    return this.http.get(`${this.baseRoute}/${id}`);
  }

  async updateUserAdmin(id: number, data: UpdateAdminPayload): Promise<UserAdmin> {
    return this.http.patch(`${this.baseRoute}/${id}`, data);
  }

  async deleteUserAdmin(id: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${id}`);
  }

  async changePassword(data: AdminChangePasswordPayload) {
    return this.http.patch(`${this.baseRoute}/change-password`, data);
  }

  async updateSelf(data: UpdateAdminPayload) {
    return this.http.patch(`${this.baseRoute}`, data);
  }
}
