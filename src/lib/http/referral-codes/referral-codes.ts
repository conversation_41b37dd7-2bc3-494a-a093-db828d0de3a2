import { HttpClient } from '@/lib/http/client';
import { PaginationPayload, PaginationResponse } from '@/types/pagination';
import BaseApi from '@/lib/http/base';
import { BaseApiResponse } from '@/types/base-response';
import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import {
  CreateReferralCodePayload,
  UpdateReferralCodePayload,
} from '@/backend/referral-codes/validations/referral-code';

export class PublicReferralCodesApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/referral-codes');
  }

  async getPublicReferralCodes(
    payload: PaginationPayload<PublicReferralCode> = {},
  ): Promise<PaginationResponse<PublicReferralCode>> {
    return this.http.get(`${this.baseRoute}?${this.generateQueryParams(payload)}`);
  }

  async deletePublicReferralCode(id: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${id}`);
  }

  async addPublicReferralCode(data: CreateReferralCodePayload): Promise<void> {
    return this.http.post(`${this.baseRoute}`, data);
  }

  async getPublicReferralCode(id: number): Promise<BaseApiResponse<PublicReferralCode>> {
    return this.http.get(`${this.baseRoute}/${id}`);
  }

  async updatePublicReferralCode(id: number, data: UpdateReferralCodePayload): Promise<BaseApiResponse<void>> {
    return this.http.patch(`${this.baseRoute}/${id}`, data);
  }

  async checkCodeExist(code: string): Promise<BaseApiResponse<{ isCodeExist: boolean }>> {
    return this.http.get(`${this.baseRoute}/code-verification?code=${code}`);
  }
}
