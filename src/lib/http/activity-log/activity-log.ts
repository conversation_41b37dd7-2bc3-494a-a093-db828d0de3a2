import BaseApi from '@/lib/http/base';
import { HttpClient } from '@/lib/http/client';
import { BaseApiResponse } from '@/types/base-response';
import { GetUserActionsPayload, UserActionsResponse } from '@/backend/users/validations/user-action';

export class ActivityLogApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/activity-log');
  }

  async getActivityLogs(payload: GetUserActionsPayload): Promise<BaseApiResponse<UserActionsResponse>> {
    return this.http.get(`${this.baseRoute}?${this.generateQueryParams(payload)}`);
  }
}
