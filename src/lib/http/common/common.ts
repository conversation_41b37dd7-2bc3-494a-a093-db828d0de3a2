import BaseApi from '@/lib/http/base';
import { HttpClient } from '@/lib/http/client';
import { IndustryInfo } from '@/backend/users/types/user';
import { BaseApiResponse } from '@/types/base-response';

export class CommonApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '');
  }

  async getIndustryInfo(): Promise<BaseApiResponse<IndustryInfo>> {
    return this.http.get(`${this.baseRoute}/users/industry-info`);
  }
}
