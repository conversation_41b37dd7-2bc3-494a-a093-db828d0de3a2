import { HttpClient } from '@/lib/http/client';
import { PaginationPayload, PaginationResponse } from '@/types/pagination';
import { SupportRequest } from '@/backend/support/entities/SupportRequest';
import BaseApi from '@/lib/http/base';

export class SupportsApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/supports');
  }

  async getSupports(payload: PaginationPayload<SupportRequest> = {}): Promise<PaginationResponse<SupportRequest>> {
    return this.http.get(`${this.baseRoute}?${this.generateQueryParams(payload)}`);
  }
}
