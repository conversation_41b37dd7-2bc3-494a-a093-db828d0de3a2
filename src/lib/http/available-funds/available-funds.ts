import { BaseApiResponse } from '@/types/base-response';
import BaseApi from '../base';
import { HttpClient } from '../client';
import { AvailableFundsResponse } from './type';
import { SummariesPayload } from '@/app/api/available-funds/summaries/route';
import { PaginationPayload, PaginationResponseData } from '@/types/pagination';
import { Survey } from '@/backend/surveys/entities/Survey';

export class AvailableFunds extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/available-funds');
  }

  async getAvailableFunds(payload: SummariesPayload): Promise<BaseApiResponse<AvailableFundsResponse>> {
    return this.http.get(`${this.baseRoute}/summaries?${this.generateQueryParams(payload)}`);
  }

  async getAvailableFundSurveys(
    payload: PaginationPayload<Survey>,
  ): Promise<BaseApiResponse<PaginationResponseData<Survey> & { walletFunds: number }>> {
    return this.http.get(`${this.baseRoute}/surveys?${this.generateQueryParams(payload)}`);
  }
}
