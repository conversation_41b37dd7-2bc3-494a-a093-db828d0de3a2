'use client';
import authApi from '@/lib/http/auth';
import usersApi from '@/lib/http/users';
import commonApi from '@/lib/http/common';
import specialtiesApi from '@/lib/http/specialties';
import companiesApi from '@/lib/http/companies';
import audiencesApi from '@/lib/http/audiences';
import surveysApi from '@/lib/http/surveys';
import canadapostApi from '@/lib/http/canadapost';
import supportsApi from '@/lib/http/supports';
import publicReferralCodesApi from '@/lib/http/referral-codes';
import uploadApi from '@/lib/http/upload';
import userAdminApi from '@/lib/http/user-admin';
import activityLogApi from '@/lib/http/activity-log';
import availableFundsApi from '@/lib/http/available-funds';
export const api = {
  auth: authApi,
  users: usersApi,
  common: commonApi,
  specialties: specialtiesApi,
  companies: companiesApi,
  audiences: audiencesApi,
  surveys: surveysApi,
  canadapost: canadapostApi,
  supports: supportsApi,
  publicReferralCodes: publicReferralCodesApi,
  upload: uploadApi,
  userAdmin: userAdminApi,
  activityLog: activityLogApi,
  availableFunds: availableFundsApi,
};
