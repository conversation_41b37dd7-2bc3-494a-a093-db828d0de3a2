import { HttpClient } from '@/lib/http/client';

export default class BaseApi {
  protected readonly http: HttpClient;
  protected readonly baseRoute: string;

  public constructor(http: HttpClient, baseRoute: string) {
    this.http = http;
    this.baseRoute = baseRoute;
  }

  protected generateQueryParams(params: Record<string, unknown>) {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }

    const queryParams = new URLSearchParams();

    for (const key in params) {
      const value = params[key];
      if (value === undefined) continue;
      if (Array.isArray(value)) {
        for (const item of value) {
          queryParams.append(key, item);
        }
      } else {
        queryParams.append(key, value as string);
      }
    }

    return queryParams.toString();
  }
}
