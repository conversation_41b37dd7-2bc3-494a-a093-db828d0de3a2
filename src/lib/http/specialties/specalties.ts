import BaseApi from '@/lib/http/base';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { HttpClient } from '@/lib/http/client';
import { BaseApiResponse } from '@/types/base-response';
import { CreateSpecialtyPayload } from '@/backend/specialties/validations/create-specialty';
import { UpdateSpecialtyPayload } from '@/backend/specialties/validations/update-specialty';
import { PaginationPayload, PaginationResponse } from '@/types/pagination';
import { Survey } from '@/backend/surveys/entities/Survey';

export class SpecialtiesApi extends BaseApi {
  constructor(http: HttpClient) {
    super(http, '/specialties');
  }

  async getSpecialties(payload: PaginationPayload<Specialty> = {}): Promise<PaginationResponse<Specialty>> {
    return this.http.get(`${this.baseRoute}?${this.generateQueryParams(payload)}`);
  }

  async getAllSpecialties(): Promise<BaseApiResponse<Specialty[]>> {
    return this.http.get(`${this.baseRoute}/all`);
  }

  async getSpecialty(id: number): Promise<BaseApiResponse<Specialty>> {
    return this.http.get(`${this.baseRoute}/${id}`);
  }

  async addSpecialty(data: CreateSpecialtyPayload): Promise<Specialty> {
    return this.http.post(`${this.baseRoute}`, data);
  }

  async deleteSpecialty(id: number): Promise<void> {
    return this.http.delete(`${this.baseRoute}/${id}`);
  }

  async checkNameExist(name: string): Promise<BaseApiResponse<{ isNameExist: boolean }>> {
    return this.http.get(`${this.baseRoute}/name-verification?name=${name}`);
  }

  async updateSpecialty(id: number, data: UpdateSpecialtyPayload): Promise<BaseApiResponse<Specialty>> {
    return this.http.patch(`${this.baseRoute}/${id}`, data);
  }

  async getSpecialtySurveys(id: number): Promise<BaseApiResponse<Survey[]>> {
    return this.http.get(`${this.baseRoute}/${id}/surveys`);
  }
}
