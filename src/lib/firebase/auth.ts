'use server';

import { cookies } from 'next/headers';
import { auth } from 'firebase-admin';

export async function getAuthenticatedUser() {
  const session = cookies().get('session')?.value || '';
  if (!session) return null;
  try {
    const decodedToken = await auth().verifySessionCookie(session, true);
    if (!decodedToken) return null;
    return decodedToken;
  } catch (err) {
    return null;
  }
}
