import React from 'react';
import { Input, Label } from '@/components/ui/form';
import LoginForm from '@/app/(auth)/login/login-form';
import Image from 'next/image';
import { PasswordInput } from '@/components/ui/form/password-input';

const Login = () => {
  return (
    <div className="h-full md:h-auto w-full md:w-auto absolute -translate-x-1/2 left-1/2 top-1/2 -translate-y-1/2">
      <div className="h-full w-full md:h-auto md:w-auto flex items-center bg-white rounded-xl p-6 drop-shadow-[0_20px_24px_rgba(16,24,40,0.08)]">
        <div className="flex-1">
          <LoginForm>
            <div className="w-full md:w-25.5">
              <div className="flex justify-center">
                <Image
                  src="/images/logo-text-violet.svg"
                  alt="logo-text"
                  height={35}
                  width={157}
                  className="max-h-[35px]"
                />
              </div>
              <div className="font-semibold text-lg mt-8 mb-5 text-center leading-7">Admin Portal Login</div>
              <div className="mb-4">
                <div className="h-5 max-h-5 mb-1.5">
                  <Label htmlFor="email" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
                    Email
                  </Label>
                </div>
                <Input type="text" name="email" id="email" placeholder="<EMAIL>" />
              </div>
              <div className="mb-8">
                <div className="h-5 max-h-5 mb-1.5">
                  <Label htmlFor="password" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
                    Password
                  </Label>
                </div>
                <PasswordInput name="password" id="password" placeholder="••••••••" />
              </div>
            </div>
          </LoginForm>
        </div>
      </div>
    </div>
  );
};
export default Login;
