'use client';
import React from 'react';
import Link from 'next/link';
import toast from 'react-hot-toast';
import * as z from 'zod';
import { useMutation } from '@tanstack/react-query';

import { Form } from '@/components/ui/form';
import { api } from '@/lib/http';
import { LoginParams } from '@/lib/http/auth';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

type LoginFormProps = {
  children?: React.ReactElement;
};

const validateSchema = z.object({
  email: z
    .string({
      required_error: 'Email is required',
    })
    .email('Please enter a valid email'),
  password: z.string({ required_error: 'Password is required' }).min(8, 'Password must be at least 8 characters'),
});

type FormData = z.infer<typeof validateSchema>;

const LoginForm = ({ children }: LoginFormProps) => {
  const { mutate, isPending, isSuccess } = useMutation({
    mutationFn: (params: LoginParams) => api.auth.login(params),
    onSuccess: () => {
      window.location.reload();
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  const onSubmit = async (data: FormData): Promise<void> => {
    mutate(data);
  };

  return (
    <Form
      schema={validateSchema}
      onSubmit={onSubmit}
      mode="onSubmit"
      className={cn(isPending && 'group is-submitting')}
    >
      {children}
      <Button
        type="submit"
        size="lg"
        className="w-full font-semibold group-[.is-submitting]:pointer-events-none group-[.is-submitting]:opacity-50"
        isLoading={isPending || isSuccess}
      >
        Login
      </Button>
      <div className="text-center mt-8">
        <Link href="/forgot-password" className="hover:underline underline-offset-4 hover:text-primary">
          Forgot Password
        </Link>
      </div>
    </Form>
  );
};
export default LoginForm;
