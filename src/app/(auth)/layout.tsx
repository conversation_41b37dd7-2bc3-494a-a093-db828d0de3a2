import type { Metadata } from 'next';
import { PropsWithChildren } from 'react';
import Image from 'next/image';

export const metadata: Metadata = {
  title: 'Login - Industrii Admin',
};

export default function AuthLayout({ children }: PropsWithChildren) {
  return (
    <main className="min-h-screen flex justify-center items-center bg-[#EAECF0]">
      <div className="w-full h-screen md:h-fit relative">
        <div className="absolute -translate-x-1/2 left-1/2 top-1/2 -translate-y-1/2">
          <Image width={561} height={561} src={'/images/background-icon.png'} alt="background-icon" />
        </div>
        {children}
      </div>
    </main>
  );
}
