'use client';
import React from 'react';
import * as z from 'zod';
import Link from 'next/link';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';

import { Form, Label } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { PasswordInput } from '@/components/ui/form/password-input';
import { useResetPassword } from '@/components/sections/auth/hooks/useResetPassword';
import { useVerifyCode } from '@/components/sections/auth/hooks/useVerifyCode';
import ResetSuccess from './reset-success';
import { passwordSchema } from '@/backend/shared/validations/password';
import { Loader } from 'lucide-react';

const validateSchema = z
  .object({
    confirmPassword: z.string().optional(),
  })
  .merge(passwordSchema)
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type FormData = z.infer<typeof validateSchema>;

const ResetPasswordForm = () => {
  const searchParams = useSearchParams();
  const code = searchParams.get('oobCode') || '';
  const { resetPassword, isResetting, isReset } = useResetPassword();
  const { isVerifying, isFailed, verifiedStatus } = useVerifyCode(code);

  const onSubmit = async (data: FormData): Promise<void> => {
    await resetPassword({ password: data.password as string, code });
  };

  if (isReset) return <ResetSuccess />;

  return (
    <Form schema={validateSchema} onSubmit={onSubmit} mode="onSubmit" autoComplete="off">
      <div className="w-full md:w-25.5">
        <div className="flex justify-center">
          <Image src="/images/logo-text-violet.svg" alt="logo-text" height={35} width={157} className="max-h-[35px]" />
        </div>
        {isFailed ? (
          <>
            <div className="font-semibold text-lg mt-8 mb-2 text-center leading-7">Unfortunately</div>
            <div className="font-medium text-sm mt-2 mb-4 text-center leading-7">The link is invalid or expired</div>
            <Link href="/login">
              <Button size="lg" className="w-full font-semibold">
                Back to Login
              </Button>
            </Link>
          </>
        ) : (
          <>
            {isVerifying ? (
              <div className="flex justify-center py-20">
                <Loader height={100} width={100} className="animate-spin text-muted-foreground" />
              </div>
            ) : (
              <>
                <div className="font-semibold text-lg mt-8 mb-2 text-center leading-7">
                  {verifiedStatus?.isResetPassword ? 'Reset Password' : 'Set Password'}
                </div>
                <div className="font-medium text-sm mt-2 mb-4 text-center leading-7">Enter new password</div>
                <div className="mb-4">
                  <div className="h-5 max-h-5 mb-1.5">
                    <Label htmlFor="password" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
                      Password
                    </Label>
                  </div>

                  <PasswordInput
                    name="password"
                    id="password"
                    placeholder="••••••••"
                    disabled={isVerifying || isResetting}
                    isShowGroupErrors={true}
                  />
                </div>
                <div className="mb-8">
                  <div className="h-5 max-h-5 mb-1.5">
                    <Label htmlFor="confirm-password" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
                      Confirm Password
                    </Label>
                  </div>

                  <PasswordInput
                    name="confirmPassword"
                    id="confirm-password"
                    placeholder="••••••••"
                    disabled={isVerifying || isResetting}
                  />
                </div>
              </>
            )}

            <Button type="submit" size="lg" className="w-full font-semibold mt-4" disabled={isVerifying || isResetting}>
              {isVerifying ? 'Verifying...' : verifiedStatus?.isResetPassword ? 'Reset Password' : 'Set Password'}
            </Button>
          </>
        )}
      </div>
    </Form>
  );
};

export default ResetPasswordForm;
