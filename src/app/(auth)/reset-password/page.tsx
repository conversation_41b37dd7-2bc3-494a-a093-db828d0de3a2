import React, { Suspense } from 'react';
import { Metadata } from 'next/types';
import ResetPasswordForm from './reset-password-form';

export const metadata: Metadata = {
  title: 'Reset Password - Industrii Admin',
};

const ResetPasswordPage: React.FC = () => {
  return (
    <div className="h-full md:h-auto w-full md:w-auto absolute -translate-x-1/2 left-1/2 top-1/2 -translate-y-1/2">
      <div className="h-full w-full md:h-auto md:w-auto flex items-center bg-white rounded-xl p-6 drop-shadow-[0_20px_24px_rgba(16,24,40,0.08)]">
        <div className="flex-1">
          <Suspense>
            <ResetPasswordForm />
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
