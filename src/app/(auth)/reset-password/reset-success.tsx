'use client';
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

import { Button } from '@/components/ui/button';

const ResetSuccess: React.FC = () => {
  return (
    <div className="w-full md:w-25.5">
      <div className="flex justify-center">
        <Image src="/images/logo-text-violet.svg" alt="logo-text" height={35} width={157} className="max-h-[35px]" />
      </div>
      <div className="font-semibold text-lg mt-8 mb-2 text-center leading-7">Password Reset Completed</div>
      <div className="font-medium text-sm mt-2 mb-8 text-center leading-7">
        You can now login with your new password
      </div>
      <Link href="/login" replace>
        <Button size="lg" className="w-full font-semibold">
          Back to Login
        </Button>
      </Link>
    </div>
  );
};

export default ResetSuccess;
