'use client';
import React from 'react';
import * as z from 'zod';
import Link from 'next/link';
import Image from 'next/image';

import { Form, Input, Label } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { useForgotPassword } from '@/components/sections/auth/hooks/useForgotPassword';
import RequestSuccess from './request-success';

const validateSchema = z.object({
  email: z
    .string({
      required_error: 'Email is required',
    })
    .toLowerCase()
    .email('Please enter a valid email'),
});

type FormData = z.infer<typeof validateSchema>;

const ForgotPasswordForm = () => {
  const { forgotPassword, isSending, isSent } = useForgotPassword();
  const [email, setEmail] = React.useState('');
  const onSubmit = async (data: FormData): Promise<void> => {
    setEmail(data.email);
    await forgotPassword(data.email);
  };

  if (isSent) return <RequestSuccess email={email} />;

  return (
    <Form schema={validateSchema} onSubmit={onSubmit} mode="onBlur">
      <div className="w-full md:w-25.5">
        <div className="flex justify-center">
          <Image src="/images/logo-text-violet.svg" alt="logo-text" height={35} width={157} className="max-h-[35px]" />
        </div>
        <div className="font-semibold text-lg mt-8 mb-2 text-center leading-7">Forgot Password</div>
        <div className="font-medium text-sm mt-2 mb-4 text-center leading-7">
          Enter your email to receive a password reset link
        </div>
        <div className="mb-4">
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="email" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
              Email
            </Label>
          </div>
          <Input type="email" name="email" id="email" placeholder="<EMAIL>" disabled={isSending} />
        </div>

        <Button type="submit" size="lg" className="w-full font-semibold mt-4" disabled={isSending}>
          Request Link
        </Button>
        <div className="text-center mt-8">
          <Link href="/login" className="hover:underline underline-offset-4 hover:text-primary">
            Back to Login
          </Link>
        </div>
      </div>
    </Form>
  );
};
export default ForgotPasswordForm;
