'use client';
import React from 'react';
import Image from 'next/image';
import { useForgotPassword } from '@/components/sections/auth/hooks/useForgotPassword';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

type Props = {
  email: string;
};

const RequestSuccess = ({ email }: Props) => {
  const { forgotPassword, isSending, isSent } = useForgotPassword();
  return (
    <div className="w-full md:w-25.5">
      <div className="flex justify-center">
        <Image src="/images/logo-text-violet.svg" alt="logo-text" height={35} width={157} className="max-h-[35px]" />
      </div>
      <div className="font-semibold text-lg mt-8 mb-2 text-center leading-7">
        {isSent ? 'Link Re-sent' : 'Link Sent'}
      </div>
      <div className="font-medium text-sm mt-2 mb-4 text-center leading-7">Please check your email</div>
      <Button
        type="submit"
        size="lg"
        className={cn('w-full font-semibold mt-4', isSent && 'hidden')}
        disabled={isSending}
        onClick={() => forgotPassword(email)}
      >
        Resend Link
      </Button>
    </div>
  );
};

export default RequestSuccess;
