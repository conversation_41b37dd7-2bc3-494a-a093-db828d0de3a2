'use client';

import { redirect } from 'next/navigation';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import LoadingSpinner from '@/components/common/loading-spinner';

export default function Home() {
  const { user, isLoadingCurrentUser } = useCurrentUser();

  if (isLoadingCurrentUser) {
    return (
      <div className="flex w-full items-center relative justify-center h-[100dvh]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (user?.role === AdminRole.AccountManager) {
    redirect('/surveys');
  } else {
    redirect('/users');
  }
}
