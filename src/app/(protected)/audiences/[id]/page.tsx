import React from 'react';
import { Metadata } from 'next/types';

import EditAudienceForm from '@/components/sections/audiences/edit-audience-form';
import { GoBack } from '@/components/common/go-back';
import AudienceTabs from '@/components/sections/audiences/audience-tabs/audience-tabs';

export const metadata: Metadata = {
  title: 'Audience - Industrii Admin',
};

const ViewEditAudience = ({ params }: { params: { id: number } }) => {
  return (
    <div className="w-full">
      <div className="flex">
        <GoBack name="Audiences" path="/audiences" />
      </div>
      <div className="w-full px-2 sm:px-8 mt-6">
        <div className="w-full">
          <EditAudienceForm audienceId={params.id} />
        </div>
        <AudienceTabs audienceId={params.id} />
      </div>
    </div>
  );
};
export default ViewEditAudience;
