import React from 'react';
import { Metadata } from 'next/types';
import EditSpecialtyForm from '@/components/sections/specialties/edit-specialty-form';
import TableSpecialtySurveys from '@/components/sections/specialties/table-specialty-surveys';
import { GoBack } from '@/components/common/go-back';

export const metadata: Metadata = {
  title: 'Specialties - Industrii Admin',
};

const ViewEditSpecialties = ({ params }: { params: { id: number } }) => {
  return (
    <div className="w-full">
      <div className="w-fit">
        <GoBack name="Specialties" path="/specialties" />
      </div>
      <div className="w-full px-2 sm:px-8 mt-6">
        <EditSpecialtyForm specialtyId={params.id} />
      </div>
      <div className="mt-6">
        <TableSpecialtySurveys specialtyId={params.id} />
      </div>
    </div>
  );
};
export default ViewEditSpecialties;
