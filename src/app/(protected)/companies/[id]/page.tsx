import React from 'react';
import EditCompanyForm from '@/components/sections/companies/edit-company';
import { Metadata } from 'next/types';
import TableCompanySurveys from '@/components/sections/companies/table-company-surveys';
import { GoBack } from '@/components/common/go-back';

export const metadata: Metadata = {
  title: 'Companies - Industrii Admin',
};

const ViewEditCompany = ({ params }: { params: { id: number } }) => {
  return (
    <div className="w-full">
      <div className="w-fit">
        <GoBack name="Companies" path="/companies" />
      </div>
      <div className="w-full px-2 sm:px-8 mt-6">
        <EditCompanyForm companyId={params.id} />
      </div>
      <TableCompanySurveys companyId={params.id} />
    </div>
  );
};
export default ViewEditCompany;
