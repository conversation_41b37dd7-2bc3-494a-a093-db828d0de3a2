'use client';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { ColumnDef } from '@tanstack/table-core';

import { Company } from '@/backend/companies/entities/Company';
import { Survey, SurveyStatus } from '@/backend/surveys/entities/Survey';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import UnverifiedBadge from '@/components/common/badges/unverified';
import VerifiedBadge from '@/components/common/badges/verified';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import Link from 'next/link';
import InformationBadge from '@/components/common/badges/information';

export function generateColumns({ isLoading }: { isLoading: boolean }) {
  const columns: ColumnDef<Survey & { userSurveys: UserSurvey[] }>[] = [
    {
      accessorKey: 'title',
      header: ({ column }) => <Header column={column}>Survey Name</Header>,
      enableSorting: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} skeletonCount={1}>
          <span className="text-foreground font-medium">{row.getValue('title')}</span>
        </SkeletonCell>
      ),
      minSize: 300,
    },
    {
      accessorKey: 'company.name',
      header: ({ column }) => <Header column={column}>Sponsor Company</Header>,
      enableSorting: false,
      cell: ({ row }) => {
        const company = row.original.company as Company;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span>{company?.name}</span>
          </SkeletonCell>
        );
      },
      size: 200,
    },
    {
      accessorKey: 'userSurveys',
      header: ({ column }) => <Header column={column}># of Responses</Header>,
      enableSorting: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} skeletonCount={1}>
          <span>{row.original.successfulCompletions}</span>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'expiryDate',
      header: ({ column }) => <Header column={column}>Expiry Date</Header>,
      cell: ({ row }) => {
        const date = row.getValue('expiryDate') as Date;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="text-muted-foreground">{date ? formatDate(date) : ''}</span>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'updatedAt',
      header: ({ column }) => <Header column={column}>Last Updated</Header>,
      cell: ({ row }) => {
        const date = row.getValue('updatedAt') as Date;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="text-muted-foreground">{date ? formatDate(date) : ''}</span>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => <Header column={column}>Status</Header>,
      enableSorting: false,
      cell: ({ row }) => {
        const status = row.getValue('status') as SurveyStatus;

        if (status === SurveyStatus.Active) {
          return (
            <SkeletonCell isLoading={isLoading}>
              <VerifiedBadge className="capitalize">{status as string}</VerifiedBadge>
            </SkeletonCell>
          );
        }

        if (status === SurveyStatus.Draft) {
          return (
            <SkeletonCell isLoading={isLoading}>
              <InformationBadge className="capitalize">{status as string}</InformationBadge>
            </SkeletonCell>
          );
        }

        return (
          <SkeletonCell isLoading={isLoading}>
            <UnverifiedBadge className="capitalize">{status as string}</UnverifiedBadge>
          </SkeletonCell>
        );
      },
      size: 100,
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-8 overflow-hidden' : '')}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <DotsHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="p-0">
              <Link href={`/surveys/${row.original.id}`}>
                <DropdownMenuItem className="first:rounded-b-none text-foreground font-medium py-2.5 px-3.5 cursor-pointer">
                  View Details
                </DropdownMenuItem>
              </Link>
            </DropdownMenuContent>
          </DropdownMenu>
        </SkeletonCell>
      ),
      size: 50,
    },
  ];

  return columns;
}
