import type { Metadata } from 'next';
import InfoForm from '@/components/sections/users/edit-user-form/info-form';
import { UserTabs } from '@/components/sections/users/edit-user-form/user-tabs';
import { GoBack } from '@/components/common/go-back';

export const metadata: Metadata = {
  title: 'Users - Industrii Admin',
};

const ViewEditUser = ({ params }: { params: { id: number } }) => {
  return (
    <div className="w-full">
      <div className="w-fit">
        <GoBack name="Users" path="/users" />
      </div>
      <InfoForm userId={params.id} />
      <UserTabs userId={params.id} />
    </div>
  );
};
export default ViewEditUser;
