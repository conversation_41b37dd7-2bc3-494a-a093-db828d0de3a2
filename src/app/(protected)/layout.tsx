'use client';

import Sidebar from '@/components/common/sidebar/sidebar';
import { usePathInitializer as UsePathInitializer } from '@/hooks/usePathInitializer';
import { PropsWithChildren, Suspense } from 'react';

export default function ProtectedLayout({ children }: PropsWithChildren) {
  return (
    <main className="h-full relative flex flex-col sm:flex-row bg-white">
      <Sidebar />
      <div className="relative flex-1 overflow-x-hidden h-full min-h-screen flex items-start sm:ml-[205px]">
        <div className="w-full h-full p-4 md:p-8">{children}</div>
        <div id="portal-dialog" />
      </div>

      <Suspense fallback={null}>
        <UsePathInitializer /> {/* useSearchParams() should be wrapped in a suspense boundary */}
      </Suspense>
    </main>
  );
}
