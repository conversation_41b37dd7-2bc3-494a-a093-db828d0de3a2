import { Metadata } from 'next/types';
import SurveyInfo from '@/components/sections/surveys/survey-info';
import { GoBack } from '@/components/common/go-back';
import SurveyDetails from '@/components/sections/surveys/survey-details';

export const metadata: Metadata = {
  title: 'Surveys - Industrii Admin',
};

const ViewSurVey = ({ params }: { params: { id: number } }) => {
  return (
    <div className="w-full">
      <div className="w-fit">
        <GoBack name="Surveys" path="/surveys" />
      </div>

      <div className="w-full px-2 mt-6 sm:px-8">
        <SurveyInfo surveyId={params.id} />
      </div>

      <div className="mt-10">
        <SurveyDetails surveyId={params.id} />
      </div>
    </div>
  );
};

export default ViewSurVey;
