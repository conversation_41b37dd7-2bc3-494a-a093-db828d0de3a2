import config, { isDevelopment } from '@/config';
import * as fs from 'fs';
import { createSwaggerSpec } from 'next-swagger-doc';
import { Metadata } from 'next/types';
import * as path from 'path';
import ReactSwagger from './react-swagger';

export const metadata: Metadata = {
  title: 'Industrii - API Docs',
};

export default async function IndexPage() {
  const filePath = isDevelopment
    ? path.join(process.cwd(), '/next-swagger-doc.json')
    : path.join(process.cwd(), '/public/swagger.json');

  const buffer = fs.readFileSync(filePath);
  const doc = JSON.parse(buffer.toString());

  if (isDevelopment) {
    doc.definition.servers = [{ url: config.API_URL }];
  } else {
    doc.servers = [{ url: config.API_URL }];
  }

  const spec = isDevelopment ? createSwaggerSpec(doc) : doc;

  return (
    <section className="container">
      <ReactSwagger spec={spec} />
    </section>
  );
}
