'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import React from 'react';

export default function NotFound() {
  const router = useRouter();

  return (
    <div className="h-screen flex flex-col items-center justify-center">
      <div className="text-center space-y-6 max-w-md">
        <h1 className="text-3xl font-bold text-primary-brand">Page not found</h1>
        <p className="text-gray-600">
          The page you are looking for does not exist. Please check the URL and try again.
        </p>
        <Button onClick={() => router.push('/')} className="mt-4">
          Return to Dashboard
        </Button>
      </div>
    </div>
  );
}
