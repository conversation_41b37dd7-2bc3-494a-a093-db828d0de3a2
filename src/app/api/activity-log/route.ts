import { NextRequest, NextResponse } from 'next/server';
import { GetUserActionsPayload, getUserActionsSchema } from '@/backend/users/validations/user-action';
import { UserActionService } from '@/backend/users/services/user-action-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

/**
 * @swagger
 * /activity-log:
 *   get:
 *     summary: Get user activity log
 *     tags:
 *       - User Actions
 *     parameters:
 *       - name: from
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for the activity log range
 *       - name: to
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for the activity log range
 *     responses:
 *       '200':
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserActionsResponse'
 *     security:
 *       - bearerAuth: []
 *
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *
 *   schemas:
 *     UserAction:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique ID of the user action
 *         userId:
 *           type: integer
 *           description: The ID of the user who performed the action
 *         description:
 *           type: string
 *           description: Description of the user action
 *         entity:
 *           type: string
 *           description: The entity associated with the user action
 *         previousMetadata:
 *           type: object
 *           nullable: true
 *           description: Previous metadata associated with the user action
 *         newMetadata:
 *           type: object
 *           nullable: true
 *           description: New metadata associated with the user action
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the user action was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the user action was last updated
 *         user:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *               description: The unique ID of the _user
 *             name:
 *               type: string
 *               description: The name of the user
 *
 *     UserActionsResponse:
 *       type: object
 *       properties:
 *         actions:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/UserAction'
 *         from:
 *           type: string
 *           format: date-time
 *           description: Start date of the activity log range
 *         to:
 *           type: string
 *           format: date-time
 *           description: End date of the activity log range
 */
async function handler(req: NextRequest & { data: GetUserActionsPayload }) {
  const userActionService = new UserActionService();
  const userActions = await userActionService.getLogActions(req.data);
  return NextResponse.json({ data: userActions, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, schema: getUserActionsSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
