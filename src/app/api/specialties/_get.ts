import { wrapper } from '@/backend/middlewares/wrapper';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /specialties:
 *   get:
 *     summary: Get a list of specialties
 *     description: Retrieve a paginated list of specialties with optional filtering and sorting
 *     tags:
 *       - Specialties
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           description: The property to sort specialties by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *           description: The order to sort specialties by
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 */
async function _get(req: NextRequest & { data: PaginationPayload<Specialty> }): Promise<NextResponse> {
  const specialtyService = new SpecialtyService();
  const specialties = await specialtyService.getSpecialties(req.data);

  return NextResponse.json({ status: 'Success', data: specialties }, { status: 200 });
}

export default wrapper({ handler: _get, schema: paginationSchema, roles: ['*'] });
