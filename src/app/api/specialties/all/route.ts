import { NextRequest, NextResponse } from 'next/server';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

async function handler(_: NextRequest) {
  const specialtyService = new SpecialtyService();
  const specialties = await specialtyService.getAllSpecialties();

  return NextResponse.json({ data: specialties, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
