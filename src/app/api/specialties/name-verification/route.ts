import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { wrapper } from '@/backend/middlewares/wrapper';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /specialties/name-verification:
 *   get:
 *     summary: Check if specialty name exists
 *     description: Return whether a specialty name exists
 *     tags:
 *       - Specialties
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         required: true
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid query
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(req: NextRequest): Promise<NextResponse> {
  const searchParams = req.nextUrl.searchParams;
  const name = searchParams.get('name');

  if (!name) {
    throw customHttpError(400, 'Invalid specialty name', ErrorCode.BadRequest);
  }

  const specialtyService = new SpecialtyService();
  const isNameExist = await specialtyService.checkNameExist(name);

  return NextResponse.json({ status: 'Success', data: { isNameExist } }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });

export const dynamic = 'force-dynamic';
