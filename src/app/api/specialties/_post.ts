import { wrapper } from '@/backend/middlewares/wrapper';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { CreateSpecialtyPayload, createSpecialtySchema } from '@/backend/specialties/validations/create-specialty';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';

/**
 * @swagger
 * /specialties:
 *   post:
 *     summary: Create a new specialty
 *     description: Create a new specialty with the given name and referral value
 *     tags:
 *       - Specialties
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *               referralValue:
 *                 type: number
 *                 minimum: 0
 *             required:
 *               - name
 *               - referralValue
 *     responses:
 *       201:
 *         description: Created
 *       400:
 *         description: Invalid body or name already exists
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function _post(req: AuthorizedRequest & { data: CreateSpecialtyPayload }): Promise<NextResponse> {
  const specialtyService = new SpecialtyService();
  const specialty = await specialtyService.create(req.data);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `New Specialty - ${specialty.name}`,
    entity: EntitiesName.Specialties,
  });

  return NextResponse.json({ status: 'Success', data: specialty }, { status: 201 });
}

export default wrapper({
  handler: _post,
  schema: createSpecialtySchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
