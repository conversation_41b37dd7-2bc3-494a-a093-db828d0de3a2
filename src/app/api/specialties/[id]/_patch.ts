import { wrapper } from '@/backend/middlewares/wrapper';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { UpdateSpecialtyPayload, updateSpecialtySchema } from '@/backend/specialties/validations/update-specialty';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { NextParams } from '@/types/next-params';
import { compareEntities } from '@/backend/shared/utils/compare-entities';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';

/**
 * @swagger
 * /specialties/{id}:
 *   patch:
 *     summary: Update a specialty
 *     description: Update a specialty by its ID
 *     tags:
 *       - Specialties
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *               referralValue:
 *                 type: number
 *                 minimum: 0
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Specialty name already exists
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Specialty not found
 */
async function _patch(
  req: AuthorizedRequest & { data: UpdateSpecialtyPayload },
  { params }: NextParams,
): Promise<NextResponse> {
  const specialtyId = params.id;

  const specialtyService = new SpecialtyService();
  const specialty = await specialtyService.getById(specialtyId);
  const specialtyUpdated = await specialtyService.update(specialtyId, req.data);

  const userActionService = new UserActionService();
  const isEqual = compareEntities(specialty, specialtyUpdated);

  if (!isEqual) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated Specialty - ${specialtyUpdated.name}`,
      entity: EntitiesName.Specialties,
    });
  }

  return NextResponse.json({ status: 'Success', data: specialty }, { status: 200 });
}

export default wrapper({
  handler: _patch,
  schema: updateSpecialtySchema,
  validatePathParams: true,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
