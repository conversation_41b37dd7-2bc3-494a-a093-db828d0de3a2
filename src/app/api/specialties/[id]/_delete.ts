import { wrapper } from '@/backend/middlewares/wrapper';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { NextParams } from '@/types/next-params';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';

/**
 * @swagger
 * /specialties/{id}:
 *   delete:
 *     summary: Delete a specialty
 *     description: Delete a specialty by its ID
 *     tags:
 *       - Specialties
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       204:
 *         description: OK
 *       400:
 *         description: Specialty is in use
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Specialty not found
 */
async function _delete(req: AuthorizedRequest, { params }: NextParams): Promise<Response> {
  const specialtyId = params.id;

  const specialtyService = new SpecialtyService();
  const specialty = await specialtyService.getById(specialtyId);
  await specialtyService.delete(specialtyId);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `Deleted Specialty - ${specialty.name}`,
    entity: EntitiesName.Specialties,
  });

  return new Response(null, { status: 204 });
}

export default wrapper({ handler: _delete, validatePathParams: true, roles: [AdminRole.Admin, AdminRole.Editor] });
