import { wrapper } from '@/backend/middlewares/wrapper';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /specialties/{id}:
 *   get:
 *     summary: Get specialty by ID
 *     description: Retrieve details of a specialty by its ID
 *     tags:
 *       - Specialties
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Specialty not found
 */
async function _get(_: NextRequest, { params }: { params: { id: number } }): Promise<NextResponse> {
  const specialtyId = params.id;

  const specialtyService = new SpecialtyService();
  const specialty = await specialtyService.getById(specialtyId);

  return NextResponse.json({ status: 'Success', data: specialty }, { status: 200 });
}

export default wrapper({ handler: _get, roles: [AdminRole.Admin, AdminRole.Editor] });
