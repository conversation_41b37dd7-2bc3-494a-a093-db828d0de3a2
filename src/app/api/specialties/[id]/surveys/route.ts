import { wrapper } from '@/backend/middlewares/wrapper';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextParams } from '@/types/next-params';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /specialties/{id}/surveys:
 *   get:
 *     summary: Get all surveys of a specialty
 *     description: Retrieve all surveys associated with a specific specialty
 *     tags:
 *       - Specialties
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 1
 *           description: The ID of the specialty to retrieve surveys for
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Survey'
 *       404:
 *         description: Specialty not found or no surveys found for the specified specialty ID
 */
async function handler(_: NextRequest, { params }: NextParams) {
  const specialtyService = new SpecialtyService();
  const surveys = await specialtyService.getSurveysBySpecialtyId(params.id);
  return NextResponse.json({ data: surveys, status: 200 }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
