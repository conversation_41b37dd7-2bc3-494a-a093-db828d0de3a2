import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';
import { SupportRequest } from '@/backend/support/entities/SupportRequest';
import { SupportRequestService } from '@/backend/support/services/support-request-service';

/**
 * @swagger
 * /supports:
 *   get:
 *     summary: Get listSupports
 *     description: Retrieve a paginated list of supports with optional filtering and sorting
 *     tags:
 *       - Supports
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: ['id', 'name', 'status', 'createdAt']
 *           description: The property to sort supports by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *           description: The order to sort supports by
 *     responses:
 *       200:
 *         description: Successful response
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: invalid enum value
 */
async function listSupports(req: NextRequest & { data: PaginationPayload<SupportRequest> }): Promise<NextResponse> {
  const supportService = new SupportRequestService();
  const result = await supportService.listSupports(req.data);
  return NextResponse.json({ data: result, status: MessagesApi.SUCCESS }, { status: 200 });
}

export const GET = wrapper({
  handler: listSupports,
  schema: paginationSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
