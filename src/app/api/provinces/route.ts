import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { RegionService } from '@/backend/users/services/region-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /provinces:
 *   get:
 *     summary:
 *     description: Return List Provinces
 *     tags:
 *       - Provinces
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(_: NextRequest): Promise<NextResponse> {
  const regionService = new RegionService();
  const provinces = await regionService.listProvinces();
  return NextResponse.json({ status: 'Success', data: provinces }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });

export const dynamic = 'force-dynamic';
