import { AuthService } from '@/backend/auth/services/auth-service';
import {
  AdminChangePasswordPayload,
  adminChangePasswordSchema,
} from '@/backend/auth/validations/admin-change-password';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { UserAdminService } from '@/backend/users/services/user-admin-service';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /user-admin/change-password:
 *   patch:
 *     summary: Change password for admin user
 *     description: Change the password for the currently authenticated admin user
 *     tags:
 *       - User Admin
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AdminChangePasswordPayload'
 *     responses:
 *       200:
 *         description: Password changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Invalid request body or missing required fields
 *
 * components:
 *   schemas:
 *     AdminChangePasswordPayload:
 *       type: object
 *       properties:
 *         currentPassword:
 *           type: string
 *           format: password
 *           description: The current password of the admin user
 *         newPassword:
 *           type: string
 *           format: password
 *           description: The new password for the admin user
 */
async function handler(req: AuthorizedRequest & { data: AdminChangePasswordPayload }) {
  const authService = new AuthService();
  const userAdminService = new UserAdminService();
  const user = await userAdminService.getDashboardUserById(req.user.id);
  await authService.adminChangePassword(req.user.id, req.data);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `Changed Dashboard User password - ${user.firstName} ${user.lastName}`,
    entity: EntitiesName.UserAdmins,
  });
  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const PATCH = wrapper({
  handler,
  firebase: true,
  schema: adminChangePasswordSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
