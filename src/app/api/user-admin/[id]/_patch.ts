import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserAdminService } from '@/backend/users/services/user-admin-service';
import { UpdateAdminPayload, updateAdminSchema } from '@/backend/users/validations/add-update-admin';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { compareEntities } from '@/backend/shared/utils/compare-entities';

/**
 * @swagger
 * /user-admin/{id}:
 *   patch:
 *     summary: Update an admin user by ID
 *     description: Update information of an admin user by specifying the user ID
 *     tags:
 *       - User Admin
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           description: The ID of the admin user to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAdminPayload'
 *     responses:
 *       200:
 *         description: Admin user updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserAdmin'
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Admin user not found
 *       400:
 *         description: Invalid request body or missing required fields
 *
 * components:
 *   schemas:
 *     UpdateAdminPayload:
 *       type: object
 *       properties:
 *         firstName:
 *           type: string
 *           description: The updated first name of the admin user
 *         lastName:
 *           type: string
 *           description: The updated last name of the admin user
 *         role:
 *           type: string
 *           enum:
 *             - Admin
 *             - Editor
 *           description: The updated role of the admin user
 *           example: Admin
 */
async function handler(req: AuthorizedRequest & { data: UpdateAdminPayload }, { params }: NextParams) {
  const userAdminService = new UserAdminService();
  const user = await userAdminService.getDashboardUserById(params.id);
  const updatedUser = await userAdminService.updateDashboardUser(params.id, req.data);

  const userActionService = new UserActionService();
  if (user.role !== updatedUser.role) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated Dashboard User role - ${updatedUser.firstName} ${updatedUser.lastName}, ${updatedUser.role}`,
      entity: EntitiesName.Permissions,
    });
  }

  const isEqual = compareEntities(user, updatedUser, ['createdAt', 'updatedAt', 'deletedAt', 'role']);
  if (!isEqual) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated Dashboard User - ${updatedUser.firstName} ${updatedUser.lastName}`,
      entity: EntitiesName.UserAdmins,
    });
  }
  return NextResponse.json({ status: 'Success', data: user }, { status: 200 });
}

export const PATCH = wrapper({
  handler,
  firebase: true,
  validatePathParams: true,
  schema: updateAdminSchema,
  roles: [AdminRole.Admin],
});
