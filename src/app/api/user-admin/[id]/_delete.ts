import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserAdminService } from '@/backend/users/services/user-admin-service';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { badRequestError } from '@/backend/middlewares/capture-errors';

/**
 * @swagger
 * /user-admin/{id}:
 *   delete:
 *     summary: Delete an admin user by ID
 *     description: Delete an admin user by specifying the user ID
 *     tags:
 *       - User Admin
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           description: The ID of the admin user to delete
 *     responses:
 *       200:
 *         description: Admin user deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Admin user not found
 */

async function handler(req: AuthorizedRequest, { params }: NextParams) {
  const userAdminService = new UserAdminService();
  const user = await userAdminService.getDashboardUserById(params.id);

  if (user.id === req.user.id) {
    throw badRequestError('You cannot delete yourself');
  }

  await userAdminService.deleteDashboardUser(params.id);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `Deleted Dashboard User - ${user.firstName} ${user.lastName}, ${user.role}`,
    entity: EntitiesName.UserAdmins,
  });
  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const DELETE = wrapper({
  handler,
  firebase: true,
  validatePathParams: true,
  roles: [AdminRole.Admin],
});
