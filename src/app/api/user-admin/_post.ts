import { AddAdminPayload, addAdminSchema } from '@/backend/users/validations/add-update-admin';
import { UserAdminService } from '@/backend/users/services/user-admin-service';
import { NextResponse } from 'next/server';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';

/**
 * @swagger
 * /user-admin:
 *   post:
 *     summary: Create a new admin user
 *     description: Create a new admin user with the specified details
 *     tags:
 *       - User Admin
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AddAdminPayload'
 *     responses:
 *       201:
 *         description: Admin user created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserAdmin'
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Invalid request body or missing required fields
 *
 * components:
 *   schemas:
 *     AddAdminPayload:
 *       type: object
 *       properties:
 *         firstName:
 *           type: string
 *           description: The first name of the admin user
 *         lastName:
 *           type: string
 *           description: The last name of the admin user
 *         email:
 *           type: string
 *           format: email
 *           description: The email address of the admin user
 *         role:
 *           type: string
 *           enum:
 *             - Admin
 *             - Editor
 *     UserAdmin:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The unique ID of the admin user
 *         email:
 *           type: string
 *           format: email
 *           description: The email address of the admin user
 *         firstName:
 *           type: string
 *           description: The first name of the admin user
 *         lastName:
 *           type: string
 *           description: The last name of the admin user
 *         role:
 *           type: string
 *           enum:
 *             - Admin
 *             - Editor
 *           description: The role of the admin user
 */
async function handler(req: AuthorizedRequest & { data: AddAdminPayload }) {
  const userAdminService = new UserAdminService();
  const user = await userAdminService.addDashboardUser(req.data);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `New Dashboard User - ${user.firstName} ${user.lastName}, ${user.role}`,
    entity: EntitiesName.Permissions,
  });
  return NextResponse.json({ status: 'Success', data: user }, { status: 201 });
}

export const POST = wrapper({ handler, firebase: true, schema: addAdminSchema, roles: [AdminRole.Admin] });
