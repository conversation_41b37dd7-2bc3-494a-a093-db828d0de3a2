import { NextResponse } from 'next/server';
import { UserAdminService } from '@/backend/users/services/user-admin-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

/**
 * @swagger
 * /user-admin:
 *   get:
 *     summary: Get all admin users
 *     description: Retrieve a list of all admin users
 *     tags:
 *       - User Admin
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/UserAdmin'
 *                 message:
 *                   type: string
 *                   example: Success
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(_: Request) {
  const userAdminService = new UserAdminService();
  const users = await userAdminService.getDashboardUsers();
  return NextResponse.json({ data: users, message: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
