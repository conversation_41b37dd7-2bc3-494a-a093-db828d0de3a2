import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { compareEntities } from '@/backend/shared/utils/compare-entities';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { UserAdminService } from '@/backend/users/services/user-admin-service';
import { UpdateAdminPayload, updateAdminSchema } from '@/backend/users/validations/add-update-admin';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /user-admin:
 *   patch:
 *     summary: Update admin user information
 *     description: Update information of the currently authenticated admin user
 *     tags:
 *       - User Admin
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAdminPayload'
 *     responses:
 *       200:
 *         description: Admin user information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserAdmin'
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Admin user not found
 *       400:
 *         description: Invalid request body or missing required fields
 *
 * components:
 *   schemas:
 *     UpdateAdminPayload:
 *       type: object
 *       properties:
 *         firstName:
 *           type: string
 *           description: The updated first name of the admin user
 *         lastName:
 *           type: string
 *           description: The updated last name of the admin user
 */
async function handler(req: AuthorizedRequest & { data: UpdateAdminPayload }) {
  const userAdminService = new UserAdminService();

  const user = await userAdminService.getDashboardUserById(req.user.id);
  const updatedUser = await userAdminService.updateDashboardUser(req.user.id, req.data);

  const userActionService = new UserActionService();
  const isEqual = compareEntities(user, updatedUser);
  if (!isEqual) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated Dashboard User - ${user.firstName} ${user.lastName}`,
      entity: EntitiesName.UserAdmins,
    });
  }

  return NextResponse.json({ status: 'Success', data: user }, { status: 200 });
}

export const PATCH = wrapper({
  handler,
  firebase: true,
  schema: updateAdminSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
