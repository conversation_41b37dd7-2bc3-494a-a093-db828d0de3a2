import { wrapper } from '@/backend/middlewares/wrapper';
import { PublicReferralCodeService } from '@/backend/referral-codes/services/public-referral-code';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /referral-codes/{id}:
 *   get:
 *     summary: Get a public referral code by ID
 *     description: Retrieve details of a specific public referral code by its ID
 *     tags:
 *       - Referral Codes
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the referral code to retrieve
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PublicReferralCode'
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Referral code not found
 */
async function handler(_: NextRequest, { params }: { params: { id: number } }) {
  const referralCodeService = new PublicReferralCodeService();
  const result = await referralCodeService.get(params.id);

  return NextResponse.json({ data: result, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
