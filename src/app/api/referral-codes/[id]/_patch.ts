import { wrapper } from '@/backend/middlewares/wrapper';
import { PublicReferralCodeService } from '@/backend/referral-codes/services/public-referral-code';
import {
  UpdateReferralCodePayload,
  updateReferralCodeSchema,
} from '@/backend/referral-codes/validations/referral-code';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { compareEntities } from '@/backend/shared/utils/compare-entities';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { NextParams } from '@/types/next-params';

/**
 * @swagger
 * /referral-codes/{id}:
 *   patch:
 *     summary: Update a public referral code
 *     description: Update an existing public referral code with the specified details
 *     tags:
 *       - Referral Codes
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the referral code to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: The unique code for the referral
 *               name:
 *                 type: string
 *                 description: The name of the referral code
 *               description:
 *                 type: string
 *                 description: A brief description of the referral code
 *               value:
 *                 type: number
 *                 format: float
 *                 description: The value associated with the referral code
 *               status:
 *                 type: string
 *                 enum:
 *                   - Enabled
 *                   - Disabled
 *                 description: The status of the referral code
 *               maxUses:
 *                 type: integer
 *                 description: The maximum number of times the referral code can be used
 *               expiryDate:
 *                 type: string
 *                 format: date-time
 *                 description: The expiry date of the referral code
 *     responses:
 *       200:
 *         description: Referral code updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PublicReferralCode'
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Referral code not found
 *       400:
 *         description: Invalid request body or missing required fields
 */
async function handler(req: AuthorizedRequest & { data: UpdateReferralCodePayload }, { params }: NextParams) {
  const publicReferralCoderService = new PublicReferralCodeService();

  const code = await publicReferralCoderService.get(params.id);
  const updatedCode = await publicReferralCoderService.update(params.id, req.data);

  const userActionService = new UserActionService();
  const isEqual = compareEntities(code, updatedCode);
  if (!isEqual) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated ReferralCode - ${updatedCode.code}`,
      entity: EntitiesName.ReferralCodes,
    });
  }
  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const PATCH = wrapper({
  handler,
  schema: updateReferralCodeSchema,
  validatePathParams: true,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
