import { wrapper } from '@/backend/middlewares/wrapper';
import { PublicReferralCodeService } from '@/backend/referral-codes/services/public-referral-code';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { NextParams } from '@/types/next-params';

/**
 * @swagger
 * /referral-codes/{id}:
 *   delete:
 *     summary: Delete a public referral code by ID
 *     description: Delete a specific public referral code by its ID
 *     tags:
 *       - Referral Codes
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the referral code to delete
 *     responses:
 *       204:
 *         description: Referral code deleted successfully
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Referral code not found
 */
async function handler(req: AuthorizedRequest, { params }: NextParams) {
  const referralCodeService = new PublicReferralCodeService();
  const code = await referralCodeService.get(params.id);
  await referralCodeService.delete(params.id);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `Deleted Referral Code - ${code.code}`,
    entity: EntitiesName.ReferralCodes,
  });
  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const DELETE = wrapper({ handler, validatePathParams: true, roles: [AdminRole.Admin, AdminRole.Editor] });
