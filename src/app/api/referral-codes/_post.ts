import { NextResponse } from 'next/server';
import {
  CreateReferralCodePayload,
  createReferralCodeSchema,
} from '@/backend/referral-codes/validations/referral-code';
import { PublicReferralCodeService } from '@/backend/referral-codes/services/public-referral-code';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';

/**
 * @swagger
 * /referral-codes:
 *   post:
 *     summary: Create a new public referral code
 *     description: Create a new public referral code with the specified details
 *     tags:
 *       - Referral Codes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: The unique code for the referral
 *               name:
 *                 type: string
 *                 description: The name of the referral code
 *               description:
 *                 type: string
 *                 description: A brief description of the referral code
 *               value:
 *                 type: number
 *                 format: float
 *                 description: The value associated with the referral code
 *               status:
 *                 type: string
 *                 enum:
 *                   - Enabled
 *                   - Disabled
 *                 default: Enabled
 *                 description: The status of the referral code
 *               maxUses:
 *                 type: integer
 *                 description: The maximum number of times the referral code can be used
 *               expiryDate:
 *                 type: string
 *                 format: date-time
 *                 description: The expiry date of the referral code
 *     responses:
 *       201:
 *         description: Referral code created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PublicReferralCode'
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Invalid request body or missing required fields
 * components:
 *   schemas:
 *     PublicReferralCode:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The ID of the created referral code
 *         code:
 *           type: string
 *           description: The unique code for the referral
 *         name:
 *           type: string
 *           description: The name of the referral code
 *         description:
 *           type: string
 *           description: A brief description of the referral code
 *         value:
 *           type: number
 *           format: float
 *           description: The value associated with the referral code
 *         status:
 *           type: string
 *           enum:
 *             - Enabled
 *             - Disabled
 *           description: The status of the referral code
 *         maxUses:
 *           type: integer
 *           description: The maximum number of times the referral code can be used
 *         expiryDate:
 *           type: string
 *           format: date-time
 *           description: The expiry date of the referral code
 */
async function handler(req: AuthorizedRequest & { data: CreateReferralCodePayload }) {
  const referralCodeService = new PublicReferralCodeService();
  const result = await referralCodeService.create(req.data);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `New Referral Code - ${req.data.code}`,
    entity: EntitiesName.ReferralCodes,
  });

  return NextResponse.json({ data: result, status: 'Success' }, { status: 201 });
}

export const POST = wrapper({ handler, schema: createReferralCodeSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
