import { NextRequest, NextResponse } from 'next/server';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { PublicReferralCodeService } from '@/backend/referral-codes/services/public-referral-code';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

/**
 * @swagger
 * /referral-codes:
 *   get:
 *     summary: Get list of public referral codes
 *     description: Retrieve a paginated list of public referral codes with optional filtering and sorting
 *     tags:
 *       - Referral Codes
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           description: The search query to filter the referral codes
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: ['id', 'code', 'name', 'description', 'value', 'maxUses', 'expiryDate', 'status']
 *           description: The property to sort referral codes by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: ['ASC', 'DESC']
 *           description: The order to sort referral codes by
 *     responses:
 *       200:
 *         description: Successful response
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Invalid enum value
 */
async function handler(req: NextRequest & { data: PaginationPayload<PublicReferralCode> }) {
  const referralCodeService = new PublicReferralCodeService();
  const result = await referralCodeService.list(req.data);

  return NextResponse.json({ data: result, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, schema: paginationSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
