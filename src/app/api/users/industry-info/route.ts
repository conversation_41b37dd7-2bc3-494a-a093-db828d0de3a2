import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserService } from '@/backend/users/services/user-service';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/industry-info:
 *   get:
 *     summary: Get practice settings and employment status
 *     description: Return list of practice settings and employment status
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(_req: AuthorizedRequest): Promise<NextResponse> {
  const userService = new UserService();
  const data = await userService.getIndustryInfo();

  return NextResponse.json({ status: 'Success', data }, { status: 200 });
}

export const GET = wrapper({ handler, roles: ['*'] });
