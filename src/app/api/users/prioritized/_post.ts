import { wrapper } from '@/backend/middlewares/wrapper';
import { User } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

/**
 * @swagger
 * /users/prioritized:
 *   post:
 *     summary: Get prioritized users
 *     description: Returns a list of prioritized users based on the provided IDs.
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           description: The search term to filter users by
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           description: The property to sort users by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *           description: The order to sort users by
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: number
 *                 description: Array of user IDs to prioritize.
 *     responses:
 *       200:
 *         description: A list of prioritized users.
 *       400:
 *         description: Bad request.
 *       500:
 *         description: Internal server error.
 */
export async function handler(req: NextRequest & { data: PaginationPayload<User> & { ids?: number[] } }) {
  const userService = new UserService();
  const users = await userService.getUsers(req.data, req.data.ids);

  return NextResponse.json({ data: users, status: 'Success' }, { status: 200 });
}

const getPrioritizedUser = paginationSchema.merge(
  z.object({
    ids: z.array(z.number()).optional(),
  }),
);

export default wrapper({
  handler,
  schema: getPrioritizedUser,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
