import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { NextParams } from '@/types/next-params';

/**
 * @swagger
 * /users/{id}:
 *   delete:
 *     summary: Delete a user
 *     description: Delete a user by their ID
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user to delete
 *     responses:
 *       204:
 *         description: Successful response (no content)
 *       400:
 *         description: Invalid user ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 */
async function handler(req: AuthorizedRequest, { params }: NextParams) {
  const userId = params.id;
  const userService = new UserService();
  const userActionService = new UserActionService();

  const user = await userService.getUser(userId);
  await userService.deleteUser(userId);
  await userActionService.logAction({
    userId: req.user.id,
    description: `Deleted User - ${user.firstName} ${user.lastName}`,
    entity: EntitiesName.Users,
  });
  return new Response(null, { status: 204 });
}
export default wrapper({ handler: handler, validatePathParams: true, roles: [AdminRole.Admin, AdminRole.Editor] });
