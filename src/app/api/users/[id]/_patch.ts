import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { compareEntities } from '@/backend/shared/utils/compare-entities';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { UserService } from '@/backend/users/services/user-service';
import { UpdateUserPayload, updateUserSchema } from '@/backend/users/validations/update-user';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}:
 *   patch:
 *     summary: Update a user
 *     description: Update a user's information by their ID
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserSchema'
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 *
 * components:
 *   schemas:
 *     UpdateUserSchema:
 *       type: object
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         phone:
 *           type: string
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         country:
 *           type: string
 *         province:
 *           type: string
 *         city:
 *           type: string
 *         address:
 *           type: string
 *         postalCode:
 *           type: string
 *         birthday:
 *           type: string
 *           format: date
 *         specialtyId:
 *           type: number
 *         licenseNumber:
 *           type: string
 *         practiceSetting:
 *           type: string
 *         employmentStatus:
 *           type: string
 *         userType:
 *           type: string
 *           enum: [HCP User, Client, Client - Imported, HCP User - Imported, Unverified, Denied, Internal]
 *           description: User type classification
 */
async function patch(req: AuthorizedRequest & { data: UpdateUserPayload }, { params }: NextParams) {
  const userId = params.id;

  const userActionService = new UserActionService();
  const userService = new UserService();
  const user = await userService.getUser(userId);
  const userUpdated = await userService.updateUser(userId, req.data);

  const isEqual = compareEntities(user, userUpdated);
  if (!isEqual) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated User - ${userUpdated.firstName} ${userUpdated.lastName}`,
      entity: EntitiesName.Users,
    });
  }

  return NextResponse.json({ data: userUpdated, status: 'Success' }, { status: 200 });
}

export default wrapper({
  handler: patch,
  validatePathParams: true,
  schema: updateUserSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
