import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { VerificationStatus } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { UserService } from '@/backend/users/services/user-service';
import { VerifyUserPayload, verifyUserSchema } from '@/backend/users/validations/verify-user';
import { NextParams } from '@/types/next-params';
import { waitUntil } from '@vercel/functions';

/**
 * @swagger
 * /users/{id}/verification:
 *   patch:
 *     summary: Verify a user
 *     description: Verify a user by ID
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               verificationStatus:
 *                 type: string
 *                 enum: [Verified, Denied, Unverified]
 *               note:
 *                 type: string
 *                 maxLength: 500
 *             required:
 *               - verificationStatus
 *     responses:
 *       204:
 *         description: OK
 *       400:
 *         description: Invalid ID provided
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: User not found
 */
async function handler(
  req: AuthorizedRequest & { data: VerifyUserPayload },
  { params }: NextParams,
): Promise<Response> {
  const userId = params.id;

  const userService = new UserService();
  await userService.verifyUser(userId, req.data);

  waitUntil(
    (async (): Promise<void> => {
      const userActionService = new UserActionService();
      const user = await userService.getUser(userId);

      const description =
        req.data.verificationStatus === VerificationStatus.Verified
          ? 'Verified User'
          : req.data.verificationStatus === VerificationStatus.Denied
            ? 'Denied User'
            : 'Unverified User';

      await userActionService.logAction({
        userId: req.user.id,
        description: `${description} - ${user.firstName} ${user.lastName}`,
        entity: EntitiesName.Users,
      });
    })(),
  );

  return new Response(null, { status: 204 });
}

export const PATCH = wrapper({
  handler,
  schema: verifyUserSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
