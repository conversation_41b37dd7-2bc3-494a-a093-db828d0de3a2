import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { UpdateNotePayload, updateNoteSchema } from '@/backend/users/validations/update-note';
import { NextParams } from '@/types/next-params';

/**
 * @swagger
 * /users/{id}/notes:
 *   patch:
 *     summary: Update notes for a user
 *     description: Updates the notes associated with a specific user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               note:
 *                 type: string
 *                 maxLength: 500
 *             required:
 *              - note
 *     responses:
 *       204:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
async function handler(
  req: AuthorizedRequest & { data: UpdateNotePayload },
  { params }: NextParams,
): Promise<Response> {
  const userId = params.id;

  const userService = new UserService();
  await userService.updateNote(userId, req.data);

  return new Response(null, { status: 204 });
}

export const PATCH = wrapper({
  handler,
  schema: updateNoteSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
