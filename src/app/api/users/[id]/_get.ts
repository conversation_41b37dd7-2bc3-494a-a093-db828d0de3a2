import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}:
 *   get:
 *     summary: Get a user
 *     description: Retrieve a user by their ID
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user to retrieve
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       400:
 *         description: Invalid user ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 *
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         email:
 *           type: string
 *         phone:
 *           type: string
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         country:
 *           type: string
 *         province:
 *           type: string
 *         city:
 *           type: string
 *         address:
 *           type: string
 *         postalCode:
 *           type: string
 *         birthday:
 *           type: string
 *           format: date
 *         specialtyId:
 *           type: number
 *         licenseNumber:
 *           type: string
 *         practiceSetting:
 *           type: string
 *         employmentStatus:
 *           type: string
 */
async function get(_: NextRequest, { params }: { params: { id: number } }) {
  const userId = params.id;

  const userService = new UserService();
  const user = await userService.getUser(userId);
  return NextResponse.json({ data: user, status: 'Success' }, { status: 200 });
}

export default wrapper({ handler: get, roles: [AdminRole.Admin, AdminRole.Editor] });
