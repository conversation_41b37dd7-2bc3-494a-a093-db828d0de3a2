import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserTransactionService } from '@/backend/users/services/user-transaction-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}/transactions:
 *   get:
 *     summary: Get user transactions
 *     description: Retrieve a list of transactions for a user by their ID
 *     tags:
 *       - Users
 *       - Transactions
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Transaction'
 *       400:
 *         description: Invalid user ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 *
 * components:
 *   schemas:
 *     Transaction:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         userId:
 *           type: string
 *         amount:
 *           type: number
 *         type:
 *           type: string
 *           enum: [Withdrawal, Compensation, ReferralSuccess, ReferralReward]
 *         description:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time *
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
async function get(_: NextRequest, { params }: { params: { id: number } }) {
  const userId = params.id;

  const userTransactions = new UserTransactionService();
  const transactions = await userTransactions.getUserTransactions(userId);

  return NextResponse.json({ data: transactions, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler: get, roles: [AdminRole.Admin, AdminRole.Editor] });
