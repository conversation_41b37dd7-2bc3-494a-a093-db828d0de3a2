import { HubspotContactStatus } from '@/backend/hubspot/types/contact';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { ContactStatus } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { UserService } from '@/backend/users/services/user-service';
import { RequestUserInfoPayload, requestUserInfoSchema } from '@/backend/users/validations/request-user-info';
import { NextParams } from '@/types/next-params';
import { waitUntil } from '@vercel/functions';

/**
 * @swagger
 * /users/{id}/request-info:
 *   patch:
 *     summary: Request information from a user
 *     description: Updates a user's contact status and logs the action
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               contactStatus:
 *                 type: string
 *                 enum: [ReviewInfo, WaitForInfo]
 *     responses:
 *       204:
 *         description: OK
 *       400:
 *         description: Invalid ID provided
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: User not found
 */
async function handler(
  req: AuthorizedRequest & { data: RequestUserInfoPayload },
  { params }: NextParams,
): Promise<Response> {
  const userId = params.id;

  const userService = new UserService();
  await userService.requestUserInfo(userId, req.data);

  waitUntil(
    (async (): Promise<void> => {
      const userActionService = new UserActionService();
      const user = await userService.getUser(userId);

      const description =
        req.data.contactStatus === ContactStatus.ReviewInfo
          ? HubspotContactStatus.ReviewInfo
          : HubspotContactStatus.WaitForInfo;

      await userActionService.logAction({
        userId: req.user.id,
        description: `Set contact status to ${description} - ${user.firstName} ${user.lastName}`,
        entity: EntitiesName.Users,
      });
    })(),
  );

  return new Response(null, { status: 204 });
}

export const PATCH = wrapper({
  handler,
  schema: requestUserInfoSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
