import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserSurveyService } from '@/backend/users/services/user-survey-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}/completed-surveys:
 *   get:
 *     summary: Get user completed surveys
 *     description: Retrieve a list of completed surveys for a user by their ID
 *     tags:
 *       - Users
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user
 *     responses:
 *       200:
 *         description: Successful response
 *       400:
 *         description: Invalid user ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 */
async function get(_: NextRequest, { params }: { params: { id: number } }) {
  const userId = params.id;

  const userSurveyService = new UserSurveyService();
  const surveys = await userSurveyService.getUserCompletedSurveys(userId);

  return NextResponse.json({ data: surveys, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler: get, roles: [AdminRole.Admin, AdminRole.Editor] });
