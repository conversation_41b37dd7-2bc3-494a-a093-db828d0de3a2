import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { UpdateUserBalancePayload, updateUserBalanceSchema } from '@/backend/users/validations/update-user-balance';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}/balance:
 *   post:
 *     summary: Update user balance
 *     description: Update the balance of a user identified by ID
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: ['Credit', 'Adjustment']
 *               amount:
 *                 type: number
 *                 minimum: 1
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid request or validation error
 *       401:
 *         description: Unauthorized - missing or invalid authentication token
 *       403:
 *         description: Forbidden - user does not have permission (Admin role required)
 */
async function handler(
  req: NextRequest & { data: UpdateUserBalancePayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const userId = params.id;

  const userService = new UserService();
  await userService.setUserBalance(userId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [AdminRole.Admin], schema: updateUserBalanceSchema });
