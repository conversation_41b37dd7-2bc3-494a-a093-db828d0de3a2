import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}/audiences:
 *   get:
 *     summary: Get list of audiences visible to the user
 *     description: Get list of audiences visible to the user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Successful response
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Invalid request
 */
async function handler(_req: NextRequest, { params }: { params: { id: number } }): Promise<NextResponse> {
  const userId = params.id;

  const userService = new UserService();
  const audiences = await userService.getAudiencesByUser(userId);

  return NextResponse.json({ status: 'Success', data: audiences }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin] });
