import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import {
  EditUserInAudiencesPayload,
  editUserInAudiencesSchema,
} from '@/backend/users/validations/edit-user-in-audiences';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}/audiences/add:
 *   post:
 *     summary: Add user to audiences
 *     description: Add a user to multiple audiences
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               audienceIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       200:
 *         description: Successful response
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Invalid request
 */
async function handler(
  req: NextRequest & { data: EditUserInAudiencesPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const userId = params.id;

  const userService = new UserService();
  await userService.addUserToAudiences(userId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({
  handler,
  schema: editUserInAudiencesSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
