import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { UpdateUserTypePayload, updateUserTypeSchema } from '@/backend/users/validations/update-user-type';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/{id}/assign-user-type:
 *   post:
 *     summary: Assign a user type to a user
 *     description: Changes a user's type/role. Requires Admin or Editor privileges.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The user ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userType
 *             properties:
 *               userType:
 *                 type: string
 *                 enum:
 *                   - ActualUser
 *                   - Client
 *                   - ClientImported
 *                   - UserImported
 *                   - NotVerified
 *                   - Denied
 *                   - Internal
 *                 description: |
 *                   The user type to assign:
 *                   - ActualUser: Verified, active HCPs
 *                   - Client: Clients who have completed signup
 *                   - ClientImported: Clients added via import
 *                   - UserImported: HCPs bulk-uploaded
 *                   - NotVerified: Users not yet verified
 *                   - Denied: Users who have been denied access
 *                   - Internal: Staff/Tester/Dev/Demo accounts
 *     responses:
 *       200:
 *         description: User type updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: Success
 *       401:
 *         description: Unauthorized - User does not have required role
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: Error
 *                 message:
 *                   type: string
 *                   example: Unauthorized
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: Error
 *                 message:
 *                   type: string
 *                   example: User not found
 */
async function handler(
  req: NextRequest & { data: UpdateUserTypePayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const userId = params.id;

  const userService = new UserService();
  await userService.setUserType(userId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, schema: updateUserTypeSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
