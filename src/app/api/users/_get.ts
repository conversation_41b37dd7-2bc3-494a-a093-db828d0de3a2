import { wrapper } from '@/backend/middlewares/wrapper';
import { User } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users:
 *   get:
 *     summary: Get a list of users
 *     description: Retrieve a paginated list of users with optional filtering and sorting
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           description: The search term to filter users by
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           description: The property to sort users by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *           description: The order to sort users by
 *     responses:
 *       200:
 *         description: Successful response
 *       401:
 *         description: Authorization information is missing or invalid
 */
export async function handler(req: NextRequest & { data: PaginationPayload<User> }) {
  const userService = new UserService();
  const users = await userService.getUsers(req.data);

  return NextResponse.json({ data: users, status: 'Success' }, { status: 200 });
}

export default wrapper({
  handler,
  schema: paginationSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
