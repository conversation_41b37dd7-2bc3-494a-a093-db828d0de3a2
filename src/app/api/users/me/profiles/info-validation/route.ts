import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserService } from '@/backend/users/services/user-service';
import { UserRole } from '@/backend/users/types/user';
import { ValidateUserProfilePayload, validateUserProfileSchema } from '@/backend/users/validations/update-user-profile';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/profiles/info-validation:
 *   post:
 *     summary: Update user profile
 *     description: Update user profile information
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               phone:
 *                 type: string
 *                 pattern: "^\\+1\\d{10}$"
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 */
async function handler(req: AuthorizedRequest & { data: ValidateUserProfilePayload }): Promise<NextResponse> {
  const { id } = req.user;

  const userService = new UserService();
  const data = await userService.validateUserProfile(id, req.data);

  return NextResponse.json({ status: 'Success', data }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User], schema: validateUserProfileSchema });
