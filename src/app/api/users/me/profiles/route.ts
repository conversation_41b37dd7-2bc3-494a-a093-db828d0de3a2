import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserService } from '@/backend/users/services/user-service';
import { UserRole } from '@/backend/users/types/user';
import { ValidateUserProfilePayload, updateUserProfileSchema } from '@/backend/users/validations/update-user-profile';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/profiles:
 *   patch:
 *     summary: Update user profile
 *     description: Update user profile data
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *                 pattern: "^[a-zA-Z']+$"
 *               lastName:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *                 pattern: "^[a-zA-Z']+$"
 *               phone:
 *                 type: string
 *                 pattern: "^\\+1\\d{10}$"
 *               email:
 *                 type: string
 *                 format: email
 *               idToken:
 *                 type: string
 *               notificationEnabled:
 *                 type: boolean
 *               isEmailOptIn:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid input data or email or phone already taken
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 */
async function handler(req: AuthorizedRequest & { data: ValidateUserProfilePayload }): Promise<NextResponse> {
  const { id } = req.user;

  const userService = new UserService();
  const data = await userService.updateUserProfile(id, req.data);

  return NextResponse.json({ status: 'Success', data }, { status: 200 });
}

export const PATCH = wrapper({
  handler,
  roles: [UserRole.User],
  firebase: true,
  schema: updateUserProfileSchema,
});
