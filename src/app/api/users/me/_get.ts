import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserService } from '@/backend/users/services/user-service';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me:
 *   get:
 *     summary: Get user profile
 *     description: Return profile of user or admin user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 */
async function _get(req: AuthorizedRequest): Promise<NextResponse> {
  const { id, role } = req.user;

  const userService = new UserService();
  const user = await userService.me(id, role);

  return NextResponse.json({ status: 'Success', data: user }, { status: 200 });
}

export default wrapper({ handler: _get, roles: ['*'] });
