import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/surveys:
 *   get:
 *     summary: Get list surveys that is visible to user
 *     description: Get list surveys that is visible to user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Survey'
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: invalid enum value
 */
async function handler(req: AuthorizedRequest): Promise<NextResponse> {
  const { id } = req.user;

  const surveyService = new SurveyService();
  const surveys = await surveyService.getUserAvailableSurveys(id);

  return NextResponse.json({ status: 'Success', data: surveys }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [UserRole.User] });
