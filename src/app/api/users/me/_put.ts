import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserService } from '@/backend/users/services/user-service';
import { UserRole } from '@/backend/users/types/user';
import {
  UpdateOnboardingInfoPayload,
  updateOnboardingInfoSchema,
} from '@/backend/users/validations/update-onboarding-info';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me:
 *   put:
 *     summary: Update app user
 *     description: Update profile information of an app user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               gender:
 *                 type: string
 *                 enum:
 *                   - Male
 *                   - Female
 *                   - Other
 *               birthday:
 *                 type: string
 *                 format: date
 *               address:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 255
 *               city:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               province:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               country:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               postalCode:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               licenseNumber:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *               specialtyId:
 *                 type: integer
 *                 format: int32
 *                 minimum: 1
 *               practiceSettingId:
 *                 type: integer
 *                 format: int32
 *                 minimum: 1
 *               employmentStatusId:
 *                 type: integer
 *                 format: int32
 *                 minimum: 1
 *               canadaPostId:
 *                 type: string
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid body
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Info not found
 */
async function _put(req: AuthorizedRequest & { data: UpdateOnboardingInfoPayload }): Promise<NextResponse> {
  const { id } = req.user;

  const userService = new UserService();
  const user = await userService.updateOnboardingInfo(id, req.data);

  return NextResponse.json({ status: 'Success', data: user }, { status: 200 });
}

export default wrapper({ handler: _put, schema: updateOnboardingInfoSchema, roles: [UserRole.User] });
