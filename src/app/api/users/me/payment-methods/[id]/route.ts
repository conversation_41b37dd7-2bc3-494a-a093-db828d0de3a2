import { wrapper } from '@/backend/middlewares/wrapper';
import { PaymentService } from '@/backend/payments/services/payment-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserRole } from '@/backend/users/types/user';

/**
 * @swagger
 * /users/me/payment-methods/{id}:
 *   delete:
 *     summary: Delete a payment method
 *     description: Delete a payment method associated with the authenticated user
 *     tags:
 *       - Users
 *       - Payments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       204:
 *         description: OK
 *       400:
 *         description: Invalid ID provided
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *        description: Payment method not found
 */
async function handler(req: AuthorizedRequest, { params }: { params: { id: number } }): Promise<Response> {
  const pmId = params.id;

  const paymentService = new PaymentService();
  await paymentService.delete(pmId, req.user.id);

  return new Response(null, { status: 204 });
}

export const DELETE = wrapper({ handler: handler, roles: [UserRole.User] });
