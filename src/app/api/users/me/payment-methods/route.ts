import { wrapper } from '@/backend/middlewares/wrapper';
import { PaymentService } from '@/backend/payments/services/payment-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/payment-methods:
 *   get:
 *     summary: Get user's payment methods
 *     description: Retrieve a list of payment methods associated with the authenticated user
 *     tags:
 *       - Users
 *       - Payments
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 */
async function handler(req: AuthorizedRequest): Promise<NextResponse> {
  const { id } = req.user;

  const paymentService = new PaymentService();
  const paymentMethods = await paymentService.getPaymentMethods(id);

  return NextResponse.json({ status: 'Success', data: paymentMethods }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [UserRole.User] });
