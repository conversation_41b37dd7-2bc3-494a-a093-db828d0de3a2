import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SupportRequestService } from '@/backend/support/services/support-request-service';
import { SubmitSupportPayload, submitSupportSchema } from '@/backend/support/validations/submit-support';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/supports:
 *   post:
 *     summary: Submit a support request
 *     description: Submit a support request for the authenticated user
 *     tags:
 *       - Users
 *       - Supports
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 1000
 *             required:
 *               - message
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Invalid body
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 */
async function handler(req: AuthorizedRequest & { data: SubmitSupportPayload }): Promise<NextResponse> {
  const { id } = req.user;

  const supportService = new SupportRequestService();
  await supportService.submit(id, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User], schema: submitSupportSchema });
