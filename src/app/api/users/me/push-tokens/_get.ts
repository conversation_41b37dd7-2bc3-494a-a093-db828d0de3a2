import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserService } from '@/backend/users/services/user-service';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/push-tokens:
 *   get:
 *     summary: Check if the authenticated user has push tokens
 *     description: Check if the authenticated user has push tokens
 *     tags:
 *       - Users
 *       - Push Tokens
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function _get(req: AuthorizedRequest): Promise<NextResponse> {
  const { id } = req.user;

  const userService = new UserService();
  const isExist = await userService.isUserPushTokensExist(id);

  return NextResponse.json({ status: 'Success', data: { isExist } }, { status: 200 });
}

export default wrapper({ handler: _get, roles: [UserRole.User] });
