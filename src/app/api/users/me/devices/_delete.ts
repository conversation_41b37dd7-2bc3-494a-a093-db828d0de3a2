import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserDeviceService } from '@/backend/users/services/user-device-service';
import { UserRole } from '@/backend/users/types/user';
import { DeleteUserDevicePayload, deleteUserDeviceSchema } from '@/backend/users/validations/delete-user-device';

/**
 * @swagger
 * /users/me/devices:
 *   delete:
 *     summary: Delete a user device
 *     description: Delete a user device associated with the authenticated user
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               token:
 *                 type: string
 *                 minLength: 1
 *     responses:
 *       204:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(req: AuthorizedRequest & { data: DeleteUserDevicePayload }): Promise<Response> {
  const { id } = req.user;

  const userDeviceService = new UserDeviceService();
  await userDeviceService.delete(id, req.data);

  return new Response(null, { status: 204 });
}

export default wrapper({
  handler,
  schema: deleteUserDeviceSchema,
  roles: [UserRole.User],
});
