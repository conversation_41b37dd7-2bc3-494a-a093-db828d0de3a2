import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserDeviceService } from '@/backend/users/services/user-device-service';
import { UserRole } from '@/backend/users/types/user';
import { CreateUserDevicePayload, createUserDeviceSchema } from '@/backend/users/validations/create-user-device';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/devices:
 *   post:
 *     summary: Create a user device
 *     description: Create a new user device with provided details
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               token:
 *                 type: string
 *                 minLength: 1
 *               deviceId:
 *                 type: string
 *                 minLength: 1
 *               platform:
 *                 type: string
 *                 enum:
 *                   - iOS
 *                   - Android
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid input data or missing fields
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: User not found
 */
async function handler(req: AuthorizedRequest & { data: CreateUserDevicePayload }): Promise<NextResponse> {
  const { id } = req.user;

  const userDeviceService = new UserDeviceService();
  await userDeviceService.create(id, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export default wrapper({
  handler,
  schema: createUserDeviceSchema,
  roles: [UserRole.User],
});
