import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { Locale } from '@/backend/surveys/entities/Survey';
import { UserTransaction } from '@/backend/users/entities/UserTransaction';
import { UserTransactionService } from '@/backend/users/services/user-transaction-service';
import { UserRole } from '@/backend/users/types/user';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/transactions:
 *   get:
 *     summary: Get a list of user transactions
 *     description: Retrieve a paginated list of user transactions with optional filtering and sorting
 *     tags:
 *       - Users
 *       - Transactions
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: ['Earnings', 'Withdrawals']
 *       - in: query
 *         name: locale
 *         schema:
 *           type: string
 *           enum: ['Fr', 'En']
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 */
async function handler(req: AuthorizedRequest & { data: PaginationPayload<UserTransaction> }): Promise<NextResponse> {
  const query = req.nextUrl.searchParams;
  const locale = query.get('locale');
  const type = query.get('type');
  const { id } = req.user;

  const userTransactionService = new UserTransactionService();
  const transactions = await userTransactionService.getTransactions(
    id,
    req.data,
    locale && Object.values(Locale).includes(locale as Locale) ? (locale as Locale) : null,
    type,
  );

  return NextResponse.json({ status: 'Success', data: transactions }, { status: 200 });
}

export const GET = wrapper({ handler, schema: paginationSchema, roles: [UserRole.User] });
