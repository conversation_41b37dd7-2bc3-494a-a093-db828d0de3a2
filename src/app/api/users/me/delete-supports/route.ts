import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SupportRequestService } from '@/backend/support/services/support-request-service';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users/me/delete-supports:
 *   post:
 *     summary: Request account deletion
 *     description: Request account deletion for the authenticated user
 *     tags:
 *       - Users
 *       - Supports
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 */
async function handler(req: AuthorizedRequest): Promise<NextResponse> {
  const { id } = req.user;

  const supportService = new SupportRequestService();
  await supportService.requestAccountDelete(id);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User] });
