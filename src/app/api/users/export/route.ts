import { wrapper } from '@/backend/middlewares/wrapper';
import { getCsvHeaders } from '@/backend/shared/utils/export-csv';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { ExportUsersPayload, exportUsersSchema } from '@/backend/users/validations/export-users';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /users/export:
 *   post:
 *     summary: Export users to CSV
 *     description: Export all users to a CSV file
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(req: NextRequest & { data: ExportUsersPayload }) {
  const userService = new UserService();
  const blob = await userService.exportUsers(req.data);

  return new NextResponse(blob, { status: 200, headers: getCsvHeaders('users') });
}

export const POST = wrapper({ handler, schema: exportUsersSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
