import { NextRequest, NextResponse } from 'next/server';
import { CityService } from '@/backend/users/services/city-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

async function handler(_: NextRequest) {
  const cityService = new CityService();
  const cities = await cityService.getCities();
  return NextResponse.json({ status: 'Success', data: cities }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
