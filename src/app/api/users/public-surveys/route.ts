import { wrapper } from '@/backend/middlewares/wrapper';
import { UserService } from '@/backend/users/services/user-service';
import { paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

async function handler(req: NextRequest & { data: { email: string } }) {
  const userService = new UserService();
  const isEmailExist = await userService.validateAppUserEmail(req.data.email);

  return NextResponse.json({ data: { isEmailExist }, status: 'Success' }, { status: 200 });
}

const validateEmailSchema = paginationSchema.merge(
  z.object({
    email: z.string().email(),
  }),
);

export const GET = wrapper({
  handler,
  schema: validateEmailSchema,
});
