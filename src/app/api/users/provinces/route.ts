import { NextRequest, NextResponse } from 'next/server';
import { ProvinceService } from '@/backend/users/services/province-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

async function handler(_: NextRequest) {
  const provinceService = new ProvinceService();
  const provinces = await provinceService.getProvinces();
  return NextResponse.json({ status: 'Success', data: provinces }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
