import { NextResponse } from 'next/server';
import { UserService } from '@/backend/users/services/user-service';
import { ImportUsersPayload, importUsersSchema } from '@/backend/users/validations/import-user';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';

async function handler(req: AuthorizedRequest & { data: ImportUsersPayload }): Promise<NextResponse> {
  const userService = new UserService();
  const result = await userService.importUsers(req.data);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: 'Bulk upload users',
    entity: EntitiesName.Users,
  });

  return NextResponse.json({ status: 'Success', data: result }, { status: 200 });
}

export const POST = wrapper({ handler, schema: importUsersSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
