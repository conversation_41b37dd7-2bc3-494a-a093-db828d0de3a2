import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { NextRequest, NextResponse } from 'next/server';

async function post(req: NextRequest) {
  const formData = await req.formData();
  const file = formData.get('file') as File;
  const fileContent = await file.text();

  const userService = new UserService();
  const validatedResult = await userService.validateCsvImportUser(fileContent);

  return NextResponse.json({ data: validatedResult, status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler: post, roles: [AdminRole.Admin, AdminRole.Editor] });
