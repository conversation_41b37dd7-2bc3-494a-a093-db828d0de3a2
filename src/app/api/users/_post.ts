import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { UserService } from '@/backend/users/services/user-service';
import { CreateUserPayload, createUserSchema } from '@/backend/users/validations/create-user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /users:
 *   post:
 *     summary: Create a new user
 *     description: Create a new user with the provided details
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserSchema'
 *     responses:
 *       201:
 *         description: Created
 *       400:
 *         description: Invalid body or user already exists
 *       401:
 *         description: Authorization information is missing or invalid
 *
 * components:
 *   schemas:
 *     CreateUserSchema:
 *       type: object
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           minLength: 1
 *           description: Must be a valid email address
 *           required: true
 *         phone:
 *           type: string
 *           description: Phone number in a valid format
 *         firstName:
 *           type: string
 *           minLength: 1
 *           description: First name is required and must be at least 1 character long
 *           required: true
 *         lastName:
 *           type: string
 *           minLength: 1
 *           description: Last name is required and must be at least 1 character long
 *           required: true
 *         country:
 *           type: string
 *         province:
 *           type: string
 *         city:
 *           type: string
 *         address:
 *           type: string
 *         postalCode:
 *           type: string
 *         birthday:
 *           type: string
 *           format: date
 *         specialtyId:
 *           type: number
 *           description: Specialty is required and must be a valid number
 *           required: true
 *         licenseNumber:
 *           type: string
 *         practiceSetting:
 *           type: string
 *         employmentStatus:
 *           type: string
 *         canadaPostId:
 *           type: string
 *         userType:
 *           type: string
 *           enum: [HCP User, Client, Client - Imported, HCP User - Imported, Unverified, Denied, Internal]
 *           description: User type classification
 *       required:
 *         - email
 *         - firstName
 *         - lastName
 *         - specialtyId
 */
async function post(req: AuthorizedRequest & { data: CreateUserPayload }) {
  const userService = new UserService();
  const userActionService = new UserActionService();
  const newUser = await userService.adminAddUser(req.data);
  await userActionService.logAction({
    userId: req.user.id,
    description: `New User - ${newUser.firstName} ${newUser.lastName}`,
    entity: EntitiesName.Users,
  });
  return NextResponse.json({ data: newUser, status: 'Success' }, { status: 201 });
}

export default wrapper({ handler: post, schema: createUserSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
