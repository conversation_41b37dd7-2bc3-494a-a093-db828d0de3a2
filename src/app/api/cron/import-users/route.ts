import { CronService } from '@/backend/crons/services';
import { cronWrapper } from '@/backend/middlewares/wrapper';
import { NextRequest, NextResponse } from 'next/server';

async function handler(_req: NextRequest): Promise<NextResponse> {
  const cronService = new CronService();
  await cronService.importUsers();

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const GET = cronWrapper({ handler });
export const dynamic = 'force-dynamic';
