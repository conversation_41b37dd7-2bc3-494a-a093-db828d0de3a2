import { CompanyService } from '@/backend/companies/services/company-service';
import { AddUpdateCompanyPayload, addUpdateCompanySchema } from '@/backend/companies/validations/add-update-company';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /companies:
 *   post:
 *     summary: Post createCompany
 *     description: Return messages
 *     tags:
 *       - Companies
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the company to create.
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Name not available or Company Name exist
 */
async function addCompany(req: AuthorizedRequest & { data: AddUpdateCompanyPayload }): Promise<NextResponse> {
  const companyService = new CompanyService();
  await companyService.create(req.data.name);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `New Company - ${req.data.name}`,
    entity: EntitiesName.Companies,
  });
  return NextResponse.json({ status: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler: addCompany,
  schema: addUpdateCompanySchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
