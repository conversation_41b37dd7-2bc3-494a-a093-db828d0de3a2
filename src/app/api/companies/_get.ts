import { Company } from '@/backend/companies/entities/Company';
import { CompanyService } from '@/backend/companies/services/company-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /companies:
 *   get:
 *     summary: Get listCompanies
 *     description: Retrieve a paginated list of companies with optional filtering and sorting
 *     tags:
 *       - Companies
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: ['id','name','surveysInProgress','surveysCompleted','lastSurveyDate']
 *           description: The property to sort companies by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *           description: The order to sort companies by
 *     responses:
 *       200:
 *         description: Successful response
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: invalid enum value
 */
async function handler(req: NextRequest & { data: PaginationPayload<Company> }): Promise<NextResponse> {
  const companyService = new CompanyService();
  const result = await companyService.listCompanies(req.data);

  return NextResponse.json({ data: result, status: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler,
  schema: paginationSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
