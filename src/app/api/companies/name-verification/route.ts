import { CompanyService } from '@/backend/companies/services/company-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /companies/name-verification:
 *   get:
 *     summary: Get findNameCompanyExist
 *     description: Return messages
 *     tags:
 *       - Companies
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *           description: The name in system audiences
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Name not available or Company Name exist
 */
async function verifyNameExist(req: NextRequest) {
  const name = req.nextUrl.searchParams.get('name') as string;
  if (!name) {
    return NextResponse.json({ message: MessagesApi.NAME_COMPANY_VALID }, { status: 400 });
  }
  const companyService = new CompanyService();
  const isNameExist = await companyService.checkNameExist(String(name));
  return NextResponse.json({ status: MessagesApi.SUCCESS, data: { isNameExist } }, { status: 200 });
}

export const GET = wrapper({ handler: verifyNameExist, roles: [AdminRole.Admin, AdminRole.Editor] });
export const dynamic = 'force-dynamic';
