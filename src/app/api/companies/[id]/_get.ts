import { CompanyService } from '@/backend/companies/services/company-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /companies/{id}:
 *   get:
 *     summary: Get a company
 *     description: Retrieve a company by its ID
 *     tags:
 *       - Companies
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the company to retrieve
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       400:
 *         description: Invalid company ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Company not found
 *
 * components:
 *   schemas:
 *     Company:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         surveysInProgress:
 *           type: number
 *         surveysCompleted:
 *           type: number
 *         lastSurveyDate:
 *           type: number
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 */
async function handler(_: NextRequest, { params }: { params: { id: number } }) {
  const companyId = params.id;

  const companyService = new CompanyService();
  const company = await companyService.get(companyId);
  return NextResponse.json({ status: MessagesApi.SUCCESS, data: company }, { status: 200 });
}

export default wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
