import { CompanyService } from '@/backend/companies/services/company-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextParams } from '@/types/next-params';

/**
 * @swagger
 * /companies/{id}:
 *   delete:
 *     summary: Delete deleteCompany
 *     description: Return messages
 *     tags:
 *       - Companies
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the company
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Name not available or Company Name exist
 */
async function deleteCompany(req: AuthorizedRequest, { params }: NextParams): Promise<Response> {
  const companyId = params.id;

  const surveyServices = new SurveyService();
  await surveyServices.findCompany(companyId);
  const companyService = new CompanyService();
  const company = await companyService.get(companyId);
  await companyService.delete(companyId);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `Deleted Company - ${company.name}`,
    entity: EntitiesName.Companies,
  });

  return new Response(null, { status: 204 });
}

export default wrapper({
  handler: deleteCompany,
  validatePathParams: true,
  roles: [AdminRole.Admin],
});
