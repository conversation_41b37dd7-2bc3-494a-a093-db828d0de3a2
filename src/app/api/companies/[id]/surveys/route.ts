import { CompanyService } from '@/backend/companies/services/company-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /companies/{id}/surveys:
 *   get:
 *     summary: Get company surveys
 *     description: Retrieve a list of surveys for a company by its ID
 *     tags:
 *       - Companies
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the company
 *     responses:
 *       200:
 *         description: Successful response
 *       400:
 *         description: Invalid company ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Company not found
 */
async function handler(_: NextRequest, { params }: { params: { id: number } }) {
  const companyId = params.id;

  const companyService = new CompanyService();
  await companyService.get(companyId);
  const surveyServices = new SurveyService();
  const surveys = await surveyServices.surveysByCompanyId(companyId);
  return NextResponse.json({ status: MessagesApi.SUCCESS, data: surveys }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
