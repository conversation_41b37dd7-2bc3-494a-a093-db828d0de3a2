import { CompanyService } from '@/backend/companies/services/company-service';
import { AddUpdateCompanyPayload, addUpdateCompanySchema } from '@/backend/companies/validations/add-update-company';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { compareEntities } from '@/backend/shared/utils/compare-entities';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /companies/{id}:
 *   patch:
 *     summary: Patch editCompany
 *     description: Return messages
 *     tags:
 *       - Companies
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the company
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the company to update.
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Name not available or Company Name exist
 */
async function editCompany(
  req: AuthorizedRequest & { data: AddUpdateCompanyPayload },
  { params }: NextParams,
): Promise<NextResponse> {
  const companyId = params.id;

  const companyService = new CompanyService();
  const company = await companyService.get(companyId);
  const updatedCompany = await companyService.update(companyId, req.data.name);

  const userActionService = new UserActionService();
  const isEqual = compareEntities(updatedCompany, company);
  if (!isEqual) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated Company - ${updatedCompany.name}`,
      entity: EntitiesName.Companies,
    });
  }
  return NextResponse.json({ message: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler: editCompany,
  validatePathParams: true,
  schema: addUpdateCompanySchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
