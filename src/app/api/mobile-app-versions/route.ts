import { wrapper } from '@/backend/middlewares/wrapper';
import { Platform } from '@/backend/users/entities/UserDevice';
import config from '@/config';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /mobile-app-versions:
 *   get:
 *     summary: Retrieve mobile app versions
 *     description: Returns a list of all mobile app versions
 *     tags:
 *       - Mobile App Versions
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: os
 *         schema:
 *           type: string
 *           enum: ['Android', 'iOS']
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(req: NextRequest): Promise<NextResponse> {
  const query = req.nextUrl.searchParams;
  const os = query.get('os');

  const mobileAppVersions = [
    {
      id: 1,
      os: Platform.ANDROID,
      minVersion: config.ANDROID_MIN_VERSION,
      currentVersion: config.ANDROID_CURRENT_VERSION,
    },
    {
      id: 2,
      os: Platform.IOS,
      minVersion: config.IOS_MIN_VERSION,
      currentVersion: config.IOS_CURRENT_VERSION,
    },
  ];

  if (os && os === Platform.ANDROID) {
    return NextResponse.json(
      { status: 'Success', data: mobileAppVersions.find(version => version.os === Platform.ANDROID) },
      { status: 200 },
    );
  } else {
    return NextResponse.json(
      { status: 'Success', data: mobileAppVersions.find(version => version.os === Platform.IOS) },
      { status: 200 },
    );
  }
}

export const GET = wrapper({ handler });
export const dynamic = 'force-dynamic';
