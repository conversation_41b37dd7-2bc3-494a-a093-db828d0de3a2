import { AvailableFundsService } from '@/backend/available-funds/services';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

/**
 * @swagger
 * /available-funds/summaries:
 *   get:
 *     summary: Get available funds summaries
 *     description: Retrieves summary information about available funds based on date filters
 *     tags:
 *       - Available Funds
 *     parameters:
 *       - in: query
 *         name: cashedOutFrom
 *         schema:
 *           type: string
 *           format: date-time
 *         required: false
 *       - in: query
 *         name: cashedOutTo
 *         schema:
 *           type: string
 *           format: date-time
 *         required: false
 *       - in: query
 *         name: creditedFrom
 *         schema:
 *           type: string
 *           format: date-time
 *         required: false
 *       - in: query
 *         name: creditedTo
 *         schema:
 *           type: string
 *           format: date-time
 *         required: false
 *     responses:
 *       204:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */
async function handler(req: NextRequest & { data: SummariesPayload }): Promise<NextResponse> {
  const availableFundsService = new AvailableFundsService();
  const data = await availableFundsService.getSummaries(req.data);

  return NextResponse.json({ status: 'Success', data }, { status: 200 });
}

const schema = z.object({
  cashedOutFrom: z.string().datetime().nullable().catch(null),
  cashedOutTo: z.string().datetime().nullable().catch(null),
  creditedFrom: z.string().datetime().nullable().catch(null),
  creditedTo: z.string().datetime().nullable().catch(null),
});
export type SummariesPayload = z.input<typeof schema>;

export const GET = wrapper({ handler, schema, roles: [AdminRole.Admin] });
