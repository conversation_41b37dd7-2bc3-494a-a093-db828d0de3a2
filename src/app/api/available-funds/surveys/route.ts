import { AvailableFundsService } from '@/backend/available-funds/services';
import { wrapper } from '@/backend/middlewares/wrapper';
import { Survey } from '@/backend/surveys/entities/Survey';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /available-funds/surveys:
 *   get:
 *     summary: Get available funds surveys
 *     description: Retrieves a paginated list of surveys associated with available funds
 *     tags:
 *       - Available Funds
 *       - Surveys
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         required: false
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         required: false
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         required: false
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */
async function handler(req: NextRequest & { data: PaginationPayload<Survey> }) {
  const availableFundsService = new AvailableFundsService();
  const data = await availableFundsService.getSurveys(req.data);

  return NextResponse.json({ data, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, schema: paginationSchema, roles: [AdminRole.Admin] });
