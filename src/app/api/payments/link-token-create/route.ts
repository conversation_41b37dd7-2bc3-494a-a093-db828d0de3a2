import { wrapper } from '@/backend/middlewares/wrapper';
import { PaymentService } from '@/backend/payments/services/payment-service';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /payments/link-token-create:
 *   post:
 *     summary: Create payment link token
 *     description: Generate a payment link token for the authenticated user
 *     tags:
 *       - Payments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 */
async function handler(req: AuthorizedRequest): Promise<NextResponse> {
  const { id } = req.user;

  const paymentService = new PaymentService();
  const linkToken = await paymentService.createLinkToken(id);

  return NextResponse.json({ status: 'Success', data: { linkToken } }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User] });
