import { wrapper } from '@/backend/middlewares/wrapper';
import { PaymentService } from '@/backend/payments/services/payment-service';
import {
  ExchangePublicTokenPayload,
  exchangePublicTokenSchema,
} from '@/backend/payments/validations/exchange-public-token';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /payments/public-token-exchange:
 *   post:
 *     summary: Exchange public token for user's accounts
 *     description: Exchanges a public token for the user's bank accounts
 *     tags:
 *       - Payments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ExchangePublicTokenSchema'
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid body
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *
 * components:
 *   schemas:
 *     ExchangePublicTokenSchema:
 *       type: object
 *       properties:
 *         publicToken:
 *           type: string
 *           minLength: 1
 *         accounts:
 *           type: array
 *           minItems: 1
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               name:
 *                 type: string
 *               mask:
 *                 type: string
 *                 maxLength: 4
 *               type:
 *                 type: string
 *                 enum: [depository]
 *               subtype:
 *                 type: string
 *                 enum: [checking, savings]
 *               verificationStatus:
 *                 type: string
 *               classType:
 *                 type: string
 *             required:
 *               - id
 *               - name
 *               - mask
 *               - type
 *               - subtype
 *       required:
 *         - publicToken
 *         - accounts
 */
async function handler(req: AuthorizedRequest & { data: ExchangePublicTokenPayload }): Promise<NextResponse> {
  const { id } = req.user;

  const paymentService = new PaymentService();
  await paymentService.exchangePublicToken(id, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User], schema: exchangePublicTokenSchema });
