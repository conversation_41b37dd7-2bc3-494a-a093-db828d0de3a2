import { wrapper } from '@/backend/middlewares/wrapper';
import { PaymentService } from '@/backend/payments/services/payment-service';
import { WithdrawPayload, withdrawSchema } from '@/backend/payments/validations/withdraw';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /payments/withdrawal:
 *   post:
 *     summary: Initiate a withdrawal
 *     description: Initiate a withdrawal for the authenticated user
 *     tags:
 *       - Payments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: ['Plaid', 'Etransfer']
 *               amount:
 *                 type: number
 *                 minimum: 1
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Withdrawal failed
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 */
async function handler(req: AuthorizedRequest & { data: WithdrawPayload }): Promise<NextResponse> {
  const { id } = req.user;

  const paymentService = new PaymentService();
  await paymentService.withdraw(id, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User], schema: withdrawSchema });
