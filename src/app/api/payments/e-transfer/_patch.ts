import { wrapper } from '@/backend/middlewares/wrapper';
import { PaymentService } from '@/backend/payments/services/payment-service';
import { EtransferPayload, etransferSchema } from '@/backend/payments/validations/e-transfer';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /payments/e-transfer:
 *   patch:
 *     summary: Update e-transfer payment details
 *     description: Update e-transfer payment details for the authenticated user
 *     tags:
 *       - Payments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid body
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Payment method not found
 */
async function handler(req: AuthorizedRequest & { data: EtransferPayload }): Promise<NextResponse> {
  const { id } = req.user;

  const paymentService = new PaymentService();
  await paymentService.updateEtransfer(id, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export default wrapper({ handler, roles: [UserRole.User], schema: etransferSchema });
