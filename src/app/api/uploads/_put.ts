import { DeleteImgPayload, deleteImgSchema } from '@/backend/aws/validations/upload';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import config from '@/config';
import { del } from '@vercel/blob';
import { NextRequest } from 'next/server';

/**
 * @swagger
 * /upload:
 *   put:
 *     summary: Delete Image on AWS_S3
 *     description: Return message
 *     tags:
 *       - Uploads
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *             required:
 *               - url
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Remove faild
 */
async function deleteImageFromAWS(req: NextRequest & { data: DeleteImgPayload }): Promise<Response> {
  await del(req.data.url, { token: config.BLOB_READ_WRITE_TOKEN });
  return new Response(null, { status: 204 });
}

export default wrapper({
  handler: deleteImageFromAWS,
  schema: deleteImgSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
