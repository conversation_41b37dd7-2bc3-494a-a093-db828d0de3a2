import { validateImgFormData } from '@/backend/aws/validations/upload';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import config from '@/config';
import { put } from '@vercel/blob';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /uploads:
 *   post:
 *     summary: Post uploadImage on AWS_S3
 *     description: Return URL of the uploaded image
 *     tags:
 *       - Uploads
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: The image file to upload
 *               type:
 *                 type: string
 *                 enum: ['surveys']
 *                 description: Type of upload ('surveys')
 *     responses:
 *       '200':
 *         description: URL of the uploaded image
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates if the operation was successful
 *                 data:
 *                   type: string
 *                   format: url
 *                   description: URL of the uploaded image
 *       '401':
 *         description: Authorization information is missing or invalid
 *       '400':
 *         description: Upload image failed
 */
async function uploadImage(req: NextRequest): Promise<NextResponse> {
  const [file, type] = await validateImgFormData(req);
  const filename = `${type}/${Date.now()}.${file.type.split('/')[1]}`;

  const blob = await put(filename, file, {
    access: 'public',
    token: config.BLOB_READ_WRITE_TOKEN,
  });

  return NextResponse.json({ success: true, data: { url: blob.url } }, { status: 200 });
}

export default wrapper({
  handler: uploadImage,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
