import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(_request: NextRequest) {
  const accessToken = {
    name: 'accessToken',
    value: '',
    maxAge: -1,
  };

  const refreshToken = {
    name: 'refreshToken',
    value: '',
    maxAge: -1,
  };

  cookies().set(accessToken);
  cookies().set(refreshToken);
  return NextResponse.json({}, { status: 200 });
}
