import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

async function handler() {
  return NextResponse.json({ message: 'Authenticated' }, { status: 200 });
}

export const GET = wrapper({
  handler,
  roles: [AdminRole.Admin, AdminRole.Editor, UserRole.User, AdminRole.AccountManager],
});
