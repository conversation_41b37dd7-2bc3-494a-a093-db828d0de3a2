import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { wrapper } from '@/backend/middlewares/wrapper';
import { UserService } from '@/backend/users/services/user-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/login/phone-verification:
 *   get:
 *     summary: Check if phone number exists
 *     description: Return whether a phone number exists in the system
 *     tags:
 *       - Auth
 *     parameters:
 *       - in: query
 *         name: phone
 *         schema:
 *           type: string
 *         required: true
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid query
 */
async function handler(req: NextRequest): Promise<NextResponse> {
  const searchParams = req.nextUrl.searchParams;
  const phone = searchParams.get('phone');

  if (!phone || !new RegExp('^\\+1\\d{10}$').test(phone)) {
    throw customHttpError(400, 'Invalid phone number', ErrorCode.BadRequest);
  }

  const userService = new UserService();
  const isPhoneExist = await userService.checkPhoneWhenLogin(phone);

  return NextResponse.json({ status: 'Success', data: { isPhoneExist } }, { status: 200 });
}

export const GET = wrapper({ handler, firebase: true });
export const dynamic = 'force-dynamic';
