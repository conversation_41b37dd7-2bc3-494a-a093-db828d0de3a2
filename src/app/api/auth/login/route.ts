import { AuthService } from '@/backend/auth/services/auth-service';
import { LoginPayload, loginSchema } from '@/backend/auth/validations/login';
import { wrapper } from '@/backend/middlewares/wrapper';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Admin login
 *     description: Login with email and password
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *             required:
 *               - email
 *               - password
 *     responses:
 *       200:
 *         description: Success
 *         headers:
 *           Set-Cookie:
 *             description: Access token
 *             schema:
 *               type: string
 *               example: accessToken=abcdefgh; Path=/; Secure; HttpOnly; Expires=Thu, 01 May 2024 01:00:00 GMT;
 *           "\0Set-Cookie":
 *             description: Refresh token
 *             schema:
 *               type: string
 *               example: refreshToken=abcdefgh; Path=/; Secure; HttpOnly; Expires=Thu, 01 May 2024 01:00:00 GMT;
 *       400:
 *         description: Invalid input data or invalid credential
 */
async function handler(req: NextRequest & { data: LoginPayload }): Promise<NextResponse> {
  const authService = new AuthService();

  const tokens = await authService.login(req.data);
  const { accessCookie, refreshCookie } = authService.createCookieOptions(tokens);

  cookies().set(accessCookie);
  cookies().set(refreshCookie);
  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, schema: loginSchema, firebase: true });
