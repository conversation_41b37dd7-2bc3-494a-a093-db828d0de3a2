import { AuthService } from '@/backend/auth/services/auth-service';
import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { wrapper } from '@/backend/middlewares/wrapper';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/reset-password/code-verification:
 *   get:
 *     summary: Verify password reset code for admin
 *     description: Verify the password reset code sent to the admin's email
 *     tags:
 *       - Auth
 *     parameters:
 *       - in: query
 *         name: code
 *         schema:
 *           type: string
 *         required: true
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Invalid password reset code
 */
async function handler(req: NextRequest): Promise<NextResponse> {
  const searchParams = req.nextUrl.searchParams;
  const code = searchParams.get('code');

  if (!code) {
    throw customHttpError(400, 'Invalid password reset code', ErrorCode.BadRequest);
  }

  const authService = new AuthService();
  const email = await authService.verifyAdminPasswordResetCode(code);
  const isResetPassword = await authService.adminHasPassword(email);

  return NextResponse.json({ status: 'Success', isResetPassword }, { status: 200 });
}

export const GET = wrapper({ handler, firebase: true });
export const dynamic = 'force-dynamic';
