import { AuthService } from '@/backend/auth/services/auth-service';
import { AdminResetPasswordPayload, adminResetPasswordSchema } from '@/backend/auth/validations/admin-reset-password';
import { wrapper } from '@/backend/middlewares/wrapper';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/reset-password:
 *   post:
 *     summary: Admin reset password
 *     description: Reset the password for an admin
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *               password:
 *                 type: string
 *                 minLength: 8
 *             required:
 *               - code
 *               - password
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Invalid input data or failed to reset password
 */
async function handler(req: NextRequest & { data: AdminResetPasswordPayload }): Promise<NextResponse> {
  const authService = new AuthService();
  await authService.resetAdminPassword(req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, schema: adminResetPasswordSchema, firebase: true });
