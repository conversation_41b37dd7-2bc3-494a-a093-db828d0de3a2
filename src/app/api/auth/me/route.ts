import { AuthorizedRequest } from '@/backend/shared/types/app';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { AuthService } from '@/backend/auth/services/auth-service';
import { NextResponse } from 'next/server';
import { wrapper } from '@/backend/middlewares/wrapper';

async function handler(req: AuthorizedRequest) {
  const user = req.user;
  const authService = new AuthService();
  let currentUser;
  if (user.role === AdminRole.Admin || user.role === AdminRole.Editor) {
    currentUser = await authService.getCurrentUserAdmin(user.id);
  } else {
    currentUser = await authService.getCurrentUser(user.id);
  }
  return NextResponse.json({ data: currentUser, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, roles: ['*'] });
