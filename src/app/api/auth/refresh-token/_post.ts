import { TokenService } from '@/backend/auth/services/token-service';
import { RefreshTokenPayload, refreshTokenSchema } from '@/backend/auth/validations/refresh-token';
import { wrapper } from '@/backend/middlewares/wrapper';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/refresh-token:
 *   post:
 *     summary: Refresh token
 *     description: Renew access token from refresh token
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refreshToken:
 *                 type: string
 *             required:
 *               - refreshToken
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid body
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function _post(req: NextRequest & { data: RefreshTokenPayload }): Promise<NextResponse> {
  const tokenService = new TokenService();
  const { accessToken, refreshToken } = await tokenService.renewAccessToken(req.data.refreshToken);

  return NextResponse.json({ status: 'Success', data: { accessToken, refreshToken } }, { status: 200 });
}

export default wrapper({ handler: _post, schema: refreshTokenSchema });
