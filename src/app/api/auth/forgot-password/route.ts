import { AuthService } from '@/backend/auth/services/auth-service';
import {
  AdminForgotPasswordPayload,
  adminForgotPasswordSchema,
} from '@/backend/auth/validations/admin-forgot-password';
import { wrapper } from '@/backend/middlewares/wrapper';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/forgot-password:
 *   post:
 *     summary: Admin forgot password
 *     description: Send a password reset email to the admin's email address
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *             required:
 *               - email
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Invalid input data or failed to send password reset email
 *       404:
 *         description: Email not found
 */
async function handler(req: NextRequest & { data: AdminForgotPasswordPayload }): Promise<NextResponse> {
  const authService = new AuthService();
  await authService.forgotAdminPassword(req.data.email);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, schema: adminForgotPasswordSchema, firebase: true });
