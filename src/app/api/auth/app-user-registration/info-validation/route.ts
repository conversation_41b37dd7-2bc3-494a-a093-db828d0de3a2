import {
  UserRegistrationValidationPayload,
  userRegistrationValidationSchema,
} from '@/backend/auth/validations/app-user-registration';
import { wrapper } from '@/backend/middlewares/wrapper';
import { UserService } from '@/backend/users/services/user-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/app-user-registration/info-validation:
 *   post:
 *     summary: Validate app user registration information
 *     description: Validate app user registration information including phone, email, and referral code
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               phone:
 *                 type: string
 *                 pattern: "^\\+1\\d{10}$"
 *                 description: The phone number of the user in the format +1xxxxxxxxxx
 *               email:
 *                 type: string
 *                 format: email
 *               referralCode:
 *                 type: string
 *             required:
 *               - phone
 *               - email
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Invalid input data
 */

async function handler(req: NextRequest & { data: UserRegistrationValidationPayload }): Promise<NextResponse> {
  const userService = new UserService();
  const { email, phone, referralCode } = req.data;
  const info = await userService.validateAppUserInfo(email, phone, referralCode);

  const isRefCodeValid = !referralCode ? true : info.publicReferralCode || info.referralUser ? true : false;

  const data = {
    isEmailExist: info.isEmailExist,
    isPhoneExist: info.isPhoneExist,
    isRefCodeValid,
  };

  return NextResponse.json({ status: 'Success', data }, { status: 200 });
}

export const POST = wrapper({ handler, firebase: true, schema: userRegistrationValidationSchema });
