import { UserRegistrationPayload, userRegistrationSchema } from '@/backend/auth/validations/app-user-registration';
import { wrapper } from '@/backend/middlewares/wrapper';
import { UserService } from '@/backend/users/services/user-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/app-user-registration:
 *   post:
 *     summary: App user registration
 *     description: Register a new app user
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *                 pattern: "^[a-zA-Z']+$"
 *               lastName:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *                 pattern: "^[a-zA-Z']+$"
 *               phone:
 *                 type: string
 *                 pattern: "^\\+1\\d{10}$"
 *                 description: The phone number of the user in the format +1xxxxxxxxxx
 *               email:
 *                 type: string
 *                 format: email
 *               referralCode:
 *                 type: string
 *               preferredLanguage:
 *                 type: string
 *                 enum:
 *                   - en-ca
 *                   - fr-ca
 *               idToken:
 *                 type: string
 *               isEmailOptIn:
 *                 type: boolean
 *             required:
 *               - firstName
 *               - lastName
 *               - phone
 *               - email
 *               - idToken
 *               - preferredLanguage
 *               - isEmailOptIn
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Invalid input data
 */
async function handler(req: NextRequest & { data: UserRegistrationPayload }): Promise<NextResponse> {
  const userService = new UserService();
  const response = await userService.registerAppUser(req.data);

  return NextResponse.json({ status: 'Success', data: response }, { status: 200 });
}

export const POST = wrapper({ handler, firebase: true, schema: userRegistrationSchema });
