import { AuthService } from '@/backend/auth/services/auth-service';
import { UserLoginPayload, userLoginSchema } from '@/backend/auth/validations/app-user-login';
import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { wrapper } from '@/backend/middlewares/wrapper';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /auth/app-user-login:
 *   post:
 *     summary: App user login
 *     description: Login with ID token
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               idToken:
 *                 type: string
 *             required:
 *               - idToken
 *     responses:
 *       200:
 *         description: Success
 *       400:
 *         description: Invalid ID token
 */
async function handler(req: NextRequest & { data: UserLoginPayload }): Promise<NextResponse> {
  const authService = new AuthService();

  const tokens = await authService.verifyCredential(req.data.idToken);
  if (!tokens) {
    throw customHttpError(400, 'Invalid ID token', ErrorCode.BadRequest);
  }

  return NextResponse.json(
    {
      status: 'Success',
      data: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
      },
    },
    { status: 200 },
  );
}

export const POST = wrapper({ handler, schema: userLoginSchema, firebase: true });
