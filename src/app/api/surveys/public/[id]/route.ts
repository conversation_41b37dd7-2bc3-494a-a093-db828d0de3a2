import { wrapper } from '@/backend/middlewares/wrapper';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { NextRequest, NextResponse } from 'next/server';

async function handler(_req: NextRequest, { params }: { params: { id: string } }): Promise<NextResponse> {
  const slug = params.id;

  const surveyServices = new SurveyService();
  const surveyId = await surveyServices.resolveSurveySlug(slug);

  return NextResponse.json({ data: { surveyId }, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, validatePathParams: false });
