import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { CreateSurveyPayload, createSurveySchema } from '@/backend/surveys/validations/create-survey';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys:
 *   post:
 *     summary: Create a new survey
 *     description: Create a new survey based on provided data
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               companyId:
 *                 type: integer
 *                 minimum: 1
 *               title:
 *                 type: string
 *                 minLength: 1
 *               description:
 *                 type: string
 *               compensation:
 *                 type: integer
 *                 minimum: 1
 *               maxParticipants:
 *                 type: integer
 *                 minimum: 1
 *                 nullable: true
 *               responsesPerUser:
 *                 type: integer
 *                 minimum: 1
 *               time:
 *                 type: integer
 *                 minimum: 1
 *               expiryDate:
 *                 type: string
 *                 format: date
 *               image:
 *                 type: string
 *               backgroundImage:
 *                 type: string
 *               isPinned:
 *                 type: boolean
 *               isPublic:
 *                 type: boolean
 *               status:
 *                 type: string
 *                 enum:
 *                   - Active
 *                   - Draft
 *               translation:
 *                 type: object
 *                 properties:
 *                   locale:
 *                     type: string
 *                     enum:
 *                       - Fr
 *                   title:
 *                     type: string
 *                   description:
 *                     type: string
 *                 required:
 *                   - locale
 *                   - title
 *               questions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     order:
 *                       type: integer
 *                       minimum: 1
 *                     questionType:
 *                       type: string
 *                       enum:
 *                         - Text
 *                         - Date
 *                         - Number
 *                         - Slider
 *                         - MultipleChoice
 *                         - SingleChoice
 *                         - Rank
 *                         - Screening
 *                     title:
 *                       type: string
 *                       minLength: 1
 *                     subtitle:
 *                       type: string
 *                     translation:
 *                       type: object
 *                       properties:
 *                         locale:
 *                           type: string
 *                           enum:
 *                             - Fr
 *                         title:
 *                           type: string
 *                         subtitle:
 *                           type: string
 *                       required:
 *                         - locale
 *                         - title
 *                     minValue:
 *                       type: integer
 *                       minimum: 0
 *                       nullable: true
 *                     maxValue:
 *                       type: integer
 *                       minimum: 1
 *                       nullable: true
 *                     options:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           title:
 *                             type: string
 *                             minLength: 1
 *                           translation:
 *                             type: object
 *                             properties:
 *                               locale:
 *                                 type: string
 *                                 enum:
 *                                   - Fr
 *                               title:
 *                                 type: string
 *                                 minLength: 1
 *                             required:
 *                               - locale
 *                               - title
 *                         required:
 *                           - title
 *                       minItems: 1
 *                     hasOtherOption:
 *                       type: boolean
 *                     isMultiSelectionEnabled:
 *                       type: boolean
 *                   required:
 *                     - order
 *                     - questionType
 *                     - title
 *                 minItems: 1
 *               audience:
 *                 type: object
 *                 properties:
 *                   isPublish:
 *                     type: boolean
 *                   audienceId:
 *                     type: number
 *                     minimum: 1
 *                   cities:
 *                     type: array
 *                     items:
 *                        type: string
 *                   completedSurveys:
 *                     type: array
 *                     items:
 *                        type: number
 *                   employmentStatuses:
 *                     type: array
 *                     items:
 *                        type: string
 *                   practiceSettings:
 *                     type: array
 *                     items:
 *                        type: string
 *                   provinces:
 *                     type: array
 *                     items:
 *                        type: string
 *                   specialtyIds:
 *                     type: array
 *                     items:
 *                        type: number
 *                   userIds:
 *                     type: array
 *                     items:
 *                        type: number
 *             required:
 *              - companyId
 *              - title
 *              - compensation
 *              - maxParticipants
 *              - responsesPerUser
 *              - time
 *              - expiryDate
 *              - isPinned
 *              - status
 *              - translation
 *              - isPublic
 *     responses:
 *       201:
 *         description: Created
 *       400:
 *         description: Invalid body or company not found
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Resource not found
 */
async function handler(req: AuthorizedRequest & { data: CreateSurveyPayload }): Promise<NextResponse> {
  const userId = req.user.id;
  const role = req.user.role;

  const surveyService = new SurveyService();
  surveyService.validateAccountManagerCreatePublicSurvey(role, req.data);
  const newSurvey = await surveyService.create(userId, req.data);

  return NextResponse.json({ status: 'Success', data: newSurvey }, { status: 201 });
}

export default wrapper({
  handler,
  schema: createSurveySchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
