import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { Survey } from '@/backend/surveys/entities/Survey';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /api/surveys:
 *   get:
 *     summary: Get a paginated list of surveys
 *     description: Retrieves surveys with pagination support. Restricted to Admin and Editor roles.
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 */
async function handler(req: AuthorizedRequest & { data: PaginationPayload<Survey> }): Promise<NextResponse> {
  const user = req.user;

  const surveyServices = new SurveyService();
  const result = await surveyServices.listSurveys(req.data, user);

  return NextResponse.json({ data: result, status: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler,
  schema: paginationSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
