import { wrapper } from '@/backend/middlewares/wrapper';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { PublishSurveyPayload, publishSurveySchema } from '@/backend/surveys/validations/publish-survey';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/audiences:
 *   post:
 *     summary: Save draft audience for a survey
 *     description: Saves a draft audience for the survey with the specified survey ID.
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               audienceId:
 *                 type: number
 *                 minimum: 1
 *               isPublish:
 *                 type: boolean
 *               cities:
 *                 type: array
 *                 items:
 *                   type: string
 *                 nullable: true
 *               completedSurveys:
 *                 type: array
 *                 items:
 *                   type: number
 *                 nullable: true
 *               employmentStatuses:
 *                 type: array
 *                 items:
 *                   type: string
 *                 nullable: true
 *               practiceSettings:
 *                 type: array
 *                 items:
 *                   type: string
 *                 nullable: true
 *               provinces:
 *                 type: array
 *                 items:
 *                   type: string
 *                 nullable: true
 *               specialtyIds:
 *                 type: array
 *                 items:
 *                   type: number
 *                 nullable: true
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: number
 *                 nullable: true
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Bad request
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Not found
 */
async function handler(
  req: NextRequest & { data: PublishSurveyPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const surveyId = params.id;

  const surveyService = new SurveyService();
  await surveyService.saveDraftAudience(surveyId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export default wrapper({
  handler,
  schema: publishSurveySchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
