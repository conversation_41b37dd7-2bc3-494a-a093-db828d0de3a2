import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/audiences:
 *   get:
 *     summary: Retrieve audiences by survey ID
 *     description: Returns a list of audiences associated with the specified survey ID.
 *     tags:
 *       - Surveys
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 */
async function handler(req: NextRequest, { params }: { params: { id: number } }): Promise<NextResponse> {
  const surveyId = params.id;

  const audienceServices = new AudienceService();
  const audiences = await audienceServices.audiencesBySurveyId(surveyId);
  return NextResponse.json({ data: audiences, status: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({ handler, roles: ['*'] });
