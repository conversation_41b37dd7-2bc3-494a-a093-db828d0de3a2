import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { getCsvHeaders } from '@/backend/shared/utils/export-csv';
import { SurveyExportService } from '@/backend/surveys/services/survey-export-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/exports:
 *   get:
 *     summary: Export surveys
 *     description: Export surveys by its ID
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the Surveys
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid Surveys ID or request body
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Surveys not found
 */
async function handler(req: AuthorizedRequest, { params }: NextParams) {
  const surveyId = params.id;
  const user = req.user;

  const surveyExportService = new SurveyExportService();
  const blob = await surveyExportService.exportSurvey(surveyId, user);

  return new NextResponse(blob, { status: 200, headers: getCsvHeaders('surveys') });
}

export const GET = wrapper({
  handler,
  validatePathParams: true,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
