import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/results:
 *   get:
 *     summary: Get survey results
 *     description: Retrieve survey results by survey ID
 *     tags:
 *       - Surveys
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the survey to retrieve results for
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   description: Indicates the status of the response
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/QuestionUserAnswers'
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Survey with the specified ID not found
 *
 * components:
 *   schemas:
 *     QuestionUserAnswers:
 *       type: object
 *       properties:
 *         id:
 *           type: number
 *           description: The ID of the question
 *         surveyId:
 *           type: number
 *           description: The ID of the survey
 *         questionType:
 *           type: string
 *           enum: ['SingleSelection', 'MultipleSelection', 'Text', 'Number', 'Date', 'Slider', 'Rank']
 *         title:
 *           type: string
 *           description: The text of the question
 *         subtitle:
 *           type: string
 *           description: The subtitle of the question
 *         locale:
 *           type: string
 *           description: The locale of the question
 *         optionsCalculate:
 *           type: object
 *           properties:
 *             option1:
 *               type: object
 *               properties:
 *                 count:
 *                   type: number
 *                   description: number of respondents
 *                 percentage:
 *                   type: number
 *                   description: percentage of respondents
 *           description: Calculation of options
 *         totalUsers:
 *           type: number
 *           description: Total number of users who answered the question
 */
async function handler(req: AuthorizedRequest, { params }: NextParams) {
  const surveyId = params.id;
  const user = req.user;

  const surveyServices = new SurveyService();
  const surveyResult = await surveyServices.surveyResults(surveyId, user);

  return NextResponse.json({ status: 'Success', data: surveyResult }, { status: 200 });
}

export const GET = wrapper({
  handler,
  validatePathParams: true,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
