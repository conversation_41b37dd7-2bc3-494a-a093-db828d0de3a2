import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/unpublish:
 *   post:
 *     summary: Unpublish a survey
 *     description: Unpublishes a survey with the specified survey ID
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Bad request
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Not found
 */
async function handler(req: AuthorizedRequest, { params }: { params: { id: number } }): Promise<NextResponse> {
  const surveyId = params.id;
  const user = req.user;

  const surveyService = new SurveyService();
  await surveyService.unpublish(surveyId, user);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({
  handler,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
