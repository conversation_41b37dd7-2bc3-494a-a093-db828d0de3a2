import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { SubmitPrivateSurveyPayload, submitPrivateSurveySchema } from '@/backend/surveys/validations/submit-survey';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/submission:
 *   post:
 *     summary: Submit a survey
 *     description: Submits a survey response for the specified survey ID
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startDate:
 *                 type: string
 *                 format: date-time
 *               surveyAnswers:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/SurveyAnswer'
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Bad request - Invalid survey data
 * components:
 *   schemas:
 *     SurveyAnswer:
 *       type: object
 *       properties:
 *         questionId:
 *           type: integer
 *         questionOptionIds:
 *           type: array
 *           items:
 *             type: integer
 *         value:
 *           type: string
 */
async function handler(
  req: AuthorizedRequest & { data: SubmitPrivateSurveyPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const { id } = req.user;
  const surveyId = params.id;

  const surveyService = new SurveyService();
  await surveyService.submit(id, surveyId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User], schema: submitPrivateSurveySchema });
