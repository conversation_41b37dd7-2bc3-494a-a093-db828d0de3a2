import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { UpdateSurveyPayload, updateSurveySchema } from '@/backend/surveys/validations/update-survey';
import { AdminRole, UserAdmin } from '@/backend/users/entities/UserAdmin';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}:
 *   patch:
 *     summary: Update an existing survey
 *     description: Update an existing survey with the provided data
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               companyId:
 *                 type: integer
 *                 minimum: 1
 *               title:
 *                 type: string
 *                 minLength: 1
 *               description:
 *                 type: string
 *                 nullable: true
 *               compensation:
 *                 type: integer
 *                 minimum: 1
 *               maxParticipants:
 *                 type: integer
 *                 minimum: 1
 *                 nullable: true
 *               time:
 *                 type: integer
 *                 minimum: 1
 *               expiryDate:
 *                 type: string
 *                 format: date
 *               image:
 *                 type: string
 *               backgroundImage:
 *                 type: string
 *               isPinned:
 *                 type: boolean
 *               isPublic:
 *                 type: boolean
 *               translation:
 *                 type: object
 *                 properties:
 *                   locale:
 *                     type: string
 *                     enum:
 *                       - Fr
 *                   title:
 *                     type: string
 *                   description:
 *                     type: string
 *                     nullable: true
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Resource not found
 */
async function handler(
  req: AuthorizedRequest & { data: UpdateSurveyPayload },
  { params }: NextParams,
): Promise<NextResponse> {
  const surveyId = params.id;

  const surveyService = new SurveyService();
  const updatedSurvey = await surveyService.update(req.user as UserAdmin, surveyId, req.data);

  return NextResponse.json({ status: 'Success', data: updatedSurvey }, { status: 200 });
}

export default wrapper({
  handler,
  schema: updateSurveySchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
