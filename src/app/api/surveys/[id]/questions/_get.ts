import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyQuestionService } from '@/backend/surveys/services/survey-question-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/questions:
 *   get:
 *     summary: Get survey questions
 *     description: Retrieve survey by ID
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The ID of the Surveys
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SurveyQuestion'
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: invalid enum value
 * components:
 *   schemas:
 *     SurveyQuestion:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique ID of the survey question
 *         surveyId:
 *           type: integer
 *           description: The ID of the survey associated with this question
 *         order:
 *           type: integer
 *           description: The order of the question in the survey
 *         questionType:
 *           type: string
 *           enum:
 *             - SingleSelection
 *             - MultipleSelection
 *             - Text
 *             - Number
 *             - Date
 *             - Slider
 *             - Rank
 *           description: The type of the survey question
 *         locale:
 *           type: string
 *           enum:
 *             - EN
 *             - FR
 *           description: The locale (language) of the survey question
 *         title:
 *           type: string
 *           description: The title of the survey question
 *         subtitle:
 *           type: string
 *           nullable: true
 *           description: The subtitle of the survey question
 *         minValue:
 *           type: integer
 *           nullable: true
 *           description: The minimum value for the survey question (if applicable)
 *         maxValue:
 *           type: integer
 *           nullable: true
 *           description: The maximum value for the survey question (if applicable)
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question was last updated
 *         translation:
 *           $ref: '#/components/schemas/SurveyQuestionTranslation'
 *         options:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/SurveyQuestionOption'
 *
 *     SurveyQuestionOption:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique ID of the survey question option
 *         questionId:
 *           type: integer
 *           description: The ID of the survey question associated with this option
 *         surveyId:
 *           type: integer
 *           description: The ID of the survey associated with this option
 *         title:
 *           type: string
 *           description: The title of the survey question option
 *         locale:
 *           type: string
 *           enum:
 *             - EN
 *             - FR
 *           description: The locale (language) of the survey question option
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question option was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question option was last updated
 *         translation:
 *           $ref: '#/components/schemas/SurveyQuestionOptionTranslation'
 *
 *     SurveyQuestionOptionTranslation:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique ID of the survey question option translation
 *         questionOptionId:
 *           type: integer
 *           description: The ID of the survey question option associated with this translation
 *         questionId:
 *           type: integer
 *           description: The ID of the survey question associated with this translation
 *         surveyId:
 *           type: integer
 *           description: The ID of the survey associated with this translation
 *         locale:
 *           type: string
 *           description: The locale (language) of the survey question option translation
 *         title:
 *           type: string
 *           description: The translated title of the survey question option
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question option translation was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question option translation was last updated
 *
 *     SurveyQuestionTranslation:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique ID of the survey question translation
 *         questionId:
 *           type: integer
 *           description: The ID of the survey question associated with this translation
 *         surveyId:
 *           type: integer
 *           description: The ID of the survey associated with this translation
 *         locale:
 *           type: string
 *           description: The locale (language) of the survey question translation
 *         title:
 *           type: string
 *           description: The translated title of the survey question
 *         subtitle:
 *           type: string
 *           nullable: true
 *           description: The translated subtitle of the survey question
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question translation was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey question translation was last updated
 */
async function questionsBySurveyId(
  req: AuthorizedRequest,
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const surveyId = params.id;
  const user = req.user;

  const surveyQuestionsServices = new SurveyQuestionService();
  const survey = await surveyQuestionsServices.listQuestions(surveyId, user);

  return NextResponse.json({ data: survey, status: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler: questionsBySurveyId,
  roles: [UserRole.User, AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
