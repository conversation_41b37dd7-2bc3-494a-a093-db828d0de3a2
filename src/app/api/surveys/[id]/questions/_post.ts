import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyQuestionService } from '@/backend/surveys/services/survey-question-service';
import { CreateQuestionPayload, createQuestionSchema } from '@/backend/surveys/validations/create-survey';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/questions:
 *   post:
 *     summary: Create a new survey question
 *     description: Create a new survey question based on provided data
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the survey
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               order:
 *                 type: integer
 *                 minimum: 1
 *               questionType:
 *                 type: string
 *                 enum:
 *                   - Text
 *                   - Date
 *                   - Number
 *                   - Slider
 *                   - MultipleChoice
 *                   - SingleChoice
 *                   - Rank
 *                   - Screening
 *               title:
 *                 type: string
 *                 minLength: 1
 *               subtitle:
 *                 type: string
 *               translation:
 *                 type: object
 *                 properties:
 *                   locale:
 *                     type: string
 *                     enum:
 *                       - Fr
 *                   title:
 *                     type: string
 *                   subtitle:
 *                     type: string
 *                 required:
 *                   - locale
 *                   - title
 *               minValue:
 *                 type: integer
 *                 minimum: 0
 *                 nullable: true
 *               maxValue:
 *                 type: integer
 *                 minimum: 1
 *                 nullable: true
 *               options:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                       minLength: 1
 *                     translation:
 *                       type: object
 *                       properties:
 *                         locale:
 *                           type: string
 *                           enum:
 *                             - Fr
 *                         title:
 *                           type: string
 *                           minLength: 1
 *                       required:
 *                         - locale
 *                         - title
 *                   required:
 *                     - title
 *                 minItems: 1
 *               hasOtherOption:
 *                 type: boolean
 *               isMultiSelectionEnabled:
 *                 type: boolean
 *             required:
 *               - order
 *               - questionType
 *               - title
 *     responses:
 *       201:
 *         description: OK
 *       400:
 *         description: Invalid body or validation error
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Resource not found
 */
async function handler(
  req: AuthorizedRequest & { data: CreateQuestionPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const user = req.user;
  const surveyId = params.id;

  const surveyQuestionService = new SurveyQuestionService();
  await surveyQuestionService.create(user, surveyId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 201 });
}

export default wrapper({
  handler,
  schema: createQuestionSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
