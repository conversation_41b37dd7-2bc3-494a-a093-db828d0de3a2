import { forbiddenError } from '@/backend/middlewares/capture-errors';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyQuestionService } from '@/backend/surveys/services/survey-question-service';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import {
  UpdateSurveyQuestionPayload,
  updateSurveyQuestionSchema,
} from '@/backend/surveys/validations/update-survey-question';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/questions:
 *   patch:
 *     summary: Update a survey question
 *     description: Update an existing survey question with the provided data
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the survey question to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               order:
 *                 type: integer
 *                 minimum: 1
 *                 description: Order of the question in the survey
 *               questionType:
 *                 type: string
 *                 enum:
 *                   - Text
 *                   - Date
 *                   - Number
 *                   - Slider
 *                   - MultipleChoice
 *                   - SingleChoice
 *                   - Rank
 *                   - Screening
 *               locale:
 *                 type: string
 *                 enum:
 *                   - EN
 *                   - FR
 *               title:
 *                 type: string
 *                 minLength: 1
 *               subtitle:
 *                 type: string
 *               translation:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   locale:
 *                     type: string
 *                     enum:
 *                       - FR
 *                   title:
 *                     type: string
 *               minValue:
 *                 type: integer
 *               maxValue:
 *                 type: integer
 *               options:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     title:
 *                       type: string
 *                       minLength: 1
 *                     translation:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         locale:
 *                           type: string
 *                           enum:
 *                             - FR
 *                         title:
 *                           type: string
 *               surveyId:
 *                 type: integer
 *               hasOtherOption:
 *                 type: boolean
 *               isMultiSelectionEnabled:
 *                type: boolean
 *     responses:
 *       200:
 *         description: Survey question updated successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized access
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Survey question not found
 */
async function handler(
  req: AuthorizedRequest & { data: UpdateSurveyQuestionPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const surveyId = params.id;
  const user = req.user;

  const surveyServices = new SurveyService();
  const survey = await surveyServices.findSurveyExpired(surveyId);

  if (user.role === AdminRole.AccountManager && survey.createdBy !== user.id && survey.isPublic) {
    throw forbiddenError('You are not allowed to update this survey question');
  }

  const surveyQuestionsServices = new SurveyQuestionService();
  await surveyQuestionsServices.update(req.data);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: user.id,
    description: `Updated Questions of Survey - ${survey.title}`,
    entity: EntitiesName.Surveys,
  });

  return NextResponse.json({ message: 'Success' }, { status: 200 });
}

export default wrapper({
  handler,
  schema: updateSurveyQuestionSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
