import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyQuestionService } from '@/backend/surveys/services/survey-question-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

/**
 * @swagger
 * /surveys/{id}/questions/{questionId}:
 *   delete:
 *     summary: Delete a survey question
 *     description: Delete a survey question by its ID
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *       - in: path
 *         name: questionId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       204:
 *         description: OK
 *       400:
 *         description: Survey already has responses
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Survey question or survey not found
 */
async function handler(
  req: AuthorizedRequest,
  { params }: { params: { id: number; questionId: number } },
): Promise<Response> {
  const { id, questionId } = params;
  const user = req.user;

  const surveyQuestionService = new SurveyQuestionService();
  await surveyQuestionService.delete(id, questionId, user);

  return new Response(null, { status: 204 });
}

export default wrapper({
  handler,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
