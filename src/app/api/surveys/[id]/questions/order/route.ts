import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyQuestionService } from '@/backend/surveys/services/survey-question-service';
import {
  UpdateQuestionOrderPayload,
  updateQuestionOrderSchema,
} from '@/backend/surveys/validations/update-survey-question';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/questions/order:
 *   patch:
 *     summary: Update the order of survey questions
 *     description: Update the order of questions within a survey
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               questions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     order:
 *                       type: integer
 *                 description: A list of questions with their new orders
 *                 minItems: 1
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Bad request
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Survey not found
 */
async function handler(
  req: AuthorizedRequest & { data: UpdateQuestionOrderPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const surveyId = params.id;
  const user = req.user;

  const surveyQuestionService = new SurveyQuestionService();
  await surveyQuestionService.updateOrder(surveyId, req.data, user);

  return NextResponse.json({ message: 'Success' }, { status: 200 });
}

export const PATCH = wrapper({
  handler,
  schema: updateQuestionOrderSchema,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
