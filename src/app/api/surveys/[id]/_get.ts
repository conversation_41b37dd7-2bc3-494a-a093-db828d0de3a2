import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}:
 *   get:
 *     summary: Get survey by ID
 *     description: Retrieve survey by ID
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The ID of the Surveys.
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Survey'
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: invalid enum value
 * components:
 *   schemas:
 *     Survey:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique ID of the survey
 *         companyId:
 *           type: integer
 *           description: The ID of the company associated with the survey
 *         locale:
 *           type: string
 *           enum:
 *             - En
 *             - Fr
 *           description: The locale of the survey (language)
 *         title:
 *           type: string
 *           description: The title of the survey
 *         description:
 *           type: string
 *           nullable: true
 *           description: The description of the survey
 *         compensation:
 *           type: number
 *           format: float
 *           description: The compensation amount for completing the survey
 *         maxParticipants:
 *           type: integer
 *           description: The maximum number of participants allowed for the survey
 *         isUnlimited:
 *           type: boolean
 *           description: Indicates if the survey has unlimited participants
 *         time:
 *           type: integer
 *           description: The estimated time (in minutes) to complete the survey
 *         expiryDate:
 *           type: string
 *           format: date-time
 *           description: The expiry date of the survey
 *         image:
 *           type: string
 *           nullable: true
 *           description: URL of the survey image
 *         backgroundImage:
 *           type: string
 *           nullable: true
 *           description: URL of the survey background image
 *         isPinned:
 *           type: boolean
 *           description: Indicates if the survey is pinned
 *         status:
 *           type: string
 *           enum:
 *             - Draft
 *             - Active
 *             - Expired
 *           description: The status of the survey
 *         successfulCompletions:
 *           type: integer
 *           description: The number of successful completions of the survey
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey was last updated
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: The timestamp when the survey was deleted (if applicable)
 *         translation:
 *           $ref: '#/components/schemas/SurveyTranslation'
 *     SurveyTranslation:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique ID of the survey translation
 *         surveyId:
 *           type: integer
 *           description: The ID of the survey associated with this translation
 *         title:
 *           type: string
 *           description: The title of the survey in the specified locale
 *         description:
 *           type: string
 *           nullable: true
 *           description: The description of the survey in the specified locale
 *         locale:
 *           type: string
 *           enum:
 *             - FR
 *             - EN
 *           description: The locale (language) of the survey translation
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey translation was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the survey translation was last updated
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: The timestamp when the survey translation was deleted (if applicable)
 */
async function handler(req: AuthorizedRequest, { params }: { params: { id: number } }): Promise<NextResponse> {
  const surveyId = params.id;
  const user = req.user;

  const surveyServices = new SurveyService();

  const survey = await surveyServices.getUserAvailableSurvey(surveyId, user);
  return NextResponse.json({ data: survey, status: 'Success' }, { status: 200 });
}

export default wrapper({
  handler,
  roles: [UserRole.User, AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
