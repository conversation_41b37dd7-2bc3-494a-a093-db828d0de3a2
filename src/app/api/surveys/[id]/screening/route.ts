import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { SubmitScreeningPayload, submitScreeningSchema } from '@/backend/surveys/validations/submit-screening';
import { UserRole } from '@/backend/users/types/user';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/screening:
 *   post:
 *     summary: Submit screening questions for a survey
 *     description: Submits answers to screening questions to determine user eligibility for the survey
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The survey ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - surveyAnswers
 *             properties:
 *               surveyAnswers:
 *                 type: array
 *                 description: List of answers to screening questions
 *                 items:
 *                   $ref: '#/components/schemas/ScreeningAnswer'
 *               startDate:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Bad request - Invalid screening data
 * components:
 *   schemas:
 *     ScreeningAnswer:
 *       type: object
 *       required:
 *         - questionId
 *         - questionOptionIds
 *       properties:
 *         questionId:
 *           type: integer
 *           minimum: 1
 *         questionOptionIds:
 *           type: array
 *           items:
 *             type: integer
 *             minimum: 1
 */
async function handler(
  req: AuthorizedRequest & { data: SubmitScreeningPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const { id } = req.user;
  const surveyId = params.id;

  const surveyService = new SurveyService();
  const isEligible = await surveyService.submitScreeningQuestions(surveyId, req.data, id);

  return NextResponse.json({ status: 'Success', data: { isEligible } }, { status: 200 });
}

export const POST = wrapper({ handler, roles: [UserRole.User], schema: submitScreeningSchema });
