import { wrapper } from '@/backend/middlewares/wrapper';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { SubmitPublicSurveyPayload, submitPublicSurveySchema } from '@/backend/surveys/validations/submit-survey';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/public/submission:
 *   post:
 *     summary: Submit a public survey
 *     description: Submits a public survey response for the specified survey ID without authentication
 *     tags:
 *       - Surveys
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               startDate:
 *                 type: string
 *                 format: date-time
 *               surveyAnswers:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/SurveyAnswer'
 *     responses:
 *       200:
 *         description: OK
 *       400:
 *         description: Bad request - Invalid survey data
 */
async function handler(
  req: NextRequest & { data: SubmitPublicSurveyPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const surveyId = params.id;

  const surveyService = new SurveyService();
  await surveyService.submitPublicSurvey(surveyId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler, schema: submitPublicSurveySchema });
