import { wrapper } from '@/backend/middlewares/wrapper';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { SubmitScreeningPayload, submitScreeningSchema } from '@/backend/surveys/validations/submit-screening';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/public/screening:
 *   post:
 *     summary: Submit screening questions for a survey (Public)
 *     description: Public endpoint that submits answers to screening questions to determine user eligibility for the survey without authentication
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - surveyAnswers
 *             properties:
 *               surveyAnswers:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/ScreeningAnswer'
 *               startDate:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Bad request - Invalid survey data
 */
async function handler(
  req: NextRequest & { data: SubmitScreeningPayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const surveyId = params.id;

  const surveyService = new SurveyService();
  const isEligible = await surveyService.submitScreeningQuestions(surveyId, req.data);

  return NextResponse.json({ status: 'Success', data: { isEligible } }, { status: 200 });
}

export const POST = wrapper({ handler, schema: submitScreeningSchema });
