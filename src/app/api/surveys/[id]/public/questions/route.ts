import { wrapper } from '@/backend/middlewares/wrapper';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/public/questions:
 *   get:
 *     summary: Get public survey questions
 *     description: Retrieves all public questions for a specific survey
 *     tags:
 *       - Surveys
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: OK
 *       404:
 *         description: Survey not found
 *       500:
 *         description: Internal server error
 */
async function handler(_req: NextRequest, { params }: { params: { id: number } }): Promise<NextResponse> {
  const surveyId = params.id;
  const surveyServices = new SurveyService();

  const survey = await surveyServices.getPublicSurveyQuestions(surveyId);
  return NextResponse.json({ data: survey, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler });
