import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextParams } from '@/types/next-params';

/**
 * @swagger
 * /surveys/{id}:
 *   delete:
 *     summary: Delete a survey
 *     description: Delete a survey by its ID
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       204:
 *         description: OK
 *       400:
 *         description: Survey already has responses
 *       401:
 *         description: Authorization information is missing or invalid
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Survey not found
 */
async function handler(req: AuthorizedRequest, { params }: NextParams): Promise<Response> {
  const surveyId = params.id;
  const user = req.user;

  const surveyService = new SurveyService();
  const survey = await surveyService.delete(surveyId, user);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: user.id,
    description: `Deleted Survey - ${survey.title}`,
    entity: EntitiesName.Surveys,
  });

  return new Response(null, { status: 204 });
}

export default wrapper({
  handler,
  roles: [AdminRole.Admin, AdminRole.Editor, AdminRole.AccountManager],
});
