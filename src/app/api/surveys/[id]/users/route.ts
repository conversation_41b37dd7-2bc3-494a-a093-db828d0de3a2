import { wrapper } from '@/backend/middlewares/wrapper';
import { User } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import { NextParams } from '@/types/next-params';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /surveys/{id}/users:
 *   get:
 *     summary: Get users who completed a specific survey
 *     description: Returns a paginated list of users who have completed the specified survey
 *     tags:
 *       - Surveys
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *     responses:
 *       200:
 *         description: OK
 *       401:
 *         description: Unauthorized - User not authenticated
 *       403:
 *         description: Forbidden - User does not have required permissions
 *       404:
 *         description: Survey not found
 */
async function handler(req: NextRequest & { data: PaginationPayload<User> }, { params }: NextParams) {
  const surveyId = params.id;

  const userService = new UserService();
  const data = await userService.getUsersCompletedSurvey(surveyId, req.data);

  return NextResponse.json({ status: 'Success', data }, { status: 200 });
}

export const GET = wrapper({
  handler,
  validatePathParams: true,
  schema: paginationSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
