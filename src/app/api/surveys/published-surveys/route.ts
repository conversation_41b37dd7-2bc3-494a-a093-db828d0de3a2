import { wrapper } from '@/backend/middlewares/wrapper';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

async function handler(_: NextRequest) {
  const surveyService = new SurveyService();
  const surveys = await surveyService.getPublishedSurveys();
  return NextResponse.json({ status: 'Success', data: surveys }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
