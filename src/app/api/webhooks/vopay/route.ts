import { badRequestError } from '@/backend/middlewares/capture-errors';
import { wrapper } from '@/backend/middlewares/wrapper';
import { PaymentService } from '@/backend/payments/services/payment-service';
import { NextRequest } from 'next/server';

async function handler(req: NextRequest & { data: Record<string, unknown> }) {
  let body;
  try {
    body = await req.json();
  } catch (error) {
    throw badRequestError('Invalid body');
  }

  const paymentService = new PaymentService();
  await paymentService.processVopayWebhook(body);

  return Response.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler });
