import { HubspotService } from '@/backend/hubspot/services/hubspot-service';
import { badRequestError } from '@/backend/middlewares/capture-errors';
import { wrapper } from '@/backend/middlewares/wrapper';
import { headers } from 'next/headers';
import { NextRequest } from 'next/server';

async function handler(req: NextRequest) {
  let body;
  try {
    body = await req.json();
  } catch (error) {
    throw badRequestError('Invalid body');
  }

  const signature = headers().get('x-hubspot-signature') || '';
  const signatureVersion = headers().get('x-hubspot-signature-version') || '';

  const hubspotService = new HubspotService();
  hubspotService.verifySignature(JSON.stringify(body), signature, signatureVersion);
  await hubspotService.processWebhook(body);

  return Response.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({ handler });
