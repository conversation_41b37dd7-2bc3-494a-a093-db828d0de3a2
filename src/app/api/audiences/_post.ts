import { AudienceService } from '@/backend/audiences/services/audience-service';
import { CreateAudiencePayload, createAudienceSchema } from '@/backend/audiences/validations/add-update-audience';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences:
 *   post:
 *     summary: Create a new audience
 *     description: Create a new audience with the provided information
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateAudienceSchema'
 *     responses:
 *       201:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Audience'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Authorization information is missing or invalid
 *
 * components:
 *   schemas:
 *     CreateAudienceSchema:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *         cities:
 *           type: array
 *           items:
 *             type: string
 *         completedSurveys:
 *           type: array
 *           items:
 *             type: number
 *         employmentStatuses:
 *           type: array
 *           items:
 *             type: string
 *         practiceSettings:
 *           type: array
 *           items:
 *             type: string
 *         provinces:
 *           type: array
 *           items:
 *             type: string
 *         specialtyIds:
 *           type: array
 *           items:
 *             type: number
 *         userIds:
 *           type: array
 *           items:
 *             type: number
 *       required:
 *         - name
 *
 *     Audience:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         cities:
 *           type: array
 *           items:
 *             type: string
 *         completedSurveys:
 *           type: array
 *           items:
 *             type: number
 *         employmentStatuses:
 *           type: array
 *           items:
 *             type: string
 *         practiceSettings:
 *           type: array
 *           items:
 *             type: string
 *         provinces:
 *           type: array
 *           items:
 *             type: string
 *         specialtyIds:
 *           type: array
 *           items:
 *             type: number
 *         userIds:
 *           type: array
 *           items:
 *             type: number
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
async function createAudiences(req: AuthorizedRequest & { data: CreateAudiencePayload }): Promise<NextResponse> {
  const audienceServices = new AudienceService();
  await audienceServices.create(req.data);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `New Audience - ${req.data.name}`,
    entity: EntitiesName.Audiences,
  });

  return NextResponse.json({ status: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler: createAudiences,
  schema: createAudienceSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
