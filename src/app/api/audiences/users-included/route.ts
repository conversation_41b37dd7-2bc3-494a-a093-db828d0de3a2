import { AudienceService } from '@/backend/audiences/services/audience-service';
import { UsersIncludedPayload, usersIncludedSchema } from '@/backend/audiences/validations/add-update-audience';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/users-included:
 *   post:
 *     summary: Get count of users included in an audience
 *     description: Retrieve the count of users that match the provided audience criteria
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UsersIncludedPayload'
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *                   description: The count of users matching the provided criteria
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Authorization information is missing or invalid
 *
 * components:
 *   schemas:
 *     UsersIncludedPayload:
 *       type: object
 *       properties:
 *         cities:
 *           type: array
 *           items:
 *             type: string
 *         completedSurveys:
 *           type: array
 *           items:
 *             type: number
 *         employmentStatuses:
 *           type: array
 *           items:
 *             type: string
 *         practiceSettings:
 *           type: array
 *           items:
 *             type: string
 *         provinces:
 *           type: array
 *           items:
 *             type: string
 *         specialtyIds:
 *           type: array
 *           items:
 *             type: number
 *         userIds:
 *           type: array
 *           items:
 *             type: number
 */
async function usersIncluded(req: NextRequest & { data: UsersIncludedPayload }): Promise<NextResponse> {
  const audienceServices = new AudienceService();
  const [_, result] = await audienceServices.usersIncluded(req.data);
  return NextResponse.json({ data: { usersIncluded: result }, status: MessagesApi.SUCCESS }, { status: 200 });
}

export const POST = wrapper({
  handler: usersIncluded,
  schema: usersIncludedSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
