import { wrapper } from '@/backend/middlewares/wrapper';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}/surveys:
 *   get:
 *     summary: Get audience surveys
 *     description: Retrieve a list of surveys for an audience by its ID
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the audience
 *     responses:
 *       200:
 *         description: Successful response
 *       400:
 *         description: Invalid audience ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Audience not found
 *
 */
async function handler(_: Request, { params }: { params: { id: number } }) {
  const audienceId = params.id;

  const surveyServices = new SurveyService();
  const surveys = await surveyServices.surveysByAudienceId(audienceId);
  return NextResponse.json({ data: surveys, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
