import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { SurveyAudienceService } from '@/backend/surveys/services/survey-audience-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextParams } from '@/types/next-params';

/**
 * @swagger
 * /audiences/{id}:
 *   delete:
 *     summary: Delete deleteAudience
 *     description: Return messages
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The ID of the company Audiences
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: Name not available or Company Name exist
 */
async function deleteAudience(req: AuthorizedRequest, { params }: NextParams): Promise<Response> {
  const audienceId = params.id;

  const audienceServices = new AudienceService();
  const audience = await audienceServices.findAndValidate(audienceId);
  await audienceServices.delete(audienceId);

  const surveyAudienceServices = new SurveyAudienceService();
  await surveyAudienceServices.delByAudienceId(audienceId);
  // *** UserAudience need to check

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `Deleted Audience - ${audience.name}`,
    entity: EntitiesName.Audiences,
  });

  return new Response(null, { status: 204 });
}

export default wrapper({
  handler: deleteAudience,
  validatePathParams: true,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
