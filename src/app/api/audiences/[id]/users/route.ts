import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { User } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}/users:
 *   get:
 *     summary: Get users in audience
 *     description: Retrieve a list of users for an audience by its ID
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the audience
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *     responses:
 *       200:
 *         description: Successful response
 *       400:
 *         description: Invalid audience ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Audience not found
 *
 */
async function handler(req: NextRequest & { data: PaginationPayload<User> }, { params }: { params: { id: number } }) {
  const audienceId = params.id;

  const audienceService = new AudienceService();
  const users = await audienceService.getUsersInAudience(audienceId, req.data);

  return NextResponse.json({ data: users, status: 'Success' }, { status: 200 });
}

export const GET = wrapper({ handler, schema: paginationSchema, roles: [AdminRole.Admin, AdminRole.Editor] });
