import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserService } from '@/backend/users/services/user-service';
import {
  EditUsersInAudiencePayload,
  editUsersInAudienceSchema,
} from '@/backend/users/validations/edit-users-in-audience';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}/users/remove:
 *   post:
 *     summary: Remove users from an audience
 *     description: Removes users from a specified audience by audience ID.
 *     tags:
 *       - Audiences
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the audience
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       200:
 *         description: Users successfully removed from the audience
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 */
async function handler(
  req: NextRequest & { data: EditUsersInAudiencePayload },
  { params }: { params: { id: number } },
): Promise<NextResponse> {
  const audienceId = params.id;

  const userService = new UserService();
  await userService.removeUsersFromAudience(audienceId, req.data);

  return NextResponse.json({ status: 'Success' }, { status: 200 });
}

export const POST = wrapper({
  handler,
  schema: editUsersInAudienceSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
