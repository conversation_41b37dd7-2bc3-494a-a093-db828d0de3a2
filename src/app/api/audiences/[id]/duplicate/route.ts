import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}/duplicate:
 *   post:
 *     summary: Duplicate an audience
 *     description: Duplicate an audience by its ID
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the audience to duplicate
 *     responses:
 *       201:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Audience'
 *       400:
 *         description: Invalid audience ID
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Audience not found
 */
async function handler(req: AuthorizedRequest, { params }: NextParams) {
  const audienceId = params.id;

  const audienceServices = new AudienceService();
  const data = await audienceServices.duplicate(audienceId);

  const userActionService = new UserActionService();
  await userActionService.logAction({
    userId: req.user.id,
    description: `Duplicated Audience - ${data.name}`,
    entity: EntitiesName.Audiences,
  });
  return NextResponse.json(data, { status: 200 });
}

export const POST = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
