import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { getCsvHeaders } from '@/backend/shared/utils/export-csv';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}/surveys/export:
 *   get:
 *     summary: Export audience's surveys
 *     description: Export surveys of an audience by its ID
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the audience
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid audience ID or request body
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Audience not found
 */
async function handler(_: NextRequest, { params }: { params: { id: number } }) {
  const surveyServices = new SurveyService();
  const surveys = await surveyServices.surveysByAudienceId(params.id);
  if (!surveys.length) {
    return NextResponse.json({ status: MessagesApi.DATA_NOT_FOUND }, { status: 400 });
  }

  const audienceService = new AudienceService();
  const blob = await audienceService.exportAudienceSurveys(surveys);

  return new NextResponse(blob, { status: 200, headers: getCsvHeaders('audience-surveys') });
}

export const POST = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
