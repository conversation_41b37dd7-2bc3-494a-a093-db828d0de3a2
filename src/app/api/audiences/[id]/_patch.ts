import { AudienceService } from '@/backend/audiences/services/audience-service';
import { UpdateAudiencePayload, updateAudienceSchema } from '@/backend/audiences/validations/add-update-audience';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AuthorizedRequest } from '@/backend/shared/types/app';
import { compareEntities } from '@/backend/shared/utils/compare-entities';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}:
 *   patch:
 *     summary: Update an audience
 *     description: Update an audience's information by its ID
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the audience to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAudienceSchema'
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Audience'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Audience not found
 *
 * components:
 *   schemas:
 *     UpdateAudienceSchema:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *         cities:
 *           type: array
 *           items:
 *             type: string
 *         completedSurveys:
 *           type: array
 *           items:
 *             type: number
 *         employmentStatuses:
 *           type: array
 *           items:
 *             type: string
 *         practiceSettings:
 *           type: array
 *           items:
 *             type: string
 *         provinces:
 *           type: array
 *           items:
 *             type: string
 *         specialtyIds:
 *           type: array
 *           items:
 *             type: number
 *         userIds:
 *           type: array
 *           items:
 *             type: number
 *
 *     Audience:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         cities:
 *           type: array
 *           items:
 *             type: string
 *         completedSurveys:
 *           type: array
 *           items:
 *             type: number
 *         employmentStatuses:
 *           type: array
 *           items:
 *             type: string
 *         practiceSettings:
 *           type: array
 *           items:
 *             type: string
 *         provinces:
 *           type: array
 *           items:
 *             type: string
 *         specialtyIds:
 *           type: array
 *           items:
 *             type: number
 *         userIds:
 *           type: array
 *           items:
 *             type: number
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
async function editAudiences(
  req: AuthorizedRequest & { data: UpdateAudiencePayload },
  { params }: NextParams,
): Promise<NextResponse> {
  const audienceId = params.id;

  const audienceServices = new AudienceService();
  const audience = await audienceServices.findAndValidate(audienceId);
  const updatedAudience = await audienceServices.update(audienceId, req.data);

  const userActionService = new UserActionService();
  const isEqual = compareEntities(audience, updatedAudience);
  if (!isEqual) {
    await userActionService.logAction({
      userId: req.user.id,
      description: `Updated Audience - ${updatedAudience.name}`,
      entity: EntitiesName.Audiences,
    });
  }
  return NextResponse.json({ message: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler: editAudiences,
  schema: updateAudienceSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
