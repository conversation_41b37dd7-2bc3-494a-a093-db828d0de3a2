import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { getCsvHeaders } from '@/backend/shared/utils/export-csv';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}/exports:
 *   post:
 *     summary: Export audience users
 *     description: Export users of an audience by its ID
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the audience
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid audience ID or request body
 *       401:
 *         description: Authorization information is missing or invalid
 *       404:
 *         description: Audience not found
 */
async function handler(_: NextRequest, { params }: { params: { id: number } }) {
  const audienceService = new AudienceService();
  const blob = await audienceService.exportAudience(params.id);
  return new NextResponse(blob, { status: 200, headers: getCsvHeaders('audiences') });
}

export const POST = wrapper({ handler, roles: [AdminRole.Admin, AdminRole.Editor] });
