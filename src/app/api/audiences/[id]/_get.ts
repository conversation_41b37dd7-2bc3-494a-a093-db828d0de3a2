import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { NextParams } from '@/types/next-params';
import { NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences/{id}:
 *   get:
 *     summary: 'Retrieve Audience'
 *     description: 'This API endpoint retrieves the audience data based on the provided ID.'
 *     tags:
 *       - Audiences
 *     parameters:
 *       - in: 'path'
 *         name: 'id'
 *         required: true
 *         type: 'integer'
 *         description: 'The ID of the audience to be retrieved.'
 *     responses:
 *       200:
 *         description: 'Successful operation'
 *         schema:
 *           type: 'object'
 *           properties:
 *             data:
 *               type: 'object'
 *               description: 'The audience data.'
 *             status:
 *               type: 'string'
 *               description: 'The status of the operation.'
 *     security:
 *       - AdminRole: ['Admin', 'Editor']
 */
async function handler(_: Request, { params }: NextParams) {
  const audienceId = params.id;

  const audienceServices = new AudienceService();
  const audience = await audienceServices.findAndValidate(audienceId);
  return NextResponse.json({ data: audience, status: 'Success' }, { status: 200 });
}

const GET = wrapper({ handler, validatePathParams: true, roles: [AdminRole.Admin, AdminRole.Editor] });
export default GET;
