import { Audience } from '@/backend/audiences/entities/Audience';
import { AudienceService } from '@/backend/audiences/services/audience-service';
import { wrapper } from '@/backend/middlewares/wrapper';
import { MessagesApi } from '@/backend/shared/common/messages';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { PaginationPayload, paginationSchema } from '@/types/pagination';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /audiences:
 *   get:
 *     summary: Get listAudiences
 *     description: Retrieve a paginated list of audiences with optional filtering and sorting
 *     tags:
 *       - Audiences
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: The page number to retrieve
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *           description: The number of items to return per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: ['id', 'name', 'numberOfUsers', 'lastSurveyDate']
 *           description: The property to sort audiences by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *           description: The order to sort audiences by
 *     responses:
 *       200:
 *         description: Successful response
 *       401:
 *         description: Authorization information is missing or invalid
 *       400:
 *         description: invalid enum value
 */
async function listAudiences(req: NextRequest & { data: PaginationPayload<Audience> }): Promise<NextResponse> {
  const audienceServices = new AudienceService();
  const result = await audienceServices.listAudiences(req.data);
  return NextResponse.json({ data: result, status: MessagesApi.SUCCESS }, { status: 200 });
}

export default wrapper({
  handler: listAudiences,
  schema: paginationSchema,
  roles: [AdminRole.Admin, AdminRole.Editor],
});
