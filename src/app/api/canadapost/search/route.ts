import { NextRequest, NextResponse } from 'next/server';
import { searchAddressSchema, SearchPayload } from '@/backend/canadapost/validations/canadapost';
import { CanadaPostService } from '@/backend/canadapost/services';
import { wrapper } from '@/backend/middlewares/wrapper';

/**
 * @swagger
 * /canadapost/search:
 *   get:
 *     summary: Search for addresses
 *     description: Search for addresses using the Canada Post service
 *     tags:
 *       - CanadaPost
 *     parameters:
 *       - in: query
 *         name: SearchTerm
 *         schema:
 *           type: string
 *         description: The term to search for
 *       - in: query
 *         name: LastId
 *         schema:
 *           type: string
 *         description: The last ID from the previous search result, if any
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                  type: object
 *                  properties:
 *                    Items:
 *                      type: array
 *                      items:
 *                        $ref: '#/components/schemas/SearchResult'
 *                 status:
 *                   type: string
 *                   example: Success
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *
 * components:
 *   schemas:
 *     SearchResult:
 *       type: object
 *       properties:
 *         Cursor:
 *           type: number
 *           description: The cursor value for the search result
 *         Description:
 *           type: string
 *           description: The description of the search result
 *         Highlight:
 *           type: string
 *           description: The highlighted portion of the search result
 *         Id:
 *           type: string
 *           description: The unique identifier of the search result
 *         Next:
 *           type: string
 *           enum: [Retrieve, Find]
 *           description: The next action to perform for the search result
 *         Text:
 *           type: string
 *           description: The text of the search result
 */
async function handler(req: NextRequest & { data: SearchPayload }) {
  const canadaPostService = new CanadaPostService();
  const result = await canadaPostService.searchAddress(req.data);
  return NextResponse.json({ data: result, status: 'Success' });
}

export const GET = wrapper({ handler, schema: searchAddressSchema, roles: ['*'] });
