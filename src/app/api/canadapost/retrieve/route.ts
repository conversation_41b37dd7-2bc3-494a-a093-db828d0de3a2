import { NextRequest, NextResponse } from 'next/server';
import { retrieveAddressSchema, RetrievePayload } from '@/backend/canadapost/validations/canadapost';
import { CanadaPostService } from '@/backend/canadapost/services';
import { wrapper } from '@/backend/middlewares/wrapper';

/**
 * @swagger
 * /canadapost/retrieve:
 *   get:
 *     summary: Retrieve address details
 *     description: Retrieve detailed information about an address using the Canada Post service
 *     tags:
 *       - CanadaPost
 *     parameters:
 *       - in: query
 *         name: Id
 *         schema:
 *           type: string
 *         description: The unique identifier of the address to retrieve
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                    Items:
 *                      type: array
 *                      $ref: '#/components/schemas/LocationResult'
 *                 status:
 *                   type: string
 *                   example: Success
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *
 * components:
 *   schemas:
 *     LocationResult:
 *       type: object
 *       properties:
 *         Id:
 *           type: string
 *           description: The unique identifier of the location
 *         DomesticId:
 *           type: string
 *           description: The domestic identifier of the location
 *         Language:
 *           type: string
 *           description: The language of the location
 *         LanguageAlternatives:
 *           type: string
 *           description: Alternative languages of the location
 *         Department:
 *           type: string
 *           description: The department of the location
 *         Company:
 *           type: string
 *           description: The company name of the location
 *         SubBuilding:
 *           type: string
 *           description: The sub-building information of the location
 *         BuildingNumber:
 *           type: string
 *           description: The building number of the location
 *         BuildingName:
 *           type: string
 *           description: The building name of the location
 *         SecondaryStreet:
 *           type: string
 *           description: The secondary street information of the location
 *         Street:
 *           type: string
 *           description: The street information of the location
 *         Block:
 *           type: string
 *           description: The block information of the location
 *         Neighbourhood:
 *           type: string
 *           description: The neighborhood of the location
 *         District:
 *           type: string
 *           description: The district of the location
 *         City:
 *           type: string
 *           description: The city of the location
 *         Line1:
 *           type: string
 *           description: Line 1 of the location address
 *         Line2:
 *           type: string
 *           description: Line 2 of the location address
 *         Line3:
 *           type: string
 *           description: Line 3 of the location address
 *         Line4:
 *           type: string
 *           description: Line 4 of the location address
 *         Line5:
 *           type: string
 *           description: Line 5 of the location address
 *         AdminAreaName:
 *           type: string
 *           description: The administrative area name of the location
 *         AdminAreaCode:
 *           type: string
 *           description: The administrative area code of the location
 *         Province:
 *           type: string
 *           description: The province or state abbreviation of the location
 *         ProvinceName:
 *           type: string
 *           description: The province or state name of the location
 *         ProvinceCode:
 *           type: string
 *           description: The province or state code of the location
 *         PostalCode:
 *           type: string
 *           description: The postal code of the location
 *         CountryName:
 *           type: string
 *           description: The country name of the location
 *         CountryIso2:
 *           type: string
 *           description: The ISO 2-letter country code of the location
 *         CountryIso3:
 *           type: string
 *           description: The ISO 3-letter country code of the location
 *         CountryIsoNumber:
 *           type: number
 *           description: The ISO numeric country code of the location
 *         SortingNumber1:
 *           type: string
 *           description: Sorting number 1 of the location
 *         SortingNumber2:
 *           type: string
 *           description: Sorting number 2 of the location
 *         Barcode:
 *           type: string
 *           description: The barcode associated with the location
 *         POBoxNumber:
 *           type: string
 *           description: The PO Box number of the location
 *         Label:
 *           type: string
 *           description: The label information of the location
 *         Type:
 *           type: string
 *           description: The type of location
 *         DataLevel:
 *           type: string
 *           description: The data level of the location
 */
async function retrieveAddressHandler(req: NextRequest & { data: RetrievePayload }) {
  const canadaPostService = new CanadaPostService(true);
  const result = await canadaPostService.retrieveAddress(req.data);
  return NextResponse.json({ data: result, status: 'Success' });
}

export const GET = wrapper({ handler: retrieveAddressHandler, schema: retrieveAddressSchema, roles: ['*'] });
