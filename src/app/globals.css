@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #fff;
    --foreground: #101828;
    --card: #fff;
    --card-foreground: #101828;
    --popover: #fff;
    --popover-foreground: #101828;
    --primary: #7e55d8;
    --primary-brand: #6149c4;
    --primary-bold: #473399;
    --primary-foreground: #f9fafb;
    --secondary: #f3f4f6;
    --secondary-foreground: #111827;
    --muted: #f3f4f6;
    --muted-foreground: #667085;
    --accent: #f3f4f6;
    --accent-foreground: #111827;
    --destructive: #ef4444;
    --destructive-foreground: #000000;
    --border: #e5e7eb;
    --input: #d0d5dd;
    --placeholder: #adadad;
    --ring: #7e55d8;
    --radius: 0.5rem;

    --gray-scale-5: #667085;
    --gray-text: #344054;
    --error: #f04438;
    --error-light: #fda29b;
    --error-bold: #b42318;
  }

  .dark {
    --background: #101828;
    --foreground: #f9fafb;
    --card: #101828;
    --card-foreground: #f9fafb;
    --popover: #101828;
    --popover-foreground: #f9fafb;
    --primary: #7e55d8;
    --primary-brand: #6149c4;
    --primary-bold: #473399;
    --primary-foreground: #f9fafb;
    --secondary: #1f2937;
    --secondary-foreground: #f9fafb;
    --muted: #1f2937;
    --muted-foreground: #1f2937;
    --accent: #1f2937;
    --accent-foreground: #f9fafb;
    --destructive: #7f1d1d;
    --destructive-foreground: #f9fafb;
    --border: #1f2937;
    --input: #1f2937;
    --ring: #7e55d8;

    --gray-text: #344054;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

input[type='date']::-webkit-calendar-picker-indicator {
  display: none;
}

body.dialog-open {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

input[type='date'] {
  color: var(--placeholder);
}

input[type='date'][value]:not([value='']) {
  color: var(--foreground);
}

* {
  scrollbar-color: #eaecf0 var(--background);
  scrollbar-width: auto;
}

*::-webkit-scrollbar-track {
  border-radius: 8px;
}
