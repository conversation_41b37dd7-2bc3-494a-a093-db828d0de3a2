import { MessagesApi } from '@/backend/shared/common/messages';
import { Locale, SurveyStatus } from '@/backend/surveys/entities/Survey';
import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { z } from 'zod';

export const surveyQuestionType: [string, ...string[]] = Object.values(SurveyQuestionType) as [string, ...string[]];
export const TRANSLATION = z.object({
  locale: z.enum([Locale.FR]),
  title: z.coerce.string(),
  description: z.coerce.string(),
  subtitle: z.coerce.string(),
});

const ID_CREATE = z.number({ required_error: MessagesApi.ID_REQUIRED }).min(1, { message: MessagesApi.ID_REQUIRED });

export const ZOD_VALUE = {
  // CREATE
  ID_CREATE: ID_CREATE,
  NAME_CREATE: z.string({ required_error: MessagesApi.NAME_REQUIRED }).min(1, { message: MessagesApi.NAME_REQUIRED }),
  REFERRAL_VALUE_CREATE: z.coerce
    .number({ required_error: MessagesApi.VALUE_REQUIRED })
    .nonnegative(MessagesApi.VALUE_NONE_NEGATIVE),
  URL: z.string({ required_error: MessagesApi.URL_IMG_VALID }),
  // UPDATE
  ID_UPDATE: z.number().min(1, { message: MessagesApi.ID_REQUIRED }).optional(),
  NAME_UPDATE: z.string().min(1, { message: MessagesApi.NAME_NOT_EMPTY }).optional(),
  REFERRAL_VALUE_UPDATE: z.coerce.number().nonnegative(MessagesApi.VALUE_NONE_NEGATIVE).optional(),
  // VALUE
  ID_FOR_ALL: z.number({ required_error: MessagesApi.ID_REQUIRED }).min(-1, { message: MessagesApi.ID_REQUIRED }),
  BOOLEAN_NOT_NULL: z.coerce.boolean(),
  NUM_CAN_NULL: z.coerce.number().optional(),
  STR_CAN_NULL: z.coerce.string().optional(),
  BOOLEAN_CAN_NULL: z.coerce.boolean().optional(),
  DATE_CAN_NULL: z.coerce.date().optional(),
  STATUS_SURVEY: z.enum([SurveyStatus.Draft, SurveyStatus.Active]),
  LOCALE: z.enum([Locale.EN, Locale.FR]),
  URL_CAN_NULL: z.string().optional(),
  QUESTION_TYPES: z.enum(surveyQuestionType),
  TRANSLATION: TRANSLATION,
  QUESTION_OPTION: z
    .array(
      z.object({
        title: z.coerce.string(),
        surveyQuestionOptionTranslation: TRANSLATION,
      }),
    )
    .nonempty({ message: MessagesApi.QUESTION_LIST_VALID })
    .optional(),
  QUESTION_OPTION_UPDATE: z
    .array(
      z.object({
        id: ID_CREATE,
        title: z.coerce.string(),
        surveyQuestionOptionTranslation: TRANSLATION.merge(z.object({ id: ID_CREATE })),
      }),
    )
    .optional(),
};
