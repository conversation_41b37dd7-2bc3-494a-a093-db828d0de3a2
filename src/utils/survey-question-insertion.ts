import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { CreateSurveyQuestionPayload } from '@/components/sections/surveys/validations';

export interface InsertQuestionOptions {
  currentQuestions: CreateSurveyQuestionPayload[];
  newQuestionData: CreateSurveyQuestionPayload;
  questionType: string;
}

/**
 * Ensures survey questions are properly sorted with screening questions at the top
 * and updates their order values accordingly
 */
export const sortSurveyQuestions = (questions: CreateSurveyQuestionPayload[]): CreateSurveyQuestionPayload[] => {
  const screeningQuestions = questions.filter(q => q.questionType === SurveyQuestionType.Screening);
  const nonScreeningQuestions = questions.filter(q => q.questionType !== SurveyQuestionType.Screening);

  // Sort screening questions by their current order
  screeningQuestions.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

  // Sort non-screening questions by their current order
  nonScreeningQuestions.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

  // Combine and update order values
  const sortedQuestions = [...screeningQuestions, ...nonScreeningQuestions];

  return sortedQuestions.map((question, index) => ({
    ...question,
    order: index + 1,
  }));
};

export const insertSurveyQuestion = ({
  currentQuestions,
  newQuestionData,
  questionType,
}: InsertQuestionOptions): CreateSurveyQuestionPayload[] => {
  let insertIndex: number;
  let newOrder: number;

  if (questionType === SurveyQuestionType.Screening) {
    // For screening questions, find the position after the last existing screening question
    let lastScreeningIndex = -1;

    for (let i = 0; i < currentQuestions.length; i++) {
      if (currentQuestions[i].questionType === SurveyQuestionType.Screening) {
        lastScreeningIndex = i;
      }
    }

    if (lastScreeningIndex >= 0 && currentQuestions[lastScreeningIndex]) {
      // Insert after the last screening question
      insertIndex = lastScreeningIndex + 1;
      newOrder = (currentQuestions[lastScreeningIndex].order ?? 0) + 1;
    } else {
      // No existing screening questions, insert at the top
      insertIndex = 0;
      newOrder = 1;
    }
  } else {
    // For non-screening questions, append to the end (default behavior)
    insertIndex = currentQuestions.length;
    newOrder = !currentQuestions?.length ? 1 : currentQuestions.length + 1;
  }

  const newQuestion = { ...newQuestionData, id: 'FAKE_' + self.crypto.randomUUID(), order: newOrder };

  // Create the updated questions array with the new question inserted at the correct position
  const updatedQuestions = [
    ...currentQuestions.slice(0, insertIndex),
    newQuestion,
    ...currentQuestions.slice(insertIndex),
  ];

  // Update order values for questions that come after the insertion point
  if (insertIndex < currentQuestions.length) {
    for (let i = insertIndex + 1; i < updatedQuestions.length; i++) {
      updatedQuestions[i] = {
        ...updatedQuestions[i],
        order: (updatedQuestions[i]?.order ?? 0) + 1,
      };
    }
  }

  return updatedQuestions;
};
