import { EnsureError } from './error';

export const ensureNumber = (value: unknown): [value?: number, error?: EnsureError] => {
  const number = Number(value);
  if (Number.isNaN(number)) {
    return [undefined, new EnsureError(`Expected a number, but got a ${typeof value} instead.`)];
  }
  return [number];
};

export const formatCurrency = (amount: number, currency = 'USD', maximumFractionDigits: number = 0) => {
  const options: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currency,
    maximumFractionDigits: maximumFractionDigits,
  };

  return new Intl.NumberFormat('en-US', options).format(amount ?? 0);
};
