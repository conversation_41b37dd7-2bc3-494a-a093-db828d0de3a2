import { UserType } from '@/backend/users/entities/User';
import { endOfDay, endOfYear, startOfDay, startOfMonth, startOfYear, subDays } from 'date-fns';

export const DATE_FORMAT = 'LLL dd, y';

export const TIME_RANGE_OPTIONS = [
  { label: 'Today', getValue: () => ({ from: startOfDay(new Date()), to: endOfDay(new Date()) }) },
  {
    label: 'Yesterday',
    getValue: () => ({ from: subDays(startOfDay(new Date()), 1), to: subDays(endOfDay(new Date()), 1) }),
  },
  { label: 'This week', getValue: () => ({ from: subDays(startOfDay(new Date()), 7), to: endOfDay(new Date()) }) },
  {
    label: 'Last week',
    getValue: () => ({ from: subDays(startOfDay(new Date()), 14), to: subDays(startOfDay(new Date()), 7) }),
  },
  {
    label: 'This month',
    getValue: () => {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1; // Adding 1 because getMonth() returns 0-11
      return {
        from: startOfMonth(new Date(currentYear, currentMonth - 1, 1)),
        to: endOfDay(new Date()),
      };
    },
  },
  {
    label: 'Last month',
    getValue: () => ({ from: subDays(startOfDay(new Date()), 60), to: subDays(endOfDay(new Date()), 30) }),
  },
  {
    label: 'This year',
    getValue: () => {
      const currentYear = new Date().getFullYear();
      return {
        from: startOfYear(new Date(currentYear, 0, 1)),
        to: endOfDay(new Date()),
      };
    },
  },
  {
    label: 'Last year',
    getValue: () => {
      const lastYear = new Date().getFullYear() - 1;
      return {
        from: startOfYear(new Date(lastYear, 0, 1)),
        to: endOfYear(new Date(lastYear, 0, 1)),
      };
    },
  },
  { label: 'All time', getValue: () => ({ from: subDays(startOfDay(new Date()), 3650), to: endOfDay(new Date()) }) },
];

export const USER_TYPE_ORDERED_LIST = [
  UserType.HCPUser,
  UserType.HCPUserImported,
  UserType.Client,
  UserType.ClientImported,
  UserType.Internal,
  UserType.Unverified,
  UserType.Denied,
];
