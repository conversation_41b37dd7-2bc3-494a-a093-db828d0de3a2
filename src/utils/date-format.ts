import { format } from 'date-fns';
import { toZonedTime, format as formatWithTimezone } from 'date-fns-tz';

export const DATE_FORMATS = {
  ISO_DATE_TIME: 'yyyy-MM-dd hh:mm',
  ISO_DATE: 'yyyy-MM-dd',
  US_DATE: 'MM/dd/yyyy',
  TIME_12_HOUR: 'hh:mm aa',
  ISO_DATE_TIME_24H: 'yyyy-MM-dd HH:mm',
};

export function formatDate(date: Date | string, template = DATE_FORMATS.US_DATE) {
  return format(date, template);
}

export function formatTime(date: Date | string, template = DATE_FORMATS.TIME_12_HOUR) {
  return format(date, template);
}

export function convertToEstTimezone(date: Date | string, template = DATE_FORMATS.US_DATE) {
  const estDate = toZonedTime(date, 'America/New_York');
  return formatWithTimezone(estDate, template, { timeZone: 'America/New_York' });
}
