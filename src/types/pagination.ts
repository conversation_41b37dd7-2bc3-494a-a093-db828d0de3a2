import { ObjectLiteral, SelectQueryBuilder } from 'typeorm';
import { z } from 'zod';

export const paginationSchema = z.object({
  page: z
    .union([z.number(), z.string(), z.null(), z.undefined()])
    .transform(val => {
      const num = Number(val);
      return isNaN(num) || num < 1 ? 1 : num;
    })
    .default(1)
    .optional(),
  pageSize: z
    .union([z.number(), z.string(), z.null(), z.undefined()])
    .transform(val => {
      const num = Number(val);
      return isNaN(num) || num < 1 || num > 100 ? 10 : num;
    })
    .default(10)
    .optional(),
  search: z.string().trim().optional(),
  sortOrder: z
    .string()
    .transform(val => (val === 'DESC' ? 'DESC' : 'ASC'))
    .default('ASC')
    .optional(),
});

export const withPagination = async <TData extends ObjectLiteral>(
  queryBuilder: SelectQueryBuilder<TData>,
  page: number = 1,
  pageSize: number = 10,
): Promise<PaginationResponseData<TData>> => {
  const skip = (page - 1) * pageSize;
  const [result, total] = await queryBuilder.skip(skip).take(pageSize).getManyAndCount();
  const totalPages = Math.ceil(total / pageSize);
  return { data: result, total, totalPages };
};

export type PaginationPayload<TData> = z.input<typeof paginationSchema> & {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: keyof TData;
  sortOrder?: 'ASC' | 'DESC';
} & Record<string, unknown>;

export type PaginationResponseData<TData> = { data: TData[]; total: number; totalPages: number } & Record<
  string,
  unknown
>;

export type PaginationResponse<TData> = {
  data: PaginationResponseData<TData>;
  status: string;
};
