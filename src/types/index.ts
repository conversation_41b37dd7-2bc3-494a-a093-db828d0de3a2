export enum Role {
  ADMIN = 'admin',
  EDITOR = 'editor',
}

export type DataTypes<T> = T;

export type Payloads<T> = {
  [K in keyof T]: DataTypes<T[K]>;
};

export type SelectOptions = {
  label: string;
  value: string | number;
};

export type QueryParams = {
  page: number;
  pageSize?: number;
};

export type RequireIdOnly<T extends { id: unknown }> = {
  id: T['id'];
} & Partial<Omit<T, 'id'>>;
