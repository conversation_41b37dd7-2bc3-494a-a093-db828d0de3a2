'use client';
import { ColumnDef } from '@tanstack/table-core';
import React from 'react';
import { SupportRequest, SupportRequestStatus } from '@/backend/support/entities/SupportRequest';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn } from '@/lib/utils';
import { Header } from '@/components/ui/table/header';
import { formatDate, formatTime } from '@/utils/date-format';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { ExternalLink } from 'lucide-react';
import VerifiedBadge from '@/components/common/badges/verified';
import UnverifiedBadge from '@/components/common/badges/unverified';

type ColumnsProps = {
  isLoading?: boolean;
};

function generateColumns({ isLoading }: ColumnsProps) {
  const columns: ColumnDef<SupportRequest>[] = [
    {
      accessorKey: 'email',
      header: ({ column }) => <Header column={column}>Email</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <span className="text-foreground font-medium">{row.original.email}</span>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'description',
      header: ({ column }) => <Header column={column}>Details</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <span className="text-muted-foreground">{row.original.description}</span>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => <Header column={column}>Created Date</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <div className="flex flex-col ">
            <span className="text-foreground">
              {row.original.createdAt ? formatDate(row.original.createdAt) : 'N/A'}
            </span>
            <span className="text-muted-foreground">
              {row.original.createdAt ? formatTime(row.original.createdAt) : 'N/A'}
            </span>
          </div>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'status',
      header: ({ column }) => <Header column={column}>Status</Header>,
      cell: ({ row }) => {
        const status = row.original.status;

        return (
          <SkeletonCell isLoading={isLoading}>
            {status === SupportRequestStatus.Open ? (
              <VerifiedBadge>Open</VerifiedBadge>
            ) : (
              <UnverifiedBadge>Closed</UnverifiedBadge>
            )}
          </SkeletonCell>
        );
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 overflow-hidden' : '')}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <DotsHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="p-0">
              <a href={row.original.hubspotTicketUrl ?? '#'} rel="noreferrer" target="_blank">
                <DropdownMenuItem className="text-foreground font-medium cursor-pointer rounded-none flex items-center space-x-2 py-2.5 px-3.5">
                  <span>Open in Hubspot</span>
                  <ExternalLink className="w-5 h-5" />
                </DropdownMenuItem>
              </a>
            </DropdownMenuContent>
          </DropdownMenu>
        </SkeletonCell>
      ),
    },
  ];

  return columns;
}

export { generateColumns };
