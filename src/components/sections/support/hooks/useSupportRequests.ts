import { usePagination } from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useSortQuery } from '@/hooks/useSortQuery';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { SupportRequest } from '@/backend/support/entities/SupportRequest';

export function useSupportRequests() {
  const { page, pageSize } = usePagination();
  const { search } = useSearchQuery();
  const { sortBy, sortOrder } = useSortQuery({ sortBy: 'createdAt', sortOrder: 'DESC' });

  const {
    data: supportRequests,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['supportRequests', page, pageSize, search, sortBy, sortOrder],
    queryFn: async () =>
      api.supports.getSupports({
        page,
        pageSize,
        search: search ?? '',
        sortBy: sortBy as keyof SupportRequest,
        sortOrder: sortOrder === 'ASC' ? 'ASC' : 'DESC',
      }),
    placeholderData: previousData => previousData,
  });

  return { supportRequests, isLoadingSupportRequests: isLoading, isError };
}
