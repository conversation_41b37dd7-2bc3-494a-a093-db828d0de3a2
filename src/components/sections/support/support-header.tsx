'use client';

import SearchInput from '@/components/common/search-input';
import { Badge } from '@/components/ui/badge';

const SupportHeader = ({ supportRequestsCount }: { supportRequestsCount: number }) => {
  return (
    <div className="flex flex-col lg:flex-row justify-between px-4 lg:px-6 mb-[5px]">
      <div className="flex items-center font-medium">
        <div>Support Tickets</div>
        <Badge className="text-primary text-xs bg-[#F9F5FF] ml-2 px-2 py-0.5">Count: {supportRequestsCount}</Badge>
      </div>
      <div className="lg:max-w-25 w-full my-4 lg:my-0">
        <SearchInput />
      </div>
    </div>
  );
};

export default SupportHeader;
