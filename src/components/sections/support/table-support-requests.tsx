'use client';
import React, { useMemo } from 'react';
import { generateColumns } from './columns';
import { Table } from '@/components/ui/table';
import CustomPagination from '@/components/common/custom-pagination';
import { useSupportRequests } from '@/components/sections/support/hooks/useSupportRequests';
import SupportHeader from '@/components/sections/support/support-header';
import { cn } from '@/lib/utils';

const TableSupportRequests = () => {
  const { supportRequests, isLoadingSupportRequests, isError } = useSupportRequests();

  const columns = useMemo(
    () =>
      generateColumns({
        isLoading: isLoadingSupportRequests,
      }),
    [isLoadingSupportRequests],
  );

  return (
    <>
      <SupportHeader supportRequestsCount={supportRequests?.data?.total ?? 0} />
      <Table
        columns={columns}
        data={
          supportRequests?.data?.data ? supportRequests.data.data : isLoadingSupportRequests ? Array(10).fill({}) : []
        }
        containerClassName={cn(!isLoadingSupportRequests && !isError && 'peer is-done')}
      />
      <CustomPagination totalPages={supportRequests?.data?.totalPages ?? 10} />
    </>
  );
};
export default TableSupportRequests;
