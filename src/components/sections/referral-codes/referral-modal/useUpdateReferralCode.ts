import toast from 'react-hot-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { UpdateReferralCodePayload } from '@/backend/referral-codes/validations/referral-code';

type UseUpdateReferralCodeParams = UpdateReferralCodePayload & {
  id: number;
  isToggleStatus?: boolean;
};

export function useUpdateReferralCode() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isSuccess, error } = useMutation({
    mutationFn: async (data: UseUpdateReferralCodeParams) =>
      api.publicReferralCodes.updatePublicReferralCode(data.id, data),
    onSuccess: async (_, variables) => {
      if (variables.isToggleStatus) {
        toast.success('Status updated successfully');
      } else toast.success('Code updated successfully');
      await queryClient.invalidateQueries({ queryKey: ['publicReferralCodes'] });
    },
  });

  return {
    updateReferralCode: mutateAsync,
    isUpdatingReferralCode: isPending,
    isSuccess,
    error,
  };
}
