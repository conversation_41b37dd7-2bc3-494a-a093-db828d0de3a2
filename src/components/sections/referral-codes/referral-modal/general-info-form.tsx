'use client';

import * as z from 'zod';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { Checkbox, Input, InputDate, Label, Textarea } from '@/components/ui/form';
import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { nameShape } from '@/backend/shared/validations/name';
import InputMoney, { inputMoneySchema } from '@/components/ui/form/input-money';

export const referralCodeSchema = z.object({
  value: inputMoneySchema,
  code: z
    .string({
      required_error: 'Code is required',
    })
    .regex(new RegExp('^[A-Z0-9]+$'), 'Code must be alphanumeric')
    .min(1, 'Code is required')
    .max(10, 'Code must contain at most 10 characters'),
  name: nameShape,
  description: z.string().optional().nullable(),
  unlimitedMaxUses: z.boolean().optional().nullable(),
  unlimitedDate: z.boolean().optional().nullable(),
  expiryDate: z.string().nullable().optional(),
  maxUses: z.coerce.number().optional().nullable(),
});

export type ReferralCodeFormData = z.infer<typeof referralCodeSchema>;

const itemClass = 'basis-full md:basis-[calc(50%-12px)] flex-shrink';
const labelClass = 'mb-1.5 h-5 font-medium inline-block text-[#344054]';

const GeneralInfoForm = ({ defaultValues }: { defaultValues?: PublicReferralCode }) => {
  const { setValue, clearErrors } = useFormContext();
  const [unlimitedMaxUses, setUnlimitedMaxUses] = useState(Boolean(!defaultValues ? false : !defaultValues.maxUses));
  const [unlimitedDate, setUnlimitedDate] = useState(Boolean(!defaultValues ? false : !defaultValues.expiryDate));

  return (
    <div className="flex flex-col gap-6 flex-1">
      <div className="flex flex-wrap gap-6 mt-4">
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="code" className={labelClass}>
              Code *
            </Label>
          </div>
          <Input
            type="code"
            name="code"
            id="code"
            placeholder="Example: CODE001"
            mask={value => value.replaceAll(' ', '').toUpperCase()}
          />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="value" className={labelClass}>
              Value *
            </Label>
          </div>
          <InputMoney name="value" id="value" placeholder="Example: $25" />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="name" className={labelClass}>
              Name *
            </Label>
          </div>
          <Input type="text" name="name" id="name" placeholder="Example Code" />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="description" className={labelClass}>
              Description
            </Label>
          </div>
          <Textarea
            name="description"
            id="description"
            rows={5}
            placeholder="Write a description to outline the purpose of this code"
          />
        </div>
        <div className="font-bold text-base mb-1 w-full">Availability Settings</div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5 flex justify-between">
            <Label htmlFor="max_use" className={labelClass}>
              Max Uses
            </Label>
            <div className={'flex'}>
              <Checkbox
                className="border-[#D0D5DD]"
                name="unlimitedMaxUses"
                id="unlimited_maxUses"
                checked={unlimitedMaxUses}
                onCheckedChange={() => {
                  setUnlimitedMaxUses(prev => !prev);
                  setValue('maxUses', '');
                  clearErrors('maxUses');
                }}
              />{' '}
              <label className="text-sm ml-2 leading-tight" htmlFor="unlimited_maxUses">
                Unlimited
              </label>
            </div>
          </div>
          <Input type="number" name="maxUses" id="maxUses" placeholder="Example: 300" disabled={unlimitedMaxUses} />
        </div>
        <div className={itemClass}>
          <div>
            <div className="h-5 max-h-5 mb-1.5 flex justify-between">
              <Label htmlFor="expiryDate" className={labelClass}>
                Expiry Date
              </Label>
              <div className={'flex'}>
                <Checkbox
                  className="border-[#D0D5DD]"
                  name="unlimitedDate"
                  id="unlimited_expiryDate"
                  checked={unlimitedDate}
                  onCheckedChange={() => {
                    setUnlimitedDate(prev => !prev);
                    setValue('expiryDate', undefined);
                    clearErrors('expiryDate');
                  }}
                />{' '}
                <label className="text-sm ml-2 leading-tight" htmlFor="unlimited_expiryDate">
                  Unlimited
                </label>
              </div>
            </div>
            <InputDate name="expiryDate" id="expiryDate" disabled={unlimitedDate} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralInfoForm;
