'use client';

import React, { useEffect, useRef } from 'react';
import { Cross2Icon } from '@radix-ui/react-icons';

import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { formatDate } from '@/utils/date-format';
import { Form } from '@/components/ui/form';
import { FormActions } from '@/components/ui/form/form';
import { Button } from '@/components/ui/button';
import { useUpdateReferralCode } from './useUpdateReferralCode';
import GeneralInfoForm, { ReferralCodeFormData, referralCodeSchema } from './general-info-form';
import { useReferralCodeErrors } from './useReferralCodeErrors';

type EditReferralModalProps = {
  onClose: () => void;
  defaultValues: PublicReferralCode;
};

const EditReferralModal: React.FC<EditReferralModalProps> = ({ onClose, defaultValues }: EditReferralModalProps) => {
  const { updateReferralCode, isUpdatingReferralCode, isSuccess, error } = useUpdateReferralCode();
  const ref = useRef<FormActions<typeof referralCodeSchema> | null>(null);
  useReferralCodeErrors(ref, error);

  useEffect(() => {
    if (isSuccess) {
      onClose();
    }
  }, [onClose, isSuccess]);

  async function onSubmit(data: ReferralCodeFormData) {
    let isValid = true;
    if (!ref.current) return;

    if (!data.unlimitedMaxUses && !data.maxUses) {
      ref.current.formHandler.setError('maxUses', { message: 'Max uses must be greater than 0' });
      isValid = false;
    }

    if (!data.unlimitedDate && !data.expiryDate) {
      ref.current.formHandler.setError('expiryDate', { message: 'Expiry date is required' });
      isValid = false;
    }

    if (!isValid) return;
    await updateReferralCode({
      ...data,
      id: defaultValues.id,
      expiryDate: data.expiryDate ? new Date(data.expiryDate) : null,
      maxUses: data.maxUses ? data.maxUses : null,
    });
  }

  // const defaultValues =

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="flex lg:min-w-[60%] lg:!w-[728px] 2xl:min-w-[auto] p-0">
        <div className="flex flex-col lg:flex-row">
          <div className="flex flex-col p-8">
            <div className="font-semibold text-2xl">Edit Code</div>
            <div className="font-bold text-base mt-10">General Info</div>
            <Form
              schema={referralCodeSchema}
              onSubmit={onSubmit}
              formRef={ref}
              defaultValues={
                {
                  ...defaultValues,
                  value: defaultValues.value ? `$${defaultValues.value}` : '$0',
                  expiryDate: defaultValues.expiryDate ? formatDate(defaultValues.expiryDate, 'yyyy-MM-dd') : '',
                  maxUses: defaultValues.maxUses ? defaultValues.maxUses : undefined,
                  unlimitedMaxUses: Boolean(!defaultValues.maxUses),
                  unlimitedDate: Boolean(!defaultValues.expiryDate),
                } as unknown as ReferralCodeFormData
              }
            >
              <GeneralInfoForm defaultValues={defaultValues} />
              <div className="mt-10">
                <div className="flex justify-end gap-6">
                  <Button
                    type="button"
                    variant="secondary"
                    className="font-semibold"
                    disabled={isUpdatingReferralCode}
                    onClick={onClose}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="font-semibold"
                    disabled={isUpdatingReferralCode}
                    isLoading={isUpdatingReferralCode}
                  >
                    Save Changes
                  </Button>
                </div>
              </div>
            </Form>
          </div>
        </div>
        <DialogClose className="absolute right-6 top-6 rounded-lg" aria-label="Close" onClick={onClose}>
          <Cross2Icon className="w-6 h-6" />
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default EditReferralModal;
