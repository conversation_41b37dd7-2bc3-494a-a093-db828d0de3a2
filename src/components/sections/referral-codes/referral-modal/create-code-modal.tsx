'use client';

import React, { useEffect, useRef } from 'react';
import { Cross2Icon } from '@radix-ui/react-icons';

import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { FormActions } from '@/components/ui/form/form';
import { useCreateReferralCode } from './useCreateReferralCode';
import GeneralInfoForm, { ReferralCodeFormData, referralCodeSchema } from './general-info-form';
import { useReferralCodeErrors } from './useReferralCodeErrors';

type CreateReferralModalProps = {
  onClose: () => void;
};

const CreateReferralModal: React.FC<CreateReferralModalProps> = ({ onClose }: CreateReferralModalProps) => {
  const { createReferralCode, isCreatingReferralCode, isSuccess, error } = useCreateReferralCode();
  const ref = useRef<FormActions<typeof referralCodeSchema> | null>(null);
  useReferralCodeErrors(ref, error);

  useEffect(() => {
    if (isSuccess) {
      onClose();
    }
  }, [onClose, isSuccess]);

  async function onSubmit(data: ReferralCodeFormData) {
    if (!ref.current) return;
    let isValid = true;
    if (!data.unlimitedMaxUses && !data.maxUses) {
      ref.current.formHandler.setError('maxUses', { message: 'Max uses must be greater than 0' });
      isValid = false;
    }

    if (!data.unlimitedDate && !data.expiryDate) {
      ref.current.formHandler.setError('expiryDate', { message: 'Expiry date is required' });
      isValid = false;
    }

    if (!isValid) return;

    await createReferralCode({
      ...data,
      expiryDate: data.expiryDate ? new Date(data.expiryDate) : null,
      maxUses: data.maxUses ? data.maxUses : null,
    });
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="flex lg:min-w-[60%] lg:!w-[728px] 2xl:min-w-[auto] p-0">
        <div className="flex flex-col lg:flex-row">
          <div className="flex flex-col p-8">
            <div className="font-semibold text-2xl">Add Code</div>
            <div className="font-bold text-base mt-10">General Info</div>
            <Form schema={referralCodeSchema} onSubmit={onSubmit} formRef={ref}>
              <GeneralInfoForm />
              <div className="mt-10">
                <div className="flex justify-end gap-6">
                  <Button
                    type="button"
                    className="font-semibold"
                    variant="secondary"
                    disabled={isCreatingReferralCode}
                    onClick={onClose}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="font-semibold"
                    disabled={isCreatingReferralCode}
                    isLoading={isCreatingReferralCode}
                  >
                    Create Code
                  </Button>
                </div>
              </div>
            </Form>
          </div>
        </div>
        <DialogClose className="absolute right-6 top-6 rounded-lg" aria-label="Close" onClick={onClose}>
          <Cross2Icon className="w-6 h-6" />
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default CreateReferralModal;
