import { RefObject, useEffect } from 'react';
import { MessagesApi } from '@/backend/shared/common/messages';
import toast from 'react-hot-toast';
import { referralCodeSchema } from '@/components/sections/referral-codes/referral-modal/general-info-form';
import { FormActions } from '@/components/ui/form/form';

export function useReferralCodeErrors(ref: RefObject<FormActions<typeof referralCodeSchema>>, error?: Error | null) {
  useEffect(() => {
    if (!error || !ref.current) return;
    const msg = error.message;
    switch (msg) {
      case MessagesApi.REFERRAL_CODE_EXISTS:
      case MessagesApi.REFERRAL_CODE_VALID:
        ref.current.formHandler.setError('code', { message: msg });
        break;
      case MessagesApi.REFERRAL_MAX_USES_TOO_LOW:
      case MessagesApi.REFERRAL_CAN_NOT_BE_DELETED:
        ref.current.formHandler.setError('code', { message: msg });
        break;
      default:
        toast.error(msg);
    }
  }, [ref, error]);

  return;
}
