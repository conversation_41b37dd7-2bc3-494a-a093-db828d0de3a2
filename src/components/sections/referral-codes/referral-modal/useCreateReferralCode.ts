import toast from 'react-hot-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CreateReferralCodePayload } from '@/backend/referral-codes/validations/referral-code';
import { api } from '@/lib/http';

export function useCreateReferralCode() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isSuccess, error } = useMutation({
    mutationFn: (data: CreateReferralCodePayload) => api.publicReferralCodes.addPublicReferralCode(data),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['publicReferralCodes'] });
      toast.success('Referral code created successfully');
    },
  });

  return { createReferralCode: mutateAsync, isCreatingReferralCode: isPending, isSuccess, error };
}
