'use client';

import SearchInput from '@/components/common/search-input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

type HeaderProps = {
  count: number;
  setShowModal: (visible: boolean) => void;
};

const ReferralHeader = ({ count, setShowModal }: HeaderProps) => {
  return (
    <div className="flex flex-col lg:flex-row justify-between px-4 lg:px-6 mb-[5px]">
      <div className="font-medium flex items-center">
        <div>Referral Codes</div>
        <Badge className="text-primary bg-[#F9F5FF] ml-2 h-fit px-2 py-0.5 text-xs">Count: {count}</Badge>
      </div>
      <div className="flex justify-end flex-1 items-center my-4 lg:my-0 ml-0 lg:ml-6">
        <div className="max-w-25 w-full">
          <SearchInput />
        </div>
        <div className="flex">
          <Button className="h-10 ml-6 px-2.5 md:px-4 border-none bg-primary-brand" onClick={() => setShowModal(true)}>
            <Plus />
            <div className="ml-2 hidden md:block">Add Code</div>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReferralHeader;
