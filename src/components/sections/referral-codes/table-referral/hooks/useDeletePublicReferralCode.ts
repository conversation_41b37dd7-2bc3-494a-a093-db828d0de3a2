import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useDeletePublicReferralCode() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isError, error } = useMutation({
    mutationFn: (id: number) => api.publicReferralCodes.deletePublicReferralCode(id),
    onSuccess: async () => {
      toast.success('Code deleted successfully');
      await queryClient.invalidateQueries({ queryKey: ['publicReferralCodes'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    deletePublicReferralCode: mutateAsync,
    isDeletingPublicReferralCode: isPending,
    isDeletePublicReferralCodeError: isError,
    deletePublicReferralCodeError: error,
  };
}
