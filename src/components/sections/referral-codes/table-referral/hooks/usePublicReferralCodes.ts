import { usePagination } from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useSortQuery } from '@/hooks/useSortQuery';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { useEffect } from 'react';
import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';

export function usePublicReferralCodes() {
  const { page, pageSize, previousPage } = usePagination();
  const { search } = useSearchQuery();
  const { sortBy, sortOrder } = useSortQuery();

  const { data, isPending, isError } = useQuery({
    queryKey: ['publicReferralCodes', page, pageSize, search, sortBy, sortOrder],
    queryFn: async () =>
      api.publicReferralCodes.getPublicReferralCodes({
        page,
        pageSize,
        search: search ?? '',
        sortBy: sortBy as keyof PublicReferralCode,
        sortOrder: sortOrder === 'ASC' ? 'ASC' : 'DESC',
      }),
    placeholderData: previousData => previousData,
  });

  useEffect(() => {
    if (!data?.data) return;
    if (data.data.totalPages < page && page > 1) {
      previousPage();
    }
  }, [page, previousPage, data]);

  return { publicReferralCodes: data, isLoadingPublicReferralCodes: isPending, isError };
}
