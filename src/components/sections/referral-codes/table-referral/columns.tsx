'use client';
import UnverifiedBadge from '@/components/common/badges/unverified';
import VerifiedBadge from '@/components/common/badges/verified';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import { ColumnDef } from '@tanstack/table-core';
import { formatCurrency } from '@/utils/number';
import { PublicReferralCode, PublicReferralCodeStatus } from '@/backend/referral-codes/entities/PublicReferralCode';
import { ActionsCell } from '@/components/ui/table/action-cell';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import React from 'react';

type ColumnsProps = {
  onDelete: (id: number) => void;
  isDeleting?: boolean;
  onToggleStatus: (id: number, status: PublicReferralCodeStatus) => void;
  isLoading?: boolean;
  selectRowEdit: (row: PublicReferralCode) => void;
};

function generateColumns({ onDelete, isDeleting, isLoading, onToggleStatus, selectRowEdit }: ColumnsProps) {
  const columns: ColumnDef<PublicReferralCode>[] = [
    {
      accessorKey: 'code',
      header: ({ column }) => <Header column={column}>Code</Header>,
      cell: ({ row }) => {
        const code = row.getValue('code') as number;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span>{code}</span>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'name',
      header: ({ column }) => <Header column={column}>Name</Header>,
      cell: ({ row }) => {
        const name = row.getValue('name') as string;

        return (
          <SkeletonCell isLoading={isLoading}>
            <div className="flex flex-col">
              <span className="whitespace-nowrap">{name}</span>
            </div>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'description',
      header: ({ column }) => <Header column={column}>Description</Header>,
      cell: ({ row }) => {
        const description = row.getValue('description') as string;
        return (
          <SkeletonCell isLoading={isLoading}>
            <div className="flex flex-col">
              <span className="text-muted-foreground">{description}</span>
            </div>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'value',
      header: ({ column }) => <Header column={column}>Value</Header>,
      cell: ({ row }) => {
        const value = row.getValue('value') as number;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span className="text-muted-foreground">{formatCurrency(value)}</span>
          </SkeletonCell>
        );
      },
    },

    {
      accessorKey: 'maxUses',
      header: ({ column }) => <Header column={column}>Utilization</Header>,
      cell: ({ row }) => {
        const maxUses: string | number = row.getValue('maxUses');
        const used = row.original.usages;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span className="text-muted-foreground">
              {used}/{maxUses ? maxUses : <span>&infin;</span>}
            </span>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'expiryDate',
      header: ({ column }) => <Header column={column}>Expiry Date</Header>,
      cell: ({ row }) => {
        const expiryDate = row.original?.expiryDate;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span className="text-muted-foreground whitespace-nowrap">
              {expiryDate ? formatDate(expiryDate) : 'Never'}
            </span>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => <Header column={column}>Status</Header>,
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <SkeletonCell isLoading={isLoading}>
            {status === PublicReferralCodeStatus.Enabled ? (
              <VerifiedBadge>Enabled</VerifiedBadge>
            ) : (
              <UnverifiedBadge>Disabled</UnverifiedBadge>
            )}
          </SkeletonCell>
        );
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 overflow-hidden' : '')}>
          <ActionsCell
            row={row}
            urlDetail=""
            deleteModalProps={{
              onDelete,
              isDeleting,
              title: 'Delete Referral Code',
              subtitle: 'Are you sure you want to delete this referral code? This action cannot be reversed.',
            }}
          >
            <DropdownMenuItem
              onClick={() => selectRowEdit(row.original)}
              className="text-foreground font-medium cursor-pointer rounded-none"
            >
              View / Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-foreground font-medium cursor-pointer rounded-none"
              onClick={e => {
                e.stopPropagation();
                onToggleStatus(
                  row.original.id,
                  row.original.status === PublicReferralCodeStatus.Enabled
                    ? PublicReferralCodeStatus.Disabled
                    : PublicReferralCodeStatus.Enabled,
                );
              }}
            >
              {row.original.status === PublicReferralCodeStatus.Enabled ? 'Disable' : 'Enable'}
            </DropdownMenuItem>
          </ActionsCell>
        </SkeletonCell>
      ),
    },
  ];

  return columns;
}

export { generateColumns };
