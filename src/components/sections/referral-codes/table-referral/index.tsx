'use client';
import React, { useCallback, useMemo, useState } from 'react';

import { cn } from '@/lib/utils';
import CustomPagination from '@/components/common/custom-pagination';
import { Table } from '@/components/ui/table';
import { PublicReferralCode, PublicReferralCodeStatus } from '@/backend/referral-codes/entities/PublicReferralCode';
import ReferralHeader from '../referral-header';
import { generateColumns } from '../table-referral/columns';
import { usePublicReferralCodes } from '../table-referral/hooks/usePublicReferralCodes';
import { useDeletePublicReferralCode } from '../table-referral/hooks/useDeletePublicReferralCode';
import { useUpdateReferralCode } from '../referral-modal/useUpdateReferralCode';
import CreateReferralModal from '../referral-modal/create-code-modal';
import EditReferralModal from '../referral-modal/edit-code-modal';

const TablePublicReferralCodes = () => {
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<PublicReferralCode | undefined>();
  const { publicReferralCodes, isLoadingPublicReferralCodes } = usePublicReferralCodes();
  const { deletePublicReferralCode, isDeletingPublicReferralCode } = useDeletePublicReferralCode();
  const { updateReferralCode } = useUpdateReferralCode();

  const onToggleStatus = useCallback(
    async (id: number, status: PublicReferralCodeStatus) => {
      await updateReferralCode({ id, status, isToggleStatus: true });
    },
    [updateReferralCode],
  );

  const columns = useMemo(
    () =>
      generateColumns({
        onDelete: deletePublicReferralCode,
        isDeleting: isDeletingPublicReferralCode,
        isLoading: isLoadingPublicReferralCodes,
        onToggleStatus,
        selectRowEdit: (row: PublicReferralCode) => {
          setSelectedRow(row);
          setIsOpenModal(true);
        },
      }),
    [deletePublicReferralCode, isDeletingPublicReferralCode, isLoadingPublicReferralCodes, onToggleStatus],
  );

  return (
    <>
      <ReferralHeader count={publicReferralCodes?.data?.total ?? 0} setShowModal={setIsOpenModal} />
      <Table
        columns={columns}
        data={
          publicReferralCodes?.data?.data
            ? publicReferralCodes.data.data
            : isLoadingPublicReferralCodes
              ? Array(10).fill({})
              : []
        }
        url="referral-codes"
        selectRowEdit={(row: PublicReferralCode) => {
          setSelectedRow(row);
          setIsOpenModal(true);
        }}
        containerClassName={cn(!isLoadingPublicReferralCodes && 'peer is-done')}
      />
      <CustomPagination totalPages={publicReferralCodes?.data?.totalPages ?? 10} />
      {isOpenModal ? (
        selectedRow ? (
          <EditReferralModal
            onClose={() => {
              setIsOpenModal(false);
              setSelectedRow(undefined);
            }}
            defaultValues={selectedRow}
          />
        ) : (
          <CreateReferralModal
            onClose={() => {
              setIsOpenModal(false);
            }}
          />
        )
      ) : null}
    </>
  );
};
export default TablePublicReferralCodes;
