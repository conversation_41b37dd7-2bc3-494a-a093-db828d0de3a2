'use client';
import DateRangePicker from '@/components/common/date-range-picker';
import React, { useState } from 'react';
import { DateRange } from 'react-day-picker';
import ActivityItems from './activity-items';
import { useActivityLog } from './useActivityLog';
import { endOfDay, startOfDay } from 'date-fns';

const ActivityLogPage = () => {
  const today = new Date();

  const [activityTime, setActivityTime] = useState<DateRange | undefined>({
    from: startOfDay(today),
    to: endOfDay(today),
  });

  const { activityLog, isLoading } = useActivityLog({
    time: activityTime,
  });

  return (
    <div className="w-full flex flex-col gap-6 max-h-[calc(100vh-100px)]">
      <div className="text-lg font-medium">Activity Log</div>
      <DateRangePicker onDateChange={setActivityTime} initialDate={activityTime} />
      <div className="flex-1 flex-col gap-2 overflow-y-auto">
        <ActivityItems activityLog={activityLog} isLoading={isLoading} />
      </div>
    </div>
  );
};

export default ActivityLogPage;
