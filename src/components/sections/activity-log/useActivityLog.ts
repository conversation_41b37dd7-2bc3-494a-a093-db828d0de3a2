import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { DateRange } from 'react-day-picker';
import { endOfDay, startOfDay } from 'date-fns';

export const ACTIVITY_LOG_QUERY_KEY = 'activity-log';
interface ActivityLog {
  time: DateRange | undefined;
}

export function useActivityLog({ time }: ActivityLog) {
  const { from, to } = time ?? {};
  const fromDate = from ? startOfDay(from) : undefined;
  const toDate = to ? endOfDay(to) : undefined;
  const { data, isLoading } = useQuery({
    queryKey: ['activity-log', from, to],
    queryFn: () => {
      return api.activityLog.getActivityLogs({ from: fromDate, to: toDate });
    },
  });

  return {
    activityLog: data?.data,
    isLoading,
  };
}
