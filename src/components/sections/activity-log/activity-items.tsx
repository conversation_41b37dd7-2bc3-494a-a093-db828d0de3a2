'use client';
import { formatDate } from '@/utils/date-format';
import { UserAction } from '@/backend/users/entities/UserAction';
import { Skeleton } from '@/components/ui/skeleton';
import { UserActionsResponse } from '@/backend/users/validations/user-action';

const ActivityItem = ({ data }: { data: UserAction }) => {
  return (
    <div className="mt-6 pt-5 border-t border-t-[#EAECF0]">
      <div className="flex justify-between items-center mb-2">
        <div className="font-medium text-sm text-gray-text">
          {data.user.firstName} {data.user.lastName}
        </div>
        <div className="text-xs text-gray-scale-5">{formatDate(data.createdAt, 'EEEE - MMMM d, yyyy - h:mm aaa')}</div>
      </div>

      <div className="px-4 py-2 rounded-lg rounded-tl-none shadow bg-[#F2F4F7]">
        <div>
          Made changes to <span className="font-bold">{data.entity}</span>:
        </div>
        <div>{data.description}</div>
      </div>
    </div>
  );
};

const ActivityItems = ({
  activityLog,
  isLoading,
}: {
  activityLog: UserActionsResponse | undefined;
  isLoading: boolean;
}) => {
  if (isLoading) {
    return (
      <>
        {Array(4)
          .fill(null)
          .map((_, i) => (
            <div key={i} className="mt-6 pt-5 border-t border-t-[#EAECF0]">
              <div className="flex justify-between items-center mb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
              </div>

              <Skeleton className="px-4 py-2 h-[64px] rounded-lg rounded-tl-none shadow bg-[#F2F4F7]" />
            </div>
          ))}
      </>
    );
  }

  if (!activityLog) return;

  return (
    <>
      {activityLog.actions.length > 0 ? (
        activityLog.actions.map(item => <ActivityItem data={item} key={item.id} />)
      ) : (
        <div className="mt-4">No results.</div>
      )}
    </>
  );
};

export default ActivityItems;
