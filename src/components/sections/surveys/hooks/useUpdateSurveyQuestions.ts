import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { CreateSurveyQuestionPayload } from '../validations';

export function useUpdateSurveyQuestions(surveyId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (body: Partial<CreateSurveyQuestionPayload>) => api.surveys.updateSurveyQuestions(surveyId, body),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['surveyResults'] });
    },
  });

  return {
    updateSurveyQuestions: mutateAsync,
    isUpdating: isPending,
  };
}
