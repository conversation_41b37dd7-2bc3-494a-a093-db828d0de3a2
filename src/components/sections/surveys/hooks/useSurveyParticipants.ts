import { User } from '@/backend/users/entities/User';
import { api } from '@/lib/http';
import { PaginationPayload } from '@/types/pagination';
import { useQuery } from '@tanstack/react-query';

export const useSurveyParticipants = (surveyId: number, payload: PaginationPayload<User>) => {
  const { page, pageSize, search, sortBy = 'completionDate' as keyof User, sortOrder = 'DESC' } = payload;

  const { data, isLoading } = useQuery({
    queryKey: ['survey-participants', surveyId, page, pageSize, search, sortBy, sortOrder],
    queryFn: () =>
      api.surveys.getSurveyParticipants(surveyId, {
        page,
        pageSize,
        search,
        sortBy,
        sortOrder,
      }),
  });

  return {
    participants: data,
    isLoading,
  };
};
