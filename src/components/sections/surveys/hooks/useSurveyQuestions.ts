import { api } from '@/lib/http';
import { useQuery } from '@tanstack/react-query';

export function useSurveyQuestions(surveyId: number) {
  const { data: surveyQuestions, isLoading: isLoadingSurveyQuestions } = useQuery({
    queryKey: ['surveyQuestions', surveyId],
    queryFn: async () => api.surveys.getSurveyQuestions(surveyId),
    enabled: !!surveyId,
  });

  return { surveyQuestions, isLoadingSurveyQuestions };
}
