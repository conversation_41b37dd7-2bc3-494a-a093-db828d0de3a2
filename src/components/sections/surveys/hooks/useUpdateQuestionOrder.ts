import { UpdateQuestionOrderPayload } from '@/backend/surveys/validations/update-survey-question';
import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useUpdateQuestionOrder(surveyId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (body: UpdateQuestionOrderPayload) => api.surveys.updateQuestionOrder(surveyId, body),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
    onSuccess: async () => {
      toast.success('Survey updated successfully');
      await queryClient.invalidateQueries({ queryKey: ['surveyResults'] });
    },
  });

  return {
    updateQuestionsOrder: mutateAsync,
    isUpdatingOrder: isPending,
  };
}
