import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useDeleteSurveyQuestion(surveyId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isError, error } = useMutation({
    mutationFn: (questionId: number) => api.surveys.deleteSurveyQuestion(surveyId, questionId),
    onSuccess: async () => {
      toast.success('Survey question deleted successfully');
      await queryClient.invalidateQueries({ queryKey: ['surveyQuestions'] });
      await queryClient.invalidateQueries({ queryKey: ['surveyResults'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    deleteQuestion: mutateAsync,
    isDeleting: isPending,
    isDeleteError: isError,
    deleteError: error,
  };
}
