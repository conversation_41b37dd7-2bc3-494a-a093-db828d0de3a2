import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { PublishAudiencePayload } from '../validations';

export type PublishAudiencesHook = PublishAudiencePayload & { surveyId: number };

export function usePublishAudiences() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (body: PublishAudiencesHook) => api.surveys.publishAudiences(body),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['surveys'] });
      await queryClient.invalidateQueries({ queryKey: ['survey'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    publishAudiences: mutateAsync,
    isPublishingAudiences: isPending,
  };
}
