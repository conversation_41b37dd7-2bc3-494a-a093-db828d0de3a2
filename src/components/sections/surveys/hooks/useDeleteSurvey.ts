import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useDeleteSurvey() {
  const queryClient = useQueryClient();
  const { mutateAsync: deleteSurvey, isPending: isDeletingSurvey } = useMutation({
    mutationFn: (id: number) => api.surveys.deleteSurvey(id),
    onSuccess: async () => {
      toast.success('Survey deleted successfully');
      await queryClient.invalidateQueries({ queryKey: ['surveys'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return { deleteSurvey, isDeletingSurvey };
}
