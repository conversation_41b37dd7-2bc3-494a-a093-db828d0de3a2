import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { CreateSurveyInfoPayload } from '../validations';

export function useUpdateSurvey(surveyId: number, skipToast = false) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (body: Partial<CreateSurveyInfoPayload>) => api.surveys.updateSurvey(surveyId, body),
    onSuccess: async () => {
      if (!skipToast) {
        toast.success('Survey updated successfully');
      }
      await queryClient.invalidateQueries({ queryKey: ['surveys'] });
      await queryClient.invalidateQueries({ queryKey: ['survey'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    updateSurvey: mutateAsync,
    isUpdating: isPending,
  };
}
