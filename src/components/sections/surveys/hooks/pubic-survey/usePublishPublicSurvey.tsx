import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function usePublishPublicSurvey() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (surveyId: number) => api.surveys.publishPublicSurvey(surveyId),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['surveys'] });
      await queryClient.invalidateQueries({ queryKey: ['survey'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    publishPublicSurvey: mutateAsync,
    isPublishingPublicSurvey: isPending,
  };
}
