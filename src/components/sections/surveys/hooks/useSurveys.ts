import { useQuery } from '@tanstack/react-query';
import { Survey } from '@/backend/surveys/entities/Survey';
import { usePagination } from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useSortQuery } from '@/hooks/useSortQuery';
import { api } from '@/lib/http';

export function useSurveys() {
  const { page, pageSize } = usePagination();
  const { search } = useSearchQuery();
  const { sortBy, sortOrder } = useSortQuery({ sortBy: 'updatedAt', sortOrder: 'DESC' });

  const {
    data: surveys,
    isLoading: isLoadingSurveys,
    isError,
  } = useQuery({
    queryKey: ['surveys', page, pageSize, search, sortBy, sortOrder],
    queryFn: async () =>
      api.surveys.getSurveys({
        page,
        pageSize,
        search: search ?? '',
        sortBy: sortBy as keyof Survey,
        sortOrder: sortOrder === 'ASC' ? 'ASC' : 'DESC',
      }),
    placeholderData: previousData => previousData,
  });

  return { surveys, isLoadingSurveys, isError };
}
