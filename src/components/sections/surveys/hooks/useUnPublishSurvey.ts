import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useUnPublishSurvey(surveyId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: () => api.surveys.unPublishSurvey(surveyId),
    onSuccess: async () => {
      toast.success('Survey unpublished successfully');
      await queryClient.invalidateQueries({ queryKey: ['surveys'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    unPublishSurvey: mutateAsync,
    isUnPublishingSurvey: isPending,
  };
}
