import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useDuplicateSurvey() {
  const queryClient = useQueryClient();
  const { mutateAsync: duplicateSurvey, isPending: isDuplicatingSurvey } = useMutation({
    mutationFn: (id: number) => api.surveys.duplicateSurvey(id),
    onSuccess: async () => {
      toast.success('Survey duplicated successfully');
      await queryClient.invalidateQueries({ queryKey: ['surveys'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return { duplicateSurvey, isDuplicatingSurvey };
}
