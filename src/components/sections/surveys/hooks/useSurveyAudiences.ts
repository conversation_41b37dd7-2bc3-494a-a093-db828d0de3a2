import { api } from '@/lib/http';
import { useQuery } from '@tanstack/react-query';

export function useSurveyAudiences(surveyId: number) {
  const { data: surveyAudiences, isLoading: isLoadingSurveyAudiences } = useQuery({
    queryKey: ['surveyAudiences', surveyId],
    queryFn: async () => api.surveys.getSurveyAudiences(surveyId),
    enabled: !!surveyId,
  });

  return { surveyAudiences, isLoadingSurveyAudiences };
}
