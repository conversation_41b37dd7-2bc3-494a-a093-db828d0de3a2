import { PublishSurveyPayload } from '@/backend/surveys/validations/publish-survey';
import { api } from '@/lib/http';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export const useDraftForAudience = () => {
  return useMutation({
    mutationFn: (data: PublishSurveyPayload & { surveyId: number }) => api.surveys.saveDraftAudience(data),
    onSuccess: () => {
      toast.success('Draft Saved Successfully');
    },
    onError: error => {
      toast.error(error.message);
    },
  });
};
