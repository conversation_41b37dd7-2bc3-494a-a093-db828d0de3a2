import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { CreateSurveyQuestionPayload } from '../validations';

export function useCreateSurveyQuestions() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (body: CreateSurveyQuestionPayload) => api.surveys.createSurveyQuestions(body),
    onSuccess: async () => {
      toast.success('Survey updated successfully');
      await queryClient.invalidateQueries({ queryKey: ['surveyQuestions'] });
      await queryClient.invalidateQueries({ queryKey: ['surveyResults'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    createSurveyQuestions: mutateAsync,
    isAddingSurveyQuestions: isPending,
  };
}
