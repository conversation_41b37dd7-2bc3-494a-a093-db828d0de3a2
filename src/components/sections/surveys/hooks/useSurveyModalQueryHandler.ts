import { useCallback, useEffect } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useSurveyStore } from './useSurveyStore';
import { useShallow } from 'zustand/react/shallow';

export const useSurveyModalQueryHandler = () => {
  const { isCreateSurveyModalOpen, resetSurvey, toggleCreateSurveyModal, setSurveyId } = useSurveyStore(
    useShallow(state => ({
      isCreateSurveyModalOpen: state.isCreateSurveyModalOpen,
      resetSurvey: state.resetSurvey,
      toggleCreateSurveyModal: state.toggleCreateSurveyModal,
      setSurveyId: state.setSurveyId,
    })),
  );
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const updateURLWithEditId = useCallback(
    (id?: string, mode: 'edit' | 'create' = 'edit') => {
      const params = new URLSearchParams(Array.from(searchParams.entries()));

      if (!isCreateSurveyModalOpen && (params.has('isCreate') || params.has('editId'))) {
        params.delete('isCreate');
        params.delete('editId');
      } else {
        if (mode === 'create') {
          params.set('isCreate', 'true');
        } else if (params.has('isCreate')) {
          params.delete('isCreate');
        }
        if (id) {
          params.set('editId', id);
        } else if (params.has('editId')) {
          params.delete('editId');
        }
      }

      const paramsString = params.toString();
      router.push(`${pathname}?${paramsString}`);
    },
    [pathname, router, searchParams, isCreateSurveyModalOpen],
  );

  const editId = searchParams.get('editId');
  const isCreate = searchParams.get('isCreate');

  useEffect(() => {
    if (isCreate) {
      resetSurvey();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCreate]);

  useEffect(() => {
    setSurveyId(editId ? Number(editId) : null);
    toggleCreateSurveyModal(!!editId || !!isCreate);
  }, [editId, isCreate, setSurveyId, toggleCreateSurveyModal]);

  return updateURLWithEditId;
};
