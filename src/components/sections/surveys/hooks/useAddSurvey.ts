import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { CreateSurveyPayload } from '../validations';

export function useAddSurvey() {
  const queryClient = useQueryClient();
  const { mutateAsync: addSurvey, isPending: isAddingSurvey } = useMutation({
    mutationFn: (data: CreateSurveyPayload) => api.surveys.createSurvey(data),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['surveys'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    addSurvey,
    isAddingSurvey,
  };
}
