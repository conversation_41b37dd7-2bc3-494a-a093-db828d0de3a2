import { create } from 'zustand';
import { SurveyCreationStep } from '../types';
import { CreateSurveyInfoPayload, CreateSurveyQuestionState, PublishAudiencePayload } from '../validations';

export interface SurveyDataType {
  [SurveyCreationStep.SURVEY_INFO]: CreateSurveyInfoPayload;
  [SurveyCreationStep.QUESTION_INFO]: CreateSurveyQuestionState;
  [SurveyCreationStep.AUDIENCE]: PublishAudiencePayload;
}

interface SurveyStore {
  id: number | null;
  surveyData: SurveyDataType;
  currentStep: SurveyCreationStep;
  updateSurveyData: (data: Partial<SurveyDataType>) => void;
  updateCurrentStep: (step: SurveyCreationStep) => void;
  resetSurvey: () => void;
  setSurveyData: (data: SurveyDataType) => void;

  isCreateSurveyModalOpen: boolean;
  toggleCreateSurveyModal: (isOpen?: boolean) => void;
  setSurveyId: (id: number | null) => void;
}

export const initialSurveyStore: Pick<SurveyStore, 'currentStep' | 'surveyData' | 'isCreateSurveyModalOpen' | 'id'> = {
  id: null, // Used for editing, set to null initially for new survey creation
  currentStep: SurveyCreationStep.SURVEY_INFO,
  surveyData: {
    [SurveyCreationStep.SURVEY_INFO]: {} as CreateSurveyInfoPayload,
    [SurveyCreationStep.QUESTION_INFO]: {} as CreateSurveyQuestionState,
    [SurveyCreationStep.AUDIENCE]: {} as PublishAudiencePayload,
  },
  isCreateSurveyModalOpen: false,
};

export const useSurveyStore = create<SurveyStore>(set => ({
  ...initialSurveyStore,
  updateSurveyData: (data: Partial<SurveyDataType>) =>
    set(state => {
      return {
        surveyData: { ...state.surveyData, ...data },
      };
    }),
  setSurveyData: (data: SurveyDataType) => set({ surveyData: data }),
  updateCurrentStep: (step: SurveyCreationStep) => set({ currentStep: step }),
  resetSurvey: () => set(initialSurveyStore),
  toggleCreateSurveyModal: (isOpen?: boolean) => set({ isCreateSurveyModalOpen: isOpen }),
  setSurveyId: (id: number | null) => set({ id }),
}));
