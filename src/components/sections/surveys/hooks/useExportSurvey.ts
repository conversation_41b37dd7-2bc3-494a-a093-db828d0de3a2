import { stringToBlob } from '@/backend/shared/utils/export-csv';
import { api } from '@/lib/http';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useExportSurvey() {
  const { mutate: exportSurvey, isPending: isExporting } = useMutation({
    mutationFn: (id: number) => api.surveys.exportSurvey(id),
    onSuccess: (data: string) => {
      const blob = stringToBlob(data);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'survey.csv';
      a.click();
    },
    onError: err => {
      const error = err as Error;
      toast.error(error.message);
    },
  });

  return { exportSurvey, isExporting };
}
