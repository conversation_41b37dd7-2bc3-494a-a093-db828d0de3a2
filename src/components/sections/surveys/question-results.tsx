'use client';

import { useSurveyResults } from '@/components/sections/surveys/hooks/useSurveyResults';
import SurveyQuestionDetail from '@/components/sections/surveys/survey-question';
import { Skeleton } from '@/components/ui/skeleton';

const QuestionResults = ({ surveyId }: { surveyId: number }) => {
  const { surveyResults, isLoadingSurveyResults } = useSurveyResults(surveyId);

  if (isLoadingSurveyResults)
    return (
      <>
        {Array(3)
          .fill(null)
          .map((_, i) => (
            <Skeleton className="w-full h-40 mb-6" key={i} />
          ))}
      </>
    );

  if (!surveyResults?.data) return;

  return (
    <div className="flex flex-col mt-10">
      {surveyResults.data.length > 0 ? (
        surveyResults.data
          .sort((a, b) => a.order - b.order)
          .map(question => <SurveyQuestionDetail data={question} key={question.id} />)
      ) : (
        <div className="px-8">No results yet.</div>
      )}
    </div>
  );
};

export default QuestionResults;
