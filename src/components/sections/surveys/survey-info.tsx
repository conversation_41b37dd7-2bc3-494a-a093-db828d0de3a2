'use client';

import { useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import StatusBadge from '@/components/common/badges/status';
import ModalConfirmDelete from '@/components/common/modal-confirm-delete';
import CreateSurveysModal from '@/components/sections/surveys/create-surveys-modal';
import { useDeleteSurvey } from '@/components/sections/surveys/hooks/useDeleteSurvey';
import { useExportSurvey } from '@/components/sections/surveys/hooks/useExportSurvey';
import { useSurvey } from '@/components/sections/surveys/hooks/useSurvey';
import { useSurveyStore } from '@/components/sections/surveys/hooks/useSurveyStore';
import ModalConfirmEdit from '@/components/sections/surveys/modal-confirm-edit';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate } from '@/utils/date-format';
import { useRouter } from 'next/navigation';
import { useSurveyModalQueryHandler } from './hooks/useSurveyModalQueryHandler';
import { parse } from 'date-fns';

const SurveyInfo = ({ surveyId }: { surveyId: number }) => {
  const updateURLWithEditId = useSurveyModalQueryHandler();
  const router = useRouter();
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const { survey, isLoadingSurvey } = useSurvey(surveyId);
  const { exportSurvey, isExporting } = useExportSurvey();
  const { deleteSurvey, isDeletingSurvey } = useDeleteSurvey();
  const { isCreateSurveyModalOpen } = useSurveyStore(
    useShallow(state => ({
      isCreateSurveyModalOpen: state.isCreateSurveyModalOpen,
    })),
  );

  const status = survey?.data.status as SurveyStatus;
  const expiryDate = survey?.data.expiryDate
    ? parse(survey?.data.expiryDate as unknown as string, 'yyyy-MM-dd', new Date())
    : undefined;
  const formattedExpiryDate = expiryDate && !isNaN(expiryDate.getTime()) ? formatDate(expiryDate) : undefined;

  const handleClickEdit = () => {
    if (status === SurveyStatus.Active) setOpenEditDialog(true);

    if (status === SurveyStatus.Draft) {
      updateURLWithEditId(surveyId.toString());
    }
  };

  return (
    <>
      <div className="flex justify-between gap-8">
        {isLoadingSurvey ? (
          <Skeleton className="w-1/4 h-14" />
        ) : (
          <div className="flex flex-col gap-2">
            <div className="flex flex-wrap">
              <div className="font-medium text-lg mr-4">{survey?.data.title ?? 'N/A'}</div>
              <StatusBadge status={status}>{status}</StatusBadge>
            </div>
            <div className="text-sm text-gray-text">{survey?.data.description ?? 'N/A'}</div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row items-center  gap-3">
          <Button
            className="text-error-bold font-semibold bg-inherit"
            onClick={() => setOpenDialog(true)}
            disabled={isLoadingSurvey || isExporting}
          >
            Delete Survey
          </Button>
          <ModalConfirmDelete
            openDialog={openDialog}
            setOpenDialog={setOpenDialog}
            onConfirm={async () => {
              await deleteSurvey(surveyId);
              router.replace('/surveys');
            }}
            isConfirming={isDeletingSurvey}
            title="Delete Survey"
            subtitle="Are you sure you want to delete this survey? This action cannot be reversed."
          />

          <Button
            variant="outline"
            onClick={() => exportSurvey(surveyId)}
            isLoading={isExporting}
            disabled={isLoadingSurvey || isExporting}
          >
            Export Survey
          </Button>

          <Button
            onClick={handleClickEdit}
            disabled={isLoadingSurvey || isExporting || status === SurveyStatus.Expired}
          >
            Edit Survey
          </Button>
          <ModalConfirmEdit
            openDialog={openEditDialog}
            setOpenDialog={setOpenEditDialog}
            title="Edit Survey"
            subtitle="Are you sure? This survey is currently active, editing it might significantly impact the results."
            onConfirm={() => {
              setOpenEditDialog(false);
              updateURLWithEditId(surveyId.toString());
            }}
          />
        </div>
      </div>

      <div className="flex flex-col lg:flex-row flex-wrap gap-3 lg:gap-6 mt-4">
        {isLoadingSurvey ? (
          <Skeleton className="w-1/2 h-5" />
        ) : (
          <>
            <div className="text-sm">
              Company: <span className="font-medium">{survey?.data.company.name ?? 'N/A'}</span>
            </div>
            <div className="text-sm">
              Expiry Date (UTC+0):{' '}
              <span className="font-medium">{formattedExpiryDate ? formattedExpiryDate : 'N/A'}</span>
            </div>
            <div className="text-sm">
              Compensation:{' '}
              <span className="font-medium">
                {survey?.data.compensation !== undefined ? `$${survey?.data.compensation}` : 'N/A'}
              </span>
            </div>
            <div className="text-sm">
              Time (minutes): <span className="font-medium">{survey?.data.time ?? 'N/A'}</span>
            </div>
            <div className="text-sm">
              Total Responses:{' '}
              <span className="font-medium">
                {survey?.data.successfulCompletions ?? 'N/A'}/
                {survey?.data.maxParticipants ? survey.data.maxParticipants : <span>&infin;</span>}
              </span>
            </div>
          </>
        )}
      </div>

      <div className="flex flex-col lg:flex-row flex-wrap gap-3 lg:gap-6 mt-2">
        {isLoadingSurvey ? (
          <Skeleton className="w-1/2 h-5" />
        ) : (
          <>
            <div className="text-sm">
              Created By:{' '}
              <span className="font-medium">
                {`${survey?.data.userAdmin.firstName} ${survey?.data.userAdmin.lastName}`}
              </span>
            </div>
            <div className="text-sm">
              Last Updated Date:{' '}
              <span className="font-medium">
                {survey?.data.updatedAt ? formatDate(survey?.data.updatedAt, 'MM/dd/yyyy, hh:mm aa') : 'N/A'}
              </span>
            </div>
            <div className="text-sm">
              ID: <span className="font-medium">{survey?.data.id ?? 'N/A'}</span>
            </div>
          </>
        )}
      </div>
      {isCreateSurveyModalOpen && <CreateSurveysModal handleCreatePublicSurveySuccess={() => {}} />}
    </>
  );
};

export default SurveyInfo;
