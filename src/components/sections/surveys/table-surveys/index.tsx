'use client';

import { useCallback, useMemo, useState } from 'react';
import { useSurveys } from '@/components/sections/surveys/hooks/useSurveys';
import SurveysHeader from '@/components/sections/surveys/surveys-header';
import { generateColumns } from '@/components/sections/surveys/table-surveys/columns';
import { Table } from '@/components/ui/table';
import CustomPagination from '@/components/common/custom-pagination';
import { cn } from '@/lib/utils';
import { useDuplicateSurvey } from '@/components/sections/surveys/hooks/useDuplicateSurvey';
import { useDeleteSurvey } from '@/components/sections/surveys/hooks/useDeleteSurvey';
import ModalPublishSurveyQRCode from '../create-surveys-modal/modal-publish-survey-qr-code';

const TableSurveys = () => {
  const { surveys, isLoadingSurveys, isError } = useSurveys();
  const { duplicateSurvey } = useDuplicateSurvey();
  const { deleteSurvey, isDeletingSurvey } = useDeleteSurvey();
  const [modalSurveyQRCode, setModalSurveyQRCode] = useState<{
    id: number;
    qrImageUrl: string;
    modalType: 'share' | 'publish';
  } | null>(null);

  const handleShareSurveyQRCode = useCallback((id: number, qrImageUrl: string) => {
    setModalSurveyQRCode({ id, qrImageUrl, modalType: 'share' });
  }, []);

  const handlePublishSurveyQRCodeSuccess = useCallback((id: number, qrImageUrl: string) => {
    setModalSurveyQRCode({ id, qrImageUrl, modalType: 'publish' });
  }, []);

  const columns = useMemo(
    () =>
      generateColumns({
        isLoading: isLoadingSurveys,
        onDuplicate: duplicateSurvey,
        isDeleting: isDeletingSurvey,
        onDelete: deleteSurvey,
        onShareSurveyQRCode: handleShareSurveyQRCode,
      }),
    [isLoadingSurveys, duplicateSurvey, isDeletingSurvey, deleteSurvey, handleShareSurveyQRCode],
  );

  return (
    <>
      <SurveysHeader
        surveysCount={surveys?.data?.total ?? 0}
        handleCreatePublicSurveySuccess={handlePublishSurveyQRCodeSuccess}
      />
      <Table
        columns={columns}
        data={surveys?.data?.data ? surveys.data.data : isLoadingSurveys ? Array(10).fill({}) : []}
        url="/surveys"
        containerClassName={cn(!isLoadingSurveys && !isError && 'peer is-done')}
      />
      <CustomPagination totalPages={surveys?.data?.totalPages ?? 1} />
      {modalSurveyQRCode && (
        <ModalPublishSurveyQRCode
          title={modalSurveyQRCode?.modalType === 'publish' ? 'Survey Published' : 'Share Survey'}
          openDialog={!!modalSurveyQRCode}
          setOpenDialog={() => setModalSurveyQRCode(null)}
          onConfirm={() => setModalSurveyQRCode(null)}
          surveyQRCodeImageUrl={modalSurveyQRCode?.qrImageUrl}
          surveyId={modalSurveyQRCode?.id}
        />
      )}
    </>
  );
};

export default TableSurveys;
