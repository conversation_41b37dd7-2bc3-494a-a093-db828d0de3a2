'use client';

import { ColumnDef } from '@tanstack/react-table';
import { GlobeIcon, Pin } from 'lucide-react';
import { PropsWithChildren } from 'react';

import { Survey, SurveyStatus } from '@/backend/surveys/entities/Survey';
import StatusBadge from '@/components/common/badges/status';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import { parse } from 'date-fns';
import { SurveyActionsCell } from './survey-actions-cell';

type CellProps = {
  classname?: string;
};

const Cell = ({ children, classname }: PropsWithChildren<CellProps>) => {
  return <span className={cn('text-muted-foreground', classname)}>{children}</span>;
};

type ColumnsProps = {
  isLoading?: boolean;
  onDuplicate: (id: number) => void;
  isDeleting: boolean;
  onDelete: (id: number) => void | Promise<void>;
  onShareSurveyQRCode: (id: number, qrImageUrl: string) => void;
};

export function generateColumns({ isLoading, onDuplicate, isDeleting, onDelete, onShareSurveyQRCode }: ColumnsProps) {
  const columns: ColumnDef<Survey>[] = [
    {
      accessorKey: 'title',
      header: ({ column }) => <Header column={column}>Survey Name</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <div className="flex items-center justify-between w-full">
            <span className="text-foreground font-medium line-clamp-5 lg:line-clamp-3 max-w-[85%]">
              {row.original.isPinned && (
                <span className="text-muted-foreground font-normal mr-2">
                  <Pin width={15} height={15} className="inline rotate-45" />
                </span>
              )}
              {row.getValue('title')}
            </span>
            {row.original.isPublic && (
              <span className="text-muted-foreground font-normal ml-2">
                <GlobeIcon width={18} height={18} className="text-primary" />
              </span>
            )}
          </div>
        </SkeletonCell>
      ),
      size: 350,
    },
    {
      accessorKey: 'companyId',
      header: ({ column }) => <Header column={column}>Sponsor Company</Header>,
      cell: ({ row }) => {
        const company = row.original.company;

        return (
          <SkeletonCell isLoading={isLoading}>
            <Cell>{company?.name}</Cell>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'successfulCompletions',
      header: ({ column }) => <Header column={column}># of Responses</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <Cell>
            {row.original.successfulCompletions} / {row.original.maxParticipants || <span>&infin;</span>}
          </Cell>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'expiryDate',
      header: ({ column }) => <Header column={column}>Expiry Date (UTC+0)</Header>,
      cell: ({ row }) => {
        const expiryDate = row.getValue('expiryDate') as string;
        const parsedDate = expiryDate ? parse(expiryDate, 'yyyy-MM-dd', new Date()) : undefined;
        const formattedDate = parsedDate ? formatDate(parsedDate) : '';

        return (
          <SkeletonCell isLoading={isLoading}>
            <Cell>{formattedDate}</Cell>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'updatedAt',
      header: ({ column }) => <Header column={column}>Last Updated</Header>,
      cell: ({ row }) => {
        const updatedAt = row.getValue('updatedAt') ? formatDate(row.getValue('updatedAt')) : '';

        return (
          <SkeletonCell isLoading={isLoading}>
            <Cell>{updatedAt}</Cell>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => <Header column={column}>Status</Header>,
      cell: ({ row }) => {
        const status = row.getValue('status') as SurveyStatus;

        return (
          <SkeletonCell isLoading={isLoading}>
            <StatusBadge status={status}>{status}</StatusBadge>
          </SkeletonCell>
        );
      },
      size: 100,
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => {
        const publicQRCode = row.original.publicQRCode || '';
        const isPublic = row.original.isPublic;
        return (
          <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 overflow-hidden' : '')}>
            <SurveyActionsCell
              row={row}
              onDuplicate={onDuplicate}
              isDeleting={isDeleting}
              onDelete={onDelete}
              onShareSurveyQRCode={() => onShareSurveyQRCode(row.original.id, publicQRCode)}
              publicQRCode={publicQRCode}
              isPublic={isPublic}
            />
          </SkeletonCell>
        );
      },
      size: 70,
    },
  ];

  return columns;
}
