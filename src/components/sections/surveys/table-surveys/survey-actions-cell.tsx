import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import { useSurveyModalQueryHandler } from '../hooks/useSurveyModalQueryHandler';
import { useUnPublishSurvey } from '../hooks/useUnPublishSurvey';
import { useState } from 'react';
import { ActionsCell } from '@/components/ui/table/action-cell';
import Link from 'next/link';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import ModalConfirmEdit from '../modal-confirm-edit';
import { Row } from '@tanstack/react-table';
import { Separator } from '@/components/ui/separator';

type SurveyActionsCellProps<TData extends { id: number }> = {
  row: Row<TData>;
  onDuplicate: (id: number) => void;
  isDeleting: boolean;
  onDelete: (id: number) => void | Promise<void>;
  onShareSurveyQRCode: () => void;
  publicQRCode: string;
  isPublic: boolean;
};

export const SurveyActionsCell = <TData extends { id: number }>({
  row,
  onDuplicate,
  isDeleting,
  onDelete,
  onShareSurveyQRCode,
  isPublic,
}: SurveyActionsCellProps<TData>) => {
  const updateURLWithEditId = useSurveyModalQueryHandler();

  const { unPublishSurvey, isUnPublishingSurvey } = useUnPublishSurvey(row.original.id);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const status = row.getValue('status') as SurveyStatus;
  const isExpired = status === SurveyStatus.Expired;

  const handleClickEdit = () => {
    if (status === SurveyStatus.Active) setOpenEditDialog(true);

    if (status === SurveyStatus.Draft) {
      updateURLWithEditId(row.original.id.toString());
    }
  };

  return (
    <>
      <ActionsCell
        row={row}
        urlDetail=""
        deleteModalProps={{
          onDelete,
          isDeleting,
          title: 'Delete Survey',
          subtitle: 'Are you sure you want to delete this survey? This action cannot be reversed.',
        }}
      >
        <Link href={`/surveys/${row.original.id}`}>
          <DropdownMenuItem className="text-foreground focus:text-gray-text font-medium cursor-pointer rounded-none">
            View Details
          </DropdownMenuItem>
        </Link>

        {row.getValue('status') !== SurveyStatus.Expired && (
          <>
            <DropdownMenuItem
              onClick={e => {
                e.stopPropagation();
                onDuplicate(row.original.id);
              }}
              className="text-foreground focus:text-gray-text font-medium cursor-pointer rounded-none"
            >
              Duplicate
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={e => {
                e.stopPropagation();
                handleClickEdit();
              }}
              className="text-foreground focus:text-gray-text font-medium cursor-pointer rounded-none"
            >
              Edit Survey
            </DropdownMenuItem>
          </>
        )}
        {isPublic && !isExpired && (
          <DropdownMenuItem
            onClick={e => {
              e.stopPropagation();
              onShareSurveyQRCode();
            }}
            className="text-foreground focus:text-gray-text font-medium cursor-pointer rounded-none"
          >
            Share Survey
          </DropdownMenuItem>
        )}

        <Separator className="my-1" />

        {row.getValue('status') === SurveyStatus.Active && (
          <DropdownMenuItem
            isLoading={isUnPublishingSurvey}
            onClick={async e => {
              e.stopPropagation();
              await unPublishSurvey();
            }}
            className="text-foreground focus:text-gray-text font-medium cursor-pointer rounded-none"
          >
            Unpublish
          </DropdownMenuItem>
        )}
      </ActionsCell>
      <ModalConfirmEdit
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title="Edit Survey"
        subtitle="Are you sure? This survey is currently active, editing it might significantly impact the results."
        onConfirm={() => {
          setOpenEditDialog(false);
          updateURLWithEditId(row.original.id.toString());
        }}
      />
    </>
  );
};
