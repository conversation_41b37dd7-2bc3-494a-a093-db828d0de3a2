import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, Di<PERSON>Header, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { CopyIcon } from '@radix-ui/react-icons';
import { CloudDownload, CopyCheck } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState, useRef } from 'react';

type ModalPublishSurveyQRCodeProps = {
  openDialog: boolean;
  setOpenDialog: (open: boolean) => void;
  onConfirm: () => void;
  surveyQRCodeImageUrl: string | null;
  surveyId: number;
  title: string;
};
const ModalPublishSurveyQRCode = ({
  openDialog,
  setOpenDialog,
  onConfirm,
  surveyQRCodeImageUrl,
  surveyId,
  title,
}: ModalPublishSurveyQRCodeProps) => {
  const [isCopied, setIsCopied] = useState(false);
  const qrRef = useRef<HTMLImageElement>(null);
  const url = `${process.env.NEXT_PUBLIC_WEB_APP_URL}/public/surveys/${surveyId}`;

  const handleCopy = () => {
    navigator.clipboard.writeText(url);
    setIsCopied(true);
  };

  const handleDownloadQRCode = () => {
    if (!surveyQRCodeImageUrl || !qrRef.current) return;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = qrRef.current;

    // Set canvas dimensions to match the image
    canvas.width = img.naturalWidth || 300;
    canvas.height = img.naturalHeight || 300;

    if (ctx) {
      // Draw white background (JPG doesn't support transparency)
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw the image on canvas
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // Convert to JPG
      const jpgDataUrl = canvas.toDataURL('image/jpeg', 0.9);

      // Download the image
      const link = document.createElement('a');
      link.href = jpgDataUrl;
      link.download = 'survey-qr-code.jpg';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  useEffect(() => {
    if (isCopied) {
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    }
  }, [isCopied]);
  return (
    <Dialog open={openDialog} onOpenChange={setOpenDialog}>
      <DialogContent
        onInteractOutside={e => e.preventDefault()}
        onOpenAutoFocus={e => e.preventDefault()}
        className="!w-[400px] flex flex-col gap-4"
      >
        <DialogHeader>
          <DialogTitle className="flex gap-4">
            <div className="flex items-center gap-4">
              <Image src="/icons/globe-icon.svg" alt="globe" width={24} height={24} className="w-12 h-12" />
              <p className="text-lg font-semibold text-gray-900">{title}</p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="bg-muted w-full p-4 rounded-xl flex flex-col gap-4 items-center justify-center">
          <Image
            ref={qrRef}
            src={surveyQRCodeImageUrl ?? ''}
            alt="survey-qr-code"
            width={200}
            height={200}
            className="w-36 h-auto"
            onLoad={() => {
              // Ensure image is completely loaded before download attempts
              if (qrRef.current) {
                qrRef.current.crossOrigin = 'anonymous';
              }
            }}
          />
          <Button
            variant="ghost"
            className="flex gap-2 text-sm text-gray-700 font-normal"
            onClick={handleDownloadQRCode}
          >
            <CloudDownload className="w-4 h-4" />
            Download QR Code
          </Button>
        </div>
        <div className="flex gap-2 w-full items-center border border-gray-300 rounded-md h-11 py-auto pl-2">
          <div className="flex-1 overflow-hidden">
            <p className="truncate text-gray-500">{url}</p>
          </div>
          <Button
            variant="ghost"
            className="flex-shrink-0 text-[13px] flex gap-2 text-gray-700 font-normal"
            onClick={handleCopy}
          >
            {isCopied ? 'Copied' : 'Copy'}
            {isCopied ? <CopyCheck className="w-4 h-4 text-primary" /> : <CopyIcon className="w-4 h-4 text-primary" />}
          </Button>
        </div>
        <DialogFooter>
          <Button onClick={onConfirm} variant="outline" className="w-full mt-2 text-base font-semibold">
            Done
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ModalPublishSurveyQRCode;
