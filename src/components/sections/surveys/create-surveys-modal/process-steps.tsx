import { useShallow } from 'zustand/react/shallow';
import { useSurveyStore } from '../hooks/useSurveyStore';
import { ListSteps, StatusSurvey } from '../types';

import { SurveyCreationStep } from '../types';
import { useCallback } from 'react';
import { isEmpty } from 'lodash';
import toast from 'react-hot-toast';
import { Circle, CircleCheck } from 'lucide-react';

type ProcessStepProps = {
  status: 'PENDING' | 'ACTIVE' | 'COMPLETE';
  label: string;
  isEditing?: boolean;
  step: SurveyCreationStep;
  isPublicChanged: boolean;
  steps: ListSteps[];
};

const ProcessSteps: React.FC<ProcessStepProps> = ({
  status,
  label,
  step,
  isPublicChanged,
  steps,
}: ProcessStepProps) => {
  const { currentStep, updateCurrentStep, surveyData } = useSurveyStore(
    useShallow(state => ({
      currentStep: state.currentStep,
      updateCurrentStep: state.updateCurrentStep,
      surveyData: state.surveyData,
    })),
  );

  const onClick = useCallback(() => {
    const currentIndex = steps.findIndex(item => item.value === step);

    if (currentIndex === -1) return;

    const previousStepHasData = currentIndex > 0 && !isEmpty(surveyData[steps[currentIndex - 1].value]);
    const currentStepHasData = !isEmpty(surveyData[step]);

    // If the current step is SURVEY_INFO and isPublic has changed, prevent navigation and show toast
    if (currentStep === SurveyCreationStep.SURVEY_INFO && isPublicChanged && step !== currentStep) {
      toast.error(
        'You have changed the public status of the survey. Please save your changes before proceeding to the next step',
      );
      return;
    }

    if (
      (!previousStepHasData && !currentStepHasData) ||
      (isEmpty(surveyData[currentStep]) && !currentStepHasData) ||
      step === currentStep
    ) {
      return;
    }

    updateCurrentStep(step);
  }, [steps, surveyData, step, currentStep, isPublicChanged, updateCurrentStep]);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Tab') {
        event.stopPropagation();
        const currentIndex = steps.findIndex(item => item.value === step);
        const nextStep = currentIndex === steps.length - 1 ? steps[0] : steps[currentIndex + 1];
        const currentStepHasData = !isEmpty(surveyData[step]);

        // If the current step is SURVEY_INFO and isPublic has changed, prevent navigation and show toast
        if (currentStep === SurveyCreationStep.SURVEY_INFO && isPublicChanged) {
          toast.error(
            'You have changed the public status of the survey. Please save your changes before proceeding to the next step',
          );
          return;
        }

        if (currentStepHasData) {
          updateCurrentStep(nextStep.value);
        } else {
          updateCurrentStep(steps[0].value);
        }
      }
    },
    [steps, surveyData, step, currentStep, isPublicChanged, updateCurrentStep],
  );

  return (
    <button
      className="flex items-center cursor-pointer outline-none"
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {status === StatusSurvey.PENDING && (
        <Circle strokeWidth={3} className="stroke-black stroke-width mr-2 w-3.5 h-3.5" />
      )}
      {status === StatusSurvey.ACTIVE && <Circle strokeWidth={3} className="stroke-primary mr-2 w-3.5 h-3.5" />}
      {status === StatusSurvey.COMPLETE && <CircleCheck strokeWidth={3} className="stroke-primary mr-2 w-3.5 h-3.5" />}
      <span className="text-nowrap">{label}</span>
    </button>
  );
};

export default ProcessSteps;
