import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { TriangleAlert } from 'lucide-react';

type ModalConfirmPublicSurveyProps = {
  openDialog: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isPublishing?: boolean;
  title?: string;
  subtitle?: string;
};

const ModalConfirmPublicSurvey = ({
  openDialog,
  onOpenChange,
  onConfirm,
  isPublishing,
  title,
  subtitle,
}: ModalConfirmPublicSurveyProps) => {
  return (
    <Dialog open={openDialog} onOpenChange={onOpenChange}>
      <DialogContent
        onInteractOutside={e => e.preventDefault()}
        onOpenAutoFocus={e => e.preventDefault()}
        className="top-1/2 left-1/2 -translate-y-1/2 p-6 -translate-x-1/2 sm:-translate-x-[calc(50%-100px)] w-full sm:w-auto sm:max-w-[400px] h-auto"
      >
        <div className="flex flex-col justify-center items-start">
          <div className="w-16 h-16 mb-5 rounded-full flex justify-center items-center bg-[#FFFAEB]">
            <div className="w-10 h-10 rounded-full flex justify-center items-center bg-[#FEF0C7]">
              <TriangleAlert className="text-[#DC6803]" width={20} height={20} />
            </div>
          </div>
          <div className="text-foreground font-semibold text-lg mb-2">{title ?? 'Create Public Survey?'}</div>
          <div className="text-sm text-muted-foreground">
            {subtitle ??
              'Are you sure you want to create a public survey? This will make the survey accessible to anyone with the link or QR code.'}
          </div>
        </div>
        <DialogFooter className="mt-8 flex justify-between">
          <Button
            type="button"
            variant="outline"
            className="h-10 flex-1 text-gray-text font-semibold"
            onClick={e => {
              e.stopPropagation();
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="h-10 flex-1 font-semibold"
            disabled={isPublishing}
            isLoading={isPublishing}
            onClick={e => {
              e.stopPropagation();
              onConfirm();
            }}
          >
            Publish
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ModalConfirmPublicSurvey;
