'use client';

import * as SelectPrimitive from '@radix-ui/react-select';
import React, { useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import * as ToggleGroup from '@radix-ui/react-toggle-group';
import { Check, Plus } from 'lucide-react';
import Image from 'next/image';
import { OptionType } from '../../types';

export interface SelectProps extends React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root> {
  setQuestionType: (type: string | null) => void;
  questionType?: string | null;
  listTypes: OptionType[];
}

export const QuestionTypeSelect = ({ setQuestionType, questionType, listTypes }: SelectProps) => {
  const [selectedItem, setSelectedItem] = useState<string | null>(questionType ?? null);

  const toggleGroupItems = useMemo(() => {
    const handleItemClick = (type: string) => {
      setSelectedItem(type === questionType ? null : type);
    };
    return listTypes?.map(item => (
      <ToggleGroup.Item
        key={item.value}
        value={item.value}
        className="w-full h-12 flex items-center p-4 color-[#101828] text-base !justify-between rounded-none !focus:outline-none !focus:visible-none !focus:ring-0 !outline-none hover:!bg-gray-200 !rouned"
        onClick={() => handleItemClick(item.value)}
      >
        <span>{item.label}</span>
        {selectedItem === item.value && <Check className="ml-2 h-5 w-5 text-[#7F56D9]" />}
      </ToggleGroup.Item>
    ));
  }, [selectedItem, listTypes, setSelectedItem, questionType]);

  return (
    <Card className="w-[500px]">
      <CardHeader>
        <CardTitle className="text-[18px] text-center">Add Your First Question</CardTitle>
        <CardDescription className="!mt-4 flex justify-center">
          <Image src="/icons/arrow-circle-down.svg" width={48} height={48} alt="arrow-add-question" />
        </CardDescription>
      </CardHeader>
      <CardContent className="flex justify-center px-[11.5px]">
        <ToggleGroup.Root
          type="single"
          className="ToggleGroup !flex-col w-full gap-0 overflow-hidden !focus:outline-none !focus:ring-0 !outline-none"
        >
          {toggleGroupItems}
        </ToggleGroup.Root>
      </CardContent>
      <CardFooter className="p-4">
        <Button
          variant="default"
          className="w-full h-11 text-[16px] text-[#6149C4] bg-[#F9F5FF]"
          disabled={!selectedItem}
          onClick={() => setQuestionType(selectedItem)}
        >
          <Plus className="mr-2 h-4 w-4" /> Add Question
        </Button>
      </CardFooter>
    </Card>
  );
};
