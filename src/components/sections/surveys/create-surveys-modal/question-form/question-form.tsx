'use client';

import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { Card, CardContent } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import * as SelectPrimitive from '@radix-ui/react-select';
import React from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useSurveyStore } from '../../hooks/useSurveyStore';
import { OptionType, SurveyCreationStep } from '../../types';
import { CreateSurveyQuestionPayload, validateSurveyQuestionSchema } from '../../validations';
import { useFormListener as UseFormListener } from '@/hooks/useFormListener';
import { UserSurveyAnswer } from '@/backend/users/entities/UserSurveyAnswer';
import { useQuestionFormSubmission } from './hooks';
import { CreateForm } from './components';

export const visibleQuestionTypes: string[] = [
  SurveyQuestionType.Rank,
  SurveyQuestionType.SingleSelection,
  SurveyQuestionType.MultipleSelection,
  SurveyQuestionType.Screening,
];

export interface QuestionFormProps extends React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root> {
  questionType?: string | null;
  listTypes?: OptionType[];
  setQuestionType: (type: string | null) => void;
  editItem?: CreateSurveyQuestionPayload & { answers?: UserSurveyAnswer[] };
  getType: (type: string) => string;
  isLoading?: boolean;
  hasAnswers?: boolean;
}

export const QuestionForm = ({ questionType, listTypes, setQuestionType, editItem, getType }: QuestionFormProps) => {
  const { isFrenchEnabled } = useSurveyStore(
    useShallow(state => ({
      isFrenchEnabled: state.surveyData[SurveyCreationStep.SURVEY_INFO]?.isFrenchEnabled ?? false,
      surveyData: state.surveyData,
    })),
  );

  const hasAnswers = editItem && editItem.answers && editItem.answers.length > 0;
  const isItemEditing = !!editItem?.id;
  const { onSubmit, isLoading } = useQuestionFormSubmission({ setQuestionType, editItem });

  return (
    <Form
      schema={validateSurveyQuestionSchema}
      defaultValues={{
        ...editItem,
        isMultiSelectionEnabled: editItem?.isMultiSelectionEnabled,
        hasOtherOption: editItem?.hasOtherOption,
        questionType: questionType ?? '',
        isMultiLang: isFrenchEnabled,
        subtitle:
          !editItem?.subtitle && questionType === SurveyQuestionType.Rank
            ? 'Press and hold to re-order options'
            : editItem?.subtitle,
        translation:
          editItem?.translation && isFrenchEnabled
            ? {
                ...editItem.translation,
                subtitle: editItem.translation.subtitle
                  ? editItem.translation.subtitle
                  : questionType === SurveyQuestionType.Rank
                    ? 'Appuyez et maintenez pour réorganiser les options'
                    : '',
              }
            : isFrenchEnabled
              ? {
                  subtitle:
                    questionType === SurveyQuestionType.Rank ? 'Appuyez et maintenez pour réorganiser les options' : '',
                }
              : undefined,
      }}
      key={`edit-questions-form-${editItem?.id}`}
      onSubmit={onSubmit}
      className="w-full flex flex-col flex-1 items-center"
    >
      <UseFormListener />
      <Card className="w-full max-w-[90%] p-4">
        <CardContent className="flex justify-center p-0 flex-col">
          <CreateForm {...{ isItemEditing, listTypes, setQuestionType, getType, isLoading, hasAnswers }} />
        </CardContent>
      </Card>
    </Form>
  );
};
