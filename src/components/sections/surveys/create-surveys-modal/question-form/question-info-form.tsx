'use client';

import { Locale, SurveyStatus } from '@/backend/surveys/entities/Survey';
import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { UpdateQuestionOrderPayload } from '@/backend/surveys/validations/update-survey-question';
import { But<PERSON> } from '@/components/ui/button';
import React, { useCallback, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { useShallow } from 'zustand/react/shallow';
import { useAddSurvey } from '../../hooks/useAddSurvey';
import { useSurveyStore } from '../../hooks/useSurveyStore';
import { useUpdateQuestionOrder } from '../../hooks/useUpdateQuestionOrder';
import { OptionType, SurveyCreationStep } from '../../types';
import { CreateSurveyPayload, CreateSurveyQuestionPayload, CreateSurveyQuestionState } from '../../validations';
import { QuestionForm } from './question-form';
import { QuestionTypeSelect } from './question-type-select';
import { SurveyList } from './survey-list';

type AddQuestionsProps = {
  onNext: (data: CreateSurveyQuestionState | CreateSurveyQuestionPayload[]) => void;
  onCancel?: () => void;
  isEditing?: boolean;
  isLoading?: boolean;
};
export const listTypes: OptionType[] = [
  { value: SurveyQuestionType.Number, label: 'Number' },
  { value: SurveyQuestionType.SingleSelection, label: 'Single Selection' },
  { value: SurveyQuestionType.MultipleSelection, label: 'Multiple Selection' },
  { value: SurveyQuestionType.Rank, label: 'Rank' },
  { value: SurveyQuestionType.Date, label: 'Date' },
  { value: SurveyQuestionType.Screening, label: 'Screening' },
  { value: SurveyQuestionType.Slider, label: 'Slider' },
  { value: SurveyQuestionType.Text, label: 'Text' },
];
const initialData: CreateSurveyQuestionPayload = {
  id: '',
  order: 0,
  questionType: '',
  isMultiLang: false,
  locale: Locale.EN,
  title: '',
  subtitle: '',
  translation: undefined,
};

const AddQuestionsForm: React.FC<AddQuestionsProps> = ({
  onNext,
  onCancel,
  isEditing,
  isLoading,
}: AddQuestionsProps) => {
  const {
    id: surveyId,
    surveyData,
    surveyLists,
    isFrenchEnabled,
  } = useSurveyStore(
    useShallow(state => ({
      id: state.id,
      surveyData: state.surveyData,
      surveyLists: state.surveyData[SurveyCreationStep.QUESTION_INFO]?.surveyLists,
      isFrenchEnabled: state.surveyData[SurveyCreationStep.SURVEY_INFO]?.isFrenchEnabled ?? false,
    })),
  );
  const { addSurvey, isAddingSurvey } = useAddSurvey();
  const { updateQuestionsOrder, isUpdatingOrder } = useUpdateQuestionOrder(surveyId as number);
  const [editItem, setEditItem] = useState(initialData);
  const [questionType, setQuestionType] = useState<string | null>(null);
  const isPublicSurvey = surveyData[SurveyCreationStep.SURVEY_INFO]?.isPublic;

  const processData = (force = false) => {
    let index = 1;
    const formData = {
      ...surveyData?.[SurveyCreationStep.QUESTION_INFO],
      surveyLists: surveyData?.[SurveyCreationStep.QUESTION_INFO].surveyLists.map(item => {
        // If id starts with "FAKE_", delete the id property and return the modified item
        if (!isFrenchEnabled) {
          item.translation = undefined;
        }

        if (typeof item?.id === 'string' && item?.id?.startsWith('FAKE_') && (isEditing || force)) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { id, ...rest } = item;
          return { ...rest, order: index++ };
        }
        return {
          ...item,
          order: index++,
        };
      }),
    };
    return formData;
  };

  const onSubmit = async () => {
    const formData = processData();

    if (isDraft && isPublicSurvey) {
      return onNext?.(formData as CreateSurveyQuestionState);
    }

    if (isEditing) {
      await handleUpdateOrderQuestion(formData.surveyLists as CreateSurveyQuestionState['surveyLists']);
      // If the survey is public, cancel the process after updating the order
      if (isPublicSurvey) return onCancel?.();
    }

    onNext?.(formData as CreateSurveyQuestionState);
  };

  const onSaveDraftAndCancel = async () => {
    if (isEditing) {
      onCancel?.();
      return;
    }
    const formData = processData(true);
    const body = {
      ...surveyData?.[SurveyCreationStep.SURVEY_INFO],
      status: SurveyStatus.Draft,
      ...formData,
      questions: formData.surveyLists,
    };

    await addSurvey(body as unknown as CreateSurveyPayload);
    toast.success('Draft Saved Successfully');
    onCancel?.();
  };

  const handleUpdateOrderQuestion = useCallback(
    async (surveyLists: CreateSurveyQuestionState['surveyLists']) => {
      const payload = {
        questions: surveyLists?.map(item => ({
          id: item.id,
          order: item.order,
        })),
      };

      return await updateQuestionsOrder(payload as UpdateQuestionOrderPayload);
    },
    [updateQuestionsOrder],
  );

  const surveyListsExist = surveyLists?.length;

  const renderComponent = useMemo(() => {
    const getType = (type: string) => {
      return listTypes?.find(v => v.value === type)?.label ?? '';
    };

    const handleEditItem = (item: CreateSurveyQuestionPayload) => {
      setQuestionType(item.questionType);
      setEditItem(item);
    };
    const renderQuestionForm = () => {
      return (
        <QuestionForm
          listTypes={listTypes}
          setQuestionType={setQuestionType}
          questionType={questionType}
          editItem={editItem}
          getType={getType}
          name="question_type"
        />
      );
    };

    const renderSurveyList = () => {
      setEditItem(initialData);
      return (
        <SurveyList
          listTypes={listTypes}
          setQuestionType={setQuestionType}
          onEditItem={handleEditItem}
          getType={getType}
          onUpdateOrderQuestion={handleUpdateOrderQuestion}
          isUpdatingOrder={isUpdatingOrder}
        />
      );
    };

    const renderQuestionTypeSelect = () => {
      setEditItem(initialData);
      return <QuestionTypeSelect listTypes={listTypes} setQuestionType={setQuestionType} name="question_type" />;
    };

    if (questionType) {
      return renderQuestionForm();
    } else if (surveyListsExist) {
      return renderSurveyList();
    } else {
      return renderQuestionTypeSelect();
    }
  }, [questionType, surveyListsExist, editItem, handleUpdateOrderQuestion, isUpdatingOrder]);

  const loadingState = !surveyListsExist || isLoading || isAddingSurvey;
  const isDraft = surveyData.SURVEY_INFO.status === SurveyStatus.Draft;
  const canNotSaveDraft = isEditing && !isDraft;

  const getButtonText = useCallback(() => {
    if (isPublicSurvey && !isEditing) {
      return 'Publish';
    }
    if (isPublicSurvey && isEditing && isDraft) {
      return 'Publish';
    }
    if (isDraft && isPublicSurvey) {
      return 'Next';
    }
    if (isEditing) {
      return 'Save';
    }
    return 'Set Audience';
  }, [isPublicSurvey, isDraft, isEditing]);

  return (
    <div className="flex flex-col items-center h-full gap-6 mt-4 justify-center m-auto w-full ">
      {renderComponent}
      <div className="mt-[auto] py-8 flex justify-end w-full">
        <div className="flex justify-end gap-3">
          <Button
            variant="secondary"
            className="font-semibold"
            onClick={onSaveDraftAndCancel}
            disabled={loadingState || (!isEditing && !!questionType)}
            isLoading={isAddingSurvey}
          >
            {canNotSaveDraft ? 'Cancel' : 'Save Draft'}
          </Button>
          <Button
            type="button"
            className="font-semibold"
            onClick={onSubmit}
            isLoading={isUpdatingOrder}
            disabled={loadingState || !!questionType}
          >
            {getButtonText()}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddQuestionsForm;
