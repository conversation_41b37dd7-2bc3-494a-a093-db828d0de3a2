import React from 'react';
import { Label } from '@/components/ui/form';
import { SwitchForm } from '@/components/ui/form/switch';

interface OtherOptionSettingsProps {
  hasAnswers?: boolean;
}

export const OtherOptionSettings: React.FC<OtherOptionSettingsProps> = ({ hasAnswers }) => {
  return (
    <div className="flex mt-6 items-center space-x-2 justify-start mb-8">
      <SwitchForm className="border-[#D0D5DD]" name="hasOtherOption" id="hasOtherOption" disabled={hasAnswers} />
      <Label htmlFor="hasOtherOption" className="m-0 leading-tight">
        Enable &quot;Other&quot; (with mandatory text field)
      </Label>
    </div>
  );
};
