import React from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { useQuestionTypeResetErrors } from '../useQuestionTypeReset';
import { QuestionFormProps, visibleQuestionTypes } from '../question-form';
import {
  QuestionTypeSelector,
  SliderFields,
  ScreeningQuestionSettings,
  FormSection,
  OtherOptionSettings,
  FormActions,
  FormFieldGroup,
} from './';
import { GeneralInfo } from '../general-info';
import { OptionsItem } from '../options-item';

const optionName = 'options';

export const CreateForm = React.memo((props: QuestionFormProps & { isItemEditing: boolean }) => {
  const { listTypes, setQuestionType, getType, isItemEditing, isLoading, hasAnswers } = props;
  const { control } = useFormContext();
  const questionType = useWatch({
    control,
    name: 'questionType',
  });

  const isSliderType = questionType === SurveyQuestionType.Slider;
  const isScreeningType = questionType === SurveyQuestionType.Screening;
  const isOptionsVisible = visibleQuestionTypes.includes(questionType);
  const showOtherOption =
    questionType === SurveyQuestionType.SingleSelection || questionType === SurveyQuestionType.MultipleSelection;

  // clear errors when question type changes
  useQuestionTypeResetErrors({ questionType });

  return (
    <>
      {questionType && <div className="text-[18px] p-0">{getType(questionType)}</div>}

      <FormFieldGroup>
        <QuestionTypeSelector listTypes={listTypes} hasAnswers={hasAnswers} />
        {isSliderType && <SliderFields hasAnswers={hasAnswers} />}
      </FormFieldGroup>

      {isScreeningType && <ScreeningQuestionSettings hasAnswers={hasAnswers} optionName={optionName} />}

      <FormSection title="General Info">
        <GeneralInfo />
      </FormSection>

      {isOptionsVisible && (
        <FormSection title="Options">
          <OptionsItem name={optionName} hasAnswers={hasAnswers} isScreeningQuestion={isScreeningType} />
        </FormSection>
      )}

      {showOtherOption && <OtherOptionSettings hasAnswers={hasAnswers} />}

      <FormActions isItemEditing={isItemEditing} isLoading={isLoading} onCancel={() => setQuestionType(null)} />
    </>
  );
});

CreateForm.displayName = 'CreateForm';
