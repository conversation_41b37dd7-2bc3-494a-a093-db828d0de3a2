import React from 'react';
import { Button } from '@/components/ui/button';

interface FormActionsProps {
  isItemEditing: boolean;
  isLoading?: boolean;
  onCancel: () => void;
}

export const FormActions: React.FC<FormActionsProps> = ({ isItemEditing, isLoading, onCancel }) => {
  return (
    <div className="mt-[auto] py-8 flex justify-end w-full">
      <div className="flex justify-end gap-3">
        {isItemEditing && (
          <Button variant="secondary" className="font-semibold" disabled={isLoading} onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" className="font-semibold" disabled={isLoading} isLoading={isLoading}>
          {isItemEditing ? 'Save' : 'Create Question'}
        </Button>
      </div>
    </div>
  );
};
