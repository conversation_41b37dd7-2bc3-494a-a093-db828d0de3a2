import React from 'react';
import { Input, Label } from '@/components/ui/form';

interface SliderFieldsProps {
  hasAnswers?: boolean;
}

const itemClass = 'basis-full col-span-2 md:col-span-1';
const labelClass = 'mb-2 h-5 font-medium inline-block text-[#344054] leading-tight';

export const SliderFields: React.FC<SliderFieldsProps> = ({ hasAnswers }) => {
  return (
    <>
      <div className={itemClass}>
        <div className="h-5 max-h-5 mb-2">
          <Label htmlFor="minValue" className={labelClass}>
            From
          </Label>
        </div>
        <Input type="number" name="minValue" id="minValue" placeholder="1" disabled={hasAnswers} />
      </div>
      <div className={itemClass}>
        <div className="h-5 max-h-5 mb-2">
          <Label htmlFor="maxValue" className={labelClass}>
            To
          </Label>
        </div>
        <Input type="number" name="maxValue" id="maxValue" placeholder="100" disabled={hasAnswers} />
      </div>
    </>
  );
};
