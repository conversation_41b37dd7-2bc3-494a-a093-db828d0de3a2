import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Label } from '@/components/ui/form';
import { SwitchForm } from '@/components/ui/form/switch';

interface ScreeningQuestionSettingsProps {
  hasAnswers?: boolean;
  optionName: string;
}

export const ScreeningQuestionSettings: React.FC<ScreeningQuestionSettingsProps> = ({ hasAnswers, optionName }) => {
  const { setValue, getValues, trigger } = useFormContext();

  const handleMultiSelectionChange = (checked: boolean) => {
    if (!checked) {
      const options = getValues(optionName);
      // if multiple option is off, set all options's eligibility to false only if more than one option is eligible
      if (options && options.length > 0) {
        const eligibleCount = options.filter((option: any) => option.isEligible).length;

        if (eligibleCount > 1) {
          options.forEach((_: unknown, index: number) => {
            setValue(`${optionName}.${index}.isEligible`, false);
          });

          // trigger questionType to show error
          trigger('questionType');
        }
      }
    }
  };

  return (
    <>
      <div className="flex flex-1 mt-2">
        <p className="text-sm text-muted-foreground text-left">
          Screening questions allow setting eligible responses to questions, and only users that pick eligible answers
          will be able to continue the rest of the survey.
        </p>
      </div>
      <div className="flex items-center space-x-2 mt-4 justify-start">
        <SwitchForm
          name={'isMultiSelectionEnabled'}
          id={'isMultiSelectionEnabled'}
          disabled={hasAnswers}
          onCheckedChange={handleMultiSelectionChange}
        />
        <Label htmlFor={'isMultiResponse'} className="text-[14px] font-medium text-[#344054]">
          Enable Multi-Selection
        </Label>
      </div>
    </>
  );
};
