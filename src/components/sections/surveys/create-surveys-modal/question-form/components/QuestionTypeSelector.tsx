import React from 'react';
import { Label, Select } from '@/components/ui/form';
import { SelectItem } from '@/components/ui/form/select';
import { OptionType } from '../../../types';

interface QuestionTypeSelectorProps {
  listTypes?: OptionType[];
  hasAnswers?: boolean;
}

const itemClass = 'basis-full col-span-2';
const labelClass = 'mb-2 h-5 font-medium inline-block text-[#344054] leading-tight';

export const QuestionTypeSelector: React.FC<QuestionTypeSelectorProps> = ({ listTypes, hasAnswers }) => {
  return (
    <div className={itemClass}>
      <div className="h-5 max-h-5 mb-2">
        <Label htmlFor="question_type" className={labelClass}>
          Question Type
        </Label>
      </div>
      <Select name="questionType" placeholder="Select Question Type" className="h-11 text-base" disabled={hasAnswers}>
        {listTypes?.map((item, inx) => (
          <SelectItem key={inx} value={item.value} className="!text-sm">
            {item.label}
          </SelectItem>
        ))}
      </Select>
    </div>
  );
};
