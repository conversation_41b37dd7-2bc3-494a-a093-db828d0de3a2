import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import { useQueryClient } from '@tanstack/react-query';
import { UseFormReturn } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useShallow } from 'zustand/react/shallow';
import { useCreateSurveyQuestions } from '../../../hooks/useCreateSurveyQuestions';
import { useSurveyStore } from '../../../hooks/useSurveyStore';
import { useUpdateSurveyQuestions } from '../../../hooks/useUpdateSurveyQuestions';
import { SurveyCreationStep } from '../../../types';
import { CreateSurveyQuestionPayload } from '../../../validations';
import { insertSurveyQuestion, sortSurveyQuestions } from '@/utils/survey-question-insertion';
import { useQuestionFormValidation } from './useQuestionFormValidation';

interface UseQuestionFormSubmissionProps {
  setQuestionType: (type: string | null) => void;
  editItem?: CreateSurveyQuestionPayload;
}

export const useQuestionFormSubmission = ({ setQuestionType, editItem }: UseQuestionFormSubmissionProps) => {
  const queryClient = useQueryClient();
  const { validateForm, updatePayload } = useQuestionFormValidation();

  const { id: surveyId, updateSurveyData } = useSurveyStore(
    useShallow(state => ({
      id: state.id,
      updateSurveyData: state.updateSurveyData,
      surveyData: state.surveyData,
    })),
  );

  const isItemEditing = !!editItem?.id;
  const isEditing = !!surveyId;

  const { updateSurveyQuestions, isUpdating } = useUpdateSurveyQuestions(surveyId as number);
  const { createSurveyQuestions, isAddingSurveyQuestions } = useCreateSurveyQuestions();

  const onSubmit = async (
    data: CreateSurveyQuestionPayload,
    formHandler?: UseFormReturn<CreateSurveyQuestionPayload>,
  ) => {
    if (!validateForm(data, formHandler)) return;

    const { surveyData } = useSurveyStore.getState();
    const currentQuestions = surveyData[SurveyCreationStep.QUESTION_INFO]?.surveyLists || [];

    if (isEditing) {
      updatePayload(data);
      if (data.id) {
        await updateSurveyQuestions({ status: SurveyStatus.Active, ...data });
        toast.success('Survey updated successfully');
        await queryClient.invalidateQueries({ queryKey: ['surveyQuestions'] });
      } else {
        const order = !currentQuestions?.length ? 1 : currentQuestions.length + 1;
        await createSurveyQuestions({ ...data, status: SurveyStatus.Active, order: order, surveyId: surveyId });
      }
      setQuestionType(null);
    } else {
      const updateSurveyLists = (newLists: CreateSurveyQuestionPayload[]) => {
        updateSurveyData({
          [SurveyCreationStep.QUESTION_INFO]: {
            ...surveyData[SurveyCreationStep.QUESTION_INFO],
            surveyLists: newLists,
          },
        });
        setQuestionType(null);
      };

      if (isItemEditing) {
        const editedIndex = currentQuestions.findIndex((item: CreateSurveyQuestionPayload) => item.id === data.id);
        if (editedIndex !== -1) {
          updatePayload(data);

          // Remove the question from its current position
          const questionsWithoutEdited = currentQuestions.filter(
            (item: CreateSurveyQuestionPayload) => item.id !== data.id,
          );

          // Reinsert the edited question using the same logic as creating new questions
          const updatedQuestions = insertSurveyQuestion({
            currentQuestions: questionsWithoutEdited,
            newQuestionData: data,
            questionType: data.questionType,
          });

          // Apply sorting to ensure screening questions are at the top
          const sortedQuestions = sortSurveyQuestions(updatedQuestions);

          updateSurveyLists(sortedQuestions);
        }
      } else {
        updatePayload(data);

        // Use helper function to insert the question at the correct position
        const updatedQuestions = insertSurveyQuestion({
          currentQuestions,
          newQuestionData: data,
          questionType: data.questionType,
        });

        updateSurveyLists(updatedQuestions);
      }
    }
  };

  const isLoading = isAddingSurveyQuestions || isUpdating;

  return {
    onSubmit,
    isLoading,
  };
};
