import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { Locale } from '@/backend/surveys/entities/Survey';
import { UseFormReturn } from 'react-hook-form';
import { CreateSurveyQuestionPayload } from '../../../validations';
import { visibleQuestionTypes } from '../question-form';

export const useQuestionFormValidation = () => {
  const validateForm = (
    data: CreateSurveyQuestionPayload,
    formHandler?: UseFormReturn<CreateSurveyQuestionPayload>,
  ) => {
    const questionType = data.questionType;

    let isValid = true;

    if (questionType === SurveyQuestionType.Slider) {
      const minValue = data.minValue;
      const maxValue = data.maxValue;
      if (!minValue) {
        formHandler && formHandler.setError('minValue', { message: 'This field is required' }, { shouldFocus: true });
        isValid = false;
      }

      if (!maxValue) {
        formHandler && formHandler.setError('maxValue', { message: 'This field is required' });
        isValid = false;
      }

      if (minValue && maxValue && minValue >= maxValue) {
        formHandler &&
          formHandler.setError('minValue', { message: 'Min value must be less than max value' }, { shouldFocus: true });
        isValid = false;
      }
    }
    if (visibleQuestionTypes.includes(questionType)) {
      if (!data?.options?.length) {
        formHandler && formHandler.setError('options', { message: 'Please add at least one option.' });
        isValid = false;
      }
    }
    return isValid;
  };

  const updatePayload = (item: CreateSurveyQuestionPayload) => {
    const questionType = item.questionType;

    // Clean up question-type-specific fields

    // Remove options array for question types that don't use options
    if (!visibleQuestionTypes.includes(questionType)) {
      delete (item as any).options;
    }

    // Remove slider-specific fields for non-slider questions
    if (questionType !== SurveyQuestionType.Slider) {
      delete (item as any).maxValue;
      delete (item as any).minValue;
    }

    // Remove hasOtherOption for question types that don't support it
    if (questionType !== SurveyQuestionType.SingleSelection && questionType !== SurveyQuestionType.MultipleSelection) {
      delete (item as any).hasOtherOption;
    }

    // Remove isMultiSelectionEnabled for non-screening questions
    if (questionType !== SurveyQuestionType.Screening) {
      delete (item as any).isMultiSelectionEnabled;
    }

    // Remove isEligible from options for non-screening questions
    if (questionType !== SurveyQuestionType.Screening && item?.options?.length) {
      item.options.forEach(option => {
        delete (option as any).isEligible;
      });
    }

    if (questionType === SurveyQuestionType.Rank && !item.subtitle) {
      item.subtitle = 'Press and hold to re-order options';
      if (item.translation && !item.translation.subtitle) {
        item.translation.subtitle = 'Appuyez et maintenez pour réorganiser les options';
      }
    }

    // If the item is not multi-language, clear the translation titles
    if (!item.isMultiLang) {
      item.translation = undefined;
      if (item?.options?.length) {
        item.options?.forEach(option => {
          option.translation = undefined;
        });
      }
      return;
    }

    item.translation = {
      ...item.translation,
      locale: Locale.FR,
      subtitle: item.translation?.subtitle ? item.translation.subtitle : '',
    };
    if (item?.options?.length) {
      item.options?.forEach(option => {
        option.translation = {
          ...option.translation,
          locale: Locale.FR,
        };
      });
    }
  };

  return {
    validateForm,
    updatePayload,
  };
};
