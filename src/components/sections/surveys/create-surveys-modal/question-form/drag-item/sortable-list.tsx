import { DndContext, UniqueIdentifier, DragEndEvent } from '@dnd-kit/core';
import { SortableContext, arrayMove } from '@dnd-kit/sortable';
import type { ReactNode } from 'react';
import React from 'react';
import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { CreateSurveyQuestionPayload } from '../../../validations';
import SurveyListItem from './survey-item';
interface BaseItem {
  id: UniqueIdentifier;
}

interface Props<T extends BaseItem> {
  items: T[];
  onChange(items: T[]): void;
  renderItem(item: T): ReactNode;
}

export function SortableList<T extends CreateSurveyQuestionPayload>({ items, onChange, renderItem }: Props<T>) {
  const handleDragEnd = ({ active, over }: DragEndEvent) => {
    if (!over || active.id === over.id) {
      return;
    }

    const activeIndex = items.findIndex(({ id }) => id === active.id);
    const overIndex = items.findIndex(({ id }) => id === over.id);

    if (activeIndex === -1 || overIndex === -1) {
      return;
    }

    const activeItem = items[activeIndex];
    const overItem = items[overIndex];

    // Check if active item is a screening question - screening questions cannot be moved
    if (activeItem.questionType === SurveyQuestionType.Screening) {
      return;
    }

    // Check if trying to drop over a screening question - not allowed
    if (overItem.questionType === SurveyQuestionType.Screening) {
      return;
    }

    // Find the boundary between screening and non-screening questions
    const firstNonScreeningIndex = items.findIndex(item => item.questionType !== SurveyQuestionType.Screening);

    // If there are no non-screening questions, don't allow any movement
    if (firstNonScreeningIndex === -1) {
      return;
    }

    // Prevent moving non-screening questions above the screening section
    if (overIndex < firstNonScreeningIndex) {
      return;
    }

    // Only allow movement within the non-screening questions section
    const reorderedItems = arrayMove(items, activeIndex, overIndex);

    // Update order values to maintain proper sequence
    const updatedItems = reorderedItems.map((item, index) => ({
      ...item,
      order: index + 1,
    })) as T[];

    onChange(updatedItems);
  };

  return (
    <DndContext onDragEnd={handleDragEnd}>
      <SortableContext items={items}>
        <ul className="list-decimal flex-col marker:font-bold flex justify-start pl-10 w-full ml-0" role="application">
          {items.map(item => (
            <React.Fragment key={item.id}>{renderItem(item)}</React.Fragment>
          ))}
        </ul>
      </SortableContext>
    </DndContext>
  );
}

SortableList.Item = SurveyListItem;
