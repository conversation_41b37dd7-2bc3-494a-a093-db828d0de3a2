'use client';

import { Badge } from '@/components/ui/badge';
import Image from 'next/image';

import { Locale } from '@/backend/surveys/entities/Survey';
import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { EllipsisVertical, Loader2 } from 'lucide-react';
import { CreateSurveyQuestionPayload } from '../../../validations';
import { CSSProperties, FC } from 'react';
import { UserSurveyAnswer } from '@/backend/users/entities/UserSurveyAnswer';
import { cn } from '@/lib/utils';

interface SurveyListItemProps {
  item: CreateSurveyQuestionPayload & { answers?: UserSurveyAnswer[] };
  locale: Locale;
  onEditItem: (item: CreateSurveyQuestionPayload) => void;
  handleDelete: (idToRemove: string | number) => void;
  getType: (type: string) => string;
  isUpdating?: boolean;
  isDeleting?: boolean;
  isUpdatingOrder?: boolean;
  hasResponse?: boolean;
}

const SurveyListItem: FC<SurveyListItemProps> = ({
  item,
  locale,
  onEditItem,
  handleDelete,
  getType,
  isUpdating,
  isDeleting,
  isUpdatingOrder,
  hasResponse,
}) => {
  const isFrench = item?.translation?.title;
  const isScreeningQuestion = item.questionType === SurveyQuestionType.Screening;
  const { attributes, listeners, isDragging, setNodeRef, setActivatorNodeRef, transform, transition } = useSortable({
    id: item.id,
    disabled: isScreeningQuestion, // Disable dragging for screening questions
  });

  const isLoading = isUpdating || isDeleting || isUpdatingOrder;
  const isCannotRemove = hasResponse;
  const style: CSSProperties = {
    opacity: isDragging || isLoading ? 0.5 : undefined,
    transform: CSS.Transform.toString(transform),
    transition,
    pointerEvents: isLoading ? 'none' : 'initial',
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getFieldValue = (item: any, fieldName: keyof Partial<CreateSurveyQuestionPayload>) => {
    return locale === Locale.FR && isFrench ? item?.translation?.[fieldName] : item?.[fieldName];
  };

  return (
    <li className="mb-3 relative" style={style} ref={setNodeRef}>
      <button
        className={cn(
          'DragHandle absolute -left-[47px] top-[2px]',
          isScreeningQuestion && 'opacity-30 cursor-not-allowed',
        )}
        ref={setActivatorNodeRef}
        {...attributes}
        {...listeners}
        disabled={isScreeningQuestion || isLoading}
        title={isScreeningQuestion ? 'Screening questions cannot be reordered' : 'Drag to reorder'}
      >
        <EllipsisVertical size={18} />
      </button>
      <div className="flex justify-start items-center max-w-[80%]">
        <div className="mr-8 min-w-60">
          <h1 className="text-base font-bold">{getFieldValue(item, 'title')}</h1>
          <p> {getFieldValue(item, 'subtitle')}</p>
        </div>
        <div className="actions flex">
          <Image
            src="/icons/edit.svg"
            onClick={() => onEditItem(item)}
            className="mr-4 cursor-pointer"
            alt="next-icon"
            height={20}
            width={20}
          />

          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Image
              src="/icons/trash.svg"
              alt="next-icon"
              className={cn('cursor-pointer', isCannotRemove && 'opacity-50 cursor-not-allowed')}
              onClick={() => {
                if (isCannotRemove) return;
                handleDelete(item.id);
              }}
              height={20}
              width={20}
            />
          )}
        </div>
      </div>
      {item.questionType && (
        <Badge variant="secondary" className="text-xs mr-4 font-medium text-[#175CD3] bg-[#EFF8FF]">
          {getType(item.questionType)}
        </Badge>
      )}
      {!isFrench && (
        <Badge variant="secondary" className="text-xs font-medium text-[#B54708] bg-[#FFFAEB]">
          English Only
        </Badge>
      )}
    </li>
  );
};

export default SurveyListItem;
