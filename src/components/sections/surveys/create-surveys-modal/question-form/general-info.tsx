'use client';

import { Locale } from '@/backend/surveys/entities/Survey';
import { maxLengthQuestion } from '@/components/sections/surveys/validations';
import { Input, Label } from '@/components/ui/form';
import React from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

const itemClass = 'flex flex-col sm:flex-row sm:space-x-6 py-2 rounded-md';

export const GeneralInfo = () => {
  const { control } = useFormContext();
  const isMultiLang = useWatch({
    control,
    name: 'isMultiLang',
  });
  const generateFieldName = (field: string, lang: Locale = Locale.EN) => {
    return lang === Locale.FR ? `translation.${field}` : field;
  };
  const generateFieldDiv = (
    field: string,
    lang: Locale = Locale.EN,
    placeholder = '',
    label = '',
    required = false,
  ) => {
    const placeholderLang = (placeholder += lang === Locale.FR ? ' (FR)' : '');

    return (
      <div className="flex-1">
        <Label
          htmlFor={generateFieldName(field, lang)}
          className="h-5 font-medium inline-block text-[#344054] leading-tight"
        >
          {label} ({lang === Locale.EN ? 'English' : 'French'}) {required && '*'}
        </Label>
        <Input
          maxLength={maxLengthQuestion}
          type="text"
          placeholder={placeholderLang}
          name={generateFieldName(field, lang)}
          id={generateFieldName(field, lang)}
        />
      </div>
    );
  };

  return (
    <div>
      <div className={itemClass}>
        {generateFieldDiv('title', Locale.EN, 'Question Title', 'Question Title', true)}
        {isMultiLang && generateFieldDiv('title', Locale.FR, 'Question Title', 'Question Title', true)}
      </div>
      <div className={itemClass}>
        {generateFieldDiv('subtitle', Locale.EN, 'Question Subtitle', 'Subtitle')}
        {isMultiLang && generateFieldDiv('subtitle', Locale.FR, 'Question Subtitle', 'Subtitle')}
      </div>
    </div>
  );
};
