'use client';

import { Label } from '@/components/ui/form';
import { Switch } from '@/components/ui/form/switch';
import { useState } from 'react';
import { useSurveyStore } from '../../hooks/useSurveyStore';

import { Locale } from '@/backend/surveys/entities/Survey';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Plus } from 'lucide-react';
import { useShallow } from 'zustand/react/shallow';
import { OptionType, SurveyCreationStep } from '../../types';
import { CreateSurveyQuestionPayload, CreateSurveyQuestionState } from '../../validations';
import { SortableList } from './drag-item/sortable-list';
import { useDeleteSurveyQuestion } from '../../hooks/useDeleteSurveyQuestion';

export const SurveyList = ({
  listTypes,
  setQuestionType,
  onEditItem,
  getType,
  onUpdateOrderQuestion,
  isUpdatingOrder,
}: {
  listTypes: OptionType[];
  setQuestionType: (type: string | null) => void;
  onEditItem: (item: CreateSurveyQuestionPayload) => void;
  getType: (type: string) => string;
  onUpdateOrderQuestion: (surveyLists: CreateSurveyQuestionState['surveyLists']) => Promise<void>;
  isUpdatingOrder?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const {
    id: surveyId,
    surveyLists,
    locale,
    surveyData,
    updateSurveyData,
    isFrenchEnabled,
  } = useSurveyStore(
    useShallow(state => ({
      id: state.id,
      surveyLists: state.surveyData[SurveyCreationStep.QUESTION_INFO]?.surveyLists,
      locale: state.surveyData[SurveyCreationStep.QUESTION_INFO]?.locale,
      updateSurveyData: state.updateSurveyData,
      isFrenchEnabled: state.surveyData[SurveyCreationStep.SURVEY_INFO]?.isFrenchEnabled ?? false,
      surveyData: state.surveyData,
    })),
  );
  const { deleteQuestion, isDeleting } = useDeleteSurveyQuestion(surveyId as number);

  const isEditing = !!surveyId;
  const hadResponse = (surveyData[SurveyCreationStep.SURVEY_INFO] as any)?.successfulCompletions > 0;

  const changeLocale = (locale: Locale) => {
    const { surveyData } = useSurveyStore.getState();

    updateSurveyData({
      [SurveyCreationStep.QUESTION_INFO]: {
        ...surveyData[SurveyCreationStep.QUESTION_INFO],
        locale: locale,
      },
    });
  };

  const handleDelete = async (idToRemove: string | number) => {
    const { surveyData } = useSurveyStore.getState();
    const updatedLists = surveyLists.filter((item: CreateSurveyQuestionPayload) => item.id !== idToRemove);

    if (isEditing && typeof idToRemove === 'number') {
      await deleteQuestion(idToRemove);
    }

    updateSurveyData({
      [SurveyCreationStep.QUESTION_INFO]: {
        ...surveyData[SurveyCreationStep.QUESTION_INFO],
        surveyLists: updatedLists,
      },
    });

    if (isEditing) {
      let index = 1;
      const newData = updatedLists.map(item => {
        return { ...item, order: index++ };
      });

      await onUpdateOrderQuestion(newData);
    }
  };

  return (
    <div className="min-h-[500px] flex-1 mt-5 flex flex-col items-start w-full space-x-2 justify-start ">
      {isFrenchEnabled && (
        <div className="flex items-center space-x-2 justify-start mb-8">
          <Switch
            className="border-[#D0D5DD]"
            name="lang"
            onCheckedChange={checked => {
              changeLocale(checked ? Locale.FR : Locale.EN);
            }}
            checked={locale === Locale.FR}
            id="lang"
          />
          <Label htmlFor="lang" className="m-0 leading-tight">
            View French
          </Label>
        </div>
      )}
      <SortableList
        items={surveyLists}
        renderItem={item => (
          <SortableList.Item
            item={item}
            locale={locale}
            onEditItem={onEditItem}
            handleDelete={handleDelete}
            getType={getType}
            isDeleting={isDeleting}
            isUpdatingOrder={isUpdatingOrder}
            hasResponse={hadResponse}
          />
        )}
        onChange={items => {
          const { surveyData } = useSurveyStore.getState();
          updateSurveyData({
            [SurveyCreationStep.QUESTION_INFO]: {
              ...surveyData[SurveyCreationStep.QUESTION_INFO],
              surveyLists: items,
            },
          });
        }}
      />

      <div className="mt-6">
        <Popover open={open} onOpenChange={open => setOpen(open)}>
          <PopoverTrigger asChild>
            <Button className="text-primary-brand bg-[#F9F5FF] h-10">
              <Plus />
              <span className="ml-2">Add Question</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent align="start" className="p-0">
            {listTypes?.map(item => (
              <div
                key={item.value?.toString()}
                className="py-2.5 px-3.5 hover:bg-primary-foreground first:rounded-t-md last:rounded-b-md cursor-pointer text-foreground text-base font-medium"
                onClick={() => {
                  setOpen(false);
                  setQuestionType(item.value);
                }}
              >
                {item.label}
              </div>
            ))}
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
