import React from 'react';
import { useFormContext } from 'react-hook-form';

interface UseQuestionTypeResetProps {
  questionType: string;
}

export const useQuestionTypeResetErrors = ({ questionType }: UseQuestionTypeResetProps) => {
  const { clearErrors } = useFormContext();

  // Reset form fields when question type changes
  React.useEffect(() => {
    if (questionType) {
      // Clear all form errors first
      clearErrors();
    }
  }, [questionType, clearErrors]);
};
