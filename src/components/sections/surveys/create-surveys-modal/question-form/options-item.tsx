'use client';

import { Button } from '@/components/ui/button';
import { Input, Label } from '@/components/ui/form';
import { Plus } from 'lucide-react';
import Image from 'next/image';
import React, { useCallback } from 'react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { Locale } from '@/backend/surveys/entities/Survey';
import { ErrorMessage } from '@hookform/error-message';
import { maxOptions } from '@/components/sections/surveys/validations';
import { cn } from '@/lib/utils';
import { SwitchForm } from '@/components/ui/form/switch';

type OptionsItemProps = {
  name: string;
  isScreeningQuestion?: boolean;
  hasAnswers?: boolean;
};

export const OptionsItem = ({ name: listsName, isScreeningQuestion, hasAnswers }: OptionsItemProps) => {
  const { control, formState, clearErrors, setFocus, setValue, trigger } = useFormContext();
  const isMultiLang = useWatch({
    control,
    name: 'isMultiLang',
  });
  const isMultiSelectionEnabled = useWatch({
    control,
    name: 'isMultiSelectionEnabled',
  });

  const { fields, append, remove } = useFieldArray({ control, name: listsName });

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>, index: number) => {
      if (event.key !== 'Enter') return;
      event.preventDefault();
      if (index === fields.length - 1) {
        append({
          title: '',
          translation: {
            locale: Locale.FR,
            title: '',
          },
        });
        setTimeout(() => {
          setFocus(`${listsName}.${index + 1}.title`);
        }, 0);
      }
    },
    [append, fields.length, listsName, setFocus],
  );

  const handleEligibleChange = useCallback(
    (index: number, checked: boolean) => {
      // if screening question and multi selection is not enabled, set all other options to false
      if (isScreeningQuestion && !isMultiSelectionEnabled && checked) {
        fields.forEach((_, idx) => {
          if (idx !== index) {
            setValue(`${listsName}.${idx}.isEligible`, false);
          }
        });
      }

      // Trigger validation to clear custom validation errors
      trigger('questionType');
    },
    [isScreeningQuestion, isMultiSelectionEnabled, trigger, fields, setValue, listsName],
  );

  const fieldDivs = React.useMemo(() => {
    const generateFieldName = (index: number, lang: Locale = Locale.EN) => {
      return lang === Locale.FR ? `${listsName}.${index}.translation.title` : `${listsName}.${index}.title`;
    };
    const generateFieldDiv = (index: number, lang: Locale = Locale.EN) => (
      <div className="flex-1" key={`${index}-${lang}`}>
        <Label htmlFor={generateFieldName(index, lang)} className="font-medium text-[14px] text-[#344054] mb-1.5">
          Option {index + 1} ({lang === Locale.EN ? 'English' : 'French'})
        </Label>
        <Input
          type="text"
          maxLength={maxOptions}
          placeholder={`Type Option${lang === Locale.FR ? ' (FR)' : ''}`}
          name={generateFieldName(index, lang)}
          id={generateFieldName(index, lang)}
          onKeyDown={event => {
            if (lang === Locale.FR) return;
            handleKeyDown(event, index);
          }}
          disabled={hasAnswers}
          className="h-[44px] text-[16px] py-[10px] px-[14px]"
        />
      </div>
    );

    return fields
      .filter(field => !(field as any).isOther)
      .map((option, optionIndex) => (
        <div key={option.id} className="flex flex-col w-full">
          <div className="flex justify-center items-center gap-4 mb-2">
            <Button
              type="button"
              className={cn('bg-transparent p-0 h-6 w-6 z-10 mt-2', hasAnswers && 'opacity-50 cursor-not-allowed')}
              disabled={hasAnswers}
              onClick={event => {
                event.preventDefault();
                remove(optionIndex);
              }}
            >
              <Image src="/icons/trash.svg" alt="delete-option" height={24} width={24} />
            </Button>

            <div className="flex flex-col sm:flex-row sm:space-x-4 w-full">
              {generateFieldDiv(optionIndex, Locale.EN)}
              {isMultiLang && generateFieldDiv(optionIndex, Locale.FR)}
            </div>
          </div>

          {isScreeningQuestion && (
            <div className="flex gap-4">
              <div className="h-6 w-6" />
              <div className="flex items-center space-x-2 mb-6">
                <SwitchForm
                  name={`${listsName}.${optionIndex}.isEligible`}
                  id={`${listsName}.${optionIndex}.isEligible`}
                  onCheckedChange={checked => handleEligibleChange(optionIndex, checked)}
                  disabled={hasAnswers}
                />
                <Label
                  htmlFor={`${listsName}.${optionIndex}.isEligible`}
                  className="text-[14px] font-medium text-[#344054]"
                >
                  Mark as Eligible
                </Label>
              </div>
            </div>
          )}
        </div>
      ));
  }, [fields, listsName, hasAnswers, handleKeyDown, isMultiLang, isScreeningQuestion, remove, handleEligibleChange]);

  return (
    <div>
      {fieldDivs}
      {formState.errors[listsName] && (
        <div>
          <ErrorMessage
            name={listsName}
            render={({ message }) => {
              return <span className="text-red-500 text-sm">{message}</span>;
            }}
          />
        </div>
      )}
      {!hasAnswers && (
        <Button
          variant="default"
          type="button"
          className="w-fit h-11 mt-4 text-[16px] text-[#6149C4] bg-[#F9F5FF]"
          onClick={() => {
            append({
              title: '',
              translation: {
                locale: Locale.FR,
                title: '',
              },
            });
            clearErrors(listsName);
            trigger('questionType');
          }}
        >
          <Plus className="mr-2 h-4 w-4" /> Add Option
        </Button>
      )}
    </div>
  );
};
