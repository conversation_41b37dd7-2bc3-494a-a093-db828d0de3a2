import { useEffect, useRef } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useFormListener as UseFormListener } from '@/hooks/useFormListener';
import { Skeleton } from '@/components/ui/skeleton';
import SelectInfiniteScroll from '@/components/ui/form/select-infinite-scroll';
import { PaginationPayload } from '@/types/pagination';
import { SelectOptions } from '@/types';
import { Company } from '@/backend/companies/entities/Company';
import { api } from '@/lib/http';
import { SwitchForm } from '@/components/ui/form/switch';
import { maxLengthDescription, maxLengthTitle } from '../../validations';
import InputMoney from '@/components/ui/form/input-money';
import { Checkbox, Input, InputDate, Label, Textarea } from '@/components/ui/form';
import { Tooltip, TooltipArrow, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { DropzoneField } from '@/components/ui/form/dropzone';
import { Info } from 'lucide-react';

const itemClass = 'basis-full col-span-2 md:col-span-1';
const labelClass = 'mb-2 h-5 font-medium inline-block text-[#344054] leading-tight';

const FormContent: React.FC<{
  isLoadingSurvey?: boolean;
  onIsPublicSurveyChange: (isPublic: boolean) => void;
  isAccountManager?: boolean;
  isEditing?: boolean;
}> = ({ isLoadingSurvey, onIsPublicSurveyChange, isAccountManager, isEditing }) => {
  const { setValue, clearErrors, control, trigger, getValues } = useFormContext();
  const isUnlimited = useWatch({
    control,
    name: 'isUnlimited',
  });
  const isMultipleResponses = useWatch({ control, name: 'isMultipleResponses' });
  const watchFrenchEnabled = useWatch({ control, name: 'isFrenchEnabled' });

  const isPublicSurvey = useWatch({ control, name: 'isPublic' });

  // Store initial values when editing
  const initialValuesRef = useRef<{
    isMultipleResponses: boolean;
    responsesPerUser: string | number;
  } | null>(null);

  // Initialize initial values on mount when editing
  useEffect(() => {
    if (isEditing && initialValuesRef.current === null) {
      initialValuesRef.current = {
        isMultipleResponses: getValues('isMultipleResponses'),
        responsesPerUser: getValues('responsesPerUser'),
      };
    }
  }, [isEditing, getValues]);

  useEffect(() => {
    onIsPublicSurveyChange?.(!!isPublicSurvey);
  }, [isPublicSurvey, onIsPublicSurveyChange]);

  // Clear French validation errors when French is disabled
  useEffect(() => {
    if (!watchFrenchEnabled) {
      clearErrors(['translation.title', 'translation.description']);
    }
  }, [watchFrenchEnabled, clearErrors]);

  return (
    <>
      <UseFormListener />
      <div className="grid grid-cols-2 gap-6 mt-4 max-w-[95%]">
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-2">
            <Label htmlFor="companyId" className={labelClass}>
              Sponsoring Company
            </Label>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-11" />
          ) : (
            <SelectInfiniteScroll
              placeholder="Select Company"
              queryKey="select-list-company"
              name="companyId"
              apiFunction={async ({ page = 1, pageSize }: PaginationPayload<unknown>): Promise<SelectOptions[]> => {
                return api.companies.getCompanies({ page: page, pageSize: pageSize }).then(data => {
                  return (
                    data?.data?.data?.map((audience: Company) => ({
                      value: audience.id,
                      label: audience.name,
                    })) || []
                  );
                });
              }}
            />
          )}
        </div>
        <div className={itemClass}>
          <div>
            <div className="h-5 max-h-5 mb-2">
              <Label htmlFor="expiryDate" className={labelClass}>
                Expiry Date (Timezone: UTC+0)
              </Label>
            </div>
            {isLoadingSurvey ? (
              <Skeleton className="w-full h-11" />
            ) : (
              <InputDate name="expiryDate" id="expiryDate" showLocalDate />
            )}
          </div>
        </div>

        <div className="flex-1 flex items-center space-x-2 justify-start font-semibold text-[16px] col-span-2">
          {isLoadingSurvey ? (
            <Skeleton className="w-[55px] h-7 rounded-2xl" />
          ) : (
            <SwitchForm className="border-[#D0D5DD]" name={'isFrenchEnabled'} id={'isFrenchEnabled'} />
          )}

          <Label htmlFor={'isFrenchEnabled'} className="m-0 text-sm">
            Enable French
          </Label>
        </div>
        <div className="flex-1 flex items-center space-x-2 justify-start font-semibold text-[16px] col-span-2">
          {isLoadingSurvey ? (
            <Skeleton className="w-[55px] h-7 rounded-2xl" />
          ) : (
            <SwitchForm
              className="border-[#D0D5DD]"
              name="isPublic"
              id="isPublic"
              disabled={isAccountManager}
              onCheckedChange={checked => {
                if (checked) {
                  setValue('isMultipleResponses', false);
                  setValue('responsesPerUser', 1);
                  clearErrors('responsesPerUser');
                } else {
                  // When unchecking isPublic
                  if (isEditing && initialValuesRef.current) {
                    // Restore initial values when editing
                    setValue('isMultipleResponses', initialValuesRef.current.isMultipleResponses);
                    setValue('responsesPerUser', initialValuesRef.current.responsesPerUser);
                  } else {
                    // reset value when create new survey and uncheck isPublic
                    setValue('isMultipleResponses', false);
                    setValue('responsesPerUser', 1);
                  }
                  clearErrors('responsesPerUser');
                }
              }}
            />
          )}

          <Label htmlFor="isPublic" className="text-sm">
            Set survey as Public
            <p className="text-sm text-gray-500">
              This will generate a QR code and a public link to access the survey. <br />
              Additionally, the audience targeting step will be skipped as a result of this setting.
            </p>
          </Label>
        </div>
        <div className="font-semibold text-[16px] mt-4 col-span-2">Title</div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-2">
            <Label htmlFor="title" className={labelClass}>
              English
            </Label>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-11" />
          ) : (
            <Input maxLength={maxLengthTitle} type="text" name="title" id="title" placeholder="Title of survey" />
          )}
        </div>
        {watchFrenchEnabled ? (
          <div className={itemClass}>
            <div className="h-5 max-h-5 mb-2">
              <Label htmlFor="translation.title" className={labelClass}>
                French
              </Label>
            </div>
            {isLoadingSurvey ? (
              <Skeleton className="w-full h-11" />
            ) : (
              <Input
                maxLength={maxLengthTitle}
                type="text"
                name="translation.title"
                id="translation.title"
                placeholder="Title of survey (FR)"
              />
            )}
          </div>
        ) : (
          <div className={itemClass} />
        )}
        <div className="font-semibold text-[16px] mt-4 col-span-2">Description</div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-2">
            <Label htmlFor="description" className={labelClass}>
              English
            </Label>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-[138px]" />
          ) : (
            <Textarea
              maxLength={maxLengthDescription}
              name="description"
              id="description"
              rows={5}
              placeholder="Enter a description..."
            />
          )}
        </div>
        {watchFrenchEnabled ? (
          <div className={itemClass}>
            <div className="h-5 max-h-5 mb-2">
              <Label htmlFor="translation.description" className={labelClass}>
                French
              </Label>
            </div>
            {isLoadingSurvey ? (
              <Skeleton className="w-full h-[138px]" />
            ) : (
              <Textarea
                maxLength={maxLengthDescription}
                name="translation.description"
                rows={5}
                id="translation.description"
                placeholder="Enter a description... (FR)"
              />
            )}
          </div>
        ) : (
          <div className={itemClass} />
        )}
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-2">
            <Label htmlFor="compensation" className={labelClass}>
              Compensation
            </Label>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-11" />
          ) : (
            <InputMoney name="compensation" id="compensation" placeholder="Example: $25" />
          )}
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-2 flex justify-between">
            <Label htmlFor="maxParticipants" className={labelClass}>
              Max Responses
            </Label>
            <div className={'flex'}>
              <Checkbox
                className="border-[#D0D5DD]"
                name="isUnlimited"
                id="isUnlimited"
                onCheckedChange={() => {
                  setValue('maxParticipants', '');
                  clearErrors('maxParticipants');
                }}
              />{' '}
              <label className="text-sm ml-2 leading-tight" htmlFor="isUnlimited">
                Unlimited
              </label>
            </div>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-11" />
          ) : (
            <Input
              type="number"
              name="maxParticipants"
              id="maxParticipants"
              disabled={isUnlimited}
              placeholder="Example: 300"
            />
          )}
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-2">
            <Label htmlFor="time" className={labelClass}>
              Estimated Completion Time (mins)
            </Label>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-11" />
          ) : (
            <Input type="number" name="time" id="time" placeholder="Example: 10" />
          )}
        </div>

        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-2 flex justify-between">
            <Label htmlFor="responsesPerUser" className={labelClass}>
              Responses per User
            </Label>
            <div className={'flex'}>
              <Checkbox
                key={`MultipleResponses-${isPublicSurvey}-${isMultipleResponses}`}
                className="border-[#D0D5DD]"
                name="isMultipleResponses"
                id="isMultipleResponses"
                disabled={isPublicSurvey}
                onCheckedChange={checked => {
                  if (checked.valueOf() === true) {
                    setValue('responsesPerUser', '');
                    trigger('responsesPerUser');
                  } else {
                    setValue('responsesPerUser', 1);
                    clearErrors('responsesPerUser');
                  }
                }}
              />{' '}
              <label className="text-sm ml-2 leading-tight" htmlFor="isMultipleResponses">
                Multiple
              </label>
            </div>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-11" />
          ) : (
            <Input
              key={`ResponsesPerUser-${isPublicSurvey}-${isMultipleResponses}`}
              type="number"
              name="responsesPerUser"
              id="responsesPerUser"
              disabled={!isMultipleResponses || isPublicSurvey}
              placeholder="Example: 5"
              onChange={() => {
                trigger('responsesPerUser');
              }}
            />
          )}
        </div>

        <div className="font-semibold text-[16px] mt-4 col-span-2">Images</div>
        {!isPublicSurvey && (
          <div className={itemClass}>
            <div className="min-h-5 mb-4 flex items-center">
              <Label htmlFor="image" className={labelClass + ' mb-0 mr-2'}>
                Card Image
              </Label>
              <TooltipProvider>
                <Tooltip delayDuration={0}>
                  <TooltipTrigger asChild>
                    <Info width={20} height={20} color="#717070" className="hover:stroke-[#6149C4]" />
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <TooltipArrow />
                    <h1 className="font-semibold mb-1">Instructions</h1>
                    <p className="font-medium">Size: 343 height x 182 width</p>
                    <p className="font-medium">Format: .jpg, .png</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            {isLoadingSurvey ? <Skeleton className="w-full h-[182px]" /> : <DropzoneField name="image" />}
          </div>
        )}

        <div className={itemClass}>
          <div className="min-h-5 mb-4 flex items-center">
            <Label htmlFor="image" className={labelClass + ' mb-0 mr-2'}>
              Background Image
            </Label>
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <Info width={20} height={20} color="#717070" className="hover:stroke-[#6149C4]" />
                </TooltipTrigger>
                <TooltipContent side="right">
                  <TooltipArrow />
                  <h1 className="font-semibold mb-1">Instructions</h1>
                  <p className="font-normal">Size: 375 height x 210 width</p>
                  <p className="font-normal">Format: .jpg, .png</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          {isLoadingSurvey ? (
            <Skeleton className="w-full h-[182px]" />
          ) : (
            <DropzoneField name="backgroundImage" cropSize={{ width: 375, height: 210 }} />
          )}
        </div>
        {!isPublicSurvey && (
          <div className={'flex col-span-2'}>
            <Checkbox className="border-[#D0D5DD]" name="isPinned" id="c1" />{' '}
            <label className="text-sm ml-2 leading-tight" htmlFor="c1">
              Prioritize this survey (pin to top){' '}
            </label>
          </div>
        )}
      </div>
    </>
  );
};

export default FormContent;
