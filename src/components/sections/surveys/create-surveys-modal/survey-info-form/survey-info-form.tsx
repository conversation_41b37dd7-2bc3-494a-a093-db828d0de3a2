'use client';

import { useMemo, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';
import toast from 'react-hot-toast';

import { Locale, SurveyStatus } from '@/backend/surveys/entities/Survey';
import { ButtonSubmit } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormActions } from '@/components/ui/form/form';

import { useShallow } from 'zustand/react/shallow';
import { useAddSurvey } from '../../hooks/useAddSurvey';
import { useSurveyStore } from '../../hooks/useSurveyStore';
import { SurveyCreationStep } from '../../types';
import { CreateSurveyInfoPayload, validateSurveyInfoSchema } from '../../validations';
import { isEmpty } from 'lodash';
import { parse } from 'date-fns';
import { DATE_FORMATS, formatDate } from '@/utils/date-format';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import FormContent from './survey-info-form-content';

type SurveyInfoFormProps = {
  onNext: (data: CreateSurveyInfoPayload, isDirty: boolean, isSaveDraft?: boolean) => void;
  onCancel: () => void;
  isEditing?: boolean;
  isLoading?: boolean;
  isLoadingSurvey?: boolean;
  onIsPublicSurveyChange: (isPublic: boolean) => void;
};
enum Method {
  DRAFT = 'DRAFT',
  SAVE = 'SAVE',
}

const SurveyInfoForm = ({
  onNext,
  onCancel,
  isEditing,
  isLoading,
  isLoadingSurvey,
  onIsPublicSurveyChange,
}: SurveyInfoFormProps) => {
  const { surveyData } = useSurveyStore(
    useShallow(state => ({
      surveyData: state.surveyData,
    })),
  );
  const { addSurvey, isAddingSurvey } = useAddSurvey();
  const formRef = useRef<FormActions<typeof validateSurveyInfoSchema>>(null);
  const methodRef = useRef<Method>(Method.SAVE);

  const { user } = useCurrentUser();
  const isAccountManager = useMemo(() => user?.role === AdminRole.AccountManager, [user]);

  const processData = (values: CreateSurveyInfoPayload, status: SurveyStatus): CreateSurveyInfoPayload => ({
    ...values,
    backgroundImage: values.backgroundImage ?? '',
    image: values.image ?? '',
    translation: values.isFrenchEnabled
      ? {
          ...values.translation,
          title: values.translation?.title ?? '',
          description: values.translation?.description ?? '',
          locale: Locale.FR,
        }
      : undefined,
    maxParticipants: values.isUnlimited ? null : Number(values.maxParticipants),
    status: values?.status ? values?.status : status,
    expiryDate: values?.expiryDate ? formatDate(values.expiryDate, 'yyyy-MM-dd') : '',
    responsesPerUser: values?.responsesPerUser ? Number(values.responsesPerUser) : 1,
  });

  const onSubmit = async (values: CreateSurveyInfoPayload, formHandler?: UseFormReturn<CreateSurveyInfoPayload>) => {
    if (!formRef.current) return;
    if (!values.isUnlimited && !values.maxParticipants) {
      formRef.current?.formHandler?.setError('maxParticipants', { message: 'Max Participants must be greater than 0' });
      return;
    }

    let formData = processData(values, SurveyStatus.Active);
    if (methodRef.current == Method.DRAFT && !isEditing) {
      formData = processData(values, SurveyStatus.Draft);
      await addSurvey(formData);
      toast.success('Draft Saved Successfully');
      onCancel();
      return;
    }

    const isDirty = Object.keys(formHandler?.formState?.dirtyFields || {}).length > 0;

    onNext(formData, isDirty, methodRef.current == Method.DRAFT);
  };

  const defaultValues = surveyData[SurveyCreationStep.SURVEY_INFO];
  const isDraft = surveyData.SURVEY_INFO.status === SurveyStatus.Draft;
  const canNotSaveDraft = isEditing && !isDraft;
  const expiryDate = defaultValues.expiryDate ? parse(defaultValues.expiryDate, 'yyyy-MM-dd', new Date()) : undefined;

  return (
    <Form
      schema={validateSurveyInfoSchema}
      onSubmit={onSubmit}
      formRef={formRef}
      key={`edit-survey-form-${defaultValues.id}-${isAccountManager}`}
      defaultValues={
        {
          ...defaultValues,
          companyId: defaultValues.companyId ? Number(defaultValues.companyId) : undefined,
          compensation: defaultValues.compensation >= 0 ? `$${defaultValues.compensation}` : `$`,
          isUnlimited: !defaultValues.maxParticipants,
          isFrenchEnabled: isEmpty(defaultValues) && !isEditing ? true : !!defaultValues?.translation,
          expiryDate: expiryDate ? formatDate(expiryDate, DATE_FORMATS.ISO_DATE) : '',
          isMultipleResponses: defaultValues.responsesPerUser && defaultValues.responsesPerUser > 1,
          responsesPerUser: defaultValues.responsesPerUser ? String(defaultValues.responsesPerUser) : 1,
          isPublic: isAccountManager ? true : defaultValues.isPublic,
        } as unknown as CreateSurveyInfoPayload
      }
      className="flex flex-col gap-6 flex-1 w-full"
    >
      <FormContent
        isLoadingSurvey={isLoadingSurvey}
        onIsPublicSurveyChange={onIsPublicSurveyChange}
        isAccountManager={isAccountManager}
        isEditing={isEditing}
      />
      <div className="mt-[auto]">
        <div className="flex justify-end gap-3">
          <ButtonSubmit
            type={canNotSaveDraft ? 'button' : 'submit'}
            variant="secondary"
            className="font-semibold"
            disabled={isAddingSurvey || isLoading}
            isLoading={isAddingSurvey}
            onClick={() => {
              if (canNotSaveDraft) {
                onCancel();
                return;
              }
              methodRef.current = Method.DRAFT;
            }}
            disableOnError
          >
            {canNotSaveDraft ? 'Cancel' : 'Save Draft'}
          </ButtonSubmit>
          <ButtonSubmit
            type="submit"
            className="font-semibold"
            disabled={isAddingSurvey}
            onClick={() => {
              methodRef.current = Method.SAVE;
            }}
            isLoading={isLoading}
            disableOnError
          >
            {isDraft ? 'Next' : isEditing ? 'Save' : 'Set Questions'}
          </ButtonSubmit>
        </div>
      </div>
    </Form>
  );
};

export default SurveyInfoForm;
