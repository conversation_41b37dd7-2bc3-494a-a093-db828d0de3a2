'use client';

import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import { UpdateQuestionOrderPayload } from '@/backend/surveys/validations/update-survey-question';
import ModalConfirmDiscard from '@/components/common/modal-confirm-discard';
import { useSurvey } from '@/components/sections/surveys/hooks/useSurvey';
import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { useFormDirtyStore } from '@/hooks/useFormListener';
import { removeFakeIdQuestions } from '@/lib/utils';
import { Cross2Icon } from '@radix-ui/react-icons';
import FocusTrap from 'focus-trap-react';
import { isEmpty } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useShallow } from 'zustand/react/shallow';
import { useFilterDataStore } from '../../audiences/hooks/useFilterDataStore';
import { usePublishPublicSurvey } from '../hooks/pubic-survey/usePublishPublicSurvey';
import { useUpdatePublicSurveyQuestionsOrder } from '../hooks/pubic-survey/useUpdatePublicSurveyQuestionsOrder';
import { useAddSurvey } from '../hooks/useAddSurvey';
import { useSurveyAudiences } from '../hooks/useSurveyAudiences';
import { useSurveyModalQueryHandler } from '../hooks/useSurveyModalQueryHandler';
import { useSurveyQuestions } from '../hooks/useSurveyQuestions';
import { SurveyDataType, useSurveyStore } from '../hooks/useSurveyStore';
import { useUpdateSurvey } from '../hooks/useUpdateSurvey';
import { StatusSurvey, SurveyCreationStep } from '../types';
import { CreateSurveyInfoPayload, CreateSurveyQuestionState, PublishAudiencePayload } from '../validations';
import AudienceInfoForm from './audience-form/audience-info-form';
import ModalConfirmPublishPublicSurvey from './modal-confirm-public-survey';
import ProcessSteps from './process-steps';
import QuestionInfoForm from './question-form/question-info-form';
import SurveyInfoForm from './survey-info-form/survey-info-form';

const LIST_STEPS = [
  {
    label: 'Survey Info',
    value: SurveyCreationStep.SURVEY_INFO,
  },
  {
    label: 'Questions',
    value: SurveyCreationStep.QUESTION_INFO,
  },
  {
    label: 'Audience',
    value: SurveyCreationStep.AUDIENCE,
  },
];

const getStatus = (
  stepValue: SurveyCreationStep,
  surveyData: SurveyDataType,
  currentStep: SurveyCreationStep,
): StatusSurvey => {
  const stepData = surveyData[stepValue];

  if (!isEmpty(stepData)) {
    return StatusSurvey.COMPLETE;
  }
  return stepValue === currentStep ? StatusSurvey.ACTIVE : StatusSurvey.PENDING;
};

const CreateSurveysModal: React.FC<{
  handleCreatePublicSurveySuccess?: (id: number, qrImageUrl: string) => void;
}> = ({ handleCreatePublicSurveySuccess }) => {
  const handleSurveyModalQuery = useSurveyModalQueryHandler();
  const { isFormDirty } = useFormDirtyStore(
    useShallow(state => ({
      isFormDirty: state.isFormDirty,
    })),
  );

  const {
    id: surveyId,
    surveyData,
    currentStep,
    updateCurrentStep,
    updateSurveyData,
    resetSurvey,
  } = useSurveyStore(
    useShallow(state => ({
      id: state.id,
      surveyData: state.surveyData,
      currentStep: state.currentStep,
      updateCurrentStep: state.updateCurrentStep,
      updateSurveyData: state.updateSurveyData,
      resetSurvey: state.resetSurvey,
    })),
  );

  const { survey, isLoadingSurvey } = useSurvey(surveyId as number);
  const { surveyQuestions, isLoadingSurveyQuestions } = useSurveyQuestions(surveyId as number);
  const { surveyAudiences, isLoadingSurveyAudiences } = useSurveyAudiences(surveyId as number);
  const { updateSurvey, isUpdating } = useUpdateSurvey(surveyId as number);
  const { resetFilterTypes, resetSpecificUsers } = useFilterDataStore();
  const [openDialog, setOpenDialog] = useState(false);
  const { addSurvey, isAddingSurvey } = useAddSurvey();
  const { updatePublicSurveyQuestionsOrder, isUpdatingPublicSurveyQuestionsOrder } =
    useUpdatePublicSurveyQuestionsOrder(surveyId as number);
  const { publishPublicSurvey, isPublishingPublicSurvey } = usePublishPublicSurvey();
  const [isPublicSurvey, setIsPublicSurvey] = useState(surveyData[SurveyCreationStep.SURVEY_INFO]?.isPublic ?? false);
  const [isOpenPublishPublicModal, setIsOpenPublishPublicModal] = useState(false);
  const [pendingQuestionData, setPendingQuestionData] = useState<CreateSurveyQuestionState | null>(null);

  // Track original isPublic value and if it has been changed
  const originalIsPublicRef = useRef<boolean | null>(null);
  const [isPublicChanged, setIsPublicChanged] = useState(false);

  const isPublishing = isPublishingPublicSurvey || isAddingSurvey || isUpdatingPublicSurveyQuestionsOrder || isUpdating;

  const handleCloseAndReset = () => {
    resetSurvey();
    handleSurveyModalQuery(); //  Not passing an ID will remove 'editId' from the URL
    resetFilterTypes();
    resetSpecificUsers();
    setIsPublicChanged(false);
    originalIsPublicRef.current = null;
  };

  useEffect(() => {
    if (!isEmpty(survey?.data)) {
      const surveyIsPublic = !!survey?.data?.isPublic;
      updateSurveyData({
        [SurveyCreationStep.SURVEY_INFO]: {
          ...survey?.data,
          expiryDate: survey?.data?.expiryDate ?? '',
          maxParticipants: survey?.data?.maxParticipants,
          isUnlimited: !survey?.data?.maxParticipants,
          companyId: survey?.data.companyId ? String(survey.data.companyId) : undefined,
          isFrenchEnabled: !!survey?.data?.translation,
        } as unknown as CreateSurveyInfoPayload,
      });

      // Store the original isPublic value
      if (originalIsPublicRef.current === null && surveyId) {
        originalIsPublicRef.current = surveyIsPublic;
        setIsPublicSurvey(surveyIsPublic);
      }
    }
    if (surveyQuestions?.data?.length) {
      updateSurveyData({
        [SurveyCreationStep.QUESTION_INFO]: {
          locale: survey?.data.locale,
          surveyLists: surveyQuestions?.data,
        } as unknown as CreateSurveyQuestionState,
      });
    }
    if (surveyAudiences?.data?.length) {
      updateSurveyData({
        [SurveyCreationStep.AUDIENCE]: {
          audienceId: surveyAudiences?.data[0]?.id ? String(surveyAudiences?.data[0].id) : undefined,
        } as unknown as PublishAudiencePayload,
      });
    }
  }, [survey, updateSurveyData, surveyQuestions, surveyAudiences, surveyId]);

  // Update isPublicChanged whenever isPublicSurvey changes
  useEffect(() => {
    if (surveyId && originalIsPublicRef.current !== null) {
      setIsPublicChanged(isPublicSurvey !== originalIsPublicRef.current);
    }
  }, [isPublicSurvey, surveyId]);

  const handleOpenDialog = () => {
    if (isFormDirty) {
      return setOpenDialog(true);
    }
    handleCloseAndReset();
  };

  const isEditing = !!surveyId;

  const isLoading = isLoadingSurvey || isLoadingSurveyQuestions || isLoadingSurveyAudiences || isUpdating;

  const isDraft = surveyData[SurveyCreationStep.SURVEY_INFO]?.status === SurveyStatus.Draft;

  const visibleSteps = useMemo(() => {
    return isPublicSurvey ? LIST_STEPS.filter(step => step.value !== SurveyCreationStep.AUDIENCE) : LIST_STEPS;
  }, [isPublicSurvey]);

  const handlePublishSurvey = async (data: CreateSurveyQuestionState) => {
    const info = surveyData[SurveyCreationStep.SURVEY_INFO];
    const questions = data.surveyLists || [];
    const rawQuestions = removeFakeIdQuestions(questions);
    // Ensure each question has an id property (undefined if missing)
    const questionsWithId = rawQuestions.map((q: any, idx: number) => {
      return {
        ...q,
        id: typeof q.id === 'string' || typeof q.id === 'number' ? q.id : `AUTO_${idx}`,
        order: idx + 1, // Ensure proper order
      };
    });

    try {
      // If is a draft survey, first update question order and then publish
      if (isDraft && surveyId) {
        // Update question order
        const questionsToUpdate = questionsWithId
          .filter(q => typeof q.id === 'number')
          .map(q => ({
            id: Number(q.id),
            order: Number(q.order),
          }));

        // Only update question order if we have valid questions (must have at least one)
        if (questionsToUpdate.length > 0) {
          // The schema expects at least one item in the questions array
          const updateOrderPayload = {
            questions: questionsToUpdate,
          };

          await updatePublicSurveyQuestionsOrder(updateOrderPayload as UpdateQuestionOrderPayload);
        }

        // Publish the survey with empty payload
        await publishPublicSurvey(surveyId);

        toast.success('Survey Published Successfully!');
        handleCreatePublicSurveySuccess?.(surveyId, survey?.data?.publicQRCode ?? '');
      } else {
        // Original behavior for non-draft surveys
        const survey = await addSurvey({
          ...info,
          status: 'Active',
          isPublic: true,
          questions: questionsWithId,
        });
        handleCreatePublicSurveySuccess?.(survey.id, survey.publicQRCode ?? '');
        toast.success('Survey Published Successfully!');
      }
    } catch (e) {
      toast.error('Failed to publish survey.');
    } finally {
      setIsOpenPublishPublicModal(false);
      resetSurvey();
      handleCloseAndReset();
    }
  };

  return (
    <>
      <Dialog open onOpenChange={handleOpenDialog}>
        <DialogContent
          isWrapped={true}
          className="flex flex-col lg:!max-w-[80%] lg:max-h-[95%] lg:!w-[1080px] p-0 overflow-hidden"
          onInteractOutside={e => {
            e.preventDefault();
            if (isOpenPublishPublicModal) {
              return;
            }
            handleOpenDialog();
          }}
        >
          <div className="flex flex-col lg:flex-row w-full overflow-auto">
            <div className="basis-0 p-8 border-b lg:border-b-0">
              <h1 className="font-semibold text-lg">{isEditing ? 'Edit Survey' : 'New Survey'}</h1>
              <FocusTrap focusTrapOptions={{ clickOutsideDeactivates: true, allowOutsideClick: true }}>
                <div>
                  {visibleSteps.map((v, inx) => {
                    return (
                      <div className="mt-4 lg:mt-8" key={inx}>
                        <ProcessSteps
                          status={getStatus(v.value, surveyData, currentStep)}
                          label={v.label}
                          step={v.value}
                          isPublicChanged={isPublicChanged}
                          steps={visibleSteps}
                        />
                      </div>
                    );
                  })}
                </div>
              </FocusTrap>
            </div>
            <div className="overflow-y-auto w-full m-auto h-full lg:border-l">
              {currentStep === SurveyCreationStep.SURVEY_INFO && (
                <div className="flex flex-col p-8 w-full h-full">
                  <div className="font-medium text-sm uppercase">Survey Info</div>
                  <div className="font-semibold text-[16px] mt-10">General</div>
                  <SurveyInfoForm
                    isEditing={isEditing}
                    isLoading={isLoading}
                    isLoadingSurvey={isLoadingSurvey}
                    onIsPublicSurveyChange={setIsPublicSurvey}
                    onNext={async (data, isDirty, isSaveDraft) => {
                      if (isEditing) {
                        if (isDirty) {
                          // If the survey was changed to public, remove audience data
                          if (data.isPublic) {
                            // Clear audience data by setting a null audienceId
                            updateSurveyData({
                              [SurveyCreationStep.AUDIENCE]: { audienceId: null } as PublishAudiencePayload,
                            });
                          }
                          // When updating a survey to be public, make sure audience is not included
                          await updateSurvey(data);

                          // Reset the isPublicChanged flag after saving
                          originalIsPublicRef.current = data.isPublic;
                          setIsPublicChanged(false);
                        }
                        if (isSaveDraft) {
                          handleCloseAndReset();
                          return;
                        }
                      } else {
                        updateSurveyData({
                          [SurveyCreationStep.SURVEY_INFO]: data,
                        });
                      }
                      updateCurrentStep(SurveyCreationStep.QUESTION_INFO);
                    }}
                    onCancel={handleCloseAndReset}
                  />
                </div>
              )}
              {currentStep === SurveyCreationStep.QUESTION_INFO && (
                <div className="flex flex-col p-8 w-full  h-full">
                  <div className="font-medium text-sm uppercase">Questions</div>
                  <QuestionInfoForm
                    isEditing={isEditing}
                    isLoading={isLoading}
                    onNext={async data => {
                      updateSurveyData({
                        [SurveyCreationStep.QUESTION_INFO]: data as CreateSurveyQuestionState,
                      });

                      if (!isPublicSurvey) {
                        updateCurrentStep(SurveyCreationStep.AUDIENCE);
                        return;
                      }

                      // Check if isPublic is true, show confirmation dialog
                      if ((isPublicSurvey && !isEditing) || isDraft) {
                        setPendingQuestionData(data as CreateSurveyQuestionState);
                        setIsOpenPublishPublicModal(true);
                        return;
                      }
                    }}
                    onCancel={handleCloseAndReset}
                  />
                </div>
              )}
              {currentStep === SurveyCreationStep.AUDIENCE && (
                <div className="flex flex-col p-8 w-full h-full">
                  <div className="font-medium text-sm uppercase">Audience</div>
                  <AudienceInfoForm
                    isEditing={isEditing}
                    onNext={() => {
                      resetSurvey();
                      handleCloseAndReset();
                    }}
                    isLoading={isLoading}
                    onCancel={handleCloseAndReset}
                  />
                </div>
              )}
            </div>
          </div>
          <DialogClose
            className="absolute right-6 top-6 rounded-lg"
            aria-label="Close"
            onClick={e => {
              e.preventDefault();
              handleOpenDialog();
            }}
          >
            <Cross2Icon className="w-6 h-6" />
          </DialogClose>
        </DialogContent>
      </Dialog>
      <ModalConfirmDiscard
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
        onConfirm={handleCloseAndReset}
        title="Discard Changes"
        subtitle="Are you sure? Changes you have made won't be saved."
      />
      <ModalConfirmPublishPublicSurvey
        openDialog={isOpenPublishPublicModal}
        onOpenChange={setIsOpenPublishPublicModal}
        isPublishing={isPublishing}
        title="Publish as Public Survey"
        subtitle="Every user that has access to the QR Code and public link for this survey will be able to complete it."
        onConfirm={() => {
          handlePublishSurvey(pendingQuestionData as CreateSurveyQuestionState);
        }}
      />
    </>
  );
};

export default CreateSurveysModal;
