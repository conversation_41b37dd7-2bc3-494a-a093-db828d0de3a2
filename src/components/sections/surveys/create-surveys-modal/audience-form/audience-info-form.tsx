'use client';

import { Audience } from '@/backend/audiences/entities/Audience';
import { CreateAudiencePayload } from '@/backend/audiences/validations/add-update-audience';
import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import PrimaryBadge from '@/components/common/badges/primary';
import { Button } from '@/components/ui/button';
import { Form, Label } from '@/components/ui/form';
import { FormActions } from '@/components/ui/form/form';
import SelectInfiniteScroll from '@/components/ui/form/select-infinite-scroll';
import { api } from '@/lib/http';
import { SelectOptions } from '@/types';
import { cn, removeFakeIdQuestions } from '@/lib/utils';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useShallow } from 'zustand/react/shallow';
import FilterItems from '../../../audiences/filter-items';
import { FilterDataStore, useFilterDataStore } from '../../../audiences/hooks/useFilterDataStore';
import { useInitFilterData } from '../../../audiences/hooks/useInitFilterData';
import { useAddSurvey } from '../../hooks/useAddSurvey';
import { PublishAudiencesHook, usePublishAudiences } from '../../hooks/usePublishAudiences';
import { useSurveyStore } from '../../hooks/useSurveyStore';
import ModalConfirmPublish from '../../modal-confirm-publish';
import { SurveyCreationStep } from '../../types';
import { CreateSurveyPayload, PublishAudiencePayload, publishAudienceSchema } from '../../validations';
import { useFilterByAudience } from '@/components/sections/audiences/hooks/useFilterByAudience';
import { listParamKeys } from '@/components/sections/audiences/hooks/useUpdateAudience';
import { useFormListener as UseFormListener } from '@/hooks/useFormListener';
import { PaginationPayload } from '@/types/pagination';
import { useDraftForAudience } from '../../hooks/useDraftForAudience';
import pluralize from 'pluralize';

type AudienceInfoFormProps = {
  onNext: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  isEditing?: boolean;
};

enum Method {
  DRAFT = 'DRAFT',
  PUBLISH = 'PUBLISH',
}
const AudienceInfoForm: React.FC<AudienceInfoFormProps> = ({
  onNext,
  isLoading,
  onCancel,
  isEditing,
}: AudienceInfoFormProps) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [defaultAudience, setDefaultAudience] = useState<SelectOptions | undefined>(undefined);
  const {
    id: surveyId,
    surveyData,
    surveyLists,
    audience,
    surveyInfo,
  } = useSurveyStore(
    useShallow(state => ({
      id: state.id,
      surveyData: state.surveyData,
      surveyLists: state.surveyData[SurveyCreationStep.QUESTION_INFO]?.surveyLists,
      audience: state.surveyData[SurveyCreationStep.AUDIENCE],
      surveyInfo: state.surveyData[SurveyCreationStep.SURVEY_INFO],
      updateSurveyData: state.updateSurveyData,
    })),
  );
  const defaultValues = isEditing ? surveyData[SurveyCreationStep.AUDIENCE] : undefined;
  const [audienceId, setAudienceId] = useState<number | undefined>(defaultValues?.audienceId ?? undefined);
  const { dataFilterByAudience, isLoadingAudience } = useFilterByAudience(audienceId ?? null);
  const { addSurvey, isAddingSurvey } = useAddSurvey();
  const { publishAudiences, isPublishingAudiences } = usePublishAudiences();
  const { mutateAsync: draftForAudience, isPending: isDraftingForAudience } = useDraftForAudience();
  const { isLoadingFilterData } = useInitFilterData(dataFilterByAudience?.data.userIds ?? []);
  const methodRef = useRef<Method>(Method.PUBLISH);
  const { removeFilterType, resetFilterTypes } = useFilterDataStore();
  const { usersIncluded, ...restState } = useFilterDataStore(state => state);
  const formRef = useRef<FormActions<typeof publishAudienceSchema>>(null);
  const isFirstRenderComplete = useRef<boolean>(false);
  const filterRef = useRef<unknown[]>([]);
  const isUsedManualFilters =
    dataFilterByAudience! && !!dataFilterByAudience.data.id && !dataFilterByAudience.data.name;

  const isLoadingState = isLoading || isAddingSurvey || isPublishingAudiences || isLoadingFilterData;
  const canNotSaveDraft = isEditing && surveyData.SURVEY_INFO.status !== SurveyStatus.Draft;
  const [isManualFilterSelected, setIsManualFilterSelected] = useState(false);
  useEffect(() => {
    if (Number(defaultValues?.audienceId) === Number(audienceId) && !isLoadingAudience) {
      setIsManualFilterSelected(isUsedManualFilters);
    }
  }, [isUsedManualFilters, defaultValues, audienceId, isLoadingAudience]);

  const defaultFilters = useMemo(() => {
    if (!audience || !dataFilterByAudience) return [];
    const filters = [];
    const { data } = dataFilterByAudience;
    resetFilterTypes();
    for (const key of listParamKeys) {
      const _key = key as keyof Audience;
      if (data?.[_key]) {
        filters.push({ type: key, value: data[_key] });
        removeFilterType(key);
      }
    }

    return filters;
  }, [audience, dataFilterByAudience, removeFilterType, resetFilterTypes]);

  useEffect(() => {
    if (!formRef.current) return;

    const shouldSetDefaultFilter = !defaultValues?.audienceId && !audienceId;

    if (isUsedManualFilters) {
      formRef.current.formHandler.setValue(
        'filters',
        (filterRef.current.length === 0 ? defaultFilters : filterRef.current) as Array<{
          type: string;
          value?: string[] | number[];
        }>,
      );
    } else if (audienceId) {
      formRef.current.formHandler.setValue(
        'filters',
        defaultFilters as Array<{ type: string; value?: string[] | number[] }>,
      );
    } else if (!isFirstRenderComplete.current && shouldSetDefaultFilter) {
      formRef.current.formHandler.setValue('filters', [{ type: 'specialtyIds' }] as Array<{
        type: string;
        value?: string[] | number[];
      }>);
      removeFilterType('specialtyIds');
      isFirstRenderComplete.current = true;
    }
  }, [
    defaultFilters,
    isEditing,
    audienceId,
    removeFilterType,
    isFirstRenderComplete,
    isUsedManualFilters,
    defaultValues?.audienceId,
  ]);

  const onSubmit = async (values: PublishAudiencePayload) => {
    if (
      isEditing &&
      surveyInfo.status === SurveyStatus.Active &&
      defaultValues?.audienceId &&
      Number(defaultValues.audienceId) === values.audienceId &&
      !isUsedManualFilters
    ) {
      onNext();
      return;
    }

    if (methodRef.current == Method.DRAFT) {
      await onSaveDraft(values);
      return;
    }

    const { filters } = values;
    const filterPayload = processFilterPayload(filters);
    defaultFilters.forEach(({ type }) => {
      const key = type as keyof Omit<Partial<CreateAudiencePayload>, 'name'>;
      if (!filterPayload[key]) {
        filterPayload[key] = null;
      }
    });

    const updatedFilters = processFilters(filterPayload);
    filterRef.current = updatedFilters;

    setOpenDialog(true);
  };

  const processFilterPayload = useCallback(
    (filters: PublishAudiencePayload['filters']): Partial<CreateAudiencePayload> => {
      if (!filters) return {};
      return filters.reduce((acc, filter) => {
        const { type, value } = filter;
        let key = type as keyof Omit<FilterDataStore, 'usersIncluded'> | 'specialtyIds';
        if (key === 'specialtyIds') key = 'specialties';
        const filterValue = value?.length === restState[key]?.length ? [] : value;
        return {
          ...acc,
          [type]: filterValue,
        };
      }, {});
    },
    [restState],
  );

  const processFilters = useCallback(
    (filterPayload: Omit<Partial<CreateAudiencePayload>, 'name' | 'numberOfUsers'>) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const filters: any[] = [];
      listParamKeys.forEach(key => {
        const _key = key as keyof Omit<Partial<CreateAudiencePayload>, 'name' | 'numberOfUsers'>;
        if (filterPayload?.[_key]) {
          filters.push({ type: key, value: filterPayload[_key] });
        }
      });

      return filters;
    },
    [],
  );

  const handleConfirmPublish = async (isPublishArg: boolean, values: PublishAudiencePayload) => {
    const { filters, audienceId } = values;
    const filterPayload = processFilterPayload(filters);
    defaultFilters.forEach(({ type }) => {
      const key = type as keyof Omit<Partial<CreateAudiencePayload>, 'name'>;
      if (!filterPayload[key]) {
        filterPayload[key] = null;
      }
    });
    const audienceData = audienceId && !isUsedManualFilters ? { audienceId } : filterPayload;
    if (isEditing) {
      await publishAudiences({
        surveyId,
        ...audienceData,
        isPublish: isPublishArg,
      } as PublishAudiencesHook);
    } else {
      const data = {
        ...surveyInfo,
        status: SurveyStatus.Active,
        questions: removeFakeIdQuestions(surveyLists),
        audience: { ...audienceData, isPublish: isPublishArg } as unknown as PublishAudiencePayload,
        isPublish: isPublishArg,
      } as unknown as CreateSurveyPayload;
      await addSurvey(data);
    }
    toast.success('Survey Published Successfully!');
    setOpenDialog(false);
    onNext();
  };

  const onSaveDraft = async (values: PublishAudiencePayload) => {
    if (isEditing && surveyId) {
      const { filters, audienceId } = values;
      const filterPayload = processFilterPayload(filters);
      defaultFilters.forEach(({ type }) => {
        const key = type as keyof Omit<Partial<CreateAudiencePayload>, 'name'>;
        if (!filterPayload[key]) {
          filterPayload[key] = null;
        }
      });
      const audienceData = audienceId && !isUsedManualFilters ? { audienceId } : filterPayload;
      await draftForAudience({
        surveyId,
        ...audienceData,
        isPublish: false,
      });
      onCancel();
      return;
    }
    const { filters, audienceId } = values;
    const filterPayload = processFilterPayload(filters);
    const audienceData = audienceId ? { audienceId } : filterPayload;
    const data = {
      ...surveyInfo,
      status: SurveyStatus.Draft,
      questions: removeFakeIdQuestions(surveyLists),
      isPublish: false,
      audience: {
        isPublish: false,
        ...audienceData,
      } as unknown as PublishAudiencePayload,
    } as unknown as CreateSurveyPayload;

    await addSurvey(data);
    toast.success('Draft Saved Successfully');
    setOpenDialog(false);
    onCancel();
  };

  return (
    <div className="flex flex-col items-center h-full flex-wrap gap-6 mt-4 ba justify-center m-auto w-full">
      <div className="flex flex-1 w-full">
        <Form
          schema={publishAudienceSchema}
          onSubmit={onSubmit}
          formRef={formRef}
          defaultValues={{
            audienceId: audienceId ? Number(audienceId) : undefined,
            filters: defaultFilters as Array<{ type: string; value?: string[] | number[] }>,
          }}
          key={`edit-audiences-form-${defaultAudience?.value}`}
          className="w-full flex flex-col flex-1 h-full"
        >
          <UseFormListener />
          <div className="flex flex-col w-full justify-between min-h-[500px]">
            <div className="flex items-end gap-6 mb-8">
              <div className="max-w-[320px] w-full">
                <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
                  Custom Audience
                </Label>
                <SelectInfiniteScroll
                  placeholder="Select Custom Audience"
                  queryKey="infinite-scroll"
                  name="audienceId"
                  defaultSelected={defaultAudience}
                  searchable
                  defaultOption={{
                    value: isManualFilterSelected && defaultValues?.audienceId ? Number(defaultValues?.audienceId) : 0,
                    label: 'Select Custom Audience',
                  }}
                  onChange={option => {
                    resetFilterTypes();
                    setAudienceId(option.value ? Number(option.value) : undefined);
                    if (!option.value) {
                      removeFilterType('specialtyIds');
                      formRef.current?.formHandler?.setValue('filters', [{ type: 'specialtyIds' }] as Array<{
                        type: string;
                        value?: string[] | number[];
                      }>);
                      formRef.current?.formHandler?.trigger('audienceId');
                    }
                  }}
                  apiFunction={async ({
                    page = 1,
                    pageSize,
                    search,
                  }: PaginationPayload<unknown>): Promise<SelectOptions[]> => {
                    return api.audiences.getAudiences({ page: page, pageSize: pageSize, search }).then(data => {
                      const options: SelectOptions[] = data?.data?.data?.map((audience: Audience) => ({
                        value: audience.id,
                        label: audience.name,
                      }));
                      if (!options || options.length === 0) return [];

                      if (!defaultAudience) {
                        const defaultOption = options.find(v => v.value === Number(defaultValues?.audienceId));

                        if (defaultOption) {
                          setDefaultAudience(defaultOption);
                          setAudienceId(Number(defaultOption.value));
                        } else if (defaultValues?.audienceId && !search) {
                          setAudienceId(Number(defaultValues?.audienceId));
                        }
                      }
                      return options;
                    });
                  }}
                />
              </div>
            </div>
            <hr />

            <div className="flex mt-8 mb-3.5">
              <div className="font-bold text-base mr-6">Audience Settings</div>
              <PrimaryBadge className="bg-[#EFF8FF] text-[#175CD3] font-medium text-xs">
                {usersIncluded} Verified {pluralize('User', usersIncluded)} Included
              </PrimaryBadge>
            </div>
            <div className="flex-1 flex flex-col">
              <FilterItems
                showNoFiltersMessage
                isLoadingFilterData={isLoadingFilterData || isLoadingAudience}
                canAddFilter={!audienceId || isUsedManualFilters}
              />
            </div>
          </div>
          <div className="mt-8 sm:mt-[auto]">
            <div className="flex justify-end gap-3">
              <Button
                type={canNotSaveDraft ? 'button' : 'submit'}
                variant="secondary"
                disabled={isLoadingState || isDraftingForAudience}
                className={cn(
                  'font-semibold',
                  !isEditing && 'group-[.is-invalid]:pointer-events-none  group-[.is-invalid]:opacity-55',
                )}
                onClick={() => {
                  if (!canNotSaveDraft) {
                    methodRef.current = Method.DRAFT;
                    return;
                  }
                  onCancel();
                }}
              >
                {canNotSaveDraft ? 'Cancel' : 'Save Draft'}
              </Button>
              <Button
                type="submit"
                disabled={isLoadingState}
                onClick={() => {
                  methodRef.current = Method.PUBLISH;
                }}
                className="font-semibold"
              >
                Publish
              </Button>
            </div>
          </div>
          <ModalConfirmPublish
            onConfirm={handleConfirmPublish}
            title={`Publish Survey for ${usersIncluded} ${pluralize('User', usersIncluded)}`}
            openDialog={openDialog}
            isConfirming={isLoadingState}
            setOpenDialog={setOpenDialog}
          />
        </Form>
      </div>
    </div>
  );
};

export default AudienceInfoForm;
