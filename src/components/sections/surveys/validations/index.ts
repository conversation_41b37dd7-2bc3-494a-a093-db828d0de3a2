import { Locale } from '@/backend/surveys/entities/Survey';
import { z } from 'zod';
import { visibleQuestionTypes } from '@/components/sections/surveys/create-surveys-modal/question-form/question-form';
import { parse, startOfDay, startOfTomorrow } from 'date-fns';
import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';

export const maxLengthQuestion = 200;
export const maxOptions = 50;
export const maxLengthTitle = 50;
export const maxLengthDescription = 250;

const idSchema = z.union([z.string(), z.number()]).optional();

export const ExpiryDateSchema = z
  .string({ required_error: 'Expiry Date is required' })
  .transform(val => {
    if (!val) return null;
    const date = parse(val, 'yyyy-MM-dd', new Date());
    return !isNaN(date.getTime()) ? date.toISOString() : null;
  })
  .nullable()
  .refine(
    val => {
      if (!val) return false;
      const expiryDate = startOfDay(new Date(val));
      return expiryDate >= startOfTomorrow();
    },
    { message: 'Expiry Date must be in the future' },
  );

export const validateSurveyInfoSchema = z
  .object({
    id: idSchema,
    compensation: z.preprocess(
      val => {
        return (val as string)?.replaceAll('$', '');
      },
      z.coerce
        .number({
          invalid_type_error: 'Compensation is required',
        })
        .min(0, { message: 'Compensation must be greater than or equal to 0' }),
    ),
    maxParticipants: z.coerce.number().optional().nullable(),
    responsesPerUser: z.coerce.number().min(1, { message: 'Responses per user must be equal or greater than 1' }),
    companyId: z.coerce.number({ required_error: 'Company is required', invalid_type_error: 'Company is required' }),
    time: z.coerce.number({ required_error: 'Time is required', invalid_type_error: 'Time is required' }),
    expiryDate: ExpiryDateSchema.nullable().refine(val => val !== null, { message: 'Expiry Date is required' }),
    locale: z.string().optional(),
    title: z
      .string({ required_error: 'Title is required' })
      .trim()
      .min(1, { message: 'Title is required' })
      .max(maxLengthTitle, { message: `Must be ${maxLengthTitle} or fewer characters long` }),

    description: z
      .string({ required_error: 'Description is required' })
      .trim()
      .min(1, { message: 'Description is required' })
      .max(maxLengthDescription, { message: `Must be ${maxLengthDescription} or fewer characters long` }),

    translation: z
      .object({
        title: z
          .string({ required_error: 'Title is required' })
          .trim()
          .max(maxLengthTitle, { message: `Must be ${maxLengthTitle} or fewer characters long` })
          .optional()
          .nullable(),

        locale: z.string().optional(),
        description: z
          .string({ required_error: 'Description is required' })
          .trim()
          .max(maxLengthDescription, { message: `Must be ${maxLengthDescription} or fewer characters long` })
          .optional()
          .nullable(),
      })
      .optional()
      .nullable(),
    image: z.any().optional(),
    backgroundImage: z.any().optional(),
    isPinned: z.boolean().optional().default(false),
    isUnlimited: z.boolean().optional().default(false),
    isMultipleResponses: z.boolean().optional().default(false),
    status: z.string().optional(),
    isFrenchEnabled: z.boolean().optional(),
    isPublic: z.boolean().optional().default(false),
  })
  .superRefine((obj, ctx) => {
    if (obj.isFrenchEnabled) {
      if (!obj?.translation?.title) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Title is required',
          path: ['translation', 'title'],
        });
      }

      if (!obj?.translation?.description) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Description is required',
          path: ['translation', 'description'],
        });
      }
    }

    if (obj.maxParticipants && obj.responsesPerUser && obj.maxParticipants < obj.responsesPerUser) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Responses per user must be less than max responses',
        path: ['responsesPerUser'],
      });
    }

    if (obj.isMultipleResponses && obj.responsesPerUser < 2) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Responses per user must be greater than 1',
        path: ['responsesPerUser'],
      });
    }
  });

export type CreateSurveyInfoPayload = z.infer<typeof validateSurveyInfoSchema>;

const idObjectSchema = z.object({
  id: idSchema,
  surveyId: idSchema,
});

export const validateSurveyQuestionSchema = z
  .object({
    id: z.union([z.string(), z.number()]),
    surveyId: idSchema,
    questionType: z.string(),
    isMultiLang: z.boolean(),
    status: z.string().optional(),
    locale: z.nativeEnum(Locale),
    isMultiSelectionEnabled: z.boolean().optional(),
    title: z
      .string()
      .max(maxLengthQuestion, { message: `Must be ${maxLengthQuestion} or fewer characters long` })
      .min(1, { message: 'Title is required' }),
    subtitle: z
      .string()
      .max(maxLengthQuestion, { message: `Must be ${maxLengthQuestion} or fewer characters long` })
      .optional(),
    translation: z
      .object({
        ...idObjectSchema.shape,
        locale: z.nativeEnum(Locale).default(Locale.EN).optional(),
        title: z
          .string()
          .max(maxLengthQuestion, { message: `Must be ${maxLengthQuestion} or fewer characters long` })
          .optional(),
        subtitle: z
          .string()
          .max(maxLengthQuestion, { message: `Must be ${maxLengthQuestion} or fewer characters long` })
          .optional(),
      })
      .nullable()
      .optional(),
    options: z
      .array(
        z.object({
          ...idObjectSchema.shape,
          title: z
            .string()
            .max(maxOptions, { message: `Must be ${maxOptions} or fewer characters long` })
            .min(1, 'Option title is required'),
          translation: z
            .object({
              ...idObjectSchema.shape,
              locale: z.nativeEnum(Locale).default(Locale.EN).optional(),
              title: z
                .string()
                .max(maxOptions, { message: `Must be ${maxOptions} or fewer characters long` })
                .optional(),
            })
            .nullable()
            .optional(),
          isEligible: z.boolean().optional().default(false),
        }),
      )
      .optional(),
    minValue: z.coerce.number().optional(),
    maxValue: z.coerce.number().optional(),
    order: z.number().optional(),
    hasOtherOption: z.boolean().optional(),
  })
  .superRefine((obj, ctx) => {
    if (visibleQuestionTypes.includes(obj.questionType) && (!obj.options || obj.options.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'This type of question must include one option at least',
        path: ['questionType'],
      });
    }

    if (obj.isMultiLang) {
      if (!obj.translation?.title || obj.translation.title.length < 1) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Translation title is required for multi-language questions',
          path: ['translation', 'title'],
        });
      }
    }

    if (obj.isMultiLang && obj.options && visibleQuestionTypes.includes(obj.questionType)) {
      obj.options.forEach((option, index) => {
        if (!option.translation?.title || option.translation.title.length < 1) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Option title is required if enabled French',
            path: ['options', index, 'translation', 'title'],
          });
        }
      });
    }

    if (obj.questionType === SurveyQuestionType.Screening && obj.options && obj.options.length > 0) {
      const hasEligibleOption = obj.options.some(option => option.isEligible === true);
      if (!hasEligibleOption) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'At least one option must be marked as eligible',
          path: ['questionType'],
        });
      }
    }
  });
export type CreateSurveyQuestionPayload = z.infer<typeof validateSurveyQuestionSchema>;

export type CreateSurveyQuestionState = {
  locale: Locale;
  surveyLists: CreateSurveyQuestionPayload[];
};

export const publishAudienceSchema = z.object({
  audienceId: z.coerce
    .number()
    .int()
    .optional()
    .transform(val => (!val ? null : val)),
  isPublish: z.boolean().optional(),
  cities: z.string().array().optional(),
  completedSurveys: z.number().array().optional(),
  employmentStatuses: z.string().array().optional(),
  practiceSettings: z.string().array().optional(),
  provinces: z.string().array().optional(),
  specialtyIds: z.number().array().optional(),
  filters: z
    .array(
      z.object({
        type: z.string(),
        value: z.union([z.string().array().optional().nullable(), z.number().array().optional().nullable()]),
      }),
    )
    .optional(),
});

export type PublishAudiencePayload = z.infer<typeof publishAudienceSchema>;

export type CreateSurveyPayload = z.infer<typeof validateSurveyInfoSchema> & {
  questions?: CreateSurveyQuestionPayload[];
  audience?: PublishAudiencePayload;
};
