import { QuestionUserAnswers } from '@/backend/surveys/types/question-user-answers';
import InformationBadge from '@/components/common/badges/information';
import VerifiedBadge from '@/components/common/badges/verified';
import { listTypes } from '@/components/sections/surveys/create-surveys-modal/question-form/question-info-form';
import SurveyQuestionOptions from '@/components/sections/surveys/survey-question-options';

type SurveyQuestionDetailProps = {
  data: QuestionUserAnswers;
};

const getType = (type: string) => {
  return listTypes?.find(v => v.value === type)?.label ?? '';
};

const SurveyQuestionDetail = ({ data }: SurveyQuestionDetailProps) => {
  return (
    <div className="flex flex-col lg:flex-row border rounded shadow-custom mb-6">
      <div className="basis-1/3 lg:border-r border-b lg:border-b-0 p-8">
        <div className="flex flex-col space-y-2">
          <div className="font-bold">{`${data.order}. ${data.title}`}</div>
          <div>{data.subtitle}</div>
          <div className="flex gap-4 pt-2">
            <InformationBadge>{getType(data.questionType)}</InformationBadge>
            <VerifiedBadge>{data.totalUsers} responses</VerifiedBadge>
          </div>
        </div>
      </div>

      <div className="basis-2/3 p-8">{data.totalUsers > 0 && <SurveyQuestionOptions data={data} />}</div>
    </div>
  );
};

export default SurveyQuestionDetail;
