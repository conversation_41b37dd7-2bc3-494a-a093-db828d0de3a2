import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { TriangleAlert } from 'lucide-react';

type ModalConfirmEditProps = {
  openDialog: boolean;
  setOpenDialog: React.Dispatch<React.SetStateAction<boolean>>;
  onConfirm?: () => void;
  title?: string;
  subtitle?: string;
};

const ModalConfirmEdit = ({ openDialog, setOpenDialog, onConfirm, title, subtitle }: ModalConfirmEditProps) => {
  return (
    <Dialog open={openDialog} onOpenChange={() => setOpenDialog(false)}>
      <DialogContent
        className="top-1/2 left-1/2 -translate-y-1/2 p-6 -translate-x-1/2 sm:-translate-x-[calc(50%-100px)] w-full sm:w-auto sm:max-w-[400px] h-auto"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex flex-col justify-center items-start">
          <div className="w-16 h-16 mb-5 rounded-full flex justify-center items-center bg-[#FFFAEB]">
            <div className="w-10 h-10 rounded-full flex justify-center items-center bg-[#FEF0C7]">
              <TriangleAlert className="text-[#DC6803]" width={20} height={20} />
            </div>
          </div>
          <div className="text-foreground font-semibold text-lg mb-2">{title ?? 'Are you sure?'}</div>
          <div className="text-sm text-muted-foreground">
            {subtitle ?? 'This survey is currently active, editing it might significantly impact the results.'}
          </div>
        </div>
        <DialogFooter className="mt-8 flex justify-between">
          <Button
            type="button"
            variant="outline"
            className="h-10 flex-1 text-gray-text font-semibold"
            onClick={e => {
              e.stopPropagation();
              setOpenDialog(false);
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="h-10 flex-1 font-semibold"
            onClick={e => {
              e.stopPropagation();
              onConfirm && onConfirm();
            }}
          >
            Edit
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ModalConfirmEdit;
