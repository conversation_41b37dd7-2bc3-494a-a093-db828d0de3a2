'use client';

import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { QuestionUserAnswers } from '@/backend/surveys/types/question-user-answers';
import { useExportSurvey } from '@/components/sections/surveys/hooks/useExportSurvey';
import { Button } from '@/components/ui/button';

const SurveyQuestionOptions = ({ data }: { data: QuestionUserAnswers }) => {
  const { exportSurvey, isExporting } = useExportSurvey();

  const questionType = data.questionType;

  const sortedOptions =
    questionType === SurveyQuestionType.Rank
      ? data.options.sort((a, b) => data.optionsRanking[a.id] - data.optionsRanking[b.id])
      : data.options.sort((a, b) => a.id - b.id);

  if (questionType === SurveyQuestionType.Rank)
    return (
      <div className="flex flex-col gap-8">
        {sortedOptions.map((option, i) => (
          <div className="flex gap-8 md:gap-16" key={option.id}>
            <div className="basis-1/6 font-bold">{option.title}</div>
            <div className="basis-16">#{i + 1}</div>
          </div>
        ))}
      </div>
    );

  if (questionType === SurveyQuestionType.SingleSelection || questionType === SurveyQuestionType.MultipleSelection)
    return (
      <div className="flex flex-col gap-8">
        {sortedOptions.map(option => (
          <div className="flex gap-8 md:gap-16" key={option.id}>
            <div className="basis-1/6 font-bold">{option.title}</div>
            <div className="basis-16">{data.optionsCalculate[option.id]?.percentage.toFixed(2) ?? 0}%</div>
            <div className="text-muted-foreground">{data.optionsCalculate[option.id]?.count ?? 0} Responses</div>
          </div>
        ))}
      </div>
    );

  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col items-center gap-6 py-12 bg-[#EAECF0] rounded-md">
        <p className="font-medium text-foreground">Export the survey to see all answers</p>
        <Button onClick={() => exportSurvey(data.surveyId)} isLoading={isExporting}>
          Export Survey
        </Button>
      </div>
    </div>
  );
};

export default SurveyQuestionOptions;
