'use client';

import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import React, { useState } from 'react';
import QuestionResults from '../question-results';
import TableSurveyParticipants from './table-survey-participants/table-survey-participant';

enum SurveyTabs {
  QuestionResults = 'question-results',
  SurveyParticipants = 'survey-participants',
}

const SurveyDetails = ({ surveyId }: { surveyId: number }) => {
  const [activeTab, setActiveTab] = useState(SurveyTabs.QuestionResults);

  return (
    <Tabs defaultValue={activeTab} onValueChange={value => setActiveTab(value as SurveyTabs)} className="mt-8">
      <TabsList className="bg-background px-0">
        <TabsTrigger
          value={SurveyTabs.QuestionResults}
          className="rounded-none text-muted-foreground bg-background text-base py-4 flex flex-col items-center bg-none px-0 data-[state=active]:!bg-background data-[state=active]:text-primary data-[state=active]:shadow-none relative"
        >
          <div className="px-2">Survey Results</div>
          {activeTab === SurveyTabs.QuestionResults && (
            <Separator className="w-full h-0.5 bg-primary absolute bottom-0" />
          )}
        </TabsTrigger>
        <TabsTrigger
          value={SurveyTabs.SurveyParticipants}
          className="rounded-none text-muted-foreground bg-background text-base py-4 flex flex-col items-center bg-none px-0 data-[state=active]:!bg-background data-[state=active]:text-primary data-[state=active]:shadow-none relative"
        >
          <div className="px-2">Participants</div>
          {activeTab === SurveyTabs.SurveyParticipants && (
            <Separator className="w-full h-0.5 bg-primary absolute bottom-0" />
          )}
        </TabsTrigger>
      </TabsList>
      <TabsContent value={SurveyTabs.QuestionResults}>
        <QuestionResults surveyId={surveyId} />
      </TabsContent>
      <TabsContent value={SurveyTabs.SurveyParticipants}>
        <TableSurveyParticipants surveyId={surveyId} />
      </TabsContent>
    </Tabs>
  );
};

export default SurveyDetails;
