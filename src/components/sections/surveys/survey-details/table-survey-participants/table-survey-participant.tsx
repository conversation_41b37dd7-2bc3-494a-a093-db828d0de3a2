'use client';
import TableDataContainer from '@/components/common/table-container';
import { Table } from '@/components/ui/table';
import React, { useMemo, useState } from 'react';
import LocalPagination from '@/components/common/local-pagination/local-pagination';
import { useLocalPagination } from '@/hooks/useLocalPagination';
import { useDebounce } from '@/hooks/useDebounce';
import { useSurveyParticipants } from '../../hooks/useSurveyParticipants';
import { generateSurveyParticipantColumns } from './columns';
import { SortingState } from '@tanstack/react-table';
import { User } from '@/backend/users/entities/User';
import TableParticipantHeader from './table-participant-header';

const TableSurveyParticipants = ({ surveyId }: { surveyId: number }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const search = useDebounce(searchTerm);

  const [sorting, setSorting] = useState<SortingState>([{ id: 'completionDate', desc: true }]);

  // Extract sort parameters from the sorting state
  const sortParams = useMemo(() => {
    if (sorting.length === 0) {
      return { sortBy: 'completionDate' as keyof User, sortOrder: 'DESC' };
    }

    return {
      sortBy: sorting[0].id as keyof User,
      sortOrder: sorting[0].desc ? 'DESC' : 'ASC',
    };
  }, [sorting]);

  const { page, pageSize, goToPage, changePageSize } = useLocalPagination();
  const { participants, isLoading } = useSurveyParticipants(surveyId, {
    page,
    pageSize,
    search,
    sortBy: sortParams.sortBy,
    sortOrder: sortParams.sortOrder as 'DESC' | 'ASC',
  });

  const participantsData = useMemo(() => {
    if (isLoading) {
      return Array(10).fill({});
    }

    return participants?.data?.data ?? [];
  }, [participants, isLoading]);

  const columns = useMemo(
    () =>
      generateSurveyParticipantColumns({
        isLoading: isLoading,
      }),
    [isLoading],
  );

  const handleSortingChange = (newSorting: SortingState) => {
    setSorting(newSorting);
  };

  return (
    <div className="mt-10">
      <TableDataContainer className="pt-1">
        <div className="px-6 py-4">
          <TableParticipantHeader
            participantsCount={participants?.data.countDistinct || 0}
            responseCount={participants?.data.total || 0}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isLoading={isLoading}
          />
        </div>
        <Table
          columns={columns}
          data={participantsData}
          useLocalSort
          initialSorting={sorting}
          onSortingChange={handleSortingChange}
        />
        <LocalPagination
          page={page}
          pageSize={pageSize}
          goToPage={goToPage}
          changePageSize={changePageSize}
          totalPages={participants?.data?.totalPages || 0}
        />
      </TableDataContainer>
    </div>
  );
};

export default TableSurveyParticipants;
