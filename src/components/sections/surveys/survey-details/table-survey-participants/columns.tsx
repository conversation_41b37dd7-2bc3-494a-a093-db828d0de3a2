'use client';
import { ColumnDef } from '@tanstack/table-core';
import React from 'react';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { formatPhone } from '@/utils/phone-format';
import { formatDate } from '@/utils/date-format';
import { User } from '@/backend/users/entities/User';

type ColumnsProps = {
  isLoading?: boolean;
};

function generateSurveyParticipantColumns({ isLoading }: ColumnsProps) {
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'firstName',
      header: ({ column }) => <Header column={column}>Name</Header>,
      cell: ({ row }) => {
        const name = row.original?.firstName + ' ' + row.original?.lastName;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="whitespace-nowrap text-foreground font-medium">{name}</span>
          </SkeletonCell>
        );
      },
      size: 200,
    },

    {
      accessorKey: 'email',
      header: ({ column }) => <Header column={column}>Contact Information</Header>,
      cell: ({ row }) => {
        const phone = row.original?.phone;
        const email = row.original?.email;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className="flex flex-col w-full">
              <span className="truncate">{email}</span>
              <span className="text-muted-foreground whitespace-nowrap">{phone ? formatPhone(phone) : ''}</span>
            </div>
          </SkeletonCell>
        );
      },
      size: 300,
    },
    {
      accessorKey: 'province',
      header: ({ column }) => <Header column={column}>Province / State</Header>,
      cell: ({ row }) => {
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className="flex flex-col max-w-40">
              <span className="whitespace-nowrap text-foreground">{row.original?.province}</span>
            </div>
          </SkeletonCell>
        );
      },
      size: 200,
    },
    {
      accessorKey: 'specialty',
      header: ({ column }) => <Header column={column}>Specialty</Header>,
      cell: ({ row }) => {
        const specialtyName = row.original?.specialty?.name;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="whitespace-nowrap text-foreground">{specialtyName || ''}</span>
          </SkeletonCell>
        );
      },
      size: 200,
    },
    {
      accessorKey: 'completionDate',
      header: ({ column }) => <Header column={column}>Completed On</Header>,
      cell: ({ row }) => {
        const completedOn = row.original?.completionDate;
        const completeDate = completedOn ? new Date(completedOn) : null;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span className="whitespace-nowrap text-foreground">{completeDate ? formatDate(completeDate) : ''}</span>
          </SkeletonCell>
        );
      },
    },
  ];

  return columns;
}

export { generateSurveyParticipantColumns };
