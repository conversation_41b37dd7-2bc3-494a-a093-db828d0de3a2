import PrimaryBadge from '@/components/common/badges/primary';
import { Form, Input } from '@/components/ui/form';
import { Search } from 'lucide-react';
import pluralize from 'pluralize';
import React from 'react';
import { z } from 'zod';

interface TableParticipantProps {
  responseCount: number;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  participantsCount: number;
  isLoading: boolean;
}

const schema = z.object({
  search: z.string().optional(),
});

const TableParticipantHeader = ({
  participantsCount,
  responseCount,
  searchTerm,
  setSearchTerm,
  isLoading = false,
}: TableParticipantProps) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex gap-3">
        <PrimaryBadge isLoading={isLoading} className="bg-[#EFF8FF] text-[#175CD3] font-medium text-xs">
          {responseCount} {pluralize('Response', responseCount)}
        </PrimaryBadge>
        <PrimaryBadge isLoading={isLoading} className="bg-[#EFF8FF] text-[#175CD3] font-medium text-xs">
          {participantsCount} {pluralize('Participant', participantsCount)}
        </PrimaryBadge>
      </div>
      <div className="lg:max-w-25 w-full my-4 lg:my-0">
        <Form schema={schema} onSubmit={() => {}}>
          <Input
            name="search"
            type="text"
            placeholder="Search"
            className="pl-[42px]"
            StartIcon={Search}
            iconClassName="w-5 h-5"
            value={searchTerm}
            onChange={e => {
              setSearchTerm(e.target.value);
            }}
          />
        </Form>
      </div>
    </div>
  );
};

export default TableParticipantHeader;
