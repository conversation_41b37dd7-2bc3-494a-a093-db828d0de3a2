'use client';

import { Plus } from 'lucide-react';

import SearchInput from '@/components/common/search-input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useShallow } from 'zustand/react/shallow';
import CreateSurveyModal from './create-surveys-modal';
import { useSurveyStore } from './hooks/useSurveyStore';
import { useSurveyModalQueryHandler } from './hooks/useSurveyModalQueryHandler';

type SurveysHeaderProps = {
  surveysCount: number;
  handleCreatePublicSurveySuccess: (id: number, qrImageUrl: string) => void;
};

const SurveysHeader = ({ surveysCount, handleCreatePublicSurveySuccess }: SurveysHeaderProps) => {
  const { isCreateSurveyModalOpen } = useSurveyStore(
    useShallow(state => ({
      isCreateSurveyModalOpen: state.isCreateSurveyModalOpen,
    })),
  );
  const handleSurveyModalQuery = useSurveyModalQueryHandler();

  return (
    <div className="flex flex-col lg:flex-row justify-between px-4 lg:px-6 mb-[5px]">
      <div className="flex items-center font-medium">
        <div>Surveys</div>
        <Badge className="text-primary text-xs bg-[#F9F5FF] ml-2 px-2 py-0.5 ">Count: {surveysCount}</Badge>
      </div>
      <div className="flex justify-end flex-1 items-center my-4 lg:my-0 ml-0 lg:ml-6">
        <div className="lg:max-w-25 w-full">
          <SearchInput />
        </div>
        <Button
          className="h-10 ml-6 px-2.5 md:px-4 border-none bg-primary-brand"
          onClick={() => handleSurveyModalQuery(undefined, 'create')}
        >
          <Plus />
          <div className="ml-2 hidden md:block">Add Survey</div>
        </Button>
      </div>
      {isCreateSurveyModalOpen && (
        <CreateSurveyModal handleCreatePublicSurveySuccess={handleCreatePublicSurveySuccess} />
      )}
    </div>
  );
};

export default SurveysHeader;
