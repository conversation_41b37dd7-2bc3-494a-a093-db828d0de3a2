import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { TriangleAlert } from 'lucide-react';
import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { PublishAudiencePayload } from '../validations';

type ModalConfirmPublishProps = {
  openDialog: boolean;
  setOpenDialog: React.Dispatch<React.SetStateAction<boolean>>;
  onConfirm?: (isPublish: boolean, values: PublishAudiencePayload) => Promise<void> | void;
  isConfirming?: boolean;
  title?: string;
  subtitle?: string;
};

const ModalConfirmPublish = ({
  openDialog,
  setOpenDialog,
  onConfirm,
  isConfirming,
  title,
  subtitle,
}: ModalConfirmPublishProps) => {
  const { getValues } = useFormContext();
  const [published, setPublished] = useState(true);
  return (
    <Dialog open={openDialog} onOpenChange={() => setOpenDialog(false)}>
      <DialogContent className="top-1/2 left-1/2 -translate-y-1/2 p-6 -translate-x-1/2 sm:-translate-x-[calc(50%-100px)] w-full sm:w-auto sm:max-w-[400px] h-auto">
        <div className="flex flex-col justify-center items-start">
          <div className="w-16 h-16 mb-5 rounded-full flex justify-center items-center bg-[#FFFAEB]">
            <div className="w-10 h-10 rounded-full flex justify-center items-center bg-[#FEF0C7]">
              <TriangleAlert className="text-[#D92D20]" width={20} height={20} />
            </div>
          </div>
          <div className="text-foreground font-semibold text-lg mb-3">{title}</div>
          <div className="text-sm text-muted-foreground">
            {subtitle ??
              'Every user that has push enabled will receive a notification. If you prefer not to notify users, please check the box below.'}
          </div>
          <div className={'flex mt-5'}>
            <Checkbox
              className="border-[#D0D5DD]"
              name="isPublish"
              id="isPublish"
              checked={!published}
              onCheckedChange={() => setPublished(prevState => !prevState)}
            />{' '}
            <label className="text-sm font-medium ml-2 leading-tight" htmlFor="isPublish">
              Do not send push notification
            </label>
          </div>
        </div>
        <DialogFooter className="mt-8 flex justify-between">
          <Button
            type="button"
            variant="ghost"
            className="h-10 border border-[#D0D5DD] flex-1"
            onClick={() => setOpenDialog(false)}
            disabled={isConfirming}
          >
            Cancel
          </Button>
          <Button
            type="button"
            className="h-10 bg-[#7F56D9] flex-1"
            disabled={isConfirming}
            isLoading={isConfirming}
            onClick={async e => {
              e.preventDefault();
              onConfirm && (await onConfirm(published, getValues() as unknown as PublishAudiencePayload));
              setOpenDialog(false);
            }}
          >
            Publish
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default ModalConfirmPublish;
