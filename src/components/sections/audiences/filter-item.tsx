import { Label, SelectMultiple, SelectSearch } from '@/components/ui/form';
import Image from 'next/image';
import { useMemo } from 'react';
import { FieldValues, UseFieldArrayInsert, useFormContext } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Select, SelectItem } from '@/components/ui/form/select';
import { useShallow } from 'zustand/react/shallow';
import { useFilterDataStore } from './hooks/useFilterDataStore';
import SelectShowLabel from '@/components/ui/form/select-show-label';
import { initialFilterTypes as filterTypes } from './hooks/useFilterDataStore';

type FilterItemProps = {
  index: number;
  isDisabled?: boolean;
  fieldArrayName: string;
  remove: (idx: number) => void;
  insert: UseFieldArrayInsert<FieldValues, string>;
  canEdit?: boolean;
  setOpenSelectSpecificUser: (value: boolean) => void;
};

const FilterItem = ({
  index,
  isDisabled,
  fieldArrayName,
  remove,
  insert,
  canEdit = true,
  setOpenSelectSpecificUser,
}: FilterItemProps) => {
  const { getValues, setValue } = useFormContext();
  const filterData = useFilterDataStore(
    useShallow(state => ({
      cities: state.cities,
      completedSurveys: state.completedSurveys,
      practiceSettings: state.practiceSettings,
      employmentStatuses: state.employmentStatuses,
      specialties: state.specialties,
      provinces: state.provinces,
      specificUsers: state.specificUsers,
    })),
  );
  const remainingFilterTypes = useFilterDataStore(state => state.filterTypes);
  const filterTypesSelected = filterTypes.filter(f => !remainingFilterTypes.find(r => r.field === f.field));
  const { addFilterType, removeFilterType, resetSpecificUsers } = useFilterDataStore();

  const typeName = `${fieldArrayName}.${index}.type`;
  const valueName = `${fieldArrayName}.${index}.value`;

  const filterTypeSelected = getValues(typeName);
  const defaultValue = getValues(valueName);

  const options = useMemo(() => {
    const { specificUsers, specialties, completedSurveys, practiceSettings, cities, employmentStatuses, provinces } =
      filterData;

    switch (filterTypeSelected) {
      case 'cities':
        return cities?.map(v => ({ label: v.name, value: v.name })) ?? [];
      case 'completedSurveys':
        return completedSurveys?.map(v => ({ label: v.title, value: v.id })) ?? [];
      case 'employmentStatuses':
        return employmentStatuses?.map(v => ({ label: v.name, value: v.name })) ?? [];
      case 'practiceSettings':
        return practiceSettings?.map(v => ({ label: v.name, value: v.name })) ?? [];
      case 'provinces':
        return provinces?.map(v => ({ label: v.name, value: v.name })) ?? [];
      case 'specialtyIds':
        return specialties?.map(v => ({ label: v.name, value: v.id })) ?? [];
      case 'specificUsers':
        return specificUsers?.map(v => ({ label: `${v.firstName} ${v.lastName}`, value: v.id })) ?? [];
    }
    return [];
  }, [filterTypeSelected, filterData]);

  const computedDefaultValue = useMemo(() => {
    if (filterTypeSelected === 'specificUsers') {
      return options;
    }

    if (!defaultValue || !Array.isArray(defaultValue)) {
      return undefined;
    }

    if (defaultValue.length === 0 || defaultValue.length === options.length) {
      return options;
    }

    return options.filter(i => defaultValue.includes(i.value));
  }, [filterTypeSelected, defaultValue, options]);

  return (
    <div className="mb-4 last:mb-0">
      <div className="flex flex-col sm:flex-row sm:space-x-8 bg-[#EAECF0] py-4 px-6 rounded-md">
        <div className="flex-1">
          <Label htmlFor="filter" className="mb-1.5 h-5 font-medium inline-block">
            Filter
          </Label>
          <div className="w-full">
            <Select
              name={typeName}
              placeholder="Select filter type"
              className="!text-base h-11 text-start"
              disabled={isDisabled || !canEdit}
              defaultValue={filterTypeSelected}
              onValueChange={value => {
                setValue(typeName, value);
                setValue(valueName, []);
                if (value !== filterTypeSelected) {
                  remove(index);
                  insert(index, { type: value });
                  addFilterType(filterTypeSelected);
                  removeFilterType(value);
                }
                // reset specific users when changing filter type to userIds
                if (value !== 'specificUsers' && filterTypeSelected === 'userIds') {
                  resetSpecificUsers();
                }
              }}
            >
              {filterTypes.map(type => {
                const isDisabled = Boolean(
                  filterTypesSelected.find(f => f.field === type.field) && type.field !== filterTypeSelected,
                );
                return (
                  <SelectItem key={type.field} value={type.field} className="!text-sm" disabled={isDisabled}>
                    {type.label}
                  </SelectItem>
                );
              })}
            </Select>
          </div>
        </div>
        <div className="flex justify-center rotate-90 sm:rotate-0">
          <Image src="/icons/arrow-right.svg" alt="next-icon" height={24} width={24} className="mt-4" />
        </div>
        <div className="flex-1">
          <Label htmlFor="filter" className="mb-1.5 h-5 font-medium inline-block">
            Is
          </Label>
          <div className="w-full">
            {filterTypeSelected === 'completedSurveys' || filterTypeSelected === 'cities' ? (
              <SelectSearch
                key={filterTypeSelected}
                name={valueName}
                placeholder="Select filters"
                options={options}
                disabled={isDisabled || !canEdit}
                defaultSelected={computedDefaultValue}
              />
            ) : filterTypeSelected === 'userIds' ? (
              <SelectShowLabel
                placeholder="Select Users"
                options={options}
                defaultSelected={computedDefaultValue}
                name={valueName}
                disabled={isDisabled || !canEdit}
                onClick={() => setOpenSelectSpecificUser(true)}
              />
            ) : (
              <SelectMultiple
                key={filterTypeSelected}
                name={valueName}
                placeholder="Select filters"
                options={options}
                disabled={isDisabled || !canEdit}
                defaultSelected={computedDefaultValue}
                className="[&>span]:line-clamp-1"
              />
            )}
          </div>
        </div>
        <div className="flex items-end justify-center mt-4 sm:mt-0">
          <Button
            className="bg-white rounded-lg border border-[#D0D5DD] p-2.5 h-10 w-10 cursor-pointer z-10"
            onClick={event => {
              event.preventDefault();
              remove(index);
              addFilterType(filterTypeSelected);
              // reset specific users when removing userIds filter
              if (filterTypeSelected === 'userIds') {
                resetSpecificUsers();
              }
            }}
            disabled={isDisabled || !canEdit}
          >
            <Image src="/icons/trash.svg" alt="next-icon" height={24} width={24} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FilterItem;
