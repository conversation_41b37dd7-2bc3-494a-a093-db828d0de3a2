'use client';
import React, { useEffect, useMemo, useState } from 'react';
import { z } from 'zod';

import { Form, Input, Label } from '@/components/ui/form';
import InformationBadge from '@/components/common/badges/information';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate } from '@/utils/date-format';
import ModalConfirmDelete from '@/components/common/modal-confirm-delete';
import { CreateAudiencePayload } from '@/backend/audiences/validations/add-update-audience';
import { Audience } from '@/backend/audiences/entities/Audience';
import FilterItems from './filter-items';
import { useAudience } from './hooks/useAudience';
import { useInitFilterData } from './hooks/useInitFilterData';
import { FilterDataStore, useFilterDataStore } from './hooks/useFilterDataStore';
import { useDeleteAudience } from './hooks/useDeleteAudience';
import { listParamKeys, useUpdateAudience } from './hooks/useUpdateAudience';
import { useExportAudienceUsers } from '@/components/sections/audiences/hooks/useExportAudienceUsers';
import { specificName } from '@/backend/shared/validations/name';
import { useRouter } from 'next/navigation';
import pluralize from 'pluralize';

const schema = z.object({
  name: specificName('Audience'),
  filters: z
    .array(
      z.object({
        type: z.string(),
        value: z.union([z.string().array().optional().nullable(), z.number().array().optional().nullable()]),
      }),
    )
    .optional(),
});

type FormData = z.infer<typeof schema>;

const EditAudienceForm = ({ audienceId }: { audienceId: number }) => {
  const router = useRouter();
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const { audience, isLoadingAudience } = useAudience(audienceId);
  const { exportAudienceUser, isExporting } = useExportAudienceUsers();
  const { updateAudience, isUpdating } = useUpdateAudience(audienceId);
  const { deleteAudience, isDeleting } = useDeleteAudience();
  const { isLoadingFilterData } = useInitFilterData(audience?.data?.userIds ?? []);
  const { usersIncluded, removeFilterType, resetFilterTypes, resetSpecificUsers, ...restState } = useFilterDataStore();
  const isLoading = isLoadingFilterData || isLoadingAudience;

  const defaultFilters = useMemo(() => {
    if (!audience) return [];
    const filters = [];
    const { data } = audience;
    for (const key of listParamKeys) {
      const _key = key as keyof Audience;
      if (data && data[_key]) {
        filters.push({ type: key, value: data[_key] });
        removeFilterType(key);
      }
    }

    return filters;
  }, [audience, removeFilterType]);

  useEffect(() => {
    return () => {
      resetFilterTypes();
      resetSpecificUsers();
    };
  }, [resetFilterTypes, resetSpecificUsers]);

  async function onSubmit(data: FormData) {
    const { name, filters } = data;
    let filterPayload: Partial<CreateAudiencePayload> | undefined = filters?.reduce((acc, filter) => {
      const { type, value } = filter;
      let key = type as
        | keyof Omit<FilterDataStore, 'usersIncluded' | 'removeFilterType' | 'resetFilterTypes' | 'resetSpecificUsers'>
        | 'specialtyIds';
      if (key === 'specialtyIds') {
        key = 'specialties';
      }
      const filterValue = value && value.length === restState[key]?.length ? [] : value;
      return {
        ...acc,
        [type]: filterValue,
      };
    }, {});

    filterPayload = filterPayload ?? {};
    defaultFilters.forEach(({ type }) => {
      const key = type as keyof Omit<Partial<CreateAudiencePayload>, 'name'>;
      if (!filterPayload[key]) {
        filterPayload[key] = null;
      }
    });
    await updateAudience({ name, ...filterPayload });
    setIsEditMode(false);
  }

  return (
    <Form
      schema={schema}
      onSubmit={onSubmit}
      key={`edit-audience-form-${isLoadingAudience}`}
      mode="onSubmit"
      className="w-full"
      defaultValues={{
        name: audience?.data?.name ?? '',
        filters: defaultFilters as Array<{ type: string; value?: string[] | number[] }>,
      }}
    >
      <div className="flex w-full justify-between">
        <div className="flex flex-col">
          {isLoading ? (
            <div className="w-[302px] space-y-1">
              <Skeleton className="h-7" />
              <Skeleton className="w-[200px] h-5" />
            </div>
          ) : (
            <>
              <div className="flex flex-wrap">
                <div className="font-medium text-lg mr-6">{audience?.data?.name ?? 'N/A'}</div>
                <InformationBadge>
                  {usersIncluded} Verified {pluralize('User', usersIncluded)} Included
                </InformationBadge>
              </div>
              <div className="text-sm">
                Last Survey: {audience?.data?.lastSurveyDate ? formatDate(audience?.data?.lastSurveyDate) : 'N/A'}
              </div>
            </>
          )}
        </div>

        <div className="flex flex-col lg:flex-row items-center w-fit space-y-2 lg:space-y-0">
          <Button
            type="button"
            className="text-[#B42318] bg-inherit font-semibold whitespace-nowrap text-sm sm:text-base"
            disabled={isEditMode || isUpdating || isExporting}
            onClick={() => setOpenDialog(true)}
          >
            Delete Audience
          </Button>

          <ModalConfirmDelete
            openDialog={openDialog}
            setOpenDialog={setOpenDialog}
            title="Delete Audience"
            subtitle="Are you sure you want to delete this audience? This action cannot be reversed."
            isConfirming={isDeleting}
            onConfirm={async () => {
              await deleteAudience(audienceId);
              router.replace('/audiences');
              setOpenDialog(false);
            }}
          />

          <Button
            variant="outline"
            type="button"
            className="lg:ml-4 lg:mr-3 w-full lg:w-fit text-sm sm:text-base"
            disabled={isEditMode || isUpdating || isExporting}
            isLoading={isExporting}
            onClick={() => exportAudienceUser(audienceId)}
          >
            Export Audience
          </Button>
          {isEditMode ? (
            <Button type="submit" className="rounded-lg w-full lg:w-fit text-sm sm:text-base" disabled={isUpdating}>
              Save Changes
            </Button>
          ) : (
            <Button
              type="button"
              className="rounded-lg w-full lg:w-fit text-sm sm:text-base"
              disabled={isExporting}
              onClick={e => {
                e.preventDefault();
                setIsEditMode(true);
              }}
            >
              Edit Audience
            </Button>
          )}
        </div>
      </div>

      <div className="w-full flex flex-col flex-1 mt-10">
        <div className="font-bold text-base mr-6">Audience Settings</div>
        <div className="flex flex-wrap gap-6 my-4">
          <div className="max-w-20 w-full">
            <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
              Audience Name
            </Label>
            {isLoading ? (
              <Skeleton className="h-11" />
            ) : (
              <Input name="name" placeholder="Example: Pharmacists in ON" disabled={!isEditMode} />
            )}
          </div>
        </div>
        <div className="flex-1 max-w-[863px]">
          {isLoading ? (
            <Skeleton className="h-[107px]" />
          ) : (
            <FilterItems isLoadingFilterData={isLoading} isEditMode={isEditMode} />
          )}
        </div>
      </div>
    </Form>
  );
};
export default EditAudienceForm;
