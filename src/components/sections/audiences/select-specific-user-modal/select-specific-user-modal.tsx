'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { Table } from '@/components/ui/table';
import React, { useEffect, useMemo, useState } from 'react';
import { generateSpecificUserColumns } from './select-specific-user-columns';
import SpecificUserHeader from './specific-user-header';
import { useDebounce } from '@/hooks/useDebounce';
import { User } from '@/backend/users/entities/User';
import { useFilterDataStore } from '../hooks/useFilterDataStore';
import { useInfinitePrioritizedUsers } from '../hooks/useInfiniteUser';
import { RowSelectionState } from '@tanstack/table-core';

interface SelectSpecificUserModalProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: (users: User[]) => void;
}

const SelectSpecificUserModal = ({ open, onCancel, onConfirm }: SelectSpecificUserModalProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  const search = useDebounce(searchTerm);
  const pageSize = 10;
  const { specificUsers } = useFilterDataStore();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfinitePrioritizedUsers({
    search,
    pageSize,
    ids: specificUsers?.map(user => user.id) ?? [],
  });

  const tableData = useMemo(() => {
    if (isLoading) return Array(10).fill({});
    return data?.pages.flatMap(page => page.data.data) ?? [];
  }, [data, isLoading]);

  const columns = useMemo(() => generateSpecificUserColumns({ isLoading }), [isLoading]);

  useEffect(() => {
    if (open && specificUsers) {
      // Initialize selections from specificUsers
      const initialSelection = specificUsers.reduce(
        (acc, user) => ({ ...acc, [user.id]: true }),
        {} as RowSelectionState,
      );
      setRowSelection(initialSelection);
      setSelectedUsers(specificUsers);
    } else {
      // Reset when modal closes
      setRowSelection({});
      setSelectedUsers([]);
    }
  }, [open, specificUsers]);

  // Handle row selection changes
  const handleRowSelectionChange = (newSelection: RowSelectionState) => {
    setRowSelection(newSelection);

    const allAvailableUsers = data?.pages.flatMap(page => page.data.data) || [];

    const prevSelectedUsers = selectedUsers.filter(
      user => !allAvailableUsers.some(availableUser => availableUser.id === user.id),
    );

    const newSelectedUsers = allAvailableUsers.filter(user => newSelection[user.id]);

    setSelectedUsers([...prevSelectedUsers, ...newSelectedUsers]);
  };

  const handleConfirm = () => {
    onConfirm(selectedUsers);
    setSearchTerm('');
    onCancel();
  };

  const handleCancel = () => {
    setSearchTerm('');
    onCancel();
  };

  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent
        onInteractOutside={e => e.preventDefault()}
        className="lg:!w-[1080px] min-h-[90vh] lg:!max-h-[90vh] flex flex-col"
      >
        <div className="flex-1  flex flex-col gap-4 overflow-hidden border rounded-lg pt-5">
          <div className="px-6">
            <SpecificUserHeader
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              totalUsers={data?.pages[0].data.total ?? 0}
              selectedUsers={selectedUsers.length}
            />
          </div>
          <Table
            columns={columns}
            data={tableData}
            isFetchingNextPage={isFetchingNextPage}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
            rowSelection={rowSelection}
            onRowSelectionChange={handleRowSelectionChange}
            getRowId={row => (isLoading ? row.uuid : String(row.id))}
            stickyHeader
          />
        </div>
        <DialogFooter className="justify-end mt-6">
          <div className="flex gap-3">
            <Button variant={'outline'} onClick={handleCancel}>
              Cancel
            </Button>
            <Button disabled={isLoading} onClick={handleConfirm}>
              Confirm
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SelectSpecificUserModal;
