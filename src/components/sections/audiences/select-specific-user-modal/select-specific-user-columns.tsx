'use client';
import { ColumnDef, Row } from '@tanstack/table-core';
import React from 'react';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { User } from '@/backend/users/entities/User';
import { SelectHeader } from '@/components/ui/table/select-header';
import { SelectCell } from '@/components/ui/table/select-cell';
import { cn } from '@/lib/utils';
import { formatPhone } from '@/utils/phone-format';
import UserVerificationBadge from '../../users/user-verification-badge';

type ColumnsProps = {
  isLoading?: boolean;
};

function generateSpecificUserColumns({ isLoading }: ColumnsProps) {
  const columns: ColumnDef<User>[] = [
    {
      id: 'select',
      header: SelectHeader,
      size: 50,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 ml-2 overflow-hidden' : '')}>
          <SelectCell row={row as Row<User>} />
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'userId',
      size: 200,
      minSize: 150,
      header: ({ column }) => <Header column={column}>User ID</Header>,
      cell: ({ row }) => {
        const userId = row.original.id;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="whitespace-nowrap text-foreground font-medium truncate">{userId}</span>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'name',
      size: 200,
      minSize: 150,
      header: ({ column }) => <Header column={column}>Name</Header>,
      cell: ({ row }) => {
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className="flex flex-col w-full">
              <span className="text-foreground truncate">{`${row.original.firstName} ${row.original.lastName}`}</span>
              <span className="text-muted-foreground truncate">{row.original.licenseNumber}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'email',
      size: 200,
      minSize: 150,
      header: ({ column }) => <Header column={column}>Contact Information</Header>,
      cell: ({ row }) => {
        const phone = row.original.phone;
        const email = row.original.email;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className="flex flex-col w-full">
              <span className="truncate">{email}</span>
              <span className="text-muted-foreground truncate">{phone ? formatPhone(phone) : 'N/A'}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'status',
      size: 150,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Status</Header>,
      cell: ({ row }) => {
        const verificationStatus = row.original.verificationStatus;
        return (
          <SkeletonCell isLoading={isLoading}>
            <UserVerificationBadge verificationStatus={verificationStatus!} />
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
  ];

  return columns;
}

export { generateSpecificUserColumns };
