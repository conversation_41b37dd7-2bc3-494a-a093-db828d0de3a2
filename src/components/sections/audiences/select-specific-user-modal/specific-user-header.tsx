import PrimaryBadge from '@/components/common/badges/primary';
import { Badge } from '@/components/ui/badge';
import { Form, Input } from '@/components/ui/form';
import { Search } from 'lucide-react';
import pluralize from 'pluralize';
import React from 'react';
import { z } from 'zod';
interface AddToAudienceHeaderProps {
  totalUsers: number;
  selectedUsers: number;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}
const schema = z.object({
  search: z.string().optional(),
});

const SpecificUserHeader = ({ totalUsers, selectedUsers, setSearchTerm }: AddToAudienceHeaderProps) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex space-x-2">
        <h2 className="text-[18px]">Users</h2>
        <PrimaryBadge className="bg-[#EFF8FF] text-[#175CD3] font-medium text-xs">
          {totalUsers} {pluralize('User', totalUsers)} Found
        </PrimaryBadge>
        <Badge className="bg-[#F9F5FF] text-primary">{selectedUsers} Selected</Badge>
      </div>
      <div className="lg:max-w-25 w-full my-4 lg:my-0">
        <Form schema={schema} onSubmit={() => {}}>
          <Input
            name="search"
            type="text"
            placeholder="Search"
            className="pl-[42px]"
            StartIcon={Search}
            iconClassName="w-5 h-5"
            onChange={e => {
              setSearchTerm(e.target.value);
            }}
          />
        </Form>
      </div>
    </div>
  );
};

export default SpecificUserHeader;
