import PrimaryBadge from '@/components/common/badges/primary';
import { Form, Input } from '@/components/ui/form';
import { Search } from 'lucide-react';
import pluralize from 'pluralize';
import React from 'react';
import { z } from 'zod';

interface AddToAudienceHeaderProps {
  totalUsers: number;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}

const schema = z.object({
  search: z.string().optional(),
});

const UserAudienceHeader = ({ totalUsers, searchTerm, setSearchTerm }: AddToAudienceHeaderProps) => {
  return (
    <div className="flex items-center justify-between">
      <PrimaryBadge className="bg-[#EFF8FF] text-[#175CD3] font-medium text-xs">
        {totalUsers} {pluralize('User', totalUsers)} Found
      </PrimaryBadge>
      <div className="lg:max-w-25 w-full my-4 lg:my-0">
        <Form schema={schema} onSubmit={() => {}}>
          <Input
            name="search"
            type="text"
            placeholder="Search"
            className="pl-[42px]"
            StartIcon={Search}
            iconClassName="w-5 h-5"
            value={searchTerm}
            onChange={e => {
              setSearchTerm(e.target.value);
            }}
          />
        </Form>
      </div>
    </div>
  );
};

export default UserAudienceHeader;
