'use client';
import TableDataContainer from '@/components/common/table-container';
import React, { useMemo, useState } from 'react';
import { generateUserAudienceColumns } from './columns';
import { useAudienceUsers } from '../hooks/useAudienceUsers';
import LocalPagination from '@/components/common/local-pagination/local-pagination';
import { useLocalPagination } from '@/hooks/useLocalPagination';
import { useDebounce } from '@/hooks/useDebounce';
import TableHeader from '@/components/common/table-header';
import { Table } from '@/components/ui/table';

const TableAudienceUsers = ({ audienceId }: { audienceId: number }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const search = useDebounce(searchTerm);

  const { page, pageSize, goToPage, changePageSize } = useLocalPagination();
  const { users, isLoading } = useAudienceUsers(audienceId, { page, pageSize, search });

  const columns = useMemo(
    () =>
      generateUserAudienceColumns({
        isLoading: isLoading,
      }),
    [isLoading],
  );

  return (
    <div className="mt-10">
      <TableDataContainer className="pt-1">
        <div className="px-6 py-4">
          <TableHeader
            total={users?.data?.total || 0}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            objectName="User"
          />
        </div>
        <Table
          columns={columns}
          data={users?.data ? users.data.data : isLoading ? Array(10).fill({}) : []}
          url="/users"
        />
        <LocalPagination
          page={page}
          pageSize={pageSize}
          goToPage={goToPage}
          changePageSize={changePageSize}
          totalPages={users?.data?.totalPages || 0}
        />
      </TableDataContainer>
    </div>
  );
};

export default TableAudienceUsers;
