'use client';
import { ColumnDef } from '@tanstack/table-core';
import React from 'react';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { User } from '@/backend/users/entities/User';
import { formatPhone } from '@/utils/phone-format';
import UserVerificationBadge from '../../users/user-verification-badge';

type ColumnsProps = {
  isLoading?: boolean;
};

function generateUserAudienceColumns({ isLoading }: ColumnsProps) {
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'userId',
      header: ({ column }) => <Header column={column}>User ID</Header>,
      cell: ({ row }) => {
        const userId = row.original.id;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="whitespace-nowrap text-foreground font-medium">{userId}</span>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => <Header column={column}>Name</Header>,
      cell: ({ row }) => {
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className="flex flex-col max-w-40">
              <span className="whitespace-nowrap text-foreground">{`${row.original.firstName} ${row.original.lastName}`}</span>
              <span className="text-muted-foreground truncate">{row.original.licenseNumber}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => <Header column={column}>Contact Information</Header>,
      cell: ({ row }) => {
        const phone = row.original.phone;
        const email = row.original.email;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className="flex flex-col">
              <span className="whitespace-nowrap">{email}</span>
              <span className="text-muted-foreground whitespace-nowrap">{phone ? formatPhone(phone) : 'N/A'}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => <Header column={column}>Status</Header>,
      cell: ({ row }) => {
        const verificationStatus = row.original.verificationStatus;
        return (
          <SkeletonCell isLoading={isLoading}>
            <UserVerificationBadge verificationStatus={verificationStatus!} />
          </SkeletonCell>
        );
      },
      enableSorting: false,
    },
  ];

  return columns;
}

export { generateUserAudienceColumns };
