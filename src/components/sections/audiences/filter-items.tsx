'use client';
import React, { useEffect, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';

import FilterItem from './filter-item';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useUsersIncluded } from './hooks/useUsersIncluded';
import { FilterDataStore, useFilterDataStore } from './hooks/useFilterDataStore';
import { CreateAudiencePayload } from '@/backend/audiences/validations/add-update-audience';
import { cn, isEmpty } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import SelectSpecificUserModal from './select-specific-user-modal/select-specific-user-modal';
import { User } from '@/backend/users/entities/User';

type Props = {
  isEditMode?: boolean;
  isLoadingFilterData?: boolean;
  canAddFilter?: boolean;
  showNoFiltersMessage?: boolean;
};

const FilterItems = ({
  isEditMode = true,
  isLoadingFilterData,
  canAddFilter = true,
  showNoFiltersMessage = false,
}: Props) => {
  const [openPopover, setOpenPopover] = useState(false);
  const { control } = useFormContext();
  const { updateCountUsersIncluded, filterTypes, removeFilterType, ...restState } = useFilterDataStore();
  const { update } = useFilterDataStore();
  const filtersName: string = 'filters';
  const { fields, append, remove, insert } = useFieldArray({ control, name: filtersName });
  const filterValues = useWatch({ control, name: filtersName });

  const [openSelectSpecificUser, setOpenSelectSpecificUser] = useState(false);

  const mappedFilters = useMemo(() => {
    if (isEmpty(filterValues)) return {};
    const filterPayload: Partial<CreateAudiencePayload> | undefined = filterValues?.reduce(
      (
        acc: Record<string, Array<string | number> | undefined>,
        filter: { type: string; value: Array<string | number> },
      ) => {
        const { type, value } = filter;

        // Special handling for userIds (specific users)
        if (type === 'userIds') {
          return {
            ...acc,
            [type]: value && value.length > 0 ? value : null,
          };
        }

        const key = type as keyof Omit<
          FilterDataStore,
          'usersIncluded' | 'updateCountUsersIncluded' | 'updateFilterTypes' | 'filterTypes' | 'removeFilterType'
        >;

        let filterValue = value && restState[key] && value.length === restState[key]?.length ? null : value;
        if (filterValue && filterValue.length === 0) filterValue = null;
        return {
          ...acc,
          [type]: filterValue,
        };
      },
      {},
    );

    return filterPayload ?? {};
  }, [filterValues, restState]);

  const { usersIncluded } = useUsersIncluded(mappedFilters);

  useEffect(() => {
    updateCountUsersIncluded(usersIncluded?.data?.usersIncluded || 0);
  }, [usersIncluded, updateCountUsersIncluded]);

  const handleConfirmSpecificUsers = (users: User[]) => {
    update({ specificUsers: users });
  };

  return (
    <div className="flex flex-col flex-grow">
      {!isLoadingFilterData ? (
        fields.map((field, index) => (
          <FilterItem
            key={field.id}
            fieldArrayName={filtersName}
            index={index}
            isDisabled={!isEditMode}
            remove={remove}
            insert={insert}
            canEdit={canAddFilter}
            setOpenSelectSpecificUser={setOpenSelectSpecificUser}
          />
        ))
      ) : (
        <Skeleton className="w-full h-28" />
      )}
      <div className={cn('mt-6', (filterTypes.length === 0 || !canAddFilter) && 'hidden')}>
        <Popover open={openPopover} onOpenChange={open => setOpenPopover(open)}>
          <PopoverTrigger asChild disabled={isLoadingFilterData}>
            <Button
              className="text-primary-brand bg-[#F9F5FF] h-10"
              disabled={!isEditMode || isLoadingFilterData}
              isLoading={isLoadingFilterData}
            >
              {!isLoadingFilterData && <Plus />}
              <span className="ml-2">Add Filter</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent align="start" className="p-0">
            {filterTypes.map(type => (
              <div
                key={type.field}
                className="py-2.5 px-3.5 hover:bg-primary-foreground first:rounded-t-md last:rounded-b-md cursor-pointer text-foreground text-base font-medium"
                onClick={() => {
                  append({ type: type.field });
                  removeFilterType(type.field);
                  setOpenPopover(false);
                }}
              >
                {type.label}
              </div>
            ))}
          </PopoverContent>
        </Popover>
      </div>
      {showNoFiltersMessage && !isLoadingFilterData && fields.length === 0 && (
        <div className="px-5 text-center m-auto text-muted-foreground w-full h-full flex justify-center items-center flex-grow">
          <p className="text-muted-foreground">
            This custom audience doesn’t have any filters
            <br /> set so all users will be targeted.
          </p>
        </div>
      )}
      <SelectSpecificUserModal
        open={openSelectSpecificUser}
        onCancel={() => setOpenSelectSpecificUser(false)}
        onConfirm={handleConfirmSpecificUsers}
      />
    </div>
  );
};
export default FilterItems;
