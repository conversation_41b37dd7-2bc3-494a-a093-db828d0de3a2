'use client';
import React, { useMemo } from 'react';
import TableDataContainer from '@/components/common/table-container';
import { Table } from '@/components/ui/table';
import { generateColumns } from '@/app/(protected)/users/[id]/columns';
import { useAudienceSurveys } from './hooks/useAudienceSurveys';

const TableAudienceSurveys = ({ audienceId }: { audienceId: number }) => {
  const { surveys, isLoadingSurveys } = useAudienceSurveys(audienceId);
  const columns = useMemo(() => generateColumns({ isLoading: isLoadingSurveys }), [isLoadingSurveys]);

  return (
    <div className="mt-10">
      <TableDataContainer className="pt-1 pb-0.5" showPagination={false}>
        <Table
          columns={columns}
          data={surveys?.data ? surveys.data : isLoadingSurveys ? Array(10).fill({}) : []}
          url="/surveys"
        />
      </TableDataContainer>
    </div>
  );
};
export default TableAudienceSurveys;
