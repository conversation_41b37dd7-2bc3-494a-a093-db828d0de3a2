'use client';
import { ColumnDef } from '@tanstack/table-core';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn } from '@/lib/utils';
import { ActionsCell } from '@/components/ui/table/action-cell';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { formatDate } from '@/utils/date-format';
import Link from 'next/link';
import { AudienceItem } from '@/lib/http/audiences/types';
import { AudienceFiltersCell } from '@/components/ui/table/audience-filters-cell';

type ColumnsProps = {
  onDelete: (id: number) => void;
  isDeleting?: boolean;
  onDuplicate: (id: number) => void;
  isLoading?: boolean;
};

function generateColumns({ onDelete, isDeleting, onDuplicate, isLoading }: ColumnsProps) {
  const columns: ColumnDef<AudienceItem>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => <Header column={column}>Name</Header>,
      cell: ({ row }) => {
        const name = row.original.name;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="whitespace-nowrap text-foreground font-medium">{name}</span>
          </SkeletonCell>
        );
      },
      size: 350,
      minSize: 200,
    },
    {
      accessorKey: 'numberOfUsers',
      header: ({ column }) => <Header column={column}>Audience Size</Header>,
      cell: ({ row }) => {
        const numberOfUsers = row.original.numberOfUsers;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="whitespace-nowrap text-foreground">{numberOfUsers}</span>
          </SkeletonCell>
        );
      },
      size: 150,
      minSize: 120,
    },
    {
      accessorKey: 'lastSurveyDate',
      header: ({ column }) => <Header column={column}>Last Survey Date</Header>,
      cell: ({ row }) => {
        const lastSurveyDate = row.original.lastSurveyDate;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="whitespace-nowrap text-foreground">
              {lastSurveyDate ? formatDate(lastSurveyDate) : ''}
            </span>
          </SkeletonCell>
        );
      },
      size: 150,
      minSize: 120,
    },
    {
      accessorKey: 'cities',
      header: ({ column }) => <Header column={column}>Filters</Header>,
      enableSorting: false,
      cell: ({ row }) => {
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1} className="w-full">
            <div className="w-full">
              <AudienceFiltersCell row={row} />
            </div>
          </SkeletonCell>
        );
      },
      size: 400,
      minSize: 200,
      enableResizing: true,
    },
    {
      id: 'actions',
      enableHiding: false,
      size: 80,
      minSize: 50,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 overflow-hidden' : '')}>
          <ActionsCell
            row={row}
            urlDetail={`/audiences/${row.original.id}`}
            deleteModalProps={{
              onDelete,
              isDeleting,
              title: 'Delete Audience',
              subtitle: 'Are you sure you want to delete this audience? This action cannot be reversed.',
            }}
          >
            <Link href={`/audiences/${row.original.id}`}>
              <DropdownMenuItem className="text-foreground font-medium cursor-pointer rounded-none">
                View / Edit
              </DropdownMenuItem>
            </Link>
            <DropdownMenuItem
              onClick={e => {
                e.stopPropagation();
                onDuplicate(row.original.id);
              }}
              className="text-foreground font-medium cursor-pointer rounded-none"
            >
              Duplicate
            </DropdownMenuItem>
          </ActionsCell>
        </SkeletonCell>
      ),
    },
  ];

  return columns;
}

export { generateColumns };
