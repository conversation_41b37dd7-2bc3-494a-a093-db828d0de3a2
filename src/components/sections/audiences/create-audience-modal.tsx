import React, { useEffect } from 'react';
import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { UseFormReturn } from 'react-hook-form';
import { Cross2Icon } from '@radix-ui/react-icons';
import { z } from 'zod';
import toast from 'react-hot-toast';

import { Form, Input, Label } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import PrimaryBadge from '@/components/common/badges/primary';
import { CreateAudiencePayload } from '@/backend/audiences/validations/add-update-audience';
import FilterItems from './filter-items';
import { FilterDataStore, useFilterDataStore } from './hooks/useFilterDataStore';
import { useCreateAudience } from './hooks/useCreateAudience';
import { useCheckAudienceName } from './hooks/useCheckAudienceName';
import { useInitFilterData } from './hooks/useInitFilterData';
import { specificName } from '@/backend/shared/validations/name';
import pluralize from 'pluralize';

type CreateAudienceModalProps = {
  onClose: () => void;
};

const schema = z.object({
  name: specificName('Audience'),
  filters: z.array(z.object({ type: z.string(), value: z.union([z.string().array(), z.number().array()]) })).optional(),
});

type FormData = z.infer<typeof schema>;

const CreateAudienceModal = ({ onClose }: CreateAudienceModalProps) => {
  const { usersIncluded, ...restState } = useFilterDataStore(state => state);
  const { createAudience, isAddingAudience } = useCreateAudience(onClose);
  const { checkAudienceName, isChecking } = useCheckAudienceName();
  const { isLoadingFilterData } = useInitFilterData();
  const { resetSpecificUsers } = useFilterDataStore(state => state);

  useEffect(() => {
    resetSpecificUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  async function onSubmit(data: FormData, formHandler?: UseFormReturn<FormData>) {
    const { name, filters } = data;
    const resultCheckName = await checkAudienceName(name);
    if (resultCheckName.data?.isNameExist) {
      formHandler && formHandler.setError('name', { message: 'This audience already exists' }, { shouldFocus: true });
      return;
    }
    if (resultCheckName.errors) {
      toast.error(resultCheckName.errors[0].message);
      return;
    }
    if (!filters) {
      await createAudience({ name });
      return;
    }

    const filterPayload: Partial<CreateAudiencePayload> = filters.reduce((acc, filter) => {
      const { type, value } = filter;
      let key = type as keyof Omit<FilterDataStore, 'usersIncluded'> | 'specialtyIds';
      if (key === 'specialtyIds') key = 'specialties';
      const filterValue = value.length === restState[key]?.length ? [] : value;
      return {
        ...acc,
        [type]: filterValue,
      };
    }, {});
    await createAudience({ name, ...filterPayload });
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="flex flex-col lg:!max-w-[80%] lg:min-h-[70%] max-h-screen lg:max-h-[90%] lg:!w-[932px] p-0 overflow-auto">
        <div className="flex flex-1 p-8 w-full">
          <Form schema={schema} onSubmit={onSubmit} className="w-full flex flex-col flex-1">
            <h1 className="font-bold text-2xl mb-9">New Audience</h1>
            <div className="flex flex-wrap gap-6 mb-8">
              <div className="max-w-[320px] w-full">
                <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
                  Audience Name
                </Label>
                <Input name="name" placeholder="Example: Pharmacists in ON" />
              </div>
            </div>
            <hr />

            <div className="flex mt-8 mb-3.5">
              <div className="font-bold text-base mr-6">Audience Settings</div>
              <PrimaryBadge className="bg-[#EFF8FF] text-[#175CD3] font-medium text-xs">
                {usersIncluded} Verified {pluralize('User', usersIncluded)} Included
              </PrimaryBadge>
            </div>
            <div className="flex-1">
              <FilterItems isLoadingFilterData={isLoadingFilterData} />
            </div>
            <div className="mt-8 sm:mt-[auto]">
              <div className="flex justify-end gap-3">
                <Button variant="secondary" className="font-semibold border border-input bg-white" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" className="font-semibold" isLoading={isChecking || isAddingAudience}>
                  Create Audience
                </Button>
              </div>
            </div>
          </Form>
        </div>
        <DialogClose className="absolute right-6 top-6 rounded-lg" aria-label="Close" onClick={onClose}>
          <Cross2Icon className="w-6 h-6" />
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};
export default CreateAudienceModal;
