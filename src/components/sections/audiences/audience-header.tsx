'use client';
import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import CreateAudienceModal from '@/components/sections/audiences/create-audience-modal';
import { useFilterDataStore } from '@/components/sections/audiences/hooks/useFilterDataStore';

const AudiencesHeader = ({ audiencesCount }: { audiencesCount: number }) => {
  const [open, setOpen] = useState(false);
  const { resetFilterTypes, resetSpecificUsers } = useFilterDataStore();
  return (
    <div className="flex flex-row justify-between px-4 lg:px-6 mb-[5px]">
      <div className="font-medium flex items-center">
        <div>Audiences</div>
        <Badge className="text-primary bg-[#F9F5FF] ml-2 h-fit px-2 py-0.5 text-xs">Count: {audiencesCount}</Badge>
      </div>
      <div className="flex justify-end flex-1 items-center lg:my-0 ml-0 lg:ml-6">
        <div className="flex">
          <Button className="h-10 px-2.5 md:px-4 border-none bg-primary-brand" onClick={() => setOpen(true)}>
            <Plus />
            <div className="ml-2 hidden md:block">Add Audience</div>
          </Button>
        </div>
      </div>
      {open && (
        <CreateAudienceModal
          onClose={() => {
            setOpen(false);
            resetFilterTypes();
            resetSpecificUsers();
          }}
        />
      )}
    </div>
  );
};
export default AudiencesHeader;
