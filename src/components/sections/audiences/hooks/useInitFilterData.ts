import { useEffect } from 'react';
import { useIndustryInfo } from '@/hooks/useIndustryInfo';
import { useCities } from './useCities';
import { useProvinces } from './useProvinces';
import { usePublishedSurveys } from './usePublishedSurveys';
import { useFilterDataStore } from './useFilterDataStore';
import { useGetSelectedSpecificUsers } from './useGetSelectedSpecificUsers';

export function useInitFilterData(userIds?: number[]) {
  const { industryInfo, isLoadingIndustryInfo } = useIndustryInfo();
  const { cities, isLoadingCities } = useCities();
  const { provinces, isLoadingProvinces } = useProvinces();
  const { publishedSurveys, isLoadingPublishedSurveys } = usePublishedSurveys();
  const { update } = useFilterDataStore();
  const { data: prioritizedUsers, isLoading: isLoadingPrioritizedUsers } = useGetSelectedSpecificUsers({
    ids: userIds ?? [],
  });

  useEffect(() => {
    update({
      practiceSettings: industryInfo?.data?.practiceSettings,
      employmentStatuses: industryInfo?.data?.employmentStatus,
      specialties: industryInfo?.data?.specialties,
    });
  }, [update, industryInfo]);

  useEffect(() => {
    update({ cities: cities?.data });
  }, [update, cities]);

  useEffect(() => {
    update({ provinces: provinces?.data });
  }, [update, provinces]);

  useEffect(() => {
    update({ completedSurveys: publishedSurveys?.data });
  }, [update, publishedSurveys]);

  useEffect(() => {
    if (prioritizedUsers) {
      const specificUsers = prioritizedUsers?.pages
        .flatMap(data => data.data.data)
        .filter(user => userIds?.includes(user.id));

      update({
        specificUsers,
      });
    }
  }, [update, prioritizedUsers, userIds]);

  return {
    isLoadingFilterData:
      isLoadingIndustryInfo ||
      isLoadingCities ||
      isLoadingProvinces ||
      isLoadingPublishedSurveys ||
      isLoadingPrioritizedUsers,
  };
}
