import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';
import { UpdateAudiencePayload } from '@/backend/audiences/validations/add-update-audience';
import { AUDIENCE_USERS_QUERY_KEY } from './useAudienceUsers';

export const listParamKeys = [
  'cities',
  'provinces',
  'practiceSettings',
  'completedSurveys',
  'employmentStatuses',
  'specialtyIds',
  'userIds',
];

export function useUpdateAudience(audienceId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: UpdateAudiencePayload) => api.audiences.updateAudience(audienceId, data),
    onSuccess: () => {
      toast.success('Audience updated successfully');
      queryClient.invalidateQueries({
        queryKey: ['audience', audienceId],
      });
      queryClient.invalidateQueries({
        queryKey: [AUDIENCE_USERS_QUERY_KEY],
      });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    updateAudience: mutateAsync,
    isUpdating: isPending,
  };
}
