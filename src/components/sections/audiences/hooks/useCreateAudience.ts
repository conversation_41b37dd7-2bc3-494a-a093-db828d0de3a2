import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { api } from '@/lib/http';
import { CreateAudiencePayload } from '@/backend/audiences/validations/add-update-audience';

export function useCreateAudience(cbSuccess?: () => void) {
  const queryClient = useQueryClient();

  const { mutateAsync: createAudience, isPending: isAddingAudience } = useMutation({
    mutationFn: (data: CreateAudiencePayload) => api.audiences.addAudience(data),
    onSuccess: async () => {
      toast.success('Audience created successfully');
      await queryClient.invalidateQueries({ queryKey: ['audiences'] });
      cbSuccess && cbSuccess();
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    createAudience,
    isAddingAudience,
  };
}
