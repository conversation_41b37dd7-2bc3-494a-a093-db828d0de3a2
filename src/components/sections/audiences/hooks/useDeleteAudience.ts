import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useDeleteAudience() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isError, error } = useMutation({
    mutationFn: (id: number) => api.audiences.deleteAudience(id),
    onSuccess: async () => {
      toast.success('Audience deleted successfully');
      await queryClient.invalidateQueries({ queryKey: ['audiences'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    deleteAudience: mutateAsync,
    isDeleting: isPending,
    isDeleteError: isError,
    deleteError: error,
  };
}
