import { usePagination } from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useSortQuery } from '@/hooks/useSortQuery';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { Audience } from '@/backend/audiences/entities/Audience';
import { useEffect } from 'react';

export function useAudiences(isPagination = true) {
  const { page, pageSize, previousPage } = usePagination();
  const { search } = useSearchQuery();
  const { sortBy, sortOrder } = useSortQuery({ sortBy: 'name' });
  const queryKey = isPagination ? ['audiences', page, pageSize, search, sortBy, sortOrder] : ['audiences'];

  const {
    data: audiences,
    isLoading,
    isError,
  } = useQuery({
    queryKey: queryKey,
    queryFn: async () => {
      if (isPagination) {
        return api.audiences.getAudiences({
          page,
          pageSize,
          search: search ?? '',
          sortBy: sortBy as keyof Audience,
          sortOrder: sortOrder === 'ASC' ? 'ASC' : 'DESC',
        });
      } else {
        return api.audiences.getAudiences();
      }
    },
    placeholderData: previousData => previousData,
  });

  useEffect(() => {
    if (!audiences?.data) return;
    if (isPagination && audiences.data.totalPages < page && page > 1) {
      previousPage();
    }
  }, [isPagination, page, previousPage, audiences]);

  return { audiences, isLoadingAudiences: isLoading, isError };
}
