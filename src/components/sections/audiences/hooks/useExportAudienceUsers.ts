import { stringToBlob } from '@/backend/shared/utils/export-csv';
import { api } from '@/lib/http';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useExportAudienceUsers() {
  const { mutate, isPending } = useMutation({
    mutationFn: (id: number) => api.audiences.exportAudienceUsers(id),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
    onSuccess: (data: string) => {
      const blob = stringToBlob(data);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'audience_users.csv';
      a.click();
    },
  });

  return {
    exportAudienceUser: mutate,
    isExporting: isPending,
  };
}
