import { useInfiniteQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';

interface UseGetSelectedSpecificUsersProps {
  ids: number[];
}

export function useGetSelectedSpecificUsers({ ids }: UseGetSelectedSpecificUsersProps) {
  const pageSize = ids.length;
  return useInfiniteQuery({
    queryKey: ['selected-specific-users', ids],
    initialPageParam: 1,
    queryFn: ({ pageParam = 1 }) => api.users.getPrioritizedUsers({ page: pageParam, pageSize, search: '' }, ids),
    getNextPageParam: (lastPage, _pages) => {
      if (lastPage.data.data.length < pageSize) return undefined;
      return _pages.length + 1;
    },
    enabled: !!ids.length && ids.length > 0,
  });
}
