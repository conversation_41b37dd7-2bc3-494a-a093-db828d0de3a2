import { api } from '@/lib/http';
import { useQuery } from '@tanstack/react-query';
import { PaginationPayload } from '@/types/pagination';
import { User } from '@/backend/users/entities/User';

export const AUDIENCE_USERS_QUERY_KEY = 'audienceUsers';

export function useAudienceUsers(id: number, payload: PaginationPayload<User> = {}) {
  const { page, pageSize, search } = payload;
  const { data, ...rest } = useQuery({
    queryKey: [AUDIENCE_USERS_QUERY_KEY, id, page, pageSize, search],
    queryFn: () => api.audiences.getAudienceUsers(id, payload),
  });

  return {
    users: data,
    ...rest,
  };
}
