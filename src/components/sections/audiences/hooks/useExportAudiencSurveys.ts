import { stringToBlob } from '@/backend/shared/utils/export-csv';
import { api } from '@/lib/http';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export const useExportAudienceSurveys = () => {
  const { mutate, isPending } = useMutation({
    mutationFn: (id: number) => api.audiences.exportAudienceSurveys(id),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
    onSuccess: (data: string) => {
      const blob = stringToBlob(data);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'audience_surveys.csv';
      a.click();
    },
  });

  return {
    exportAudienceSurveys: mutate,
    isExporting: isPending,
  };
};
