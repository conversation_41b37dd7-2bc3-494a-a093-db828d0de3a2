import { City } from '@/backend/users/entities/City';
import { Survey } from '@/backend/surveys/entities/Survey';
import { EmploymentStatus } from '@/backend/users/entities/EmploymentStatus';
import { PracticeSetting } from '@/backend/users/entities/PracticeSetting';
import { Province } from '@/backend/users/entities/Province';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { create } from 'zustand';
import { User } from '@/backend/users/entities/User';

export type FilterDataStore = {
  cities: City[];
  completedSurveys: Survey[];
  employmentStatuses: EmploymentStatus[];
  practiceSettings: PracticeSetting[];
  provinces: Province[];
  specialties: Specialty[];
  specificUsers: User[];
  usersIncluded: number;
  filterTypes: Record<string, string>[];
  update(data: Partial<FilterDataStore>): void;
  updateCountUsersIncluded(quantity: number): void;
  addFilterType(field: string): void;
  removeFilterType(field: string): void;
  resetFilterTypes(): void;
  resetSpecificUsers(): void;
};

export const initialFilterTypes: Record<string, string>[] = [
  { label: 'City', field: 'cities' },
  { label: 'Completed Survey', field: 'completedSurveys' },
  { label: 'Employment Status', field: 'employmentStatuses' },
  { label: 'Practice Setting', field: 'practiceSettings' },
  { label: 'Province', field: 'provinces' },
  { label: 'Specialty', field: 'specialtyIds' },
  { label: 'Specific Users', field: 'userIds' },
];

export const useFilterDataStore = create<FilterDataStore>(set => ({
  cities: [],
  completedSurveys: [],
  employmentStatuses: [],
  practiceSettings: [],
  provinces: [],
  specialties: [],
  specificUsers: [],
  filterTypes: initialFilterTypes,
  usersIncluded: 0,
  update: (data: Partial<FilterDataStore>) => set(state => ({ ...state, ...data })),
  updateCountUsersIncluded: (quantity: number) => set(state => ({ ...state, usersIncluded: quantity })),
  addFilterType: field =>
    set(state => {
      const filter = initialFilterTypes.find(f => f.field === field);
      if (filter) return { ...state, filterTypes: [...state.filterTypes, filter] };
      return state;
    }),
  removeFilterType: field =>
    set(state => ({ ...state, filterTypes: state.filterTypes.filter(f => f.field !== field) })),
  resetFilterTypes: () => set(state => ({ ...state, filterTypes: initialFilterTypes })),
  resetSpecificUsers: () => set(state => ({ ...state, specificUsers: [] })),
}));
