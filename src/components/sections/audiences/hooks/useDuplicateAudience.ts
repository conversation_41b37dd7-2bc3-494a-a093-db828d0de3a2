import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useDuplicateAudience() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (id: number) => api.audiences.duplicateAudience(id),
    onSuccess: async () => {
      toast.success('Audience duplicated successfully');
      await queryClient.invalidateQueries({ queryKey: ['audiences'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return { duplicateAudience: mutateAsync, isDuplicating: isPending };
}
