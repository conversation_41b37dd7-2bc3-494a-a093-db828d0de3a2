import { useInfiniteQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';

interface UseInfinitePrioritizedUsersProps {
  search: string;
  pageSize: number;
  ids: number[];
}

export function useInfinitePrioritizedUsers({ search, pageSize, ids }: UseInfinitePrioritizedUsersProps) {
  return useInfiniteQuery({
    queryKey: ['prioritized-users', search, ids],
    initialPageParam: 1,
    queryFn: ({ pageParam = 1 }) => api.users.getPrioritizedUsers({ page: pageParam, pageSize, search }, ids),
    getNextPageParam: (lastPage, _pages) => {
      if (lastPage.data.data.length < pageSize) return undefined;
      return _pages.length + 1;
    },
  });
}
