import { useQuery } from '@tanstack/react-query';
import { UsersIncludedPayload } from '@/backend/audiences/validations/add-update-audience';
import { api } from '@/lib/http';

export function useUsersIncluded(payload: UsersIncludedPayload) {
  const { data, isPending } = useQuery({
    queryKey: ['usersIncluded', payload],
    queryFn: async () => api.audiences.countUsersIncluded(payload),
    placeholderData: previousData => previousData,
  });

  return {
    usersIncluded: data,
    isCounting: isPending,
  };
}
