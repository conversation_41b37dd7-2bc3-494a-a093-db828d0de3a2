'use client';
import React, { useMemo } from 'react';

import { Table } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import CustomPagination from '@/components/common/custom-pagination';
import { useAudiences } from './hooks/useAudiences';
import { useDeleteAudience } from './hooks/useDeleteAudience';
import AudiencesHeader from './audience-header';
import { generateColumns } from './columns';
import { useDuplicateAudience } from './hooks/useDuplicateAudience';

const TableAudiences = () => {
  const { audiences, isLoadingAudiences, isError } = useAudiences();
  const { deleteAudience, isDeleting } = useDeleteAudience();
  const { duplicateAudience } = useDuplicateAudience();
  const columns = useMemo(
    () =>
      generateColumns({
        onDelete: deleteAudience,
        onDuplicate: duplicateAudience,
        isLoading: isLoadingAudiences,
        isDeleting,
      }),
    [isLoadingAudiences, deleteAudience, duplicateAudience, isDeleting],
  );

  return (
    <>
      <AudiencesHeader audiencesCount={audiences?.data?.total ?? 0} />
      <Table
        columns={columns}
        data={audiences?.data?.data ? audiences?.data?.data : isLoadingAudiences ? Array(10).fill({}) : []}
        url="/audiences"
        containerClassName={cn(!isLoadingAudiences && !isError && 'peer is-done')}
      />
      <CustomPagination totalPages={audiences?.data?.totalPages ?? 10} />
    </>
  );
};
export default TableAudiences;
