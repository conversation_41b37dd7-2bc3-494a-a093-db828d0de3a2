'use client';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import React, { useState } from 'react';
import TableAudienceSurveys from '../table-audience-surveys';
import TableAudienceUsers from '../user-audience/table-user-audience';
import { Separator } from '@/components/ui/separator';

const AudienceTabs = ({ audienceId }: { audienceId: number }) => {
  const [activeTab, setActiveTab] = useState('audience-surveys');

  return (
    <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="mt-8">
      <TabsList className="bg-background px-0">
        <TabsTrigger
          value="audience-surveys"
          className="rounded-none text-muted-foreground bg-background text-base py-4 flex flex-col items-center bg-none px-0 data-[state=active]:!bg-background data-[state=active]:text-primary data-[state=active]:shadow-none relative"
        >
          <div className="px-2">Audience Surveys</div>
          {activeTab === 'audience-surveys' && <Separator className="w-full h-0.5 bg-primary absolute bottom-0" />}
        </TabsTrigger>
        <TabsTrigger
          value="audience-users"
          className="rounded-none text-muted-foreground bg-background text-base py-4 flex flex-col items-center bg-none px-0 data-[state=active]:!bg-background data-[state=active]:text-primary data-[state=active]:shadow-none relative"
        >
          <div className="px-2">Users in Audience</div>
          {activeTab === 'audience-users' && <Separator className="w-full h-0.5 bg-primary absolute bottom-0" />}
        </TabsTrigger>
      </TabsList>
      <TabsContent value="audience-surveys">
        <TableAudienceSurveys audienceId={audienceId} />
      </TabsContent>
      <TabsContent value="audience-users">
        <TableAudienceUsers audienceId={audienceId} />
      </TabsContent>
    </Tabs>
  );
};

export default AudienceTabs;
