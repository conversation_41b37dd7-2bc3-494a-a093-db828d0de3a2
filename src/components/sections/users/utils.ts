import { UserType } from '@/backend/users/entities/User';

export function getUserTypeLabel(userType: UserType) {
  switch (userType) {
    case UserType.HCPUser:
      return 'HCP User';
    case UserType.HCPUserImported:
      return 'HCP User - Imported';
    case UserType.Client:
      return 'Client';
    case UserType.ClientImported:
      return 'Client - Imported';
    case UserType.Denied:
      return 'Denied';
    case UserType.Internal:
      return 'Internal';
    case UserType.Unverified:
      return 'Unverified';
    default:
      return 'N/A';
  }
}
