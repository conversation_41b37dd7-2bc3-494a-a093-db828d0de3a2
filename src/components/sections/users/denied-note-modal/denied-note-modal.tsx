import { ModalConfirmVerificationProps } from '@/components/common/modal-confirm-verification';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/form';
import { DialogFooter, DialogTitle } from '@/components/ui/dialog';
import { DialogContent } from '@/components/ui/dialog';
import { Dialog } from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import React from 'react';
import { z } from 'zod';

interface DeniedNoteModalProps extends ModalConfirmVerificationProps {
  onSave: (data: z.infer<typeof schema>) => void;
  note?: string | null;
}

const schema = z.object({
  note: z.string().max(500, { message: 'Comments must be less than 500 characters' }).optional().nullable(),
});

const DeniedNoteModal = ({ openDialog, setOpenDialog, onSave, isConfirming, note }: DeniedNoteModalProps) => {
  const handleOnSubmit = (data: z.infer<typeof schema>) => {
    if ((!note && !data.note) || note === data.note) {
      setOpenDialog(false);
      return;
    }

    onSave?.({
      note: data.note?.trim() !== '' ? data.note : null,
    });

    setOpenDialog(false);
  };

  return (
    <Dialog open={openDialog} onOpenChange={setOpenDialog}>
      <DialogContent className="!w-[400px]" onClick={e => e.stopPropagation()}>
        <DialogTitle className="mt-5">Comments</DialogTitle>
        <Form id="deny-user-form" className="mt-4" defaultValues={{ note }} onSubmit={handleOnSubmit} schema={schema}>
          <Textarea
            placeholder="e.g. User denied to due precedent fraudulent behaviour"
            name="note"
            className="h-[118px] resize-none text-base font-normal overflow-y-auto"
          />
        </Form>
        <DialogFooter className="mt-8 flex justify-between">
          <Button variant="outline" className="h-10 flex-1" onClick={() => setOpenDialog(false)}>
            Cancel
          </Button>
          <Button type="submit" form="deny-user-form" variant="default" className="h-10 flex-1" disabled={isConfirming}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeniedNoteModal;
