'use client';
import { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { ImportUsersStep, useImportUserStore } from './useImportUsersStore';
import { User } from '@/backend/users/entities/User';
import { useImportUsers } from '@/components/sections/users/import-users-modal/useImportUsers';
import { ImportUsersPayload } from '@/backend/users/validations/import-user';
import { parse } from 'date-fns';

type ControlButtonsProps = {
  close: () => void;
  isPreviewMode: boolean;
  isResolveMode: boolean;
};

const ControlButtons = ({ close, isPreviewMode, isResolveMode }: ControlButtonsProps) => {
  const { users, updateStep, updateUsers, step } = useImportUserStore();

  const { importUsers, isImporting } = useImportUsers(close);
  const isError = useMemo(() => users.some(user => user.savingErrors.length > 0), [users]);
  const isDuplicated = useMemo(() => users.some(user => user.existedUser && !user.selectedUser), [users]);
  const isErrorOrDuplicated = isError || isDuplicated;

  function onSaveConflicts() {
    updateUsers(
      users.map(user => ({
        ...user,
        user: user.existedUser ? ((user.selectedUser || user.existedUser) as User) : user.user,
        selectedUser: user.selectedUser || (user.existedUser as User),
      })),
    );
    updateStep(ImportUsersStep.PREVIEW);
  }

  if (step === ImportUsersStep.SELECT_FILE) {
    return (
      <div className="flex justify-end items-center text-error gap-3">
        <Button
          variant="secondary"
          className="flex-1 font-semibold border border-input bg-white rounded-lg"
          onClick={close}
        >
          Cancel
        </Button>

        <Button
          className="flex-1 font-semibold rounded-lg"
          disabled={users.length === 0}
          onClick={async () => {
            updateStep(ImportUsersStep.PREVIEW);
          }}
        >
          Preview
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-end">
      {isPreviewMode && isDuplicated && (
        <Button variant="secondary" className="font-semibold border border-input bg-white rounded-lg" onClick={close}>
          Cancel
        </Button>
      )}
      <div className="flex-1 flex flex-col lg:flex-row justify-end items-end lg:items-center text-error gap-3">
        {isErrorOrDuplicated && isPreviewMode && <p className="">Please resolve errors and duplicates to continue</p>}
        <div className="flex flex-col md:flex-row gap-3">
          {isPreviewMode && isDuplicated ? (
            <Button
              variant="secondary"
              className="font-semibold rounded-lg bg-[#F9F5FF] text-primary"
              onClick={() => {
                updateStep(ImportUsersStep.RESOLVE_DUPLICATES);
              }}
              disabled={isError}
            >
              Resolve Duplicates
            </Button>
          ) : (
            <Button
              variant="secondary"
              className="font-semibold border border-input bg-white rounded-lg"
              onClick={close}
            >
              Cancel
            </Button>
          )}
          {isResolveMode ? (
            <Button className="font-semibold rounded-lg" onClick={onSaveConflicts}>
              Save Changes
            </Button>
          ) : (
            <Button
              className="font-semibold rounded-lg"
              disabled={isErrorOrDuplicated || isImporting}
              onClick={async () => {
                const usersData = users.map(user => {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  let birthday: any = user.user.birthday;
                  if (birthday && typeof birthday === 'string') {
                    const date = parse(birthday, 'yyyy-MM-dd', new Date());
                    if (isNaN(date.getTime())) {
                      birthday = null;
                    }
                  }

                  // Set userType to null if it's an empty string or undefined
                  // Note: Due to CSV processing, userType might be an empty string at runtime despite typing
                  const currentUserType = (user.user as any).userType;
                  const userType = !currentUserType || currentUserType === '' ? null : currentUserType;

                  return {
                    ...user.user,
                    birthday,
                    userType,
                  };
                });
                await importUsers({ users: usersData } as ImportUsersPayload);
              }}
              isLoading={isImporting}
            >
              Add Users
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
export default ControlButtons;
