import { User } from '@/backend/users/entities/User';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserImportValidated } from '@/backend/users/types/import-user';
import { parse } from 'date-fns';
import { z } from 'zod';
import { create } from 'zustand';

export enum ImportUsersStep {
  SELECT_FILE,
  PREVIEW,
  RESOLVE_DUPLICATES,
}

type UserWithRealErrors = UserImportValidated & {
  savingErrors: string[];
};

type ImportUsersStore = {
  step: ImportUsersStep;
  users: UserWithRealErrors[];
  updateUser: (user: UserImportValidated) => void;
  updateUsers: (users: UserImportValidated[]) => void;
  updateStep: (step: ImportUsersStep) => void;
};

const requiredFields = ['firstName', 'lastName', 'email', 'specialtyId'];
const requiredSchema = z.object({
  firstName: z
    .string()
    .min(1, { message: 'First name cannot be empty' })
    .max(50, { message: 'First name must be 50 characters or less' })
    .regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i'), {
      message: 'First name can only contain letters, spaces, hyphens, and apostrophes',
    }),
  lastName: z
    .string()
    .min(1, { message: 'Last name cannot be empty' })
    .max(50, { message: 'Last name must be 50 characters or less' })
    .regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i'), {
      message: 'Last name can only contain letters, spaces, hyphens, and apostrophes',
    }),
  email: z.string({ required_error: 'Email is required' }).email('Invalid email address').toLowerCase(),
});

export const useImportUserStore = create<ImportUsersStore>(set => ({
  step: ImportUsersStep.SELECT_FILE,
  users: [],
  updateUser: userInfo =>
    set(state => {
      const index = state.users.findIndex(u => u.user.id === userInfo.user.id);
      if (index === -1) return state;

      const users = [...state.users];
      const savingErrors = validateFields(userInfo, users, index);
      users[index] = { ...users[index], ...userInfo, savingErrors };
      return { users };
    }),
  updateUsers: users =>
    set({
      users: users.map((user, idx) => {
        const savingErrors = validateFields(user, users as UserWithRealErrors[], idx);
        user.user.id = idx;
        return {
          ...user,
          savingErrors,
        };
      }),
    }),
  updateStep: step => set({ step }),
}));

const duplicatedEmailError = (users: UserWithRealErrors[], email: string, originalErrors: string[], idx?: number) => {
  const index = users.findIndex(u => u.user.email === email);
  if (index === -1 || index === idx) return originalErrors;
  return [...originalErrors, 'email'];
};

const validateFields = (user: UserImportValidated, users: UserWithRealErrors[], idx: number) => {
  let savingErrors = requiredFields.filter(field => !user.user[field as keyof (User | UserAdmin)]);
  const schemaValidation = requiredSchema.safeParse(user.user);
  if (!schemaValidation.success) {
    savingErrors = [...savingErrors, ...schemaValidation.error.errors.map(err => err.path[0] as string)];
  }
  if (user.user.email) {
    savingErrors = duplicatedEmailError(users, user.user.email, savingErrors, idx);
  }

  if (user.user.birthday) {
    const dob = user.user.birthday;
    const date = dob ? (typeof dob === 'string' ? parse(dob, 'yyyy-MM-dd', new Date()) : dob) : undefined;
    const inValidDate = date && isNaN(date?.getTime());
    if (inValidDate) {
      savingErrors = [...savingErrors, 'birthday'];
    }
  }
  savingErrors = Array.from(new Set(savingErrors));

  return savingErrors;
};
