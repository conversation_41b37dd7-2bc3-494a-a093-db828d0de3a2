import { useMutation } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { useImportUserStore } from '@/components/sections/users/import-users-modal/useImportUsersStore';

export function useValidateCsv() {
  const { updateUsers } = useImportUserStore();
  const { mutate, isPending } = useMutation({
    mutationFn: (data: FormData) => api.users.validateCsvImported(data),
    onSuccess: async data => {
      updateUsers(data.data);
    },
  });

  return {
    validateCsv: mutate,
    isValidating: isPending,
  };
}
