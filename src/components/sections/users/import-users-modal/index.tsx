'use client';
import React from 'react';
import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { Cross2Icon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';
import TableData from './table-data';
import { ImportUsersStep, useImportUserStore } from './useImportUsersStore';
import Dropzone from './dropzone';
import ControlButtons from './control-buttons';

type ImportUsersModalProps = {
  onClose: () => void;
};

const ImportUsersModal = ({ onClose }: ImportUsersModalProps) => {
  const { step, updateUsers, updateStep } = useImportUserStore();
  const isPreviewMode = step === ImportUsersStep.PREVIEW;
  const isResolveMode = step === ImportUsersStep.RESOLVE_DUPLICATES;

  function close() {
    if (isResolveMode) {
      updateStep(ImportUsersStep.PREVIEW);
      return;
    }
    updateUsers([]);
    updateStep(ImportUsersStep.SELECT_FILE);
    onClose();
  }

  return (
    <Dialog open onOpenChange={close}>
      <DialogContent
        className={cn(
          'flex lg:min-h-fit 2xl:min-w-[auto] lg:max-w-[80%] p-0',
          isPreviewMode || isResolveMode ? 'lg:min-w-[80%] lg:!h-[90%]' : 'lg:min-w-auto',
        )}
      >
        <div className="flex flex-col flex-1 p-8 w-full">
          <div className="w-full flex flex-col h-full">
            <h1
              className={cn(
                'font-bold text-2xl',
                isPreviewMode ? 'mb-2' : 'mb-9',
                isPreviewMode || isResolveMode ? '!mb-4' : '',
              )}
            >
              {isResolveMode ? 'Resolve Duplicates' : 'Import User List'}
            </h1>
            <div className={cn('flex-1 flex flex-col', isResolveMode ? 'overflow-auto mb-4 pr-2' : 'overflow-hidden')}>
              {!isPreviewMode && !isResolveMode ? <Dropzone /> : <TableData />}
            </div>
            <ControlButtons close={close} isPreviewMode={isPreviewMode} isResolveMode={isResolveMode} />
          </div>
        </div>
        <DialogClose className="absolute right-6 top-6 rounded-lg" aria-label="Close" onClick={onClose}>
          <Cross2Icon className="w-6 h-6" />
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};
export default ImportUsersModal;
