import { User } from '@/backend/users/entities/User';
import ErrorBadge from '@/components/common/badges/error';
import InformationBadge from '@/components/common/badges/information';
import Unverified from '@/components/common/badges/unverified';
import TableDataContainer from '@/components/common/table-container';
import { Table } from '@/components/ui/table';
import { useIndustryInfo } from '@/hooks/useIndustryInfo';
import { useMemo } from 'react';
import generateColumns from './columns';
import { ImportUsersStep, useImportUserStore } from './useImportUsersStore';
import pluralize from 'pluralize';

const TableData = () => {
  const { users, updateUser, step } = useImportUserStore();
  const { industryInfo } = useIndustryInfo();
  const columns = useMemo(
    () =>
      generateColumns({
        specialties: industryInfo?.data?.specialties ?? [],
        practiceSettings: industryInfo?.data?.practiceSettings ?? [],
        employmentStatuses: industryInfo?.data?.employmentStatus ?? [],
        updateUser,
        isResolvingDuplicates: step === ImportUsersStep.RESOLVE_DUPLICATES,
      }),
    [updateUser, step, industryInfo],
  );
  const totalErrors = users.reduce((acc, user) => acc + user.savingErrors.length, 0);
  const totalDuplicates = users.filter(user => !user.selectedUser && user.existedUser).length;

  return (
    <>
      <div className="mb-6 flex gap-4">
        <InformationBadge>
          {users.length} {pluralize('User', users.length)} Ready
        </InformationBadge>
        {totalErrors > 0 && (
          <ErrorBadge>
            {totalErrors} {pluralize('Error', totalErrors)}
          </ErrorBadge>
        )}
        {totalDuplicates > 0 && (
          <Unverified>
            {totalDuplicates} {pluralize('Duplicate', totalDuplicates)}
          </Unverified>
        )}
      </div>
      {step === ImportUsersStep.PREVIEW ? (
        <TableDataContainer showPagination={false} className="mb-6 py-0.5 max-h-full overflow-auto">
          <Table columns={columns} data={users} tableCellClassName="relative" />
        </TableDataContainer>
      ) : (
        users
          .filter(user => user.existedUser)
          .map((user, index) => {
            return (
              <div key={`row-${user.user.id}`} className="flex mb-4">
                <div className="pt-12 flex flex-col mr-4">
                  <div className="flex-1 flex flex-col justify-center">
                    <input
                      type="radio"
                      name={`${user.user?.email}-${index}`}
                      className="w-5 h-5 accent-primary"
                      defaultChecked
                      onChange={() => {
                        updateUser({ ...user, selectedUser: user.existedUser as User });
                      }}
                    />
                  </div>
                  <div className="flex-1 flex flex-col justify-center">
                    <input
                      type="radio"
                      name={`${user.user?.email}-${index}`}
                      className="w-5 h-5 accent-primary"
                      onChange={() => {
                        updateUser({ ...user, selectedUser: user.user as User });
                      }}
                    />
                  </div>
                </div>
                <TableDataContainer showPagination={false} className="py-0.5 max-h-full overflow-auto">
                  <Table
                    columns={columns}
                    data={[user, { ...user, existedUser: null }]}
                    tableCellClassName="relative"
                  />
                </TableDataContainer>
              </div>
            );
          })
      )}
    </>
  );
};

export default TableData;
