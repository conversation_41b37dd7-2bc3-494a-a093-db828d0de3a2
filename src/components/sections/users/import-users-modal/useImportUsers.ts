import { ImportUsersPayload } from '@/backend/users/validations/import-user';
import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useImportUsers(onSuccess?: () => void) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: ImportUsersPayload) => api.users.importUsers(data),
    onSuccess: async () => {
      onSuccess && onSuccess();
      toast.success('Users imported successfully');
      await queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: error => {
      toast.error(error.message);
    },
  });

  return {
    importUsers: mutateAsync,
    isImporting: isPending,
  };
}
