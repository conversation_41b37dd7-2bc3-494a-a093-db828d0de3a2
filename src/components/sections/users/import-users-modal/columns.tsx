import { Specialty } from '@/backend/specialties/entities/Specialty';
import { EmploymentStatus } from '@/backend/users/entities/EmploymentStatus';
import { PracticeSetting } from '@/backend/users/entities/PracticeSetting';
import { User } from '@/backend/users/entities/User';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserImportValidated } from '@/backend/users/types/import-user';
import ErrorBadge from '@/components/common/badges/error';
import InformationBadge from '@/components/common/badges/information';
import Unverified from '@/components/common/badges/unverified';
import VerifiedBadge from '@/components/common/badges/verified';
import { Calendar } from '@/components/ui/calendar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn, formatDateToYYYYMMDD } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import { ColumnDef, Row } from '@tanstack/table-core';
import { parse } from 'date-fns';
import { ChevronDown } from 'lucide-react';
import React, { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { z } from 'zod';
import UserRoleActions from '../table-users/userRole-actions';

const emailSchema = z.string().email('Invalid email address').toLowerCase();

const ErrorCellWrapper = ({ children, className }: PropsWithChildren<{ className?: string }>) => {
  return (
    <>
      {children}
      <div className={cn('w-full h-[1px] absolute bottom-0 left-0 bg-error', className)}></div>
    </>
  );
};

const InputCell = ({
  updateUser,
  name,
  row,
  defaultValue,
}: {
  updateUser: (user: UserImportValidated) => void;
  name: string;
  row: Row<UserImportValidated & { savingErrors: string[] }>;
  defaultValue?: string;
}) => {
  const [showAsLabel, setShowAsLabel] = useState(true);
  const ref = useRef<HTMLInputElement>(null);
  const user = row.original.user;
  const value = user[name as keyof (User | UserAdmin)] as string | undefined;
  const isError = name === 'email' ? !value || !emailSchema.safeParse(value).success : !(value && value.length > 0);

  useEffect(() => {
    if (!showAsLabel) {
      setTimeout(() => {
        ref.current?.focus();
      }, 100);
    }
  }, [showAsLabel]);

  return (
    <>
      <span className="invisible w-full h-full pl-4 flex items-center truncate ...">
        {isError ? 'Not Found' : value}
      </span>
      <div className="w-full h-full absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center">
        <div className="relative w-full h-full">
          <span
            className={cn(
              isError ? 'text-error' : 'text-foreground',
              !showAsLabel && 'invisible',
              'absolute w-full h-full pl-4 flex items-center truncate ...',
            )}
            onClick={() => {
              setShowAsLabel(false);
            }}
          >
            {isError ? 'Not Found' : value}
          </span>
          <input
            className={cn(
              'w-full h-full pl-4 bg-inherit placeholder:text-error focus:outline-none focus-visible:outline-none absolute',
              showAsLabel && 'invisible',
              isError && 'text-error',
            )}
            ref={ref}
            placeholder="Not Found"
            defaultValue={defaultValue}
            onBlur={() => setShowAsLabel(true)}
            onChange={e => {
              const newValue = e.target.value;
              if (name === 'email') {
                const result = emailSchema.safeParse(newValue);
                if (result.success) {
                  updateUser({ ...row.original, user: { ...user, [`${name}`]: result.data } });
                } else {
                  updateUser({ ...row.original, user: { ...user, [`${name}`]: newValue } });
                }
              } else {
                updateUser({ ...row.original, user: { ...user, [`${name}`]: newValue } });
              }
            }}
          />
        </div>
      </div>
    </>
  );
};

const DateInputCell = ({
  defaultValue,
  updateUser,
  name,
  row,
  isResolvingDuplicates,
}: {
  defaultValue?: Date | string | null;
  row: Row<UserImportValidated & { savingErrors: string[] }>;
  updateUser: (user: UserImportValidated) => void;
  name: string;
  isResolvingDuplicates: boolean;
}) => {
  const [openCalendar, setOpenCalendar] = React.useState(false);
  const date = defaultValue
    ? typeof defaultValue === 'string'
      ? parse(defaultValue, 'yyyy-MM-dd', new Date())
      : defaultValue
    : undefined;
  const inValidDate = date && isNaN(date?.getTime());

  return (
    <ErrorCellWrapper className={cn(!inValidDate && 'hidden')}>
      <Popover open={openCalendar} onOpenChange={open => setOpenCalendar(open)}>
        <PopoverTrigger disabled={isResolvingDuplicates}>
          {inValidDate ? (
            <span className={isResolvingDuplicates ? 'text-[#98A2B3]' : 'text-error'}>Invalid Date</span>
          ) : (
            <span className={defaultValue ? 'text-foreground' : 'text-[#98A2B3]'}>
              {date ? formatDate(date) : 'Not Found'}
            </span>
          )}
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 mt-0.5 bg-white shadow-md border rounded-lg z-[999]" align="center">
          <Calendar
            mode="single"
            selected={date && !inValidDate ? date : new Date()}
            onSelect={selectedDate => {
              if (selectedDate) {
                const formattedDate = formatDateToYYYYMMDD(selectedDate);

                updateUser({
                  ...row.original,
                  user: {
                    ...row.original.user,
                    [`${name}`]: formattedDate,
                  },
                });
              }
            }}
            initialFocus
            defaultMonth={date && !inValidDate ? date : new Date()}
            disabled={date => date > new Date()}
          />
        </PopoverContent>
      </Popover>
    </ErrorCellWrapper>
  );
};

type GenerateColumnsParams = {
  specialties: Specialty[];
  practiceSettings: PracticeSetting[];
  employmentStatuses: EmploymentStatus[];
  updateUser: (user: UserImportValidated) => void;
  isResolvingDuplicates: boolean;
};

function generateColumns({
  specialties,
  updateUser,
  isResolvingDuplicates,
  practiceSettings,
  employmentStatuses,
}: GenerateColumnsParams) {
  const columns: ColumnDef<UserImportValidated & { savingErrors: string[] }>[] = [
    {
      accessorKey: 'errors',
      header: ({ column }) => <Header column={column}>Status</Header>,
      cell: ({ row }) => {
        const { existedUser, selectedUser, savingErrors } = row.original;

        if (isResolvingDuplicates) {
          if (!existedUser) {
            return (
              <div className="relative">
                <VerifiedBadge>New</VerifiedBadge>
              </div>
            );
          }
          return <InformationBadge>Old</InformationBadge>;
        }

        if (existedUser && !selectedUser) return <Unverified>Duplicate</Unverified>;
        if (savingErrors.length > 0)
          return <ErrorBadge className="whitespace-nowrap">{savingErrors.length} errors</ErrorBadge>;
        return <InformationBadge>Ready</InformationBadge>;
      },
      enableSorting: false,
      size: 120,
      minSize: 120,
    },
    {
      accessorKey: 'userType',
      header: ({ column }) => <Header column={column}>User Role</Header>,
      cell: ({ row }) => {
        const { user, existedUser } = row.original;
        const data = (existedUser && isResolvingDuplicates ? existedUser : user) as User;

        return (
          <SkeletonCell>
            <UserRoleActions
              type={data.userType}
              onChange={newUserType => {
                updateUser({
                  ...row.original,
                  user: {
                    ...user,
                    userType: newUserType,
                  },
                });
              }}
              disabled={isResolvingDuplicates}
            />
          </SkeletonCell>
        );
      },
      size: 150,
      minSize: 120,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => <Header column={column}>Email Address *</Header>,
      cell: ({ row }) => {
        const { user, errors, savingErrors, existedUser } = row.original;
        const data = (existedUser && isResolvingDuplicates ? existedUser : user) as User;
        const isError = errors.includes('email') || savingErrors.includes('email');

        if (isResolvingDuplicates) {
          return (
            <span className={data.email ? 'text-foreground' : 'text-error'}>
              {data.email ? data.email : 'Not Found'}
            </span>
          );
        }

        return (
          <ErrorCellWrapper className={cn(!isError && 'hidden')}>
            <InputCell updateUser={updateUser} name="email" row={row} defaultValue={data.email ?? ''} />
          </ErrorCellWrapper>
        );
      },
      enableSorting: false,
      minSize: 200,
      size: 250,
    },
    {
      accessorKey: 'firstName',
      header: ({ column }) => <Header column={column}>First Name *</Header>,
      cell: ({ row }) => {
        const { user, errors, existedUser, savingErrors } = row.original;
        const data = existedUser && isResolvingDuplicates ? existedUser : user;
        const isError =
          errors.includes('firstName') ||
          !data.firstName ||
          data.firstName === '' ||
          savingErrors.includes('firstName');

        if (isResolvingDuplicates) {
          return <span className="text-foreground">{data.firstName ? data.firstName : 'Not Found'}</span>;
        }

        return (
          <ErrorCellWrapper className={cn(!isError && 'hidden')}>
            <InputCell updateUser={updateUser} name="firstName" row={row} defaultValue={data.firstName ?? ''} />
          </ErrorCellWrapper>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'lastName',
      header: ({ column }) => <Header column={column}>Last Name *</Header>,
      cell: ({ row }) => {
        const { user, errors, existedUser, savingErrors } = row.original;
        const data = existedUser && isResolvingDuplicates ? existedUser : user;
        const isError =
          errors.includes('lastName') || !data.lastName || data.lastName === '' || savingErrors.includes('lastName');

        if (isResolvingDuplicates) {
          return <span className="text-foreground">{data.lastName ? data.lastName : 'Not Found'}</span>;
        }
        return (
          <ErrorCellWrapper className={cn(!isError && 'hidden')}>
            <InputCell updateUser={updateUser} name="lastName" row={row} defaultValue={data.lastName ?? ''} />
          </ErrorCellWrapper>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'specialty',
      header: ({ column }) => <Header column={column}>Specialty *</Header>,
      cell: ({ row }) => {
        const { user, existedUser } = row.original;
        const data = (existedUser && isResolvingDuplicates ? existedUser : user) as User;
        const isValid = !!(isResolvingDuplicates || data.specialtyId);
        return (
          <ErrorCellWrapper className={cn(isValid && 'hidden')}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild className="cursor-pointer" disabled={isResolvingDuplicates}>
                <div
                  className={cn(
                    'w-full h-full flex items-center justify-between gap-2 whitespace-nowrap',
                    data.specialtyId || isResolvingDuplicates ? 'text-foreground' : 'text-error',
                  )}
                >
                  <div>{data.specialtyId ? specialties.find(s => s.id === data.specialtyId)?.name : 'Not Found'}</div>
                  <ChevronDown className="h-4 w-4" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="z-[100] max-h-[300px] overflow-y-auto bg-white shadow-lg rounded-lg border">
                {specialties.map(specialty => (
                  <DropdownMenuItem
                    key={specialty.id}
                    className="py-2.5 px-3.5 cursor-pointer"
                    onClick={() => updateUser({ ...row.original, user: { ...user, specialtyId: specialty.id } })}
                  >
                    {specialty.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </ErrorCellWrapper>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'dateOfBirth',
      header: ({ column }) => <Header column={column}>Date of Birth</Header>,
      cell: ({ row }) => {
        const { user, existedUser } = row.original;
        const data = (existedUser && isResolvingDuplicates ? existedUser : user) as User;
        return (
          <DateInputCell
            defaultValue={data.birthday}
            row={row}
            updateUser={updateUser}
            name="birthday"
            isResolvingDuplicates={isResolvingDuplicates}
          />
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'practiceSetting',
      header: ({ column }) => <Header column={column}>Practice Setting</Header>,
      cell: ({ row }) => {
        const { user, existedUser } = row.original;
        const data = (existedUser && isResolvingDuplicates ? existedUser : user) as User;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild className="cursor-pointer" disabled={isResolvingDuplicates}>
              <div
                className={cn(
                  'w-full h-full flex items-center justify-between gap-2 whitespace-nowrap',
                  !data.practiceSetting && 'text-muted-foreground',
                )}
              >
                <div>
                  {data.practiceSetting
                    ? practiceSettings.find(s => s.name === data.practiceSetting)?.name
                    : 'Not Found'}
                </div>
                <ChevronDown className="h-4 w-4" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="z-[100] max-h-[300px] overflow-y-auto bg-white shadow-lg rounded-lg border">
              {practiceSettings.map(practiceSetting => (
                <DropdownMenuItem
                  key={practiceSetting.id}
                  className="py-2.5 px-3.5 cursor-pointer"
                  onClick={() =>
                    updateUser({ ...row.original, user: { ...user, practiceSetting: practiceSetting.name } })
                  }
                >
                  {practiceSetting.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: 'employmentStatus',
      header: ({ column }) => <Header column={column}>Employment Status</Header>,
      cell: ({ row }) => {
        const { user, existedUser } = row.original;
        const data = (existedUser && isResolvingDuplicates ? existedUser : user) as User;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild className="cursor-pointer" disabled={isResolvingDuplicates}>
              <div
                className={cn(
                  'w-full h-full flex items-center justify-between gap-2 whitespace-nowrap',
                  !data.employmentStatus && 'text-muted-foreground',
                )}
              >
                <div>
                  {data.employmentStatus
                    ? employmentStatuses.find(s => s.name === data.employmentStatus)?.name
                    : 'Not Found'}
                </div>
                <ChevronDown className="h-4 w-4" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="z-[100] max-h-[300px] overflow-y-auto bg-white shadow-lg rounded-lg border">
              {employmentStatuses.map(employmentStatus => (
                <DropdownMenuItem
                  key={employmentStatus.id}
                  className="py-2.5 px-3.5 cursor-pointer"
                  onClick={() =>
                    updateUser({ ...row.original, user: { ...user, employmentStatus: employmentStatus.name } })
                  }
                >
                  {employmentStatus.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
    },
  ];

  return columns;
}

export default generateColumns;
