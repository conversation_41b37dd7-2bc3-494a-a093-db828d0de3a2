'use client';
import { cn } from '@/lib/utils';
import { <PERSON><PERSON>lert, CircleCheck, CloudUpload, Loader } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import { useImportUserStore } from './useImportUsersStore';
import { useValidateCsv } from './useValidateCsv';

const Dropzone = () => {
  const { validateCsv, isValidating } = useValidateCsv();
  const { users, updateUsers } = useImportUserStore();
  const { acceptedFiles, getRootProps, getInputProps } = useDropzone({
    maxFiles: 1,
    accept: {
      'text/csv': ['.csv'],
    },
    onDropRejected() {
      toast.error('Please upload a CSV file');
    },
    onError(err) {
      toast.error(err.message);
    },
  });

  const emptyUsers = users.length === 0 && acceptedFiles.length > 0 && !isValidating;

  const readFileAsArrayBuffer = useCallback((file: File): Promise<ArrayBuffer> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = event => resolve(event.target?.result as ArrayBuffer);
      reader.onerror = error => reject(error);
      reader.readAsArrayBuffer(file);
    });
  }, []);

  useEffect(() => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];

    const processFile = async () => {
      try {
        const buffer = await readFileAsArrayBuffer(file);
        const uint8Array = new Uint8Array(buffer);

        const utf8Decoder = new TextDecoder('utf-8');
        const iso88591Decoder = new TextDecoder('iso-8859-1');

        const utf8Content = utf8Decoder.decode(uint8Array);
        const iso88591Content = iso88591Decoder.decode(uint8Array);

        // Choose the correct decoding
        const finalContent = utf8Content.includes('�') ? iso88591Content : utf8Content;

        // Re-encode as UTF-8
        const encoder = new TextEncoder();
        const utf8Array = encoder.encode(finalContent);

        const formData = new FormData();
        formData.append('file', new Blob([utf8Array], { type: 'text/csv;charset=utf-8' }));
        validateCsv(formData);
      } catch (error) {
        toast.error('Error processing file');
      }
    };

    processFile();
  }, [validateCsv, acceptedFiles, readFileAsArrayBuffer]);

  useEffect(() => {
    updateUsers([]);
  }, [acceptedFiles, updateUsers]);

  function renderDropzone() {
    if (acceptedFiles.length <= 0) {
      return (
        <>
          <CloudUpload className="w-6 h-6 mb-2" />
          <div>Upload from computer</div>
        </>
      );
    }

    if (users.length > 0 || isValidating) {
      return (
        <>
          {isValidating ? (
            <Loader className="text-primary-brand w-6 h-6 mb-2 animate-spin" />
          ) : (
            <CircleCheck className="text-primary-brand w-6 h-6 mb-2" />
          )}
          {acceptedFiles[0].name}
        </>
      );
    }

    return (
      <div className="text-error flex flex-col items-center">
        <CircleAlert className="text-error w-6 h-6 mb-2" />
        {acceptedFiles[0].name}
      </div>
    );
  }

  return (
    <>
      <div
        className={cn(
          'min-w-[320px] bg-[#EAECF0] focus-visible:!outline-none py-10 mb-10 rounded-lg border border-input flex flex-col justify-center items-center cursor-pointer',
          acceptedFiles.length > 0 && 'border-primary-brand',
          emptyUsers && 'border-error !mb-0',
        )}
        {...getRootProps()}
      >
        <input {...getInputProps()} />
        {renderDropzone()}
      </div>
      {emptyUsers && (
        <div className="text-error text-sm mt-1 mb-10 before:content-['*']">This csv file has no users</div>
      )}
    </>
  );
};
export default Dropzone;
