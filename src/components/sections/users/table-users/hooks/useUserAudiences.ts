import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';

export const USER_AUDIENCES_QUERY_KEY = 'user-audiences';

export const useUserAudiences = (userId: number) => {
  return useQuery({
    queryKey: [USER_AUDIENCES_QUERY_KEY, userId],
    queryFn: async () => {
      const response = await api.users.getUserAudiences(userId);
      return response.data;
    },
    enabled: !!userId,
  });
};
