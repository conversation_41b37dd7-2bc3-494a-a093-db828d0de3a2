import { RequestUserInfoPayload } from '@/backend/users/validations/request-user-info';
import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export const useRequestInfo = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: { id: number } & RequestUserInfoPayload) => {
      const { id, contactStatus } = payload;
      const response = await api.users.requestInfo(id, { contactStatus });
      return response.data;
    },
    onSuccess: async (_, payload) => {
      toast.success('Status updated');

      await queryClient.invalidateQueries({ queryKey: ['user', payload.id] });
      await queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: () => {
      toast.error('Failed to request info');
    },
  });
};
