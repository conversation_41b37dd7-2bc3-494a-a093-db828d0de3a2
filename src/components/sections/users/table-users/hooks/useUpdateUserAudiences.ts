import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { USER_AUDIENCES_QUERY_KEY } from './useUserAudiences';
import toast from 'react-hot-toast';
export const useAddUserToAudiences = (userId: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (audiences: number[]) => {
      return api.users.addUserToAudiences(userId, { audienceIds: audiences });
    },
    onSuccess: () => {
      toast.success('User Added to Audience');
      queryClient.invalidateQueries({ queryKey: [USER_AUDIENCES_QUERY_KEY, userId] });
    },
  });
};

export const useRemoveUserFromAudiences = (userId: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (audiences: number[]) => {
      return api.users.removeUserFromAudiences(userId, { audienceIds: audiences });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USER_AUDIENCES_QUERY_KEY, userId] });
    },
  });
};
