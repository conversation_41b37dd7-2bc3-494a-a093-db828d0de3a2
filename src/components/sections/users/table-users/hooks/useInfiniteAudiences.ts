import { api } from '@/lib/http';
import { useInfiniteQuery } from '@tanstack/react-query';

interface UseInfiniteAudiencesProps {
  search?: string;
}

export const useInfiniteAudiences = ({ search }: UseInfiniteAudiencesProps = {}) => {
  return useInfiniteQuery({
    queryKey: ['available-audiences', search],
    queryFn: async ({ pageParam }) => {
      const response = await api.audiences.getAudiences({
        page: pageParam,
        pageSize: 10,
        search,
      });
      return response.data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.totalPages) return undefined;
      const nextPage = allPages.length + 1;
      return nextPage <= lastPage.totalPages ? nextPage : undefined;
    },
    initialPageParam: 1,
  });
};
