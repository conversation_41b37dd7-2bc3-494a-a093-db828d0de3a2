'use client';

import { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Table } from '@/components/ui/table';
import { generateColumns } from './audience-columns';
import { useUserAudiences } from './hooks/useUserAudiences';
import { useInfiniteAudiences } from './hooks/useInfiniteAudiences';
import { useAddUserToAudiences } from './hooks/useUpdateUserAudiences';
import { User } from '@/backend/users/entities/User';
import type { AudienceItem } from '@/lib/http/audiences/types';
import { Search } from 'lucide-react';
import PrimaryBadge from '@/components/common/badges/primary';
import { Form, Input } from '@/components/ui/form';
import { z } from 'zod';
import { useDebounce } from '@/hooks/useDebounce';
import { Badge } from '@/components/ui/badge';
import { RowSelectionState } from '@tanstack/table-core';

const schema = z.object({
  search: z.string().optional(),
});

type AddAudiencesModalProps = {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
};

export const AddAudiencesModal = ({ isOpen, onClose, user }: AddAudiencesModalProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const search = useDebounce(searchTerm);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [selectedAudiences, setSelectedAudiences] = useState<AudienceItem[]>([]);
  const [isLoadingInitialSelectedAudiences, setIsLoadingInitialSelectedAudiences] = useState(true);

  const { data: userAudiences, isLoading: isLoadingUserAudiences } = useUserAudiences(user?.id ?? 0);

  const {
    data: infiniteData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingAvailableAudiences,
  } = useInfiniteAudiences({ search });

  const { mutateAsync: addUserToAudiences, isPending: isAdding } = useAddUserToAudiences(user?.id ?? 0);

  useEffect(() => {
    if (userAudiences) {
      const initialSelection = userAudiences.reduce(
        (acc, audience) => ({ ...acc, [audience.id]: true }),
        {} as RowSelectionState,
      );
      setRowSelection(initialSelection);
      setSelectedAudiences(userAudiences as unknown as AudienceItem[]);
      setIsLoadingInitialSelectedAudiences(false);
    }
  }, [userAudiences]);

  // Flatten infinite data pages
  const availableAudiences = useMemo(() => {
    if (isLoadingAvailableAudiences || isLoadingUserAudiences) {
      return Array(10).fill({});
    }
    return infiniteData?.pages.flatMap(page => page.data) ?? [];
  }, [isLoadingAvailableAudiences, isLoadingUserAudiences, infiniteData]);

  const handleRowSelectionChange = (newSelection: RowSelectionState) => {
    setRowSelection(newSelection);

    const allAvailableAudiences = infiniteData?.pages.flatMap(page => page.data) || [];

    const prevSelectedAudiences = selectedAudiences.filter(
      audience => !allAvailableAudiences.some(available => available.id === audience.id),
    );

    const newSelectedAudiences = allAvailableAudiences.filter(audience => newSelection[audience.id]);

    setSelectedAudiences([...prevSelectedAudiences, ...newSelectedAudiences]);
  };

  const additionalAudiences = useMemo(() => {
    return selectedAudiences.filter(audience => !userAudiences?.some(ua => ua.id === audience.id));
  }, [selectedAudiences, userAudiences]);

  const handleSave = async () => {
    await addUserToAudiences(additionalAudiences.map(audience => audience.id));
    onClose();
  };

  const columns = generateColumns({
    isLoading: isLoadingAvailableAudiences || isLoadingInitialSelectedAudiences,
    disabledRows: userAudiences?.map(audience => audience.id.toString()),
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="lg:!w-[1080px] min-h-[90vh] lg:!max-h-[90vh] flex flex-col">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center">
            <h1 className="font-bold text-2xl mb-9">Add User to Audience</h1>
          </div>
        </div>

        <div className="flex-1 flex flex-col gap-4 overflow-hidden border rounded-lg pt-5">
          <div className="flex items-center justify-between px-5">
            <div className="flex space-x-2">
              <h2 className="text-[18px]">Audiences</h2>
              <PrimaryBadge className="bg-[#EFF8FF] text-[#175CD3] font-medium text-xs">
                {infiniteData?.pages[0]?.total ?? 0} Found
              </PrimaryBadge>
              <Badge className="bg-[#F9F5FF] text-primary">{selectedAudiences.length} Selected</Badge>
            </div>
            <div className="lg:max-w-25 w-full my-4 lg:my-0">
              <Form schema={schema} onSubmit={() => {}}>
                <Input
                  name="search"
                  type="text"
                  placeholder="Search"
                  className="pl-[42px]"
                  StartIcon={Search}
                  iconClassName="w-5 h-5"
                  onChange={e => {
                    setSearchTerm(e.target.value.trim());
                  }}
                />
              </Form>
            </div>
          </div>

          <Table
            columns={columns}
            data={availableAudiences}
            isFetchingNextPage={isFetchingNextPage}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
            rowSelection={rowSelection}
            onRowSelectionChange={handleRowSelectionChange}
            getRowId={row =>
              isLoadingAvailableAudiences || isLoadingInitialSelectedAudiences ? row.uuid : String(row.id)
            }
            stickyHeader
          />
        </div>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={additionalAudiences.length === 0 || isAdding} isLoading={isAdding}>
            Add to Audience
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
