import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { MouseEvent, useState } from 'react';
import DenyUserModal from '../deny-user-modal/deny-user-modal';
import { VerificationStatus } from '@/backend/users/entities/User';
import { VerifyUserPayload } from '@/backend/users/validations/verify-user';

type Props = {
  className?: string;
  type: VerificationStatus | null;
  onChange?: (payload: VerifyUserPayload) => void;
};

const getStyle = (type: VerificationStatus) => {
  let style = '';

  switch (type) {
    case VerificationStatus.Unverified:
      style = 'bg-[#F2F4F7] text-[#344054]';
      break;
    case VerificationStatus.Verified:
      style = 'bg-[#ECFDF3] text-[#027A48]';
      break;
    case VerificationStatus.Denied:
      style = 'bg-[#FEE2E2] text-[#B42318]';
      break;
    default:
      style = 'bg-[#F2F4F7] text-[#344054]';
      break;
  }

  return style;
};

const VerificationActions = ({ className, type, onChange }: Props) => {
  const [open, setOpen] = useState(false);
  const [isDenyModalOpen, setIsDenyModalOpen] = useState(false);
  const badgeVariant = 'text-xs rounded-full py-0.5 px-[10px] h-fit';

  const handleClick = (e: MouseEvent) => {
    e.stopPropagation();
    setOpen(false);
  };

  const handleStatusChange = (payload: VerifyUserPayload) => (e: MouseEvent) => {
    e.stopPropagation();
    if (onChange) {
      onChange(payload);
    }
  };

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant={'ghost'}
            className={cn(getStyle(type ?? VerificationStatus.Unverified), badgeVariant, className)}
          >
            {type}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="p-2" onClick={handleClick}>
          <div className="flex flex-col gap-4 p-2">
            {type !== VerificationStatus.Verified && (
              <Button
                variant={'ghost'}
                onClick={handleStatusChange({ verificationStatus: VerificationStatus.Verified })}
                className={cn(getStyle(VerificationStatus.Verified), badgeVariant, className)}
              >
                Verified
              </Button>
            )}
            {type !== VerificationStatus.Unverified && (
              <Button
                variant={'ghost'}
                onClick={handleStatusChange({ verificationStatus: VerificationStatus.Unverified })}
                className={cn(getStyle(VerificationStatus.Unverified), badgeVariant, className)}
              >
                Unverified
              </Button>
            )}
            {type !== VerificationStatus.Denied && (
              <Button
                variant={'ghost'}
                onClick={() => setIsDenyModalOpen(true)}
                className={cn(getStyle(VerificationStatus.Denied), badgeVariant, className)}
              >
                Denied
              </Button>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
      <DenyUserModal
        openDialog={isDenyModalOpen}
        setOpenDialog={setIsDenyModalOpen}
        onCancel={() => setIsDenyModalOpen(false)}
        onDeny={onChange}
      />
    </>
  );
};
export default VerificationActions;
