'use client';
import { useEffect, useMemo, useState } from 'react';
import { generateColumns } from '@/components/sections/users/table-users/columns';
import { Table } from '@/components/ui/table';
import CustomPagination from '@/components/common/custom-pagination';
import { useUsers } from '@/components/sections/users/hooks/useUsers';
import { useDeleteUser } from '@/components/sections/users/hooks/useDeleteUser';
import UserHeader from '@/components/sections/users/user-header';
import { cn } from '@/lib/utils';
import { useUserVerification } from '@/components/sections/users/hooks/useUserVerification';
import { User } from '@/backend/users/entities/User';
import { AddAudiencesModal } from './add-audiences-modal';
import { RowSelectionState } from '@tanstack/react-table';
import { useRequestInfo } from './hooks/useRequestInfo';
import { useUserFilterStore } from '@/lib/store/user-filter-store';
import { useAssignUserRole } from '../hooks/useAssignUserRole';

const TableUsers = () => {
  const { users, isLoadingUsers, isError } = useUsers();
  const { deleteUser, isDeleting } = useDeleteUser();
  const { verifyUser, isVerifying } = useUserVerification();
  const { assignUserRole } = useAssignUserRole();

  const { mutate: requestInfo } = useRequestInfo();
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const { filters, clearFilters } = useUserFilterStore();

  const selectedUserIds = useMemo(() => Object.keys(selectedRows).map(Number), [selectedRows]);

  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isAddAudiencesModalOpen, setIsAddAudiencesModalOpen] = useState(false);

  const usersData = useMemo(() => {
    if (isLoadingUsers) {
      return Array(10).fill({});
    }
    return users?.data?.data ?? [];
  }, [isLoadingUsers, users]);

  const columns = useMemo(
    () =>
      generateColumns({
        isLoading: isLoadingUsers,
        onDelete: deleteUser,
        isDeleting,
        onVerification: verifyUser,
        isOnVerification: isVerifying,
        onOpenAddAudiencesModal: (user: User) => {
          setSelectedUser(user);
          setIsAddAudiencesModalOpen(true);
        },
        onRequestInfo: requestInfo,
        onAssignUserRole: assignUserRole,
      }),
    [isLoadingUsers, deleteUser, isDeleting, verifyUser, isVerifying, requestInfo, assignUserRole],
  );

  useEffect(() => {
    // Clear selected rows when filters change
    setSelectedRows({});
  }, [filters]);

  useEffect(() => {
    // Clear filters when the component mounts (page load or navigation)
    clearFilters();
  }, []);

  return (
    <>
      <UserHeader usersCount={users?.data?.total ?? 0} selectedUserIds={selectedUserIds} />
      <Table
        columns={columns}
        data={usersData}
        url="/users"
        containerClassName={cn(!isLoadingUsers && !isError && 'peer is-done')}
        rowSelection={selectedRows}
        onRowSelectionChange={setSelectedRows}
        getRowId={row => (isLoadingUsers ? row.uuid : String(row.id))}
      />
      <CustomPagination totalPages={users?.data?.totalPages ?? 10} />
      <AddAudiencesModal
        key={`add-audiences-modal-${selectedUser?.id}-${isAddAudiencesModalOpen}`}
        isOpen={isAddAudiencesModalOpen}
        onClose={() => {
          setIsAddAudiencesModalOpen(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
      />
    </>
  );
};
export default TableUsers;
