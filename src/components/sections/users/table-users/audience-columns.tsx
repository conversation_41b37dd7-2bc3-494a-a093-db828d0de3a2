import { ColumnDef } from '@tanstack/react-table';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn } from '@/lib/utils';
import { AudienceItem } from '@/lib/http/audiences/types';
import { SelectCell } from '@/components/ui/table/select-cell';
import { SelectHeader } from '@/components/ui/table/select-header';
import { AudienceFiltersCell } from '@/components/ui/table/audience-filters-cell';

type GenerateColumnsProps = {
  isLoading?: boolean;
  disabledRows?: string[];
};

export function generateColumns({ isLoading, disabledRows }: GenerateColumnsProps) {
  const columns: ColumnDef<AudienceItem>[] = [
    {
      id: 'select',
      header: SelectHeader,
      enableSorting: false,
      cell: ({ row }) => {
        const isChecked = disabledRows?.includes(row.original.id?.toString());
        return (
          <SkeletonCell
            isLoading={isLoading}
            className={cn('h-[40px] flex items-center', isLoading ? 'w-5 ml-2 !overflow-hidden' : '')}
          >
            <div className="flex items-center justify-center w-5">
              <SelectCell row={row} disabled={isChecked} checked={isChecked} />
            </div>
          </SkeletonCell>
        );
      },
      size: 50,
      minSize: 50,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => <Header column={column}>Audience Name</Header>,
      enableSorting: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className="h-[40px] flex items-center">
          <span className="text-sm">{row.getValue('name')}</span>
        </SkeletonCell>
      ),
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'numberOfUsers',
      header: ({ column }) => <Header column={column}>Audience Size</Header>,
      enableSorting: false,
      cell: ({ row }) => {
        const numberOfUsers = row.original.numberOfUsers;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1} className="h-[40px] flex items-center">
            <span className="text-sm text-muted-foreground">{numberOfUsers}</span>
          </SkeletonCell>
        );
      },
      size: 200,
      minSize: 150,
    },
    {
      accessorKey: 'cities',
      header: ({ column }) => <Header column={column}>Filters</Header>,
      enableSorting: false,
      cell: ({ row }) => {
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1} className="min-h-[40px] flex items-center">
            <AudienceFiltersCell row={row} />
          </SkeletonCell>
        );
      },
      size: 300,
      minSize: 200,
    },
  ];

  return columns;
}
