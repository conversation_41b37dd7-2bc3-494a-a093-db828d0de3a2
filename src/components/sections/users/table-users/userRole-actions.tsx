import { UserType } from '@/backend/users/entities/User';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { USER_TYPE_ORDERED_LIST } from '@/utils/constants';
import { MouseEvent, useMemo, useState } from 'react';
import { getUserTypeLabel } from '../utils';

const getStyle = (type: UserType) => {
  let style = '';

  switch (type) {
    case UserType.HCPUser:
    case UserType.HCPUserImported:
      style = 'bg-[#ECFDF3] text-[#027A48]';
      break;
    case UserType.Client:
    case UserType.ClientImported:
      style = 'bg-[#EFF8FF] text-[#175CD3]';
      break;
    case UserType.Denied:
      style = 'bg-[#FEE2E2] text-[#B42318]';
      break;
    case UserType.Internal:
      style = 'bg-[#FFFAEB] text-[#B54708]';
      break;
    case UserType.Unverified:
      style = 'bg-[#F2F4F7] text-[#344054]';
      break;
    default:
      style = 'bg-[#F2F4F7] text-[#344054]';
      break;
  }

  return style;
};

type Props = {
  className?: string;
  type: UserType | null;
  onChange?: (payload: UserType) => void;
  disabled?: boolean;
};

const UserRoleActions = ({ className, type, onChange, disabled }: Props) => {
  const [open, setOpen] = useState(false);
  const badgeVariant = 'text-xs rounded-full py-0.5 px-[10px] h-fit min-w-[110px]';

  const handleClick = (e: MouseEvent) => {
    e.stopPropagation();
    setOpen(false);
  };

  const handleStatusChange = (payload: UserType) => (e: MouseEvent) => {
    e.stopPropagation();
    if (onChange) {
      onChange(payload);
    }

    setOpen(false);
  };

  const availableUserTypes = useMemo(() => {
    return USER_TYPE_ORDERED_LIST.filter(userType => userType !== type);
  }, [type]);

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild disabled={disabled} className="disabled:opacity-100">
          <Button variant={'ghost'} className={cn(getStyle(type ?? UserType.Unverified), badgeVariant, className)}>
            {type ? getUserTypeLabel(type) : 'N/A'}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="p-2" onClick={handleClick}>
          <div className="flex flex-col gap-4 p-2">
            {availableUserTypes.map(userType => (
              <Button
                key={userType}
                variant={'ghost'}
                onClick={handleStatusChange(userType)}
                className={cn(getStyle(userType), badgeVariant, className)}
              >
                {getUserTypeLabel(userType)}
              </Button>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
export default UserRoleActions;
