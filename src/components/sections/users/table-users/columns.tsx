'use client';
import { ColumnDef } from '@tanstack/table-core';
import { formatDate } from '@/utils/date-format';
import { formatPhone } from '@/utils/phone-format';
import { Header } from '@/components/ui/table/header';
import { ContactStatus, User, UserType } from '@/backend/users/entities/User';
import VerifiedBadge from '@/components/common/badges/verified';
import UnverifiedBadge from '@/components/common/badges/unverified';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { cn } from '@/lib/utils';
import { SelectCell } from '@/components/ui/table/select-cell';
import { SelectHeader } from '@/components/ui/table/select-header';
import { ActionsCell, DeleteModalProps, VerificationModalProps } from '@/components/ui/table/action-cell';
import VerificationActions from '@/components/sections/users/table-users/verification-actions';
import ContactStatusActions from './contact-status-actions';
import { VerifyUserPayload } from '@/backend/users/validations/verify-user';
import { RequestUserInfoPayload } from '@/backend/users/validations/request-user-info';
import UserRoleActions from './userRole-actions';

type ColumnsProps = Omit<DeleteModalProps, 'title' | 'subtitle'> &
  Omit<VerificationModalProps, 'title' | 'subtitle'> & {
    isLoading?: boolean;
    onOpenAddAudiencesModal?: (user: User) => void;
    onContactStatusChange?: (payload: ContactStatus) => void;
    onRequestInfo?: (payload: { id: number } & RequestUserInfoPayload) => void;
    onAssignUserRole?: (payload: { id: number; userType: UserType }) => void;
  };

function generateColumns({
  onDelete,
  isLoading,
  isDeleting,
  onVerification,
  onOpenAddAudiencesModal,
  onRequestInfo,
  onAssignUserRole,
}: ColumnsProps) {
  const columns: ColumnDef<User>[] = [
    {
      id: 'select',
      header: SelectHeader,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 ml-2 overflow-hidden' : '')}>
          <SelectCell row={row} />
        </SkeletonCell>
      ),
      size: 50,
      minSize: 50,
    },
    {
      accessorKey: 'firstName',
      size: 180,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Name</Header>,
      cell: ({ row }) => {
        const name = `${row.original.firstName ?? ''} ${row.original.lastName ?? ''}`;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={2}>
            <div className="flex flex-col w-full">
              <span className="truncate">{name}</span>
              <span className="text-muted-foreground">{row.original.licenseNumber}</span>
            </div>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'email',
      size: 250,
      minSize: 150,
      header: ({ column }) => <Header column={column}>Contact Information</Header>,
      cell: ({ row }) => {
        const email = row.getValue<string>('email');
        const phone = (row.original as { phone: string }).phone;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={2}>
            <div className="flex flex-col w-full">
              <span className="truncate">{email}</span>
              <span className="text-muted-foreground whitespace-nowrap">{phone ? formatPhone(phone) : 'N/A'}</span>
            </div>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'province',
      size: 180,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Province / State</Header>,
      cell: ({ row }) => {
        return (
          <SkeletonCell isLoading={isLoading}>
            <div className="flex flex-col w-full">
              <span className="truncate text-muted-foreground">{row.getValue('province')}</span>
            </div>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      size: 150,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Date Joined</Header>,
      cell: ({ row }) => {
        const createdAt = row.getValue<string>('createdAt');
        return (
          <SkeletonCell isLoading={isLoading}>
            <div className="flex flex-col w-full">
              <span className="truncate text-muted-foreground">{createdAt ? formatDate(createdAt) : ''}</span>
            </div>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'lastLogin',
      size: 150,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Last Login</Header>,
      cell: ({ row }) => {
        const lastLogin = row.original.lastLogin;
        return (
          <SkeletonCell isLoading={isLoading}>
            <div className="flex flex-col w-full">
              <span className="truncate text-muted-foreground">{lastLogin ? formatDate(lastLogin) : ''}</span>
            </div>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'userType',
      size: 150,
      minSize: 120,
      header: ({ column }) => <Header column={column}>User Role</Header>,
      cell: ({ row }) => {
        const { userType: type, id } = row.original;
        return (
          <SkeletonCell isLoading={isLoading}>
            <UserRoleActions type={type} onChange={payload => onAssignUserRole?.({ id, userType: payload })} />
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'contactStatus',
      size: 150,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Contact Status</Header>,
      cell: ({ row }) => {
        const contactStatus = row.original.contactStatus;

        return (
          <SkeletonCell isLoading={isLoading}>
            <ContactStatusActions
              status={contactStatus}
              onRequestInfo={status => onRequestInfo?.({ id: row.original.id, contactStatus: status })}
            />
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'isCompleteWS',
      size: 180,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Welcome Survey</Header>,
      cell: ({ row }) => {
        const completed = row.original.isCompleteWS;

        return (
          <SkeletonCell isLoading={isLoading}>
            {completed ? (
              <VerifiedBadge className="font-medium">Completed</VerifiedBadge>
            ) : (
              <UnverifiedBadge className="font-medium">Not Completed</UnverifiedBadge>
            )}
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'verificationStatus',
      size: 150,
      minSize: 120,
      header: ({ column }) => <Header column={column}>Verification</Header>,
      cell: ({ row }) => {
        const type = row.original.verificationStatus;
        return (
          <SkeletonCell isLoading={isLoading}>
            <VerificationActions
              type={type}
              onChange={(payload: VerifyUserPayload) => {
                // Call onVerification with the appropriate parameters
                if (onVerification) {
                  onVerification({
                    id: row.original.id,
                    verificationStatus: payload.verificationStatus,
                    note: payload.note,
                  });
                }
              }}
            />
          </SkeletonCell>
        );
      },
    },
    {
      id: 'actions',
      size: 70,
      minSize: 50,
      enableHiding: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 overflow-hidden' : '')}>
          <ActionsCell
            row={row}
            urlDetail={`/users/${row.original.id}`}
            deleteModalProps={{
              onDelete,
              isDeleting,
              title: 'Delete User',
              subtitle: 'Are you sure you want to delete this user? This action cannot be reversed.',
            }}
            customActions={[
              {
                label: 'Add to Audience',
                onClick: () => {
                  onOpenAddAudiencesModal?.(row.original);
                },
              },
            ]}
          />
        </SkeletonCell>
      ),
    },
  ];

  return columns;
}

export { generateColumns };
