import { DropdownMenuContent } from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import React, { MouseEvent, useState } from 'react';
import { ContactStatus } from '@/backend/users/entities/User';

const getStyle = (status: ContactStatus | null) => {
  let style = '';

  switch (status) {
    case ContactStatus.WSPending:
      style = 'bg-[#EFF8FF] text-[#175CD3]';
      break;
    case ContactStatus.WaitForInfo:
      style = 'bg-[#FFFAEB] text-[#B54708]';
      break;
    case ContactStatus.ReviewInfo:
      style = 'bg-[#F4F3FF] text-[#5925DC]';
      break;
    case ContactStatus.Complete:
      style = 'bg-[#ECFDF3] text-[#027A48]';
      break;

    default:
      style = 'bg-[#F2F4F7] text-[#344054]';
  }

  return style;
};

const getLabel = (status: ContactStatus | null) => {
  switch (status) {
    case ContactStatus.WaitForInfo:
      return 'Wait for Info';
    case ContactStatus.ReviewInfo:
      return 'Review Info';
    case ContactStatus.Complete:
      return 'Complete';
    case ContactStatus.WSPending:
      return 'WS Pending';
    default:
      return status;
  }
};

type Props = {
  className?: string;
  status: ContactStatus | null;
  onRequestInfo?: (status: ContactStatus.WaitForInfo | ContactStatus.ReviewInfo) => void;
};

const ContactStatusActions = ({ className, status, onRequestInfo }: Props) => {
  const [open, setOpen] = useState(false);

  const badgeVariant = 'text-xs rounded-full py-0.5 px-[10px] h-fit';

  const handleClick = (e: MouseEvent) => {
    // Stop propagation to prevent row click
    e.stopPropagation();
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (status === ContactStatus.ReviewInfo || status === ContactStatus.WaitForInfo) {
      setOpen(newOpen);
    }
  };

  const handleRequestInfo = (status: ContactStatus.WaitForInfo | ContactStatus.ReviewInfo) => {
    onRequestInfo?.(status);
    setOpen(false);
  };

  return (
    <DropdownMenu open={open} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button variant={'ghost'} className={cn(getStyle(status), badgeVariant, className)}>
          {getLabel(status)}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="p-2" onClick={handleClick}>
        <div className="flex flex-col gap-4 p-2">
          {status === ContactStatus.ReviewInfo && (
            <Button
              variant={'ghost'}
              onClick={() => handleRequestInfo(ContactStatus.WaitForInfo)}
              className={cn(getStyle(ContactStatus.WaitForInfo), badgeVariant, className)}
            >
              Wait for Info
            </Button>
          )}
          {status === ContactStatus.WaitForInfo && (
            <Button
              variant={'ghost'}
              onClick={() => handleRequestInfo(ContactStatus.ReviewInfo)}
              className={cn(getStyle(ContactStatus.ReviewInfo), badgeVariant, className)}
            >
              Review Info
            </Button>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
export default ContactStatusActions;
