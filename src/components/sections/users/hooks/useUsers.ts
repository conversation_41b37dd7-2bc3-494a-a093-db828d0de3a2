import { usePagination } from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useSortQuery } from '@/hooks/useSortQuery';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { User } from '@/backend/users/entities/User';
import { useUserFilterStore } from '@/lib/store/user-filter-store';
import { useEffect, useRef, useCallback } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { FilterItem } from '@/lib/store/user-filter-store';

const convertUserFiltersToQueryString = (filters: FilterItem[]): string => {
  if (!filters || filters.length === 0) return '';

  return filters
    .map((filter, index) => {
      return [
        `filters[${index}][field]=${encodeURIComponent(filter.field)}`,
        `filters[${index}][operator]=${encodeURIComponent(filter.operator)}`,
        `filters[${index}][value]=${encodeURIComponent(filter.value)}`,
      ].join('&');
    })
    .join('&');
};

export function useUsers() {
  const { page, pageSize } = usePagination();
  const { search } = useSearchQuery();
  const { sortBy, sortOrder } = useSortQuery({ sortBy: 'updatedAt', sortOrder: 'DESC' });
  const { filters } = useUserFilterStore();

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const prevFiltersRef = useRef(filters);

  const filtersQueryString = convertUserFiltersToQueryString(filters);

  const resetToFirstPage = useCallback(() => {
    const params = new URLSearchParams(Array.from(searchParams.entries()));
    params.set('page', '1');
    const paramsString = params.toString();
    router.push(`${pathname}?${paramsString}`, { scroll: false });
  }, [router, pathname, searchParams]);

  useEffect(() => {
    const currentFiltersJson = JSON.stringify(filters);
    const prevFiltersJson = JSON.stringify(prevFiltersRef.current);

    if (currentFiltersJson !== prevFiltersJson) {
      resetToFirstPage();
      prevFiltersRef.current = filters;
    }
  }, [filters, resetToFirstPage]);

  const {
    data: users,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['users', page, pageSize, search, sortBy, sortOrder, filters],
    queryFn: async () =>
      api.users.getUsers(
        {
          page,
          pageSize,
          search: search ?? '',
          sortBy: sortBy as keyof User,
          sortOrder: sortOrder === 'ASC' ? 'ASC' : 'DESC',
        },
        filtersQueryString,
      ),
    placeholderData: previousData => previousData,
  });

  return { users, isLoadingUsers: isLoading, isError };
}
