import { api } from '@/lib/http';
import { useQuery } from '@tanstack/react-query';

export function useUserTransactions(id: number) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['user-transactions', id],
    queryFn: async () => api.users.getUserTransactions(id),
  });

  return { userTransactions: data, isLoadingUserTransactions: isLoading, isErrorUserTransactions: isError };
}
