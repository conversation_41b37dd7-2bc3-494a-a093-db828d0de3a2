import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useDeleteUser() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isError, error } = useMutation({
    mutationFn: (id: number) => api.users.deleteUser(id),
    onSuccess: async () => {
      toast.success('User deleted successfully');
      await queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    deleteUser: mutateAsync,
    isDeleting: isPending,
    isDeleteError: isError,
    deleteError: error,
  };
}
