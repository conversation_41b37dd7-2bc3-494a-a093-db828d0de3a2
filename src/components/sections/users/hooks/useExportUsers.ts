import { stringToBlob } from '@/backend/shared/utils/export-csv';
import { ExportUsersPayload } from '@/backend/users/validations/export-users';
import { api } from '@/lib/http';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useExportUsers() {
  const { mutate, isPending } = useMutation({
    mutationFn: (data: ExportUsersPayload) => api.users.exportUsers(data),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
    onSuccess: (data: string) => {
      const blob = stringToBlob(data);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'users.csv';
      a.click();
    },
  });

  return {
    exportUser: mutate,
    isExporting: isPending,
  };
}
