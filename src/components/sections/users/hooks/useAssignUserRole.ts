import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';
import { UserType } from '@/backend/users/entities/User';

export function useAssignUserRole() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: { id: number; userType: UserType }) => api.users.assignUserRole(data.id, data.userType),
    onSuccess: async () => {
      toast.success('User role assigned successfully');
      await queryClient.invalidateQueries({ queryKey: ['user'] });
      await queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    assignUserRole: mutateAsync,
    isAssigningUserRole: isPending,
  };
}
