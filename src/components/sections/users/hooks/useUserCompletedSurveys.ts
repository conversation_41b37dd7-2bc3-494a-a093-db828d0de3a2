import { api } from '@/lib/http';
import { useQuery } from '@tanstack/react-query';

export function useUserCompletedSurveys(id: number) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['user-completed-surveys', id],
    queryFn: async () => api.users.getUserCompletedSurveys(id),
  });

  return { userCompletedSurveys: data, isLoadingUserCompletedSurveys: isLoading, isErrorUserCompletedSurveys: isError };
}
