import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { VerifyUserPayload } from '@/backend/users/validations/verify-user';
import toast from 'react-hot-toast';
import { VerificationStatus } from '@/backend/users/entities/User';

export function useUserVerification() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: Partial<VerifyUserPayload> & { id?: number }) => api.users.userVerification(data),
    onSuccess: async (_, variables) => {
      if (variables.verificationStatus === VerificationStatus.Verified) {
        toast.success('User verified successfully');
      } else if (variables.verificationStatus === VerificationStatus.Denied) {
        toast.success('User denied successfully');
      } else {
        toast.success('User unverified successfully');
      }

      await queryClient.invalidateQueries({ queryKey: ['user'] });
      await queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    verifyUser: mutateAsync,
    isVerifying: isPending,
  };
}
