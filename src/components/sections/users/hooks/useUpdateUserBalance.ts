import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UpdateUserBalancePayload } from '@/backend/users/validations/update-user-balance';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useUpdateUserBalance() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: UpdateUserBalancePayload & { id: number }) => api.users.updateBalance(data),
    onSuccess: async (_, variables) => {
      await queryClient.invalidateQueries({ queryKey: ['user-transactions', variables.id] });
    },
    onError: error => {
      toast.error(error.message);
    },
  });

  return {
    updateBalance: mutateAsync,
    isUpdatingBalance: isPending,
  };
}
