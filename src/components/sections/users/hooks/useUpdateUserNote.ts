import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export const useUpdateUserNote = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, note }: { id: number; note: string | null }) => api.users.updateUserNote(id, note),
    onSuccess: (_, { id }) => {
      toast.success(`User's note updated successfully`);
      queryClient.invalidateQueries({ queryKey: ['user', id] });
    },
    onError: () => {
      toast.error(`Failed to update user's note`);
    },
  });
};
