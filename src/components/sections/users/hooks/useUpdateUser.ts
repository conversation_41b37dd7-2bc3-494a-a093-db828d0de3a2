import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UpdateUserPayload } from '@/backend/users/validations/update-user';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useUpdateUser(userId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: UpdateUserPayload) => api.users.updateUser(userId, data),
    onSuccess: async () => {
      toast.success('User updated successfully');
      await queryClient.invalidateQueries({ queryKey: ['user', userId] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    updateUser: mutateAsync,
    isUpdating: isPending,
  };
}
