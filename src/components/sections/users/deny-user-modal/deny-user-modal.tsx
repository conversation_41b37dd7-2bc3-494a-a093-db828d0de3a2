import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import React from 'react';
import Image from 'next/image';
import type { ModalConfirmVerificationProps } from '@/components/common/modal-confirm-verification';
import { DialogDescription } from '@/components/ui/dialog-3';
import { Form, Label, Textarea } from '@/components/ui/form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { VerifyUserPayload } from '@/backend/users/validations/verify-user';
import { VerificationStatus } from '@/backend/users/entities/User';
interface DenyModalProps extends ModalConfirmVerificationProps {
  onCancel?: () => void;
  onDeny?: (payload: VerifyUserPayload) => void;
}

const schema = z.object({
  note: z.string().max(500, { message: 'Comments must be less than 500 characters' }).optional(),
});

const DenyUserModal = ({ openDialog, onDeny, setOpenDialog, onCancel }: DenyModalProps) => {
  const handleOnSubmit = (data: z.infer<typeof schema>) => {
    onDeny?.({
      verificationStatus: VerificationStatus.Denied,
      note: data.note,
    });
  };

  return (
    <Dialog open={openDialog} onOpenChange={setOpenDialog}>
      <DialogContent className="!w-[400px]" onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <Image src={'/icons/alert-icon.svg'} width={12} height={12} alt="alert" className="w-12 h-12" />
        </DialogHeader>
        <DialogTitle className="mt-5">Deny User Verification</DialogTitle>
        <DialogDescription className="mt-2">Are you sure you want to deny this user?</DialogDescription>
        <Form id="deny-user-form" className="mt-4" onSubmit={handleOnSubmit} schema={schema}>
          <Label className="text-sm font-medium">Comments (optional)</Label>
          <Textarea
            placeholder="e.g. User denied to due precedent fraudulent behaviour"
            name="note"
            className="h-[118px] resize-none overflow-y-auto"
          />
        </Form>
        <DialogFooter className="mt-8 flex justify-between">
          <Button variant="outline" className="h-10 flex-1" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" form="deny-user-form" variant="default" className="h-10 flex-1">
            Deny
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DenyUserModal;
