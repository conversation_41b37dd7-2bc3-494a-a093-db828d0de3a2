'use client';

import React, { useEffect, useImperative<PERSON><PERSON><PERSON>, useRef, useState } from 'react';
import * as z from 'zod';

import { CreateUserPayload } from '@/backend/users/validations/create-user';
import { useProvinces } from '@/components/sections/audiences/hooks/useProvinces';
import { Button } from '@/components/ui/button';
import { Form, Input, Label, Select } from '@/components/ui/form';
import { InputAddress } from '@/components/ui/form/address-input';
import { FormActions } from '@/components/ui/form/form';
import { postalCodeMask } from '@/components/ui/form/input';
import InputPhone, { phoneMask } from '@/components/ui/form/input-phone';
import { SelectItem } from '@/components/ui/form/select';
import { useFormListener as UseFormListener } from '@/hooks/useFormListener';
import { useAddUserStore } from './useAddUserStore';

const validateSchema = z.object({
  email: z
    .string({
      required_error: 'Email is required',
    })
    .regex(new RegExp(/^[\w-.+]+@([\w-]+\.)+[\w-]{2,3}$/), 'Please enter a valid email'),
  phone: z
    .string()
    .optional()
    .transform(val => {
      if (!val) return val;
      return val.replaceAll(/[()\- ]/g, '');
    }),
  country: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  province: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  city: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  address: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  postalCode: z
    .string()
    .regex(new RegExp(/^([A-Z]\d[A-Z]) (\d[A-Z]\d)$/), 'Please enter a valid postal code')
    .optional()
    .transform(val => (val === '' ? null : val)),
  canadaPostId: z.string().optional(),
});

type FormData = z.infer<typeof validateSchema>;

export type HandleContactInfoProps = {
  getValues: () => Partial<CreateUserPayload> | undefined;
};

type ContactInfoFormProps = {
  onCancel: () => void;
  ref: React.ForwardedRef<HandleContactInfoProps>;
};

const itemClass = 'basis-full md:basis-[calc(50%-12px)] flex-shrink';
const labelClass = 'mb-1.5 h-5 font-medium inline-block text-[#344054]';

const ContactInfoForm = React.forwardRef<HandleContactInfoProps, ContactInfoFormProps>(({ onCancel }, ref) => {
  const [isDisabled, setIsDisabled] = useState(false);
  const { updateData, updateStep, data } = useAddUserStore();
  const { provinces, isLoadingProvinces } = useProvinces();
  const formRef = useRef<FormActions<typeof validateSchema>>(null);

  useImperativeHandle(ref, () => ({
    getValues() {
      if (!formRef.current) return;
      const data = formRef.current.formHandler.getValues() as Partial<CreateUserPayload>;
      return data;
    },
  }));

  const onSubmit = async (data: FormData): Promise<void> => {
    updateData(data);
    updateStep(1);
  };

  useEffect(() => {
    if (data.address) setIsDisabled(true);
  }, [data]);

  return (
    <Form
      schema={validateSchema}
      onSubmit={onSubmit}
      formRef={formRef}
      mode="onSubmit"
      className="flex flex-col gap-6 flex-1"
      defaultValues={{
        ...data,
        phone: phoneMask(data.phone),
      }}
    >
      <UseFormListener />
      <div className="flex flex-wrap gap-6 mt-4 z-10">
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="email" className={labelClass}>
              Email *
            </Label>
          </div>
          <Input type="email" name="email" id="email" placeholder="<EMAIL>" />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="phone" className={labelClass}>
              Phone Number
            </Label>
          </div>
          <InputPhone name="phone" id="phone" placeholder="Enter phone number" />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="city" className={labelClass}>
              Address
            </Label>
          </div>
          <Input name="canadaPostId" className="hidden" />
          <InputAddress
            type="text"
            name="address"
            id="address"
            placeholder="Enter address"
            onDisabled={setIsDisabled}
          />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="province" className={labelClass}>
              Province
            </Label>
          </div>
          <Select
            name="province"
            placeholder="Select province"
            className="h-11 text-base text-wrap text-left"
            isLoading={isLoadingProvinces}
            disabled={isDisabled}
          >
            {provinces?.data?.map(p => (
              <SelectItem key={p.id} value={p.name}>
                {p.name}
              </SelectItem>
            ))}
          </Select>
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="city" className={labelClass}>
              City
            </Label>
          </div>
          <Input type="text" name="city" id="city" placeholder="Enter city" disabled={isDisabled} />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="postalCode" className={labelClass}>
              Postal Code
            </Label>
          </div>
          <Input
            type="text"
            name="postalCode"
            id="postalCode"
            placeholder="Enter postal code"
            disabled={isDisabled}
            mask={value => postalCodeMask(value)}
          />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="country" className={labelClass}>
              Country
            </Label>
          </div>
          <Input type="text" name="country" id="country" placeholder="Enter country" disabled={isDisabled} />
        </div>
      </div>
      <div className="mt-[auto]">
        <div className="flex justify-end gap-6">
          <Button variant="secondary" className="font-semibold" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" className="font-semibold">
            Next
          </Button>
        </div>
      </div>
    </Form>
  );
});
ContactInfoForm.displayName = 'ContactInfoForm';

export default ContactInfoForm;
