import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { CreateUserPayload } from '@/backend/users/validations/create-user';
import toast from 'react-hot-toast';

export function useAddUser() {
  const queryClient = useQueryClient();
  const { mutateAsync: addUser, isPending: isAddingUser } = useMutation({
    mutationFn: (data: CreateUserPayload) => api.users.addUser(data),
    onSuccess: async () => {
      toast.success('User created successfully');
      await queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    addUser,
    isAddingUser,
  };
}
