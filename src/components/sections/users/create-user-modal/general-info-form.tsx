'use client';

import React, { useImperativeHandle, useRef } from 'react';
import { z } from 'zod';

import { Form, Input, Label, Select } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { InputDate } from '@/components/ui/form/input-date';
import { SelectItem } from '@/components/ui/form/select';
import { useIndustryInfo } from '@/hooks/useIndustryInfo';
import { useAllSpecialties } from '@/hooks/useAllSpecialties';
import { useAddUser } from './useAddUser';
import { useAddUserStore } from './useAddUserStore';
import { CreateUserPayload } from '@/backend/users/validations/create-user';
import { FormActions } from '@/components/ui/form/form';
import { useFormListener as UseFormListener } from '@/hooks/useFormListener';
import { Gender, UserType } from '@/backend/users/entities/User';
import { cn } from '@/lib/utils';
import { getUserTypeLabel } from '../utils';

const validateSchema = z.object({
  firstName: z
    .string({
      required_error: 'First Name is required',
    })
    .min(1, 'First Name is required')
    .regex(/^[a-zA-Z \-À-ÿ]+$/, "First name should only contain text and these characters: ⌴'-"),
  lastName: z
    .string({
      required_error: 'Last Name is required',
    })
    .min(1, 'First Name is required')
    .regex(/^[a-zA-Z À-ÿ]+$/, "Last name should only contain text and these characters: ⌴'-"),
  birthday: z
    .string()
    .optional()
    .refine(val => !val || val === '' || z.coerce.date().safeParse(val).success, 'Date of Birth is invalid'),
  specialtyId: z.coerce.number({
    required_error: 'Specialty is required',
    invalid_type_error: 'Specialty is required',
  }),
  gender: z.nativeEnum(Gender).optional().nullable(),
  licenseNumber: z.coerce.string().optional(),
  practiceSetting: z.string().optional(),
  employmentStatus: z.string().optional(),
  userType: z.nativeEnum(UserType).optional().nullable(),
});

type FormData = z.infer<typeof validateSchema>;

export type HandleGeneralInfoProps = {
  getValues: () => Partial<CreateUserPayload> | undefined;
};

type GeneralInfoFormProps = {
  onCancel: () => void;
  onSuccess: () => void;
  ref: React.ForwardedRef<HandleGeneralInfoProps>;
};

const itemClass = 'basis-full md:basis-[calc(50%-12px)] flex-shrink';
const labelClass = 'mb-1.5 h-5 font-medium inline-block text-[#344054]';

const GeneralInfoForm = React.forwardRef<HandleGeneralInfoProps, GeneralInfoFormProps>(
  ({ onCancel, onSuccess }, ref) => {
    const { industryInfo, isLoadingIndustryInfo } = useIndustryInfo();
    const { specialties, isLoadingSpecialties } = useAllSpecialties();
    const { addUser, isAddingUser } = useAddUser();
    const { data } = useAddUserStore();
    const formRef = useRef<FormActions<typeof validateSchema>>(null);

    useImperativeHandle(ref, () => ({
      getValues() {
        if (!formRef.current) return;
        const data = formRef.current.formHandler.getValues() as Partial<CreateUserPayload>;
        return data;
      },
    }));

    async function onSubmit(formData: FormData) {
      const payload = { ...data, ...formData };
      if (!payload.birthday) payload.birthday = undefined;
      const res = await addUser(payload as CreateUserPayload);
      if (res) onSuccess();
    }

    return (
      <Form
        schema={validateSchema}
        onSubmit={onSubmit}
        formRef={formRef}
        className="flex flex-col gap-6 flex-1"
        defaultValues={
          {
            ...data,
            specialtyId: data.specialtyId ? String(data.specialtyId) : undefined,
          } as unknown as FormData
        }
      >
        <UseFormListener />
        <div className="flex flex-col gap-6 mt-4">
          <div className="flex gap-6">
            <div className={cn(itemClass, '')}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="userType" className={labelClass}>
                  User Role
                </Label>
              </div>
              <Select name="userType" placeholder="Select Role" className="h-11 text-base text-wrap text-left">
                {Object.values(UserType).map(type => (
                  <SelectItem key={type} value={type}>
                    {getUserTypeLabel(type)}
                  </SelectItem>
                ))}
              </Select>
            </div>
          </div>
          <div className="flex flex-wrap gap-6">
            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="firstName" className={labelClass}>
                  First Name *
                </Label>
              </div>
              <Input type="text" name="firstName" id="firstName" placeholder="Enter first name" />
            </div>
            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="lastName" className={labelClass}>
                  Last Name *
                </Label>
              </div>
              <Input type="text" name="lastName" id="lastName" placeholder="Enter last name" />
            </div>
            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="date-of-birth" className={labelClass}>
                  Date of Birth
                </Label>
              </div>
              <InputDate name="birthday" id="date-of-birth" toDate={new Date()} />
            </div>
            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="gender" className={labelClass}>
                  Gender
                </Label>
              </div>
              <Select name="gender" placeholder="Select gender" className="h-11 text-base">
                {Object.values(Gender).map(gender => (
                  <SelectItem key={gender} value={gender}>
                    {gender}
                  </SelectItem>
                ))}
              </Select>
            </div>
            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="licenseNumber" className={labelClass}>
                  License Number
                </Label>
              </div>
              <Input type="number" name="licenseNumber" id="licenseNumber" placeholder="Enter license code" />
            </div>
            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="specialtyId" className={labelClass}>
                  Specialty *
                </Label>
              </div>
              <Select
                name="specialtyId"
                placeholder="Select specialty"
                className="h-11 text-base"
                isLoading={isLoadingSpecialties}
              >
                {specialties?.data?.map(s => (
                  <SelectItem key={s.id} value={String(s.id)}>
                    {s.name}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="employmentStatus" className={labelClass}>
                  Employment Status
                </Label>
              </div>
              <Select
                name="employmentStatus"
                placeholder="Select employment status"
                className="h-11 text-base"
                isLoading={isLoadingIndustryInfo}
              >
                {industryInfo?.data?.employmentStatus?.map(es => (
                  <SelectItem key={es.id} value={es.name}>
                    {es.name}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className={itemClass}>
              <div className="h-5 max-h-5 mb-1.5">
                <Label htmlFor="practiceSetting" className={labelClass}>
                  Practice Setting
                </Label>
              </div>
              <Select
                name="practiceSetting"
                placeholder="Select practice settings"
                className="h-11 text-base"
                isLoading={isLoadingIndustryInfo}
              >
                {industryInfo?.data?.practiceSettings?.map(ps => (
                  <SelectItem key={ps.id} value={ps.name}>
                    {ps.name}
                  </SelectItem>
                ))}
              </Select>
            </div>
          </div>
        </div>

        <div className="mt-[auto]">
          <div className="flex justify-end gap-6">
            <Button variant="secondary" className="font-semibold" onClick={onCancel} disabled={isAddingUser}>
              Cancel
            </Button>
            <Button type="submit" className="font-semibold" disabled={isAddingUser} isLoading={isAddingUser}>
              Create User
            </Button>
          </div>
        </div>
      </Form>
    );
  },
);
GeneralInfoForm.displayName = 'GeneralInfoForm';

export default GeneralInfoForm;
