import { CreateUserPayload } from '@/backend/users/validations/create-user';
import { create } from 'zustand';

type AddUserData = Partial<CreateUserPayload>;

type AddUserStore = {
  data: AddUserData;
  currentStep: number;
  updateData: (data: AddUserData) => void;
  updateStep: (step: number) => void;
  reset: () => void;
};

export const useAddUserStore = create<AddUserStore>(set => ({
  data: {},
  currentStep: 0,
  updateData: data => set(state => ({ data: { ...state.data, ...data } })),
  updateStep: step => set({ currentStep: step }),
  reset: () => set({ data: {}, currentStep: 0 }),
}));
