'use client';

import { Cross2Icon } from '@radix-ui/react-icons';
import { Circle, CircleCheck } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';

import { CreateUserPayload } from '@/backend/users/validations/create-user';
import ModalConfirmDiscard from '@/components/common/modal-confirm-discard';
import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { useFormDirtyStore } from '@/hooks/useFormListener';
import ContactInfoForm, { HandleContactInfoProps } from './contact-info-form';
import GeneralInfoForm, { HandleGeneralInfoProps } from './general-info-form';
import { useAddUserStore } from './useAddUserStore';

type CreateUserModalProps = {
  onClose: () => void;
};

type ProcessStepProps = {
  status: 'PENDING' | 'ACTIVE' | 'COMPLETE';
  label: string;
  step: number;
  onStepChange: () => Partial<CreateUserPayload>;
};

const ProcessStep: React.FC<ProcessStepProps> = ({ status, label, step, onStepChange }: ProcessStepProps) => {
  const { updateStep, currentStep, updateData } = useAddUserStore();

  const handleClick = () => {
    if (currentStep === step) return;

    const data = onStepChange();

    if (data) {
      updateData(data as CreateUserPayload);
    }

    updateStep(step);
  };

  return (
    <button className="flex items-center outline-none" onClick={handleClick}>
      {status === 'PENDING' && <Circle strokeWidth={3} className="stroke-black mr-2 w-3.5 h-3.5" />}
      {status === 'ACTIVE' && <Circle strokeWidth={3} className="stroke-primary mr-2 w-3.5 h-3.5" />}
      {status === 'COMPLETE' && <CircleCheck strokeWidth={3} className="stroke-primary mr-2 w-3.5 h-3.5" />}
      <span className="text-nowrap">{label}</span>
    </button>
  );
};

const CreateUserModal: React.FC<CreateUserModalProps> = ({ onClose }: CreateUserModalProps) => {
  const { isFormDirty } = useFormDirtyStore(useShallow(state => ({ isFormDirty: state.isFormDirty })));
  const { currentStep } = useAddUserStore();
  const [openDialog, setOpenDialog] = useState(false);
  const generalInfoRef = useRef<HandleGeneralInfoProps>(null);
  const contactInfoRef = useRef<HandleContactInfoProps>(null);

  const handleOpenDialog = () => {
    if (isFormDirty) {
      setOpenDialog(true);
    } else {
      onClose();
    }
  };

  const handleStepChange = () => {
    return { ...generalInfoRef.current?.getValues(), ...contactInfoRef.current?.getValues() };
  };

  return (
    <>
      <Dialog open onOpenChange={handleOpenDialog}>
        <DialogContent
          isWrapped={true}
          className="flex lg:min-w-[80%] lg:min-h-[70%] 2xl:min-w-[auto] p-0"
          onClickOutSide={() => {
            onClose();
          }}
        >
          <div className="flex flex-col lg:flex-row">
            <div className="basis-0 p-8 lg:border-r">
              <h1 className="font-semibold text-lg">New User</h1>
              <div className="mt-8">
                <ProcessStep
                  status={currentStep === 0 ? 'ACTIVE' : 'COMPLETE'}
                  label="Contact Info"
                  step={0}
                  onStepChange={handleStepChange}
                />
              </div>
              <div className="mt-4">
                <ProcessStep
                  status={currentStep === 1 ? 'ACTIVE' : 'PENDING'}
                  label="General Info"
                  step={1}
                  onStepChange={handleStepChange}
                />
              </div>
            </div>
            {currentStep === 0 && (
              <div className="flex flex-col p-8">
                <div className="font-medium text-sm">USER INFO</div>
                <div className="font-semibold text-sm mt-10">Contact Info</div>
                <ContactInfoForm onCancel={onClose} ref={contactInfoRef} />
              </div>
            )}
            {currentStep === 1 && (
              <div className="flex flex-col p-8">
                <div className="font-medium text-sm">USER INFO</div>
                <div className="font-semibold text-sm mt-10">General Info</div>
                <GeneralInfoForm onCancel={onClose} onSuccess={onClose} ref={generalInfoRef} />
              </div>
            )}
          </div>
          <DialogClose
            className="absolute right-6 top-6 rounded-lg"
            aria-label="Close"
            onClick={e => {
              e.preventDefault();
              handleOpenDialog();
            }}
          >
            <Cross2Icon className="w-6 h-6" />
          </DialogClose>
        </DialogContent>
      </Dialog>
      <ModalConfirmDiscard
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
        onConfirm={onClose}
        title="Discard Changes"
        subtitle="Are you sure? Changes you have made won't be saved."
      />
    </>
  );
};

export default CreateUserModal;
