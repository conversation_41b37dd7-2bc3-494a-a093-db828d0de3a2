'use client';

import { useState } from 'react';
import { CloudDownload, CloudUpload, Plus, User as UserIcon } from 'lucide-react';
import SearchInput from '@/components/common/search-input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import CreateUserModal from '../create-user-modal';
import ImportUsersModal from '@/components/sections/users/import-users-modal';
import { useExportUsers } from '@/components/sections/users/hooks/useExportUsers';
import { useAddUserStore } from '@/components/sections/users/create-user-modal/useAddUserStore';
import UserFilter from '../user-filter/user-filter';

type ModalType = 'CLOSE' | 'CREATE' | 'IMPORT';
type UserHeaderProps = {
  usersCount: number;
  selectedUserIds: number[];
};

const UserHeader = ({ usersCount, selectedUserIds }: UserHeaderProps) => {
  const [modalType, setModalType] = useState<ModalType>('CLOSE');
  const { exportUser, isExporting } = useExportUsers();
  const { reset } = useAddUserStore();

  function onClose() {
    setModalType('CLOSE');
    reset();
  }

  return (
    <div className="flex flex-col lg:flex-row justify-between px-4 lg:px-6 mb-[5px]">
      <div className="font-medium flex items-center">
        <div>Users</div>
        <Badge className="text-primary bg-[#F9F5FF] ml-2 h-fit px-2 py-0.5 text-xs">Count: {usersCount}</Badge>
      </div>
      <div className="flex justify-end flex-1 items-center my-4 lg:my-0 ml-0 lg:ml-6">
        <div className="max-w-25 w-full">
          <SearchInput />
        </div>
        <div className="flex gap-3">
          <Button
            className="h-10 ml-6 px-2.5 md:px-4"
            variant="outline"
            disabled={isExporting}
            onClick={e => {
              e.preventDefault();
              exportUser({ ids: selectedUserIds });
            }}
          >
            <CloudDownload className="block md:hidden" />
            <div className="hidden md:block">Export All</div>
          </Button>
          <UserFilter />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="h-10 px-2.5 md:px-4 border-none bg-primary-brand shadow-[0_1px_2px_0_rgba(16,24,40,0.05)]">
                <Plus />
                <div className="ml-2 hidden md:block">Add Users</div>
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="end" className="min-w-[197px]">
              <DropdownMenuItem
                className="text-foreground font-medium cursor-pointer py-2.5 px-3.5"
                onClick={() => setModalType('CREATE')}
              >
                <UserIcon className="stroke-gray-scale-5 mr-2" />
                Create New User
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-foreground font-medium cursor-pointer py-2.5 px-3.5"
                onClick={() => setModalType('IMPORT')}
              >
                <CloudUpload className="stroke-gray-scale-5 mr-2" />
                Import User List
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {modalType === 'CREATE' && <CreateUserModal onClose={onClose} />}
      {modalType === 'IMPORT' && <ImportUsersModal onClose={onClose} />}
    </div>
  );
};

export default UserHeader;
