import { Download, Upload } from 'lucide-react';
import React, { useState } from 'react';
import { UserTransaction } from '@/backend/users/entities/UserTransaction';
import { formatTime } from '@/utils/date-format';
import { cn } from '@/lib/utils';
import TransactionDetail from '@/components/sections/users/edit-user-form/transaction-detail';

type UserTransactionProps = {
  transaction: UserTransaction;
};

export const UserTransactionComp = ({ transaction }: UserTransactionProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div onClick={() => setIsExpanded(expanded => !expanded)} className="border rounded-lg py-4 my-2 cursor-pointer">
      <div className={cn('flex justify-between items-center px-4', isExpanded && 'border-b pb-2')}>
        <div className="flex items-center">
          <div
            className={cn(
              'w-9 h-9 flex items-center justify-center rounded-lg bg-opacity-10',
              transaction.amount < 0 ? 'text-[#FF5E5E] bg-[#FF6464]' : 'text-[#80D3FF] bg-[#80D3FF]',
            )}
          >
            {transaction.amount < 0 ? <Upload width={13} height={13} /> : <Download width={13} height={13} />}
          </div>
          <div className="ml-3">
            <div className="text-[15px] font-medium">{transaction.title}</div>
            <div className="text-[#6C6D8A] text-[13px]">{formatTime(transaction.createdAt)}</div>
          </div>
        </div>
        <div className="font-semibold text-[15px] whitespace-nowrap">
          {transaction.amount < 0 ? '-' : '+'}${Math.abs(transaction.amount)}
        </div>
      </div>
      {isExpanded && <TransactionDetail transaction={transaction} />}
    </div>
  );
};
