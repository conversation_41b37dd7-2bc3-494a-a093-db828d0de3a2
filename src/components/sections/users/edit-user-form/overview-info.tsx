import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { User, VerificationStatus } from '@/backend/users/entities/User';
import ModalConfirmDelete from '@/components/common/modal-confirm-delete';
import { useDeleteUser } from '@/components/sections/users/hooks/useDeleteUser';
import { useUserVerification } from '@/components/sections/users/hooks/useUserVerification';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { MessageSquare } from 'lucide-react';
import UserVerificationBadge from '../user-verification-badge';

type Props = {
  isEditMode?: boolean;
  setIsEditMode?: React.Dispatch<React.SetStateAction<boolean>>;
  isUpdating?: boolean;
  user?: User;
  isLoading?: boolean;
  openNoteModal: () => void;
};

const OverViewInfo = ({ isEditMode, setIsEditMode, openNoteModal, isUpdating, user, isLoading }: Props) => {
  const router = useRouter();
  const [openDialog, setOpenDialog] = useState(false);
  const { deleteUser, isDeleting } = useDeleteUser();
  const { verifyUser, isVerifying } = useUserVerification();
  const referralByUser = (user && user.userReferralReward?.referredByUser) || undefined;
  const publicReferral = (user && user.userReferralReward?.publicReferralCode) || undefined;

  return (
    <div className="flex w-full justify-between px-2 sm:px-8 py-6">
      {isLoading ? (
        <div className="w-full">
          <Skeleton className="w-1/4 h-7 mb-2" />
          <Skeleton className="w-1/2 h-12" />
        </div>
      ) : (
        <div className="gap-2 flex flex-col">
          <div className="flex">
            <div className="font-medium text-lg mr-6 whitespace-nowrap">
              {user?.firstName} {user?.lastName}
            </div>
            {user && <UserVerificationBadge verificationStatus={user.verificationStatus} />}
          </div>
          <div className="flex flex-wrap mt-2 text-sm">
            <div className="mr-6">
              License Number:{' '}
              <span className="text-[#344054] font-medium whitespace-nowrap">{user?.licenseNumber ?? 'N/A'}</span>
            </div>
            <div className="mr-6">
              Referral Code:{' '}
              <span className="text-[#344054] font-medium whitespace-nowrap">{user?.referralCode ?? 'N/A'}</span>
            </div>
            <div className="mr-6">
              Referred By:{' '}
              <span className="text-[#344054] font-medium whitespace-nowrap">
                {referralByUser
                  ? `${referralByUser.firstName} ${referralByUser.lastName}, ${referralByUser.referralCode}`
                  : publicReferral
                    ? `${publicReferral.code}`
                    : 'N/A'}
              </span>
            </div>
          </div>
          <div className="flex flex-wrap mt-2 text-sm">
            <div className="mr-6">
              User ID: <span className="text-[#344054] font-medium whitespace-nowrap">{user?.id ?? 'N/A'}</span>
            </div>
            <div className="mr-6">
              Firebase ID:{' '}
              <span className="text-[#344054] font-medium whitespace-nowrap">{user?.firebaseUserId ?? 'N/A'}</span>
            </div>
          </div>
          {user?.verificationStatus === VerificationStatus.Denied && (
            <Button
              onClick={openNoteModal}
              variant="outline"
              className={cn(
                'min-w-14 w-fit gap-2 max-w-sm text-start truncate p-2 rounded-md bg-muted text-muted-foreground ',
              )}
            >
              <MessageSquare className="w-4 h-4 text-muted-foreground flex-shrink-0" />
              <span className={cn('truncate text-sm font-normal', user?.note && 'text-gray-900')}>
                {user?.note ?? 'Add Comment'}
              </span>
            </Button>
          )}
        </div>
      )}

      <div className="flex flex-col lg:flex-row items-center w-fit space-y-2 lg:space-y-0">
        <Button
          className={cn(
            'text-error-bold bg-inherit font-semibold whitespace-nowrap text-sm sm:text-base',
            isEditMode ? 'opacity-50' : '',
          )}
          onClick={() => setOpenDialog(true)}
          disabled={isEditMode || isVerifying || isLoading}
        >
          Delete User
        </Button>

        <ModalConfirmDelete
          openDialog={openDialog}
          setOpenDialog={setOpenDialog}
          title="Delete User"
          subtitle="Are you sure you want to delete this user? This action cannot be reversed."
          onConfirm={async () => {
            if (!user) return;
            await deleteUser(user.id);
            router.replace('/users');
          }}
          isConfirming={isDeleting}
        />

        <Button
          variant="outline"
          className="lg:ml-3 lg:mr-3 w-full lg:w-fit text-sm sm:text-base"
          disabled={isEditMode || isVerifying || isLoading}
          isLoading={isVerifying}
          onClick={async () => {
            if (!user) return;
            await verifyUser({
              id: user.id,
              verificationStatus:
                user.verificationStatus === VerificationStatus.Verified
                  ? VerificationStatus.Unverified
                  : VerificationStatus.Verified,
            });
          }}
        >
          {user && user.verificationStatus === VerificationStatus.Verified ? 'Unverify User' : 'Verify User'}
        </Button>
        {isEditMode ? (
          <Button
            type="submit"
            className="rounded-lg w-full lg:w-fit text-sm sm:text-base"
            disabled={isUpdating}
            isLoading={isUpdating}
          >
            Save Changes
          </Button>
        ) : (
          <Button
            className="rounded-lg w-full lg:w-fit text-sm sm:text-base"
            onClick={e => {
              e.preventDefault();
              setIsEditMode && setIsEditMode(true);
            }}
            disabled={isVerifying || isLoading}
          >
            Edit User
          </Button>
        )}
      </div>
    </div>
  );
};
export default OverViewInfo;
