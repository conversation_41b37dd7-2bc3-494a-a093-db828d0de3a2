'use client';
import React from 'react';
import { Input, Label, Select } from '@/components/ui/form';
import { InputDate } from '@/components/ui/form/input-date';
import { SelectItem } from '@/components/ui/form/select';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { IndustryInfo } from '@/backend/users/types/user';
import { Skeleton } from '@/components/ui/skeleton';
import { Gender } from '@/backend/users/entities/User';
import { getUserTypeLabel } from '../utils';
import { USER_TYPE_ORDERED_LIST } from '@/utils/constants';

type Props = {
  isEditMode?: boolean;
  specialties?: Specialty[];
  industryInfo?: IndustryInfo;
  isLoading?: boolean;
};

const GeneralForm = ({ isEditMode, specialties, industryInfo, isLoading }: Props) => {
  return (
    <div>
      <div className="font-bold mb-4">General Info</div>
      {isLoading ? (
        <Skeleton className="h-[366px]" />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="userType">User Role</Label>
            <Select name="userType" placeholder="Select role" className="text-base h-11" disabled={!isEditMode}>
              {Object.values(USER_TYPE_ORDERED_LIST).map(type => (
                <SelectItem key={type} value={type}>
                  {getUserTypeLabel(type)}
                </SelectItem>
              ))}
            </Select>
          </div>
          <div className="hidden md:block" /> {/* Empty div for spacing on desktop */}
          <div>
            <Label htmlFor="first-name">First Name *</Label>
            <Input name="firstName" id="first-name" disabled={!isEditMode} placeholder="Enter first name" />
          </div>
          <div>
            <Label htmlFor="last-name">Last Name *</Label>
            <Input name="lastName" id="last-name" disabled={!isEditMode} placeholder="Enter last name" />
          </div>
          <div>
            <Label htmlFor="date-of-birth">Date of Birth</Label>
            <InputDate name="birthday" id="date-of-birth" disabled={!isEditMode} toDate={new Date()} />
          </div>
          <div>
            <Label htmlFor="gender">Gender</Label>
            <Select name="gender" placeholder="Select gender" className="text-base h-11" disabled={!isEditMode}>
              {Object.values(Gender).map(gender => (
                <SelectItem key={gender} value={gender}>
                  {gender}
                </SelectItem>
              ))}
            </Select>
          </div>
          <div>
            <Label htmlFor="license-number">License Number</Label>
            <Input
              type="number"
              name="licenseNumber"
              id="license-number"
              disabled={!isEditMode}
              placeholder="Enter license number"
            />
          </div>
          <div>
            <Label htmlFor="specialtyId">Specialty *</Label>
            <Select name="specialtyId" placeholder="Select specialty" className="text-base h-11" disabled={!isEditMode}>
              {specialties?.map(s => (
                <SelectItem key={s.id} value={String(s.id)}>
                  {s.name}
                </SelectItem>
              ))}
            </Select>
          </div>
          <div>
            <Label htmlFor="employment-status">Employment Status</Label>
            <Select
              name="employmentStatus"
              placeholder="Select employment status"
              className="h-11 text-base"
              disabled={!isEditMode}
            >
              {industryInfo?.employmentStatus?.map(es => (
                <SelectItem key={es.id} value={es.name}>
                  {es.name}
                </SelectItem>
              ))}
            </Select>
          </div>
          <div>
            <Label htmlFor="practice-setting">Practice Setting</Label>
            <Select
              name="practiceSetting"
              placeholder="Select practice settings"
              className="h-11 text-base"
              disabled={!isEditMode}
            >
              {industryInfo?.practiceSettings?.map(pc => (
                <SelectItem key={pc.id} value={pc.name}>
                  {pc.name}
                </SelectItem>
              ))}
            </Select>
          </div>
        </div>
      )}
    </div>
  );
};
export default GeneralForm;
