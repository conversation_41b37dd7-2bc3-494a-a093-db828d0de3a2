import React from 'react';
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { TriangleAlert } from 'lucide-react';

type ModalConfirmRemoveAudienceProps = {
  openDialog: boolean;
  setOpenDialog: React.Dispatch<React.SetStateAction<boolean>>;
  onConfirm?: () => Promise<void> | void;
  isConfirming?: boolean;
  audienceName?: string;
};

const ModalConfirmRemoveAudience = ({
  openDialog,
  setOpenDialog,
  onConfirm,
  isConfirming,
  audienceName,
}: ModalConfirmRemoveAudienceProps) => {
  return (
    <Dialog open={openDialog} onOpenChange={() => setOpenDialog(false)}>
      <DialogContent
        className="top-1/2 left-1/2 -translate-y-1/2 p-6 -translate-x-1/2 sm:-translate-x-[calc(50%-100px)] w-full sm:w-auto sm:max-w-[400px] h-auto"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex flex-col justify-center items-start">
          <div className="w-16 h-16 mb-5 rounded-full flex justify-center items-center bg-[#FFFAEB]">
            <div className="w-10 h-10 rounded-full flex justify-center items-center bg-[#FEF0C7]">
              <TriangleAlert className="text-[#DC6803]" width={20} height={20} />
            </div>
          </div>
          <div className="text-foreground font-semibold text-lg">Remove from Audience?</div>
          <div className="text-sm text-muted-foreground mt-2">
            Are you sure you want to remove this user from{' '}
            {audienceName ? `the "${audienceName}" audience` : 'this audience'}? This action cannot be undone.
          </div>
        </div>
        <DialogFooter className="mt-8 gap-2 flex justify-between">
          <Button
            type="button"
            variant="outline"
            className="h-10 flex-1"
            onClick={e => {
              e.stopPropagation();
              setOpenDialog(false);
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="default"
            className="h-10 flex-1 bg-primary"
            disabled={isConfirming}
            onClick={async e => {
              e.stopPropagation();
              onConfirm && (await onConfirm());
              setOpenDialog(false);
            }}
          >
            Remove
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ModalConfirmRemoveAudience;
