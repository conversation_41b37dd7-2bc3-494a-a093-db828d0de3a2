'use client';
import { useMemo, useState } from 'react';
import TableDataContainer from '@/components/common/table-container';
import { Table } from '@/components/ui/table';
import { generateColumns as generateSurveyColumns } from '@/app/(protected)/users/[id]/columns';
import { useUserCompletedSurveys } from '@/components/sections/users/hooks/useUserCompletedSurveys';
import { useUserAudiences } from '@/components/sections/users/table-users/hooks/useUserAudiences';
import { cn } from '@/lib/utils';
import { generateAudienceColumns } from './columns';

export const UserTabs = ({ userId }: { userId: number }) => {
  const [activeTab, setActiveTab] = useState<'completed-surveys' | 'audiences'>('completed-surveys');
  const { userCompletedSurveys, isLoadingUserCompletedSurveys } = useUserCompletedSurveys(userId);
  const { data: userAudiences, isLoading: isLoadingUserAudiences } = useUserAudiences(userId);

  const surveyColumns = useMemo(
    () => generateSurveyColumns({ isLoading: isLoadingUserCompletedSurveys }),
    [isLoadingUserCompletedSurveys],
  );

  const audienceColumns = useMemo(() => generateAudienceColumns(isLoadingUserAudiences), [isLoadingUserAudiences]);

  return (
    <div className="mt-10 sm:px-8">
      <div className="mb-4">
        <div className="flex gap-8">
          <button
            onClick={() => setActiveTab('completed-surveys')}
            className={cn(
              'pb-4 text-base font-medium text-gray-500 hover:text-gray-700 relative px-1',
              activeTab === 'completed-surveys' && '!text-primary',
            )}
          >
            Completed Surveys
            {activeTab === 'completed-surveys' && <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary" />}
          </button>
          <button
            onClick={() => setActiveTab('audiences')}
            className={cn(
              'pb-4 text-base font-medium text-gray-500 hover:text-gray-700 relative px-1',
              activeTab === 'audiences' && '!text-primary',
            )}
          >
            Audiences
            {activeTab === 'audiences' && <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary" />}
          </button>
        </div>
      </div>

      {activeTab === 'completed-surveys' && (
        <TableDataContainer className="pt-1 pb-0.5" showPagination={false}>
          <Table
            columns={surveyColumns}
            data={userCompletedSurveys?.data ? userCompletedSurveys.data.map(s => s.survey) : Array(10).fill({})}
            url="/surveys"
          />
        </TableDataContainer>
      )}

      {activeTab === 'audiences' && (
        <TableDataContainer className="pt-1 pb-0.5" showPagination={false}>
          <Table
            columns={audienceColumns}
            data={userAudiences ?? (isLoadingUserAudiences ? Array(10).fill({}) : [])}
            url="/audiences"
          />
        </TableDataContainer>
      )}
    </div>
  );
};
