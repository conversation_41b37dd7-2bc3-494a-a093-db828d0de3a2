'use client';
import { ColumnDef } from '@tanstack/react-table';
import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { AudienceItem } from '@/lib/http/audiences/types';
import Link from 'next/link';
import { formatDate } from '@/utils/date-format';
import { AudienceFiltersCell } from '@/components/ui/table/audience-filters-cell';

export const generateAudienceColumns = (isLoading?: boolean): ColumnDef<AudienceItem>[] => [
  {
    accessorKey: 'name',
    header: ({ column }) => <Header column={column}>Audience Name</Header>,
    cell: ({ row }) => {
      const name = row.original.name;
      return (
        <SkeletonCell isLoading={isLoading} skeletonCount={1}>
          <Link href={`/audiences/${row.original.id}`} className="hover:text-primary">
            <span className="whitespace-nowrap text-foreground font-medium">{name}</span>
          </Link>
        </SkeletonCell>
      );
    },
  },
  {
    accessorKey: 'numberOfUsers',
    header: ({ column }) => <Header column={column}>Audience Size</Header>,
    cell: ({ row }) => {
      const numberOfUsers = row.original.numberOfUsers;
      return (
        <SkeletonCell isLoading={isLoading} skeletonCount={1}>
          <span className="whitespace-nowrap text-foreground">{numberOfUsers}</span>
        </SkeletonCell>
      );
    },
  },
  {
    accessorKey: 'lastSurveyDate',
    header: ({ column }) => <Header column={column}>Last Survey</Header>,
    cell: ({ row }) => {
      const lastSurveyDate = row.original.lastSurveyDate;
      return (
        <SkeletonCell isLoading={isLoading} skeletonCount={1}>
          <span className="whitespace-nowrap text-foreground">
            {lastSurveyDate ? formatDate(lastSurveyDate) : 'No surveys yet'}
          </span>
        </SkeletonCell>
      );
    },
  },
  {
    accessorKey: 'cities',
    header: ({ column }) => <Header column={column}>Filters</Header>,
    enableSorting: false,
    cell: ({ row }) => {
      return (
        <SkeletonCell isLoading={isLoading} skeletonCount={1}>
          <AudienceFiltersCell row={row} />
        </SkeletonCell>
      );
    },
  },
];
