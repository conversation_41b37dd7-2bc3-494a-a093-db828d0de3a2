'use client';
import React from 'react';
import { z } from 'zod';
import { UseFormReturn } from 'react-hook-form';

import { UpdateUserPayload, updateUserSchema } from '@/backend/users/validations/update-user';
import { TransactionType } from '@/backend/users/entities/UserTransaction';
import { useAllSpecialties } from '@/hooks/useAllSpecialties';
import { useIndustryInfo } from '@/hooks/useIndustryInfo';
import { Form } from '@/components/ui/form';
import { inputPhoneSchema, phoneMask } from '@/components/ui/form/input-phone';
import GeneralForm from './general-form';
import ContactForm from './contact-form';
import OverViewInfo from './overview-info';
import Balance from './balance';
import { useUser } from '../hooks/useUser';
import { useUpdateUser } from '../hooks/useUpdateUser';
import { useUpdateUserBalance } from '../hooks/useUpdateUserBalance';
import DeniedNoteModal from '../denied-note-modal/denied-note-modal';
import { useUpdateUserNote } from '../hooks/useUpdateUserNote';

type InfoFormProps = {
  userId: number;
};

const schema = updateUserSchema.omit({ phone: true, birthday: true }).extend({
  birthday: z.string().optional().nullable(),
  balance: z.preprocess(
    val => {
      const formattedValue = (val as string)?.replaceAll('$', '');
      if (formattedValue === '') return undefined;
      return formattedValue;
    },
    z.coerce
      .number({ required_error: 'Amount is required', invalid_type_error: 'Amount is required' })
      .int()
      .min(0, { message: 'Amount must be greater or equal to zero' }),
  ),
  phone: inputPhoneSchema.optional().nullable(),
});
export type UpdateUserFormData = z.infer<typeof schema>;

const InfoForm = ({ userId }: InfoFormProps) => {
  const [isEditMode, setIsEditMode] = React.useState(false);
  const { specialties, isLoadingSpecialties } = useAllSpecialties();
  const { industryInfo, isLoadingIndustryInfo } = useIndustryInfo();
  const { user, isLoadingUser, isFetchingUser, isErrorUser } = useUser(userId);
  const { updateUser, isUpdating } = useUpdateUser(userId);
  const { updateBalance, isUpdatingBalance } = useUpdateUserBalance();
  const { mutate: updateUserNote, isPending: isUpdatingNote } = useUpdateUserNote();
  const [openNoteModal, setOpenNoteModal] = React.useState(false);
  const defaultValues = user
    ? ({
        ...user.data,
        specialtyId: user.data.specialtyId ? String(user.data.specialtyId) : undefined,
        balance: user.data.balance ? `$${user.data.balance}` : '$0',
        practiceSetting: user.data.practiceSetting ?? undefined,
        employmentStatus: user.data.employmentStatus ?? undefined,
        phone: user.data.phone ? phoneMask(user.data.phone) : undefined,
        gender: user.data.gender ?? undefined,
        userType: user.data.userType ?? undefined,
      } as unknown as UpdateUserFormData)
    : undefined;
  const isLoading = isLoadingUser || isLoadingSpecialties || isLoadingIndustryInfo;

  async function onSubmit(data: UpdateUserFormData, formHandler?: UseFormReturn<UpdateUserFormData>) {
    if (!formHandler?.formState || Object.keys(formHandler.formState.dirtyFields).length === 0) {
      setIsEditMode(false);
      return;
    }

    if (user) {
      const amount = data.balance - user.data.balance;
      if (amount !== 0) {
        const transactionType = amount > 0 ? TransactionType.Credit : TransactionType.Adjustment;
        await updateBalance({
          id: userId,
          type: transactionType,
          amount: Math.abs(amount),
        });
      }
    }

    const updatedUser = await updateUser({ ...data, birthday: data.birthday || null } as UpdateUserPayload);
    if (!updatedUser) return;
    formHandler.reset({}, { keepValues: true });
    setIsEditMode(false);
  }

  return (
    <div className="w-full">
      <Form schema={schema} key={`edit-user-form-${isLoading}`} onSubmit={onSubmit} defaultValues={defaultValues}>
        <OverViewInfo
          isEditMode={isEditMode}
          setIsEditMode={setIsEditMode}
          isUpdating={isUpdating || isUpdatingBalance}
          user={user?.data}
          isLoading={isLoading}
          openNoteModal={() => setOpenNoteModal(true)}
        />
        <div className="flex flex-col lg:flex-row justify-between px-2 sm:px-8">
          <div className="flex-1 max-w-41.5 lg:mr-8">
            <GeneralForm
              isEditMode={isEditMode}
              specialties={specialties?.data}
              industryInfo={industryInfo?.data}
              isLoading={isLoading}
            />
            <ContactForm isEditMode={isEditMode} isLoading={isLoading} />
          </div>
          <Balance userId={userId} isEditing={isEditMode && !(isFetchingUser || isErrorUser)} isLoading={isLoading} />
        </div>
      </Form>
      <DeniedNoteModal
        openDialog={openNoteModal}
        setOpenDialog={setOpenNoteModal}
        onSave={data => updateUserNote({ id: userId, note: data.note ?? null })}
        isConfirming={isUpdatingNote}
        note={user?.data.note}
      />
    </div>
  );
};
export default InfoForm;
