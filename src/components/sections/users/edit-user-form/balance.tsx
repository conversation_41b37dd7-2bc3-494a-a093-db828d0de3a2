'use client';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';

import { Label } from '@/components/ui/form';
import { formatDate } from '@/utils/date-format';
import { useUserTransactions } from '@/components/sections/users/hooks/useUserTransactions';
import { UserTransaction } from '@/backend/users/entities/UserTransaction';
import { UserTransactionComp } from './user-transaction';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import InputMoney from '@/components/ui/form/input-money';

type Props = {
  userId: number;
  isEditing?: boolean;
  isLoading?: boolean;
};

const Balance = ({ userId, isEditing, isLoading }: Props) => {
  const { userTransactions, isErrorUserTransactions } = useUserTransactions(userId);
  const { user: currentUser, isLoadingCurrentUser } = useCurrentUser();
  const [groupTransactions, setGroupTransactions] = useState<Map<string, UserTransaction[]>>(new Map());

  useEffect(() => {
    if (!userTransactions || isErrorUserTransactions) return;
    const groups = new Map();

    userTransactions.data.forEach(transaction => {
      const date = formatDate(transaction.createdAt);
      if (!groups.has(date)) {
        groups.set(date, []);
      }
      groups.get(date).push(transaction);
    });

    setGroupTransactions(groups);
  }, [isErrorUserTransactions, userTransactions]);

  const groupTransactionsObject = Object.fromEntries(groupTransactions);

  return (
    <div
      className={cn(
        'w-full flex flex-col lg:w-[300px] xl:min-w-23.438 mt-4 lg:mt-0 overflow-y-auto',
        !isLoading && 'border border-[#EAECF0]',
      )}
    >
      {isLoading ? (
        <Skeleton className="h-[755px]" />
      ) : (
        <>
          <div className="relative">
            <Image
              src="/images/balance-bg.svg"
              alt="balance-background"
              width={375}
              height={229}
              className="w-full xl:w-[375px]"
            />
            <div className="absolute w-[210px] top-[57px] left-8">
              <Label htmlFor="balance">Balance</Label>
              <InputMoney
                name="balance"
                id="balance"
                disabled={!isEditing || isLoadingCurrentUser || !currentUser || currentUser.role !== AdminRole.Admin}
                className="disabled:bg-white disabled:opacity-100"
              />
            </div>
          </div>

          <div className="px-4">
            <div className="font-bold mb-4">Transaction History</div>
            <div className="max-h-[300px]">
              {Object.keys(groupTransactionsObject).map(key => {
                const date = formatDate(new Date(key), 'd LLLL, yyyy');
                const transactions = groupTransactionsObject[key];
                return (
                  <>
                    <div className="text-[13px] text-[#6C6D8A] font-semibold mb-2">{date}</div>
                    {transactions.map(transaction => (
                      <UserTransactionComp key={transaction.id} transaction={transaction} />
                    ))}
                  </>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
export default Balance;
