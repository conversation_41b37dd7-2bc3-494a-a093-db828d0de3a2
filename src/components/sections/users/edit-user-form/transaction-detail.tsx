import { TransactionType, UserTransaction } from '@/backend/users/entities/UserTransaction';
import { DATE_FORMATS, formatDate } from '@/utils/date-format';

type TransactionDetailProps = {
  transaction: UserTransaction;
};

const TransactionDetail = ({ transaction }: TransactionDetailProps) => {
  const { type, surveyName, completionDate, paidTo, referred, referredBy, code, description } = transaction;

  if (type === TransactionType.Credit || type === TransactionType.Adjustment)
    return <div className="text-[15px] text-muted-foreground pt-2 px-4">{description}</div>;

  if (type === TransactionType.Withdrawal || type === TransactionType.ReferralSuccess)
    return (
      <div className="flex justify-between gap-2 text-[15px] pt-2 px-4">
        <div className="basis-[130px] text-muted-foreground">
          {type === TransactionType.Withdrawal ? 'Paid To' : 'Referred'}
        </div>
        <div className="text-right text-foreground">{type === TransactionType.Withdrawal ? paidTo : referred}</div>
      </div>
    );

  return (
    <div className="px-4">
      <div className="flex justify-between gap-2 text-[15px] py-2">
        <div className="basis-[130px] text-muted-foreground">
          {type === TransactionType.Compensation ? 'Survey Name' : 'Referred By'}
        </div>
        <div className="text-right text-foreground">
          {type === TransactionType.Compensation ? surveyName : referredBy}
        </div>
      </div>
      <hr className="-mx-2" />
      <div className="flex justify-between text-[15px] pt-2">
        <div className="basis-[130px] text-muted-foreground">
          {type === TransactionType.Compensation ? 'Completion Date' : 'Referral Code'}
        </div>
        <div className="text-right text-foreground">
          {type === TransactionType.Compensation
            ? formatDate(completionDate, `${DATE_FORMATS.US_DATE}, ${DATE_FORMATS.TIME_12_HOUR}`)
            : code}
        </div>
      </div>
    </div>
  );
};

export default TransactionDetail;
