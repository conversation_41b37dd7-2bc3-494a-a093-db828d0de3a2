import React, { useState } from 'react';
import { Input, Label, Select } from '@/components/ui/form';
import { InputAddress } from '@/components/ui/form/address-input';
import InputPhone from '@/components/ui/form/input-phone';
import { Skeleton } from '@/components/ui/skeleton';
import { useProvinces } from '@/components/sections/audiences/hooks/useProvinces';
import { SelectItem } from '@/components/ui/form/select';

type Props = {
  isEditMode?: boolean;
  isLoading?: boolean;
};

const ContactForm = ({ isEditMode, isLoading }: Props) => {
  const [isDisabled, setIsDisabled] = useState(false);
  const { provinces, isLoadingProvinces } = useProvinces();

  return (
    <div className="mt-10">
      <div className="font-bold mb-4">Contact Info</div>
      {isLoading ? (
        <Skeleton className="h-[268px]" />
      ) : (
        <div className="grid grid-cols-2 gap-6">
          <div>
            <Label htmlFor="email">Email *</Label>
            <Input name="email" id="email" type="email" disabled={!isEditMode} placeholder="Enter email" />
          </div>

          <div>
            <Label htmlFor="phone-number">Phone Number</Label>
            <InputPhone name="phone" id="phone-number" disabled={!isEditMode} placeholder="Enter phone number" />
          </div>

          <div>
            <Input name="canadaPostId" className="hidden" />
            <Label htmlFor="address">Address</Label>
            <InputAddress
              name="address"
              id="address"
              disabled={!isEditMode}
              placeholder="Enter address"
              onDisabled={setIsDisabled}
            />
          </div>

          <div>
            <Label htmlFor="province">Province</Label>
            <Select
              name="province"
              placeholder="Select province"
              className="h-11 text-base"
              isLoading={isLoadingProvinces}
              disabled={!isEditMode || isDisabled}
            >
              {provinces?.data?.map(p => (
                <SelectItem key={p.id} value={p.name}>
                  {p.name}
                </SelectItem>
              ))}
            </Select>
          </div>

          <div>
            <Label htmlFor="city">City</Label>
            <Input name="city" id="city" disabled={!isEditMode || isDisabled} placeholder="Enter city" />
          </div>

          <div>
            <Label htmlFor="postal-code">Postal Code</Label>
            <Input
              name="postalCode"
              id="postal-code"
              disabled={!isEditMode || isDisabled}
              placeholder="Enter postal code"
            />
          </div>

          <div>
            <Label htmlFor="country">Country</Label>
            <Input name="country" id="country" disabled={!isEditMode || isDisabled} placeholder="Enter country" />
          </div>
        </div>
      )}
    </div>
  );
};
export default ContactForm;
