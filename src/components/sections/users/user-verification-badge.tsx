import { VerificationStatus } from '@/backend/users/entities/User';
import ErrorBadge from '@/components/common/badges/error';
import UnverifiedBadge from '@/components/common/badges/unverified';
import VerifiedBadge from '@/components/common/badges/verified';
import React from 'react';

type Props = {
  verificationStatus: VerificationStatus | null;
};

const UserVerificationBadge = ({ verificationStatus }: Props) => {
  if (verificationStatus === VerificationStatus.Verified) {
    return <VerifiedBadge>{verificationStatus}</VerifiedBadge>;
  }

  if (verificationStatus === VerificationStatus.Unverified) {
    return <UnverifiedBadge>{verificationStatus}</UnverifiedBadge>;
  }

  return <ErrorBadge>{verificationStatus}</ErrorBadge>;
};

export default UserVerificationBadge;
