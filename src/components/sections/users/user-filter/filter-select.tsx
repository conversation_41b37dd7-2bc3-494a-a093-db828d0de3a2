import { SelectContent, SelectProps, SelectTrigger, SelectValue } from '@/components/ui/form/select';
import { Controller } from 'react-hook-form';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import * as SelectPrimitive from '@radix-ui/react-select';
import { ErrorMessage } from '@hookform/error-message';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { ContactStatus, UserType, VerificationStatus } from '@/backend/users/entities/User';

const BADGE_TEXT_COLORS: Record<string, string> = {
  [ContactStatus.WaitForInfo]: 'text-[#B54708] bg-[#FFFAEB] ',
  [ContactStatus.WSPending]: 'text-[#175CD3] bg-[#EFF8FF] ',
  [ContactStatus.ReviewInfo]: 'text-[#5925DC] bg-[#F4F3FF] ',
  [ContactStatus.Complete]: 'text-[#027A48] bg-[#ECFDF3] ',

  [VerificationStatus.Verified]: 'text-[#027A48] bg-[#ECFDF3] ',
  [VerificationStatus.Unverified]: 'bg-[#F2F4F7] ',
  [VerificationStatus.Denied]: 'text-[#B42318] bg-[#FEF3F2] ',

  [UserType.HCPUser]: 'text-[#027A48] bg-[#ECFDF3] ',
  [UserType.HCPUserImported]: 'text-[#027A48] bg-[#ECFDF3] ',

  [UserType.Client]: 'text-[#175CD3] bg-[#EFF8FF] ',
  [UserType.ClientImported]: 'text-[#175CD3] bg-[#EFF8FF] ',

  [UserType.Internal]: 'text-[#B54708] bg-[#FFFAEB] ',

  true: 'text-[#027A48] bg-[#ECFDF3] ',
  false: 'bg-[#F2F4F7] ',
};

const FilterSelect = ({
  children,
  isShowError = true,
  name,
  placeholder,
  className,
  isLoading,
  ...props
}: SelectProps) => {
  const { control, formState } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field, fieldState }) => {
        const getCurrentStyle = (value: string) => {
          return BADGE_TEXT_COLORS[value] || '';
        };

        return (
          <SelectPrimitive.Root
            {...field}
            onValueChange={value => {
              field.onChange(value);
            }}
            defaultValue={field.value}
            {...props}
          >
            <div className="relative">
              <SelectTrigger
                className={cn(
                  'overflow-hidden whitespace-nowrap',
                  field.value ? '' : 'text-placeholder',
                  formState.errors[name]
                    ? '!border-error-light ring-error-light ring-offset-error focus-visible:ring-error-light focus:ring-error-light focus:ring-offset-1'
                    : '',
                  className,
                )}
                isLoading={isLoading}
              >
                <Badge
                  variant="outline"
                  className={cn('rounded-full py-1 px-3 text-xs font-medium', getCurrentStyle(field.value))}
                >
                  <SelectValue placeholder={placeholder} />
                </Badge>
              </SelectTrigger>
              <SelectContent className="max-w-min">{children}</SelectContent>
            </div>
            {isShowError && fieldState.error && (
              <div>
                <ErrorMessage
                  name={name}
                  render={({ message }) => {
                    return <span className="text-red-500 text-sm">{message}</span>;
                  }}
                />
              </div>
            )}
          </SelectPrimitive.Root>
        );
      }}
    />
  );
};

export default FilterSelect;
