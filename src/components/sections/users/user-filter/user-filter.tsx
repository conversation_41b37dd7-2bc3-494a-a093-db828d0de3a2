import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Filter } from 'lucide-react';
import React, { useState } from 'react';
import * as z from 'zod';
import FilterItems from './filter-items';
import { FilterItem, useUserFilterStore } from '@/lib/store/user-filter-store';
import { Form } from '@/components/ui/form';

export interface FilterOption {
  label: string;
  value: string;
}

const filterSchema = z.object({
  filters: z.array(
    z.object({
      field: z.string(),
      operator: z.enum(['is', 'isNot']),
      value: z.string(),
    }),
  ),
});

export type FilterFormValues = z.infer<typeof filterSchema>;

export const FILTER_FIELDS: FilterOption[] = [
  { label: 'Verification Status', value: 'verificationStatus' },
  { label: 'Contact Status', value: 'contactStatus' },
  { label: 'Welcome Survey', value: 'isCompleteWS' },
  { label: 'User Role', value: 'userType' },
];

export const FILTER_OPERATORS: FilterOption[] = [
  { label: 'is', value: 'is' },
  { label: 'is not', value: 'isNot' },
];

const UserFilter = () => {
  const [open, setOpen] = useState(false);

  const { filters, setFilters } = useUserFilterStore();
  const [availableFilters, setAvailableFilters] = useState<FilterOption[]>([...FILTER_FIELDS]);

  const activeFiltersCount = filters.length;

  const initialFilters = filters.map((filter: FilterItem) => ({
    field: filter.field,
    operator: filter.operator as 'is' | 'isNot',
    value: filter.value,
  }));

  const onSubmit = (data: FilterFormValues) => {
    setFilters(data.filters);
    setOpen(false);
  };

  return (
    <div className="flex flex-col gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button size="icon" variant="outline" className="relative">
            <Filter className="w-4 h-4 text-primary" />
            {activeFiltersCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                {activeFiltersCount}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent side="bottom" align="end" className="w-[450px] p-0 shadow-lg rounded-lg border border-gray-200">
          <Form schema={filterSchema} onSubmit={onSubmit} defaultValues={{ filters: initialFilters }}>
            <FilterItems
              filters={filters}
              availableFilters={availableFilters}
              setAvailableFilters={setAvailableFilters}
            />
          </Form>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default UserFilter;
