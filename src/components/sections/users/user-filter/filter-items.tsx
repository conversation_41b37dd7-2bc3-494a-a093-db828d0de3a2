import { Button } from '@/components/ui/button';
import { Select } from '@/components/ui/form';
import { SelectItem } from '@/components/ui/form/select';
import { cn } from '@/lib/utils';
import { Plus, Trash2 } from 'lucide-react';
import React, { useEffect } from 'react';
import { useFormContext, useFieldArray, useWatch } from 'react-hook-form';
import FilterSelect from './filter-select';
import { ContactStatus, UserType, VerificationStatus } from '@/backend/users/entities/User';
import { FilterFormValues, FilterOption, FILTER_FIELDS, FILTER_OPERATORS } from './user-filter';
import { FilterItem } from '@/lib/store/user-filter-store';
import { Separator } from '@/components/ui/separator';

export const FILTER_VALUES: Record<string, FilterOption[]> = {
  verificationStatus: [
    { label: 'Verified', value: VerificationStatus.Verified },
    { label: 'Unverified', value: VerificationStatus.Unverified },
    { label: 'Denied', value: VerificationStatus.Denied },
  ],
  contactStatus: [
    { label: 'WS Pending', value: ContactStatus.WSPending },
    { label: 'Wait for Info', value: ContactStatus.WaitForInfo },
    { label: 'Review Info', value: ContactStatus.ReviewInfo },
    { label: 'Complete', value: ContactStatus.Complete },
  ],
  isCompleteWS: [
    { label: 'Completed', value: 'true' },
    { label: 'Not Completed', value: 'false' },
  ],
  userType: [
    { label: 'HCP User', value: UserType.HCPUser },
    { label: 'HCP User - Imported', value: UserType.HCPUserImported },

    { label: 'Client', value: UserType.Client },
    { label: 'Client Imported', value: UserType.ClientImported },

    { label: 'Internal', value: UserType.Internal },
    { label: 'Unverified', value: UserType.Unverified },
    { label: 'Denied', value: UserType.Denied },
    { label: 'N/A', value: 'null' },
  ],
};

interface FilterItemsProps {
  filters: FilterItem[];
  availableFilters: FilterOption[];
  setAvailableFilters: React.Dispatch<React.SetStateAction<FilterOption[]>>;
}

const FilterItems = ({ filters, availableFilters, setAvailableFilters }: FilterItemsProps) => {
  const { control, setValue } = useFormContext<FilterFormValues>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'filters',
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const filterValues = useWatch({ control, name: 'filters' }) || [];

  useEffect(() => {
    const usedFields = filterValues.map(filter => filter?.field).filter(Boolean);

    const newAvailableFilters = FILTER_FIELDS.filter(filter => !usedFields.includes(filter.value));

    setAvailableFilters(newAvailableFilters);
  }, [filterValues, setAvailableFilters]);

  useEffect(() => {
    filterValues.forEach((filter, index) => {
      if (filter && filter.field) {
        if (!filter.operator) {
          setValue(`filters.${index}.operator`, FILTER_OPERATORS[0].value as 'is' | 'isNot');
        }

        // Ensure the value is valid for the selected field
        const validValues = FILTER_VALUES[filter.field] || [];
        if (!filter.value || !validValues.some(v => v.value === filter.value)) {
          // If current value is invalid, set to first valid value
          if (validValues.length > 0) {
            setValue(`filters.${index}.value`, validValues[0].value);
          }
        }
      }
    });
  }, [filterValues, setValue]);

  const handleAddFilter = () => {
    if (availableFilters.length > 0) {
      const newField = availableFilters[0].value;
      append({
        field: newField,
        operator: FILTER_OPERATORS[0].value as 'is' | 'isNot',
        value: FILTER_VALUES[newField]?.[0]?.value || '',
      });
    }
  };

  const handleRemoveFilter = (index: number) => {
    remove(index);
  };

  return (
    <div className="w-full flex flex-col py-4 gap-4">
      <div className="flex justify-between items-center px-4">
        <span className="text-base font-medium text-muted-foreground">Filter by</span>
        <Button
          variant="ghost"
          className="gap-2 h-auto py-1 px-2"
          onClick={handleAddFilter}
          disabled={availableFilters.length === 0}
        >
          <Plus className="w-4 h-4" />
          <span className="text-sm">Add Filter</span>
        </Button>
      </div>

      <div className="flex flex-col gap-4">
        {fields.map((field, index) => {
          const currentField = filterValues[index]?.field;

          return (
            <div key={field.id} className="flex flex-col gap-2">
              <div className="flex items-start px-4 gap-2">
                <div className="flex-1 flex flex-col gap-2">
                  <div className="flex gap-2 w-full">
                    <Select name={`filters.${index}.field`} placeholder="Select a field" className="w-full">
                      {FILTER_FIELDS.map(filter => (
                        <SelectItem
                          key={filter.value}
                          value={filter.value}
                          disabled={
                            !availableFilters.some(f => f.value === filter.value) && currentField !== filter.value
                          }
                        >
                          {filter.label}
                        </SelectItem>
                      ))}
                    </Select>

                    <Select name={`filters.${index}.operator`} placeholder="Select an operator" className="w-full">
                      {FILTER_OPERATORS.map(operator => (
                        <SelectItem key={operator.value} value={operator.value}>
                          {operator.label}
                        </SelectItem>
                      ))}
                    </Select>
                  </div>

                  <FilterSelect name={`filters.${index}.value`} placeholder="Select a value" className={cn('w-full')}>
                    {FILTER_VALUES[currentField]?.map(value => (
                      <SelectItem key={value.value} value={value.value}>
                        {value.label}
                      </SelectItem>
                    ))}
                  </FilterSelect>
                </div>

                <Button variant="ghost" size="sm" className="p-2" onClick={() => handleRemoveFilter(index)}>
                  <Trash2 className="w-5 h-5 text-gray-400" />
                </Button>
              </div>
              <Separator />
            </div>
          );
        })}
      </div>

      {fields.length === 0 && (
        <div className="flex justify-center items-center h-full">
          <span className="text-sm text-muted-foreground">No filters applied</span>
        </div>
      )}

      {(fields.length || filters.length > 0) && (
        <div className="px-4">
          <Button type="submit" className="w-full">
            Apply
          </Button>
        </div>
      )}
    </div>
  );
};

export default FilterItems;
