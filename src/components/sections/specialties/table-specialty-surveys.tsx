'use client';
import { useMemo } from 'react';
import { generateColumns } from '@/app/(protected)/users/[id]/columns';
import TableDataContainer from '@/components/common/table-container';
import { Table } from '@/components/ui/table';
import { useSpecialtySurveys } from '@/components/sections/specialties/hooks/useSpecialtySurveys';
import { differenceInDays } from 'date-fns';
import { formatDate } from '@/utils/date-format';

const TableSpecialtySurveys = ({ specialtyId }: { specialtyId: number }) => {
  const { specialtySurveys, isLoadingSpecialtySurveys } = useSpecialtySurveys(specialtyId);

  const columns = useMemo(() => generateColumns({ isLoading: isLoadingSpecialtySurveys }), [isLoadingSpecialtySurveys]);

  return (
    <>
      <div className="font-bold mr-6 mb-4 whitespace-nowrap px-2 sm:px-8">Surveys</div>
      <TableDataContainer className="pt-1">
        <Table
          columns={columns}
          data={
            specialtySurveys?.data
              ? specialtySurveys.data.sort((a, b) =>
                  differenceInDays(formatDate(a.expiryDate), formatDate(b.expiryDate)),
                )
              : isLoadingSpecialtySurveys
                ? Array(10).fill({})
                : []
          }
          url="/surveys"
        />
      </TableDataContainer>
    </>
  );
};

export default TableSpecialtySurveys;
