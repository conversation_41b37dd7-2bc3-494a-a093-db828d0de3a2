'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { Form, Input, Label } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { useSpecialty } from '@/components/sections/specialties/hooks/useSpecialty';
import { useDeleteSpecialty } from '@/components/sections/specialties/hooks/useDeleteSpecialty';
import ModalConfirmDelete from '@/components/common/modal-confirm-delete';
import { formatDate } from '@/utils/date-format';
import { useUpdateSpecialty } from '@/components/sections/specialties/hooks/useUpdateSpecialty';
import { specificName } from '@/backend/shared/validations/name';
import { Skeleton } from '@/components/ui/skeleton';
import InputMoney, { inputMoneySchema } from '@/components/ui/form/input-money';

const schema = z.object({
  name: specificName('Specialty'),
  referralValue: inputMoneySchema,
  frenchName: z
    .string({ required_error: 'Specialty French Name is required' })
    .trim()
    .min(1, { message: 'Specialty French Name cannot be empty' })
    .max(50, { message: 'Specialty French Name cannot exceed 50 characters' }),
});

type FormData = z.infer<typeof schema>;

const EditSpecialtyForm = ({ specialtyId }: { specialtyId: number }) => {
  const router = useRouter();
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const { specialty, isLoadingSpecialty } = useSpecialty(specialtyId);
  const { deleteSpecialty, isDeletingSpecialty } = useDeleteSpecialty();
  const { updateSpecialty, isUpdatingSpecialty } = useUpdateSpecialty(specialtyId);

  const defaultValues = specialty
    ? ({
        ...specialty.data,
        referralValue: specialty.data.referralValue ? `$${specialty.data.referralValue}` : '$0',
        frenchName: specialty.data.translations.length > 0 ? specialty.data.translations[0].name : '',
      } as unknown as FormData)
    : undefined;

  async function onSubmit(formData: FormData) {
    await updateSpecialty(formData);
    setIsEditMode(false);
  }

  return (
    <Form
      schema={schema}
      onSubmit={onSubmit}
      mode="onSubmit"
      className="w-full"
      defaultValues={defaultValues}
      key={`specialty-form-${isLoadingSpecialty}`}
    >
      <div className="flex w-full justify-between">
        {isLoadingSpecialty ? (
          <div className="w-full">
            <Skeleton className="w-1/4 h-7 mb-2" />
            <Skeleton className="w-1/2 h-5" />
          </div>
        ) : (
          <div className="flex flex-col">
            <div className="font-medium text-lg mb-2">{specialty?.data?.name ?? 'N/A'}</div>
            <div className="flex flex-col lg:flex-row lg:space-x-6">
              <div className="text-sm text-gray-text">
                User Count: <span className="font-medium">{specialty?.data?.numberOfUsers ?? 'N/A'}</span>
              </div>
              <div className="text-sm text-gray-text">
                # of Surveys: <span className="font-medium">{specialty?.data?.numberOfSurveys ?? 'N/A'}</span>
              </div>
              <div className="text-sm text-gray-text">
                Last Survey Date:{' '}
                <span className="font-medium">
                  {specialty?.data?.lastSurveyDate ? formatDate(specialty?.data?.lastSurveyDate) : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row items-center w-fit space-x-2 space-y-2 lg:space-y-0">
          <Button
            className="text-error-bold bg-inherit font-semibold whitespace-nowrap text-sm sm:text-base"
            disabled={isEditMode || isLoadingSpecialty}
            onClick={e => {
              e.preventDefault();
              setOpenDialog(true);
            }}
          >
            Delete Specialty
          </Button>

          <ModalConfirmDelete
            openDialog={openDialog}
            setOpenDialog={setOpenDialog}
            onConfirm={async () => {
              await deleteSpecialty(specialtyId);
              router.replace('/specialties');
            }}
            isConfirming={isDeletingSpecialty}
            title="Delete Specialty"
            subtitle="Are you sure you want to delete this specialty? This action cannot be reversed."
          />

          {isEditMode ? (
            <Button
              type="submit"
              className="rounded-lg w-full lg:w-fit text-sm sm:text-base"
              isLoading={isUpdatingSpecialty}
            >
              Save Changes
            </Button>
          ) : (
            <Button
              type="button"
              className="rounded-lg w-full lg:w-fit text-sm sm:text-base"
              disabled={isLoadingSpecialty}
              onClick={e => {
                e.preventDefault();
                setIsEditMode(true);
              }}
            >
              Edit Specialty
            </Button>
          )}
        </div>
      </div>

      <div className="w-full flex flex-col flex-1 mt-10">
        <div className="font-bold mr-6">Specialty Info</div>
        <div className="flex flex-wrap gap-6 my-4">
          <div className="max-w-20 w-full">
            <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
              Specialty Name (English)
            </Label>
            {isLoadingSpecialty ? (
              <Skeleton className="h-11" />
            ) : (
              <Input name="name" placeholder="Example: Pharmacists" disabled={!isEditMode} />
            )}
          </div>
          <div className="max-w-20 w-full">
            <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
              Specialty Name (French)
            </Label>
            {isLoadingSpecialty ? (
              <Skeleton className="h-11" />
            ) : (
              <Input name="frenchName" placeholder="Example: Pharmaciens" disabled={!isEditMode} />
            )}
          </div>
          <div className="max-w-20 w-full">
            <Label htmlFor="referralValue" className="mb-1.5 h-5 font-medium inline-block">
              Referral Value
            </Label>
            {isLoadingSpecialty ? (
              <Skeleton className="h-11" />
            ) : (
              <InputMoney name="referralValue" placeholder="Example: $25" disabled={!isEditMode} />
            )}
          </div>
        </div>
      </div>
    </Form>
  );
};
export default EditSpecialtyForm;
