import * as z from 'zod';
import { UseFormReturn } from 'react-hook-form';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Form, Input, Label } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { useAddSpecialty } from './hooks/useAddSpecialty';
import { useCheckSpecialtyName } from '@/components/sections/specialties/hooks/useCheckSpecialtyName';
import { specificName } from '@/backend/shared/validations/name';
import InputMoney, { inputMoneySchema } from '@/components/ui/form/input-money';

type CreateSpecialtyModalProps = {
  onClose: () => void;
};

const schema = z.object({
  referralValue: inputMoneySchema,
  name: specificName('Specialty'),
  frenchName: z
    .string({ required_error: 'Specialty French Name is required' })
    .trim()
    .min(1, { message: 'Specialty French Name cannot be empty' })
    .max(50, { message: 'Specialty French Name cannot exceed 50 characters' }),
});

type FormData = z.infer<typeof schema>;

const CreateSpecialtyModal = ({ onClose }: CreateSpecialtyModalProps) => {
  const { addSpecialty, isAddingSpecialty } = useAddSpecialty();
  const { checkSpecialtyName, isCheckingSpecialtyName } = useCheckSpecialtyName();

  async function onSubmit(formData: FormData, formHandler?: UseFormReturn<FormData>) {
    const checkNameResult = await checkSpecialtyName(formData.name);
    if (checkNameResult.data.isNameExist) {
      formHandler && formHandler.setError('name', { message: 'This specialty already exists' }, { shouldFocus: true });
      return;
    }

    await addSpecialty(formData);
    onClose();
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="flex flex-col md:!max-w-[80%] max-h-screen md:!w-fit md:h-fit p-0 overflow-auto md:top-1/2 md:left-1/2 md:-translate-y-1/2 md:-translate-x-[calc(50%-102.5px)] md:rounded-2xl">
        <div className="flex flex-1 p-8 w-full">
          <Form schema={schema} onSubmit={onSubmit} className="w-full flex flex-col flex-1" mode="onChange">
            <h1 className="font-bold text-2xl mb-9">New Specialty</h1>
            <div className="flex flex-wrap gap-6 mb-6">
              <div className="w-[320px]">
                <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
                  Specialty Name (English)
                </Label>
                <Input name="name" id="name" placeholder="Example: Pharmacists" />
              </div>
            </div>
            <div className="flex flex-wrap gap-6 mb-6">
              <div className="w-[320px]">
                <Label htmlFor="frenchName" className="mb-1.5 h-5 font-medium inline-block">
                  Specialty Name (French)
                </Label>
                <Input name="frenchName" id="frenchName" placeholder="Example: Pharmaciens" />
              </div>
            </div>
            <div className="flex flex-wrap gap-6 mb-10">
              <div className="w-[320px]">
                <Label htmlFor="referralValue" className="mb-1.5 h-5 font-medium inline-block">
                  Referral Value
                </Label>
                <InputMoney name="referralValue" id="referralValue" placeholder="Example: $25" />
              </div>
            </div>

            <div className="flex justify-between gap-3">
              <Button
                variant="secondary"
                className="flex-1 font-semibold border border-input bg-white rounded-lg"
                onClick={onClose}
                disabled={isAddingSpecialty || isCheckingSpecialtyName}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="font-semibold rounded-lg group-[.is-invalid]:pointer-events-none group-[.is-invalid]:bg-[#9f91dd]"
                isLoading={isAddingSpecialty || isCheckingSpecialtyName}
              >
                Create Specialty
              </Button>
            </div>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default CreateSpecialtyModal;
