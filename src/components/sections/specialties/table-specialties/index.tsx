'use client';

import { useMemo } from 'react';

import { Table } from '@/components/ui/table';
import SpecialtiesHeader from '../specialties-header';
import { generateColumns } from './columns';
import { useSpecialties } from '../hooks/useSpecialties';
import { useDeleteSpecialty } from '../hooks/useDeleteSpecialty';
import { cn } from '@/lib/utils';
import CustomPagination from '@/components/common/custom-pagination';

const TableSpecialties = () => {
  const { specialties: { data } = {}, isLoading: isLoadingSpecialties } = useSpecialties();
  const { deleteSpecialty, isDeletingSpecialty } = useDeleteSpecialty();

  const columns = useMemo(
    () =>
      generateColumns({ isLoading: isLoadingSpecialties, onDelete: deleteSpecialty, isDeleting: isDeletingSpecialty }),
    [isLoadingSpecialties, deleteSpecialty, isDeletingSpecialty],
  );

  return (
    <>
      <SpecialtiesHeader specialtiesCount={data?.total ?? 0} />
      <Table
        columns={columns}
        data={data?.data ? data.data : isLoadingSpecialties ? Array(10).fill({}) : []}
        url="/specialties"
        containerClassName={cn(!isLoadingSpecialties && 'peer is-done')}
      />
      <CustomPagination totalPages={data?.totalPages ?? 1} />
    </>
  );
};

export default TableSpecialties;
