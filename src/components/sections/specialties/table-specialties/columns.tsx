'use client';

import { ColumnDef } from '@tanstack/table-core';
import React, { PropsWithChildren } from 'react';

import { Header } from '@/components/ui/table/header';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { ActionsCell, DeleteModalProps } from '@/components/ui/table/action-cell';
import { formatDate } from '@/utils/date-format';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { cn } from '@/lib/utils';

const Cell = ({ children }: PropsWithChildren) => {
  return <span className="text-muted-foreground">{children}</span>;
};

type ColumnsProps = Omit<DeleteModalProps, 'title' | 'subtitle'> & {
  isLoading?: boolean;
};

export function generateColumns({ isLoading, onDelete, isDeleting }: ColumnsProps) {
  const columns: ColumnDef<Specialty>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => <Header column={column}>Specialty Name</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <span className="text-foreground font-medium">{row.getValue('name')}</span>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'numberOfUsers',
      header: ({ column }) => <Header column={column}>Verified Users</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <Cell>{row.getValue('numberOfUsers')}</Cell>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'numberOfSurveys',
      header: ({ column }) => <Header column={column}># of Surveys</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <Cell>{row.getValue('numberOfSurveys')}</Cell>
        </SkeletonCell>
      ),
    },
    {
      accessorKey: 'lastSurveyDate',
      header: ({ column }) => <Header column={column}>Last Survey</Header>,
      cell: ({ row }) => {
        const lastSurveyDate = row.getValue('lastSurveyDate') ? formatDate(row.getValue('lastSurveyDate')) : '';

        return (
          <SkeletonCell isLoading={isLoading}>
            <Cell>{lastSurveyDate}</Cell>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'referralValue',
      header: ({ column }) => <Header column={column}>Referral Value</Header>,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading}>
          <Cell>${row.getValue('referralValue')}</Cell>
        </SkeletonCell>
      ),
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 overflow-hidden' : '')}>
          <ActionsCell
            row={row}
            urlDetail={`/specialties/${row.original.id}`}
            deleteModalProps={{
              onDelete,
              isDeleting,
              title: 'Delete Specialty',
              subtitle: 'Are you sure you want to delete this specialty? This action cannot be reversed.',
            }}
          />
        </SkeletonCell>
      ),
    },
  ];

  return columns;
}
