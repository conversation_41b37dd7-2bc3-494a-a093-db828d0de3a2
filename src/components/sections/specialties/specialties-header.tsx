'use client';
import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import CreateSpecialtyModal from '@/components/sections/specialties/create-specialty-modal';

type SpecialtiesHeaderProps = {
  specialtiesCount: number;
};

const SpecialtiesHeader = ({ specialtiesCount }: SpecialtiesHeaderProps) => {
  const [open, setOpen] = useState(false);
  return (
    <div className="flex flex-row justify-between px-4 lg:px-6 mb-[5px]">
      <div className="font-medium flex items-center">
        <div>Specialties</div>
        <Badge className="text-primary bg-[#F9F5FF] ml-2 h-fit px-2 py-0.5 text-xs">Count: {specialtiesCount}</Badge>
      </div>

      <Button className="h-10 px-2.5 md:px-4 border-none bg-primary-brand" onClick={() => setOpen(true)}>
        <Plus />
        <div className="ml-2 hidden md:block">Add Specialty</div>
      </Button>
      {open && <CreateSpecialtyModal onClose={() => setOpen(false)} />}
    </div>
  );
};
export default SpecialtiesHeader;
