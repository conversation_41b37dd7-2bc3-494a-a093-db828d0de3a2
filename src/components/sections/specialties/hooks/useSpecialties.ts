import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { useSortQuery } from '@/hooks/useSortQuery';
import { usePagination } from '@/hooks/usePagination';
import { Specialty } from '@/backend/specialties/entities/Specialty';

export function useSpecialties() {
  const { sortBy, sortOrder } = useSortQuery({ sortBy: 'name' });
  const { page, pageSize } = usePagination();

  const {
    data: specialties,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['specialties', sortBy, sortOrder, page, pageSize],
    queryFn: async () =>
      api.specialties.getSpecialties({
        page,
        pageSize,
        sortBy: sortBy as keyof Specialty,
        sortOrder: sortOrder === 'ASC' ? 'ASC' : 'DESC',
      }),
    placeholderData: previousData => previousData,
  });

  return { specialties, isLoading, isError };
}
