import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

import { api } from '@/lib/http';
import { CreateSpecialtyPayload } from '@/backend/specialties/validations/create-specialty';

export function useAddSpecialty() {
  const queryClient = useQueryClient();
  const { mutateAsync: addSpecialty, isPending: isAddingSpecialty } = useMutation({
    mutationFn: (data: CreateSpecialtyPayload) => api.specialties.addSpecialty(data),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['specialties'] });
      toast.success('Specialty created successfully');
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return { addSpecialty, isAddingSpecialty };
}
