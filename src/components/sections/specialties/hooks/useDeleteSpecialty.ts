import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useDeleteSpecialty() {
  const queryClient = useQueryClient();
  const { mutateAsync: deleteSpecialty, isPending: isDeletingSpecialty } = useMutation({
    mutationFn: (id: number) => api.specialties.deleteSpecialty(id),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['specialties'] });
      toast.success('Specialty deleted successfully');
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return { deleteSpecialty, isDeletingSpecialty };
}
