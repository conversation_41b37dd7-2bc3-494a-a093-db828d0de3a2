import { UpdateSpecialtyPayload } from '@/backend/specialties/validations/update-specialty';
import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useUpdateSpecialty(specialtyId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync: updateSpecialty, isPending: isUpdatingSpecialty } = useMutation({
    mutationFn: (data: UpdateSpecialtyPayload) => api.specialties.updateSpecialty(specialtyId, data),
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['specialty', specialtyId] });
      toast.success('Specialty updated successfully');
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return { updateSpecialty, isUpdatingSpecialty };
}
