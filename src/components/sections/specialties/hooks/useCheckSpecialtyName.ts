import { api } from '@/lib/http';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export function useCheckSpecialtyName() {
  const { mutateAsync: checkSpecialtyName, isPending: isCheckingSpecialtyName } = useMutation({
    mutationFn: (name: string) => api.specialties.checkNameExist(name),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return { checkSpecialtyName, isCheckingSpecialtyName };
}
