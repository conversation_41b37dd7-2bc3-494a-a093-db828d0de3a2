import { ScrollArea } from '@/components/ui/scroll-area';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { SurveyItem, SurveyItemSkeleton } from './survey-item';
import { Survey } from '@/backend/surveys/entities/Survey';
import { cn } from '@/lib/utils';

interface SurveyListProps {
  fetchNextPage: () => void;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  isLoading: boolean;
  surveysData: Survey[];
}

const SurveyList = ({ fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, surveysData }: SurveyListProps) => {
  const ref = useInfiniteScroll({
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    threshold: 0.4,
  });

  return (
    <div className="flex flex-1 flex-col w-full">
      <div className="table w-full table-fixed mb-4">
        <div className="table-row">
          <div className="table-cell w-[40%] text-start px-2 py-2 border-r">
            <span className="text-base font-semibold">Survey Name</span>
          </div>
          <div className="table-cell w-[20%] text-start px-2 py-2 border-r">
            <span className="text-base font-semibold">Status</span>
          </div>
          <div className="table-cell w-[20%] text-start px-2 py-2 border-r">
            <span className="text-base font-semibold">Estimated Funds</span>
          </div>
          <div className="table-cell w-[20%] text-start px-2 py-2">
            <span className="text-base font-semibold">Wallet Payout</span>
          </div>
        </div>
      </div>
      <ScrollArea className={cn('h-[calc(90vh-25rem)]', surveysData.length > 0 && 'border-b')}>
        {isLoading && !isFetchingNextPage && <SurveyItemSkeleton />}
        {!isLoading && surveysData.length === 0 && (
          <div className="text-center py-4 text-muted-foreground">No Results</div>
        )}
        {surveysData.map((item, index) => (
          <div key={item.id} className="py-1">
            <SurveyItem survey={item} ref={index === surveysData.length - 1 ? ref : null} />
          </div>
        ))}
        {isFetchingNextPage && (
          <div className="flex justify-center py-4 w-full">
            <div className="h-6 w-6 animate-spin border-2 border-primary border-t-transparent rounded-full" />
          </div>
        )}
      </ScrollArea>
    </div>
  );
};

export default SurveyList;
