import { Survey } from '@/backend/surveys/entities/Survey';
import { Skeleton } from '@/components/ui/skeleton';
import React, { forwardRef } from 'react';
import { formatNumberToDollar } from '@/lib/utils';
import Link from 'next/link';
import StatusBadge from '@/components/common/badges/status';
interface SurveyItemProps {
  survey: Survey;
}

const SurveyItem = forwardRef<HTMLDivElement, SurveyItemProps>(({ survey }, ref) => {
  return (
    <div ref={ref} className="table w-full table-fixed">
      <div className="table-row">
        <div className="table-cell w-[40%] text-start px-2 py-2">
          <Link href={`/surveys/${survey.id}`} className="text-sm font-medium block truncate">
            <span className="text-sm font-medium block truncate hover:underline">{survey.title}</span>
          </Link>
        </div>
        <div className="table-cell w-[20%] text-start px-2 py-2">
          <StatusBadge className="text-sm" status={survey.status}>
            {survey.status}
          </StatusBadge>
        </div>
        <div className="table-cell w-[20%] text-start px-2 py-2">
          <span className="text-base font-medium block truncate">
            {survey.estimatedFunds !== null ? formatNumberToDollar(survey.estimatedFunds) : 'Unlimited'}
          </span>
        </div>
        <div className="table-cell w-[20%] text-start px-2 py-2">
          <span className="text-base font-medium block truncate">
            {survey.walletPayout !== null ? formatNumberToDollar(survey.walletPayout) : '$0'}
          </span>
        </div>
      </div>
    </div>
  );
});

SurveyItem.displayName = 'SurveyItem';

const SurveyItemSkeleton = ({ count = 10 }: { count?: number }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="table w-full table-fixed">
          <div className="table-row">
            <div className="table-cell w-[40%] text-start px-2 py-2">
              <Skeleton className="h-5 w-full" />
            </div>
            <div className="table-cell w-[20%] text-start px-2 py-2">
              <Skeleton className="h-5 w-3/4" />
            </div>
            <div className="table-cell w-[20%] text-start px-2 py-2">
              <Skeleton className="h-6 w-3/4" />
            </div>
            <div className="table-cell w-[20%] text-start px-2 py-2">
              <Skeleton className="h-6 w-3/4" />
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

SurveyItemSkeleton.displayName = 'SurveyItemSkeleton';

export { SurveyItem, SurveyItemSkeleton };
