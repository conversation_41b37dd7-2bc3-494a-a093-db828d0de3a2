'use client';
import SurveyList from './survey-list';
import { Form, Input } from '@/components/ui/form';
import { Search } from 'lucide-react';
import React, { useMemo, useState } from 'react';
import * as z from 'zod';
import { formatNumberToDollar } from '@/lib/utils';
import { useInfiniteAvailableFundSurveys } from '../hooks/useInfiniteAvailableFundSurveys';
import { useDebounce } from '@/hooks/useDebounce';
import toast from 'react-hot-toast';

const schema = z.object({
  search: z.string().optional(),
});

const SurveysInfo = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const search = useDebounce(searchTerm);
  const pageSize = 10;

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingWalletFunds,
    error: surveysError,
  } = useInfiniteAvailableFundSurveys({
    search,
    pageSize,
  });

  const walletFunds = useMemo(() => data?.pages[0]?.data.walletFunds ?? 0, [data]);
  const surveysData = useMemo(() => data?.pages.flatMap(page => page.data.data) ?? [], [data]);

  if (surveysError) {
    toast.error('Error fetching available surveys funds');
  }
  return (
    <div className="flex h-full flex-col gap-4 mt-8 w-full">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <p className="text-sm font-medium">Total Wallet Balance</p>
          <span className="text-base font-medium">{formatNumberToDollar(walletFunds)}</span>
        </div>
        <div className="flex space-y-4 flex-col">
          <div className="text-base font-bold">Detailed Breakdown</div>
          <Form schema={schema} onSubmit={() => {}}>
            <Input
              name="search"
              onChange={e => setSearchTerm(e.target.value.trim())}
              placeholder="Search for Surveys"
              className="w-full"
              StartIcon={Search}
            />
          </Form>
        </div>
      </div>

      <SurveyList
        fetchNextPage={fetchNextPage}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoadingWalletFunds}
        surveysData={surveysData}
      />
    </div>
  );
};

export default SurveysInfo;
