'use client';
import DateRangePicker from '@/components/common/date-range-picker';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import React, { useMemo, useState } from 'react';
import { DateRange } from 'react-day-picker';
import { AvailableFundsResponse } from '@/lib/http/available-funds/type';
import { formatNumberToDollar } from '@/lib/utils';
import { useAvailableSummaries } from '../hooks/useAvailableSummaries';
import { endOfDay, startOfDay } from 'date-fns';
import toast from 'react-hot-toast';

const SummariesInfo = () => {
  const today = new Date();
  const startOfYear = new Date(today.getFullYear(), 0, 1); // January 1st of current year

  const [creditedTime, setCreditedTime] = useState<DateRange | undefined>({
    from: startOfYear,
    to: today,
  });
  const [cashedOutTime, setCashedOutTime] = useState<DateRange | undefined>({
    from: startOfYear,
    to: today,
  });

  const {
    data: summariesData,
    isLoading,
    error: summariesError,
  } = useAvailableSummaries({
    cashedOutFrom: cashedOutTime?.from ? startOfDay(cashedOutTime?.from).toISOString() : undefined,
    cashedOutTo: cashedOutTime?.to ? endOfDay(cashedOutTime?.to).toISOString() : undefined,
    creditedFrom: creditedTime?.from ? startOfDay(creditedTime?.from).toISOString() : undefined,
    creditedTo: creditedTime?.to ? endOfDay(creditedTime?.to).toISOString() : undefined,
  });

  const availableFunds = useMemo(() => {
    return summariesData?.data ?? ({} as AvailableFundsResponse);
  }, [summariesData]);

  if (summariesError) {
    toast.error('Error fetching available funds');
  }
  return (
    <div className="flex flex-col space-y-4">
      <div className="flex flex-col space-y-4 mt-8">
        <h2 className="font-bold">VoPay Balances</h2>
        <div className="flex justify-between items-center">
          <p className="text-sm">Total Available Funds</p>
          {isLoading ? (
            <Skeleton className="h-6 w-20" />
          ) : (
            <span>{formatNumberToDollar(availableFunds?.availableFunds)}</span>
          )}
        </div>
        <div className="flex justify-between items-center">
          <p className="text-sm">Total Funds in Users&apos; Wallets</p>
          {isLoading ? (
            <Skeleton className="h-6 w-20" />
          ) : (
            <span>{formatNumberToDollar(availableFunds?.walletFunds)}</span>
          )}
        </div>
      </div>
      <Separator className="w-full" />

      <div className="flex flex-col items-start space-y-4 mt-8">
        <h2 className="font-bold">Total Credited for Surveys</h2>

        <DateRangePicker
          key="credited-time"
          onDateChange={setCreditedTime}
          initialDate={creditedTime}
          align="start"
          sideOffset={-100}
          disabledDateButton={isLoading}
        />
        <div className="flex w-full justify-between items-center">
          <p className="text-sm">Total Credited for Surveys</p>
          {isLoading ? (
            <Skeleton className="h-6 w-20" />
          ) : (
            <span>{formatNumberToDollar(availableFunds?.surveyCredited)}</span>
          )}
        </div>
        <div className="flex justify-between w-full items-center">
          <p className="text-sm">Total Credited for Referrals</p>
          {isLoading ? (
            <Skeleton className="h-6 w-20" />
          ) : (
            <span>{formatNumberToDollar(availableFunds?.referralCredited)}</span>
          )}
        </div>
      </div>

      <Separator className="w-full" />

      <div className="flex flex-col items-start space-y-4 mt-8">
        <h2 className="font-bold">Cashed Out Funds</h2>

        <DateRangePicker
          key="cashed-out-time"
          onDateChange={setCashedOutTime}
          initialDate={cashedOutTime}
          disabledDateButton={isLoading}
        />
        <div className="flex w-full justify-between items-center">
          <p className="text-sm">Via Interac</p>
          {isLoading ? (
            <Skeleton className="h-6 w-20" />
          ) : (
            <span>{formatNumberToDollar(availableFunds?.interacCashedOut)}</span>
          )}
        </div>
        <div className="flex justify-between w-full items-center">
          <p className="text-sm">Via Bank Transfer</p>
          {isLoading ? (
            <Skeleton className="h-6 w-20" />
          ) : (
            <span>{formatNumberToDollar(availableFunds?.bankCashedOut)}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default SummariesInfo;
