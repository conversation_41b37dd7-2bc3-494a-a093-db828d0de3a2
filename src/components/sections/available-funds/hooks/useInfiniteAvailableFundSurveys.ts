import { api } from '@/lib/http';
import { useInfiniteQuery } from '@tanstack/react-query';

interface UseInfinitePrioritizedUsersProps {
  search: string;
  pageSize: number;
}

export function useInfiniteAvailableFundSurveys({ search, pageSize }: UseInfinitePrioritizedUsersProps) {
  return useInfiniteQuery({
    queryKey: ['available-fund-surveys', search, pageSize],
    initialPageParam: 1,
    queryFn: ({ pageParam = 1 }) => api.availableFunds.getAvailableFundSurveys({ page: pageParam, pageSize, search }),
    getNextPageParam: (lastPage, _pages) => {
      if (lastPage.data.data.length < pageSize) return undefined;
      return _pages.length + 1;
    },
  });
}
