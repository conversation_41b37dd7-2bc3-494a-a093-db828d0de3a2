import { api } from '@/lib/http';
import { SummariesPayload } from '@/app/api/available-funds/summaries/route';
import { useQuery } from '@tanstack/react-query';

export const useAvailableSummaries = (payload: SummariesPayload) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['available-funds', payload],
    queryFn: () => api.availableFunds.getAvailableFunds(payload),
  });

  return { data, isLoading, error };
};
