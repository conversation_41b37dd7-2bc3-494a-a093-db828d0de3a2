'use client';
import TableContainer from '@/components/common/table-container';
import { Separator } from '@/components/ui/separator';
import { TabsContent, TabsList } from '@/components/ui/tabs';
import { Tabs, TabsTrigger } from '@/components/ui/tabs';
import React from 'react';
import SummariesInfo from './summaries/summaries-info';
import SurveysInfo from './surveys/surveys-info';
import { useQueryState } from 'nuqs';

type ActiveTab = 'summaries' | 'surveys';

const AvailableFundPage = () => {
  const [activeTab, setActiveTab] = useQueryState<ActiveTab>('tab', {
    defaultValue: 'summaries',
    parse: value => {
      return value === 'summaries' || value === 'surveys' ? value : 'summaries';
    },
  });

  return (
    <div className="flex justify-center items-center">
      <TableContainer className="xl:max-w-screen-md max-h-[90dvh] overflow-hidden sm:max-w-xl p-8 gap-8">
        <div className="flex flex-col gap-4 items-start justify-center">
          <h2 className="text-lg font-medium">Available Funds</h2>
          <Tabs value={activeTab} onValueChange={value => setActiveTab(value as ActiveTab)} className="w-full">
            <TabsList className="bg-background px-0">
              <TabsTrigger
                value="summaries"
                className="rounded-none text-muted-foreground bg-background text-sm py-4 flex flex-col items-center bg-none px-0  data-[state=active]:!bg-background data-[state=active]:text-primary data-[state=active]:shadow-none relative"
              >
                <div className="px-2">Summaries</div>
                {activeTab === 'summaries' && <Separator className="w-full h-0.5 bg-primary absolute bottom-0" />}
              </TabsTrigger>
              <TabsTrigger
                value="surveys"
                className="rounded-none text-muted-foreground bg-background text-sm py-4 flex flex-col items-center bg-none px-0 data-[state=active]:!bg-background data-[state=active]:text-primary data-[state=active]:shadow-none relative"
              >
                <div className="px-2">Surveys</div>
                {activeTab === 'surveys' && <Separator className="w-full h-0.5 bg-primary absolute bottom-0" />}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="summaries">
              <SummariesInfo />
            </TabsContent>
            <TabsContent className="flex-1" value="surveys">
              <SurveysInfo />
            </TabsContent>
          </Tabs>
        </div>
      </TableContainer>
    </div>
  );
};

export default AvailableFundPage;
