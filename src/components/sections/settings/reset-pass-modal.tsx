import { Button } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { Form, Label } from '@/components/ui/form';
import { PasswordInput } from '@/components/ui/form/password-input';
import { Cross2Icon } from '@radix-ui/react-icons';
import * as z from 'zod';
import { passwordShape } from '@/backend/shared/validations/password';
import { useChangePassword } from '@/components/sections/settings/hooks/useChangePassword';
import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

type ResetPasswordModalProps = {
  onClose: () => void;
};

const schema = z
  .object({
    currentPassword: z
      .string({
        required_error: 'Current Password is required',
      })
      .min(8, { message: 'Current Password must be at least 8 characters' }),
    confirmPassword: z
      .string({
        required_error: 'Confirm Password is required',
      })
      .min(8, { message: 'Confirm Password must be at least 8 characters' }),
    newPassword: passwordShape,
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type FormData = z.infer<typeof schema>;

const ResetPasswordModal = ({ onClose }: ResetPasswordModalProps) => {
  const { changePassword, isPending, isSuccess } = useChangePassword();

  async function onSubmit(data: FormData) {
    await changePassword(data);
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent
        className={cn(
          'flex flex-col md:!max-w-[80%] max-h-screen md:h-fit p-0 overflow-auto md:top-1/2 md:left-1/2 md:-translate-y-1/2 md:-translate-x-[calc(50%-102.5px)] md:rounded-2xl',
          isSuccess ? 'w-full md:!w-fit' : '!w-[508px]',
        )}
      >
        {!isSuccess ? (
          <>
            <div className="flex flex-1 p-8 w-full">
              <Form schema={schema} onSubmit={onSubmit} className="w-full flex flex-col flex-1">
                <div className="font-semibold text-lg mt-4 mb-2 text-center leading-7">Password Reset</div>
                <div className="font-medium text-sm mt-2 mb-4 text-center leading-7">Enter new password</div>
                <div className="mb-4">
                  <Label htmlFor="currentPassword" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
                    Current Password
                  </Label>

                  <PasswordInput name="currentPassword" id="currentPassword" placeholder="••••••" />
                </div>
                <div className="mb-4">
                  <Label htmlFor="newPassword" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
                    New Password
                  </Label>

                  <PasswordInput name="newPassword" id="newPassword" placeholder="••••••" isShowGroupErrors />
                </div>
                <div className="mb-4">
                  <Label htmlFor="confirmPassword" className="mb-1.5 h-5 font-medium inline-block text-[#344054]">
                    Confirm New Password
                  </Label>

                  <PasswordInput name="confirmPassword" id="confirmPassword" placeholder="••••••" />
                </div>
                <Button
                  type="submit"
                  size="lg"
                  className="w-full font-semibold mt-4"
                  checkDirty
                  disabled={isPending}
                  isLoading={isPending}
                >
                  Reset Password
                </Button>
              </Form>
            </div>
            <DialogClose className="absolute right-6 top-6 rounded-lg" aria-label="Close" onClick={onClose}>
              <Cross2Icon className="w-6 h-6" />
            </DialogClose>
          </>
        ) : (
          <div className="w-full md:w-25.5 flex flex-col justify-center items-center p-6">
            <div className="font-semibold text-lg mt-8 mb-2 text-center leading-7">Password Reset Completed</div>
            <div className="font-medium text-sm mt-2 mb-8 text-center leading-7">
              You can now login with your new password
            </div>
            <Link href="/login" replace className="w-full">
              <Button size="lg" className="w-full font-semibold">
                Back to Login
              </Button>
            </Link>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
export default ResetPasswordModal;
