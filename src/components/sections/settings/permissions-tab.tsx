'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { useCallback, useMemo, useState } from 'react';
import CreateUserModal from './create-permissions-modal';
import { useAdminUsers } from '@/components/sections/settings/hooks/useAdminUsers';
import { AdminRole, UserAdmin } from '@/backend/users/entities/UserAdmin';
import { Skeleton } from '@/components/ui/skeleton';
import { useDeleteUser } from '@/components/sections/settings/hooks/useDeleteUser';
import ModalConfirmDelete from '@/components/common/modal-confirm-delete';
import { useCurrentUser } from '@/hooks/useCurrentUser';

const PermissionsTab = ({ isAdmin }: { isAdmin: boolean }) => {
  const [showModal, setShowModal] = useState(false);
  const { adminUsers, isLoading } = useAdminUsers();
  const { deleteUser, isPending: isDeleting } = useDeleteUser();
  const [selectedUser, setSelectedUser] = useState<UserAdmin>();
  const { user: currentUser } = useCurrentUser();

  // Sort function to put current user first
  const sortUsersWithCurrentFirst = useCallback(
    (users: UserAdmin[]) => {
      if (!currentUser?.id) return users;
      return [...users].sort((a, b) => {
        if (a.id === currentUser.id) return -1;
        if (b.id === currentUser.id) return 1;
        return 0;
      });
    },
    [currentUser],
  );

  const admins = useMemo(
    () => sortUsersWithCurrentFirst(adminUsers ? adminUsers.filter(user => user.role === AdminRole.Admin) : []),
    [adminUsers, sortUsersWithCurrentFirst],
  );
  const editors = useMemo(
    () => sortUsersWithCurrentFirst(adminUsers ? adminUsers.filter(user => user.role === AdminRole.Editor) : []),
    [adminUsers, sortUsersWithCurrentFirst],
  );

  const accountManagers = useMemo(
    () =>
      sortUsersWithCurrentFirst(adminUsers ? adminUsers.filter(user => user.role === AdminRole.AccountManager) : []),
    [adminUsers, sortUsersWithCurrentFirst],
  );

  const handleEdit = (data: UserAdmin) => {
    setSelectedUser(data);
    setShowModal(true);
  };

  return (
    <div className="flex flex-col gap-6 flex-1">
      <div className="flex">
        <div className="space-y-4 w-full">
          <div>
            <h2 className="text-sm font-semibold mb-4">Admins</h2>
            <ul className="space-y-4 pb-4 w-full border-b-[1px] border-[#EAECF0]">
              {admins.length > 0 ? (
                admins.map(user => (
                  <li key={user.id} className="flex items-center w-full justify-between py-1">
                    <span className="text-sm font-medium text-[#344054]">
                      {user.firstName} {user.lastName}
                    </span>
                    {isAdmin && user.id !== currentUser?.id && (
                      <ActionsDropdown handleEdit={() => handleEdit(user)} handleDelete={() => deleteUser(user.id)} />
                    )}
                  </li>
                ))
              ) : isLoading ? (
                <>
                  <Skeleton className="w-full h-10" />
                  <Skeleton className="w-full h-10" />
                </>
              ) : (
                <li className="text-sm italic text-muted-foreground">There are no admins</li>
              )}
            </ul>
          </div>
          <div>
            <ul className="space-y-4 pb-4 w-full border-b-[1px] border-[#EAECF0]">
              <h2 className="text-sm font-semibold">Editors</h2>
              {editors.length > 0 ? (
                editors.map(user => (
                  <li key={user.id} className="flex items-center w-full justify-between py-1">
                    <span className="text-sm font-medium text-[#344054]">
                      {user.firstName} {user.lastName}
                    </span>
                    {isAdmin && user.id !== currentUser?.id && (
                      <ActionsDropdown
                        handleEdit={() => handleEdit(user)}
                        handleDelete={() => deleteUser(user.id)}
                        isDeleting={isDeleting}
                      />
                    )}
                  </li>
                ))
              ) : isLoading ? (
                <>
                  <Skeleton className="w-full h-10" />
                  <Skeleton className="w-full h-10" />
                </>
              ) : (
                <li className="text-sm italic text-muted-foreground">There are no editors</li>
              )}
            </ul>
          </div>
          <div>
            <ul className="space-y-4 pb-4 w-full border-b-[1px] border-[#EAECF0]">
              <h2 className="text-sm font-semibold">Account Managers</h2>
              {accountManagers.length > 0 ? (
                accountManagers.map(user => (
                  <li key={user.id} className="flex items-center w-full justify-between py-1">
                    <span className="text-sm font-medium text-[#344054]">
                      {user.firstName} {user.lastName}
                    </span>
                    {isAdmin && user.id !== currentUser?.id && (
                      <ActionsDropdown
                        handleEdit={() => handleEdit(user)}
                        handleDelete={() => deleteUser(user.id)}
                        isDeleting={isDeleting}
                      />
                    )}
                  </li>
                ))
              ) : isLoading ? (
                <>
                  <Skeleton className="w-full h-10" />
                  <Skeleton className="w-full h-10" />
                </>
              ) : (
                <li className="text-sm italic text-muted-foreground">There are no account managers</li>
              )}
            </ul>
          </div>
        </div>
      </div>
      {isAdmin && (
        <div className="mt-[auto]">
          <div className="flex justify-end gap-4">
            <Button type="button" className="font-semibold h-10" onClick={() => setShowModal(true)}>
              Add Dashboard User
            </Button>
          </div>
        </div>
      )}
      {showModal && (
        <CreateUserModal
          onClose={() => {
            setShowModal(false);
            setSelectedUser(undefined);
          }}
          defaultValues={selectedUser}
        />
      )}
    </div>
  );
};

export default PermissionsTab;

export interface ActionsDropdownProps {
  handleEdit(): void;
  handleDelete(): void;
  isDeleting?: boolean;
}

const ActionsDropdown = ({ handleEdit, handleDelete, isDeleting }: ActionsDropdownProps) => {
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild className="data-[state=open]:text-primary-brand">
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <DotsHorizontalIcon className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={handleEdit}
            className="text-foreground font-medium cursor-pointer rounded-none text-base py-2.5 px-3.5"
          >
            Edit Role
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setIsDeleteOpen(true)}
            className="text-error focus:text-error font-medium rounded-none text-base cursor-pointer py-2.5 px-3.5 pr-[55px]"
          >
            Remove User
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ModalConfirmDelete
        openDialog={isDeleteOpen}
        setOpenDialog={setIsDeleteOpen}
        onConfirm={handleDelete}
        isConfirming={isDeleting}
        title="Remove User"
        subtitle="Are you sure you want to remove this user? This action cannot be reversed."
      />
    </>
  );
};
