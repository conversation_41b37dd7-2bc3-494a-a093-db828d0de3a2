import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UpdateAdminPayload } from '@/backend/users/validations/add-update-admin';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useUpdateUser() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isSuccess } = useMutation({
    mutationFn: async (data: UpdateAdminPayload & { id: number }) => api.userAdmin.updateUserAdmin(data.id, data),
    onSuccess: async () => {
      toast.success('Successfully updated your profile');
      await queryClient.invalidateQueries({ queryKey: ['adminUsers'] });
      await queryClient.invalidateQueries({ queryKey: ['currentUser'] });
    },
    onError: error => {
      toast.error(error.message);
    },
  });

  return {
    updateUser: mutateAsync,
    isPending,
    isSuccess,
  };
}
