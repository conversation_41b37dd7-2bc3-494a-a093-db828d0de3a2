import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { AddAdminPayload } from '@/backend/users/validations/add-update-admin';
import toast from 'react-hot-toast';

export function useAddUser() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isSuccess } = useMutation({
    mutationFn: (data: AddAdminPayload) => api.userAdmin.addUserAdmin(data),
    onSuccess: async () => {
      toast.success('User added successfully');
      await queryClient.invalidateQueries({ queryKey: ['adminUsers'] });
    },
    onError: error => {
      toast.error(error.message);
    },
  });

  return {
    addUser: mutateAsync,
    isLoading: isPending,
    isSuccess,
  };
}
