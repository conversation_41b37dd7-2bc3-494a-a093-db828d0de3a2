import { api } from '@/lib/http';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UpdateAdminPayload } from '@/backend/users/validations/add-update-admin';
import toast from 'react-hot-toast';

export function useUpdateSelf() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isSuccess } = useMutation({
    mutationFn: async (data: UpdateAdminPayload) => api.userAdmin.updateSelf(data),
    onSuccess: async () => {
      toast.success('Successfully updated your profile');
      await queryClient.invalidateQueries({ queryKey: ['currentUser'] });
    },
  });

  return {
    updateSelf: mutateAsync,
    isPending,
    isSuccess,
  };
}
