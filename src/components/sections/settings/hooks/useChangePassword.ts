import { useMutation } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';
import { AdminChangePasswordPayload } from '@/backend/auth/validations/admin-change-password';

export function useChangePassword() {
  const { mutateAsync, isPending, isSuccess } = useMutation({
    mutationFn: async (data: AdminChangePasswordPayload) => api.userAdmin.changePassword(data),
    onError: error => {
      toast.error(error.message);
    },
  });

  return {
    changePassword: mutateAsync,
    isPending,
    isSuccess,
  };
}
