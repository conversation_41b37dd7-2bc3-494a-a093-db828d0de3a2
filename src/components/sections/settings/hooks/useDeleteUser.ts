import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useDeleteUser() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: number) => api.userAdmin.deleteUserAdmin(id),
    onSuccess: async () => {
      toast.success('User deleted successfully');
      await queryClient.invalidateQueries({ queryKey: ['adminUsers'] });
    },
    onError: error => {
      toast.error(error.message);
    },
  });

  return {
    deleteUser: mutateAsync,
    isPending,
  };
}
