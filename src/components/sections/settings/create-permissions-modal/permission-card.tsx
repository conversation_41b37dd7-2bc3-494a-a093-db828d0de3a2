import React, { useCallback } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { Input } from '@/components/ui/form';
import { cn } from '@/lib/utils';

type PermissionCardProps = {
  role: AdminRole;
  name: string;
  defaultRole?: AdminRole;
};

const PermissionCard: React.FC<PermissionCardProps> = ({ role, defaultRole, name }) => {
  const { setValue, control } = useFormContext();
  const { field } = useController({ control, name });
  const checked = field.value === role;

  const getRoleName = useCallback((role: AdminRole) => {
    switch (role) {
      case AdminRole.AccountManager:
        return 'Account Manager';
      case AdminRole.Editor:
        return 'Editor';
      case AdminRole.Admin:
        return 'Admin';
      default:
        return role;
    }
  }, []);

  return (
    <div className="flex items-center justify-center w-full h-full">
      <label className="cursor-pointer w-full h-full">
        <Input
          type="radio"
          className="peer sr-only"
          defaultChecked={checked}
          onChange={() => {
            setValue(name, role, { shouldDirty: defaultRole !== role });
          }}
          name={name}
        />
        <div
          className={cn(
            'w-full rounded-md bg-white p-5 ring-2 ring-transparent transition-all hover:shadow text-[#667085] border h-full md:max-h-[106px]',
            checked && 'text-[#7F56D9] bg-[#F9F5FF] border-[#D6BBFB]',
          )}
        >
          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between">
              <p className={cn('text-sm font-medium uppercase text-[#344054]', checked && 'text-primary-brand')}>
                {getRoleName(role)}
              </p>
              <div>
                {checked ? (
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" fill="#6149C4" />
                    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" stroke="#6149C4" />
                    <path
                      d="M11.3332 5.5L6.74984 10.0833L4.6665 8"
                      stroke="white"
                      strokeWidth="1.66667"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                ) : (
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" fill="white" />
                    <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" stroke="#D0D5DD" />
                  </svg>
                )}
              </div>
            </div>
            <div className="flex flex-col">
              <div className="text-sm font-normal">
                {role === AdminRole.AccountManager ? (
                  <p>
                    Able to create/manage their own public surveys but unable to view
                    <br /> the rest of the admin portal.
                  </p>
                ) : role === AdminRole.Editor ? (
                  <p>
                    Unable to:
                    <br /> Add admin panel users; Update wallet balances; Edit/delete live surveys.
                  </p>
                ) : (
                  <p>Full access to all admin panel features</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </label>
    </div>
  );
};

export default PermissionCard;
