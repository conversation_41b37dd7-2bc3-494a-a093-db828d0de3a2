'use client';

import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog';
import { Cross2Icon } from '@radix-ui/react-icons';
import React from 'react';
import UserInfoForm from './user-info-form';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';

type CreateUserModalProps = {
  onClose: () => void;
  defaultValues?: UserAdmin;
};

const CreateUserModal: React.FC<CreateUserModalProps> = ({ onClose, defaultValues }: CreateUserModalProps) => {
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="flex lg:min-w-[60%] lg:!w-[728px] 2xl:min-w-[auto] p-0 overflow-y-auto">
        <div className="flex flex-col lg:flex-row">
          <div className="flex flex-col p-8">
            <div className="font-semibold text-2xl mb-10">{defaultValues ? 'Update' : 'New'} Dashboard User</div>
            <UserInfoForm onClose={onClose} onCancel={onClose} defaultValues={defaultValues} />
          </div>
        </div>
        <DialogClose className="absolute right-6 top-6 rounded-lg" aria-label="Close" onClick={onClose}>
          <Cross2Icon className="w-6 h-6" />
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default CreateUserModal;
