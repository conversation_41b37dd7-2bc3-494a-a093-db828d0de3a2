'use client';

import { Button } from '@/components/ui/button';
import { Form, Input, Label } from '@/components/ui/form';
import { Mail } from 'lucide-react';
import React, { useEffect } from 'react';
import * as z from 'zod';
import PermissionCard from './permission-card';
import { AdminRole, UserAdmin } from '@/backend/users/entities/UserAdmin';
import { useAddUser } from '@/components/sections/settings/hooks/useAddUser';
import { useUpdateUser } from '@/components/sections/settings/hooks/useUpdateUser';

const validateSchema = z.object({
  firstName: z
    .string({
      required_error: 'First Name is required',
    })
    .min(1, 'First Name is required'),
  lastName: z
    .string({
      required_error: 'Last Name is required',
    })
    .min(1, 'First Name is required'),
  email: z
    .string({
      required_error: 'Email is required',
    })
    .email('Please enter a valid email'),
  role: z.nativeEnum(AdminRole),
});

type FormDataRefType = z.infer<typeof validateSchema>;

type UserInfoFormProps = {
  onCancel: () => void;
  defaultValues?: UserAdmin;
  onClose: () => void;
};

const itemClass = 'basis-full md:basis-[calc(50%-12px)] flex-shrink';
const labelClass = 'mb-1.5 h-5 font-medium inline-block text-[#344054]';

const UserInfoForm: React.FC<UserInfoFormProps> = ({ onCancel, onClose, defaultValues }: UserInfoFormProps) => {
  const { addUser, isLoading, isSuccess } = useAddUser();
  const { updateUser, isPending, isSuccess: isUpdateSuccess } = useUpdateUser();

  useEffect(() => {
    if (isUpdateSuccess || isSuccess) {
      onClose();
    }
  }, [onClose, isUpdateSuccess, isSuccess]);

  const onSubmit = async (data: FormDataRefType): Promise<void> => {
    if (defaultValues) {
      await updateUser({ id: defaultValues.id, ...data });
    } else {
      await addUser(data);
    }
  };

  return (
    <Form
      schema={validateSchema}
      defaultValues={{
        role: defaultValues?.role ?? AdminRole.Editor,
        firstName: defaultValues?.firstName ?? undefined,
        lastName: defaultValues?.lastName ?? undefined,
        email: defaultValues?.email ?? undefined,
      }}
      onSubmit={onSubmit}
      mode="onSubmit"
      className="flex flex-col gap-6 flex-1"
    >
      <div className="flex flex-wrap gap-6 mt-4">
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="firstName" className={labelClass}>
              First Name
            </Label>
          </div>
          <Input type="firstName" name="firstName" id="firstName" placeholder="John" />
        </div>
        <div className={itemClass}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="lastName" className={labelClass}>
              Last Name
            </Label>
          </div>
          <Input type="lastName" name="lastName" id="lastName" placeholder="Smith" />
        </div>
        <div className={itemClass + ' !basis-full'}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="email" className={labelClass}>
              Email Address
            </Label>
          </div>
          <Input
            type="email"
            name="email"
            id="email"
            StartIcon={Mail}
            placeholder="<EMAIL>"
            disabled={Boolean(defaultValues)}
          />
        </div>
        <div className={itemClass + ' !basis-full'}>
          <div className="h-5 max-h-5 mb-1.5">
            <Label htmlFor="email" className={labelClass}>
              Permissions
            </Label>
          </div>
          <div className="grid grid-rows-2 gap-3">
            <PermissionCard role={AdminRole.Editor} name="role" />
            <PermissionCard role={AdminRole.AccountManager} name="role" />
            <PermissionCard role={AdminRole.Admin} name="role" />
          </div>
        </div>
      </div>
      <div className="mt-[auto]">
        <div className="flex justify-end gap-6">
          <Button variant="secondary" className="font-semibold" onClick={onCancel} disabled={isLoading || isPending}>
            Cancel
          </Button>
          <Button
            type="submit"
            className="font-semibold"
            checkDirty
            disabled={isLoading || isPending}
            isLoading={isLoading || isPending}
          >
            {defaultValues ? 'Update' : 'Create'} User
          </Button>
        </div>
      </div>
    </Form>
  );
};

export default UserInfoForm;
