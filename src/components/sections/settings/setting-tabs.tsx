'use client';
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import ProfileInfoForm from '@/components/sections/settings/profile-info-tab';
import PermissionsTab from '@/components/sections/settings/permissions-tab';
import { useCurrentUser } from '@/hooks/useCurrentUser';

const commonTriggerStyles =
  'py-4 px-[2px] flex rounded-none font-medium bg-transparent text-[#667085] text-sm !shadow-none data-[state=active]:text-[#6149C4] data-[state=active]:border-[#6149C4] data-[state=active]:border-b-[2px]';

const SettingTabs = () => {
  const { user, isLoadingCurrentUser } = useCurrentUser();
  return (
    <Tabs defaultValue="profile_info" className="w-full">
      <TabsList className="bg-transparent p-0 space-x-4  mb-8">
        <TabsTrigger className={commonTriggerStyles} value="profile_info">
          Profile Info
        </TabsTrigger>
        {user?.role === 'Admin' && (
          <TabsTrigger className={commonTriggerStyles} value="permissions">
            Permissions
          </TabsTrigger>
        )}
      </TabsList>
      <TabsContent value="profile_info">
        <ProfileInfoForm user={user} isLoading={isLoadingCurrentUser} />
      </TabsContent>
      {user?.role === 'Admin' && (
        <TabsContent value="permissions">
          <PermissionsTab isAdmin={user?.role === 'Admin'} />
        </TabsContent>
      )}
    </Tabs>
  );
};
export default SettingTabs;
