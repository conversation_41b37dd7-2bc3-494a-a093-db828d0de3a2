'use client';
import { useEffect, useRef, useState } from 'react';
import * as z from 'zod';
import { Mail } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

import { Button, ButtonReset } from '@/components/ui/button';
import { Form, Input, Label } from '@/components/ui/form';
import ResetPasswordModal from './reset-pass-modal';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { useUpdateSelf } from '@/components/sections/settings/hooks/useUpdateSelf';
import { FormActions } from '@/components/ui/form/form';
import { Skeleton } from '@/components/ui/skeleton';

const validateSchema = z.object({
  firstName: z
    .string({
      required_error: 'First Name is required',
    })
    .min(1, 'First Name is required'),
  lastName: z
    .string({
      required_error: 'Last Name is required',
    })
    .min(1, 'Last Name is required'),
  email: z.string().email('Please enter a valid email').optional().nullable(),
  password: z.string().optional().nullable(),
});

type FormData = z.infer<typeof validateSchema>;

const itemClass = 'basis-full md:basis-[calc(50%-12px)] flex-shrink';
const labelClass = 'mb-1.5 h-5 font-medium inline-block text-[#344054]';

const ProfileInfoTab = ({ user, isLoading }: { user?: UserAdmin; isLoading?: boolean }) => {
  const [openModalResetPassword, setOpenModalResetPassword] = useState(false);
  const { updateSelf, isPending, isSuccess } = useUpdateSelf();
  const ref = useRef<FormActions<typeof validateSchema>>(null);

  useEffect(() => {
    if (isSuccess) {
      ref.current?.formHandler.reset();
    }
  }, [isSuccess]);

  const onSubmit = async (data: FormData, formHandler?: UseFormReturn<z.TypeOf<typeof validateSchema>>) => {
    await updateSelf(data);
    formHandler?.reset(
      {},
      {
        keepValues: true,
      },
    );
  };

  return (
    <>
      <Form
        schema={validateSchema}
        onSubmit={onSubmit}
        key={`${user?.id}-${isLoading}`}
        mode="onChange"
        className="flex flex-col gap-6 flex-1"
        defaultValues={user as unknown as FormData}
      >
        <div className="flex flex-wrap gap-6 z-10">
          <div className={itemClass}>
            <div className="h-5 max-h-5 mb-1.5">
              <Label htmlFor="firstName" className={labelClass}>
                First Name
              </Label>
            </div>
            {isLoading ? (
              <Skeleton className="h-11" />
            ) : (
              <Input type="text" name="firstName" id="firstName" placeholder="Enter first name" />
            )}
          </div>
          <div className={itemClass}>
            <div className="h-5 max-h-5 mb-1.5">
              <Label htmlFor="lastName" className={labelClass}>
                Last Name
              </Label>
            </div>
            {isLoading ? (
              <Skeleton className="h-11" />
            ) : (
              <Input type="text" name="lastName" id="lastName" placeholder="Enter last name" />
            )}
          </div>
          <div className={itemClass + ' !basis-full'}>
            <div className="h-5 max-h-5 mb-1.5">
              <Label htmlFor="email" className={labelClass}>
                Email Address
              </Label>
            </div>
            <Input type="email" name="email" id="email" StartIcon={Mail} placeholder="<EMAIL>" disabled />
          </div>
          <div className={itemClass + ' !basis-full'}>
            <div className="h-5 max-h-5 mb-1.5">
              <Label htmlFor="password" className={labelClass}>
                Password
              </Label>
            </div>
            <Input name="password" id="password" placeholder="••••••" disabled />
          </div>
        </div>
        <div className={itemClass + ' !basis-full'}>
          <Button
            variant="outline"
            type="button"
            className="font-semibold h-10"
            onClick={() => setOpenModalResetPassword(true)}
          >
            Reset Password
          </Button>
        </div>
        <div className="mt-[auto]">
          <div className="flex justify-end gap-4">
            <ButtonReset variant="outline" type="button" className="font-semibold h-10" checkDirty disabled={isPending}>
              Discard
            </ButtonReset>
            <Button type="submit" className="font-semibold h-10" checkDirty isLoading={isPending}>
              Save Changes
            </Button>
          </div>
        </div>
      </Form>
      {openModalResetPassword && <ResetPasswordModal onClose={() => setOpenModalResetPassword(false)} />}
    </>
  );
};

export default ProfileInfoTab;
