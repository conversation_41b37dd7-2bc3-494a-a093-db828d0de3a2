'use client';
import { ColumnDef } from '@tanstack/table-core';
import React from 'react';

import { formatDate } from '@/utils/date-format';
import { Header } from '@/components/ui/table/header';
import { Company } from '@/backend/companies/entities/Company';
import SkeletonCell from '@/components/ui/table/skeleton-cell';
import { ActionsCell, DeleteModalProps } from '@/components/ui/table/action-cell';
import { cn } from '@/lib/utils';

type ColumnsProps = Omit<DeleteModalProps, 'title' | 'subtitle'> & {
  isLoading?: boolean;
};

function generateColumns({ onDelete, isDeleting, isLoading }: ColumnsProps) {
  const columns: ColumnDef<Company>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => <Header column={column}>Name</Header>,
      cell: ({ row }) => {
        const name = row.original.name;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className="text-foreground font-medium whitespace-nowrap">{name}</span>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'surveysInProgress',
      header: ({ column }) => <Header column={column}>Surveys In Progress</Header>,
      cell: ({ row }) => {
        const surveysInProgress = row.original.surveysInProgress;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span className="text-muted-foreground">{surveysInProgress}</span>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'surveysCompleted',
      header: ({ column }) => <Header column={column}>Surveys Completed</Header>,
      cell: ({ row }) => {
        const surveysCompleted = row.original.surveysCompleted;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span className="text-muted-foreground">{surveysCompleted}</span>
          </SkeletonCell>
        );
      },
    },
    {
      accessorKey: 'lastSurveyDate',
      header: ({ column }) => <Header column={column}>Last Survey Date</Header>,
      cell: ({ row }) => {
        const lastSurveyDate = row.original.lastSurveyDate;
        return (
          <SkeletonCell isLoading={isLoading}>
            <span className="text-muted-foreground whitespace-nowrap">
              {lastSurveyDate ? formatDate(lastSurveyDate) : ''}
            </span>
          </SkeletonCell>
        );
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      cell: ({ row }) => (
        <SkeletonCell isLoading={isLoading} className={cn(isLoading ? 'w-5 overflow-hidden' : '')}>
          <ActionsCell
            row={row}
            urlDetail={`/companies/${row.original.id}`}
            deleteModalProps={{
              onDelete,
              isDeleting,
              title: 'Delete Company',
              subtitle: 'Are you sure you want to delete this company? This action cannot be reversed.',
            }}
          />
        </SkeletonCell>
      ),
    },
  ];

  return columns;
}

export { generateColumns };
