'use client';
import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import CreateCompanyModal from '@/components/sections/companies/create-company-modal';

const CompaniesHeader = ({ companiesCount }: { companiesCount?: number }) => {
  const [open, setOpen] = useState(false);
  return (
    <div className="flex flex-row justify-between px-4 lg:px-6 mb-[5px]">
      <div className="font-medium flex items-center">
        <div>Companies</div>
        <Badge className="text-primary bg-[#F9F5FF] ml-2 h-fit px-2 py-0.5 text-xs">Count: {companiesCount}</Badge>
      </div>
      <div className="flex justify-end flex-1 items-center lg:my-0 ml-0 lg:ml-6">
        <div className="flex">
          <Button className="h-10 px-2.5 md:px-4 border-none bg-primary-brand" onClick={() => setOpen(true)}>
            <Plus />
            <div className="ml-2 hidden md:block">Add Company</div>
          </Button>
        </div>
      </div>
      {open && <CreateCompanyModal onClose={() => setOpen(false)} />}
    </div>
  );
};
export default CompaniesHeader;
