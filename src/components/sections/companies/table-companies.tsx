'use client';
import React, { useMemo } from 'react';
import { generateColumns } from './columns';
import { Table } from '@/components/ui/table';
import CustomPagination from '@/components/common/custom-pagination';
import { useCompanies } from '@/components/sections/companies/hooks/useCompanies';
import { useDeleteCompany } from '@/components/sections/companies/hooks/useDeleteCompany';
import CompaniesHeader from '@/components/sections/companies/companies-header';
import { cn } from '@/lib/utils';

const TableCompanies = () => {
  const { companies, isLoadingCompanies, isError } = useCompanies();
  const { deleteCompany, isDeleting } = useDeleteCompany();
  const columns = useMemo(
    () => generateColumns({ onDelete: deleteCompany, isLoading: isLoadingCompanies, isDeleting }),
    [isLoadingCompanies, deleteCompany, isDeleting],
  );

  return (
    <>
      <CompaniesHeader companiesCount={companies?.data?.total ?? 0} />
      <Table
        columns={columns}
        data={companies?.data?.data ? companies?.data?.data : isLoadingCompanies ? Array(10).fill({}) : []}
        url="/companies"
        containerClassName={cn(!isLoadingCompanies && !isError && 'peer is-done')}
      />
      <CustomPagination totalPages={companies?.data?.totalPages ?? 10} />
    </>
  );
};
export default TableCompanies;
