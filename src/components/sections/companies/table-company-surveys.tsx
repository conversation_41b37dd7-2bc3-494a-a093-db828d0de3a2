'use client';
import React, { useMemo } from 'react';
import TableDataContainer from '@/components/common/table-container';
import { Table } from '@/components/ui/table';
import { useCompanySurveys } from '@/components/sections/companies/hooks/useCompanySurveys';
import { generateColumns } from '@/app/(protected)/users/[id]/columns';
import { formatDate } from '@/utils/date-format';
import { differenceInDays } from 'date-fns';

type Props = {
  companyId: number;
};

const TableCompanySurveys = ({ companyId }: Props) => {
  const { companySurveys, isLoadingCompanySurveys } = useCompanySurveys(companyId);

  const columns = useMemo(() => generateColumns({ isLoading: isLoadingCompanySurveys }), [isLoadingCompanySurveys]);
  return (
    <div className="mt-10">
      <div className="font-bold mr-6 mb-4 whitespace-nowrap px-2 sm:px-8">Company’s Surveys</div>
      <TableDataContainer className="pt-1" showPagination={false}>
        <Table
          columns={columns}
          data={
            companySurveys?.data
              ? companySurveys.data.sort((a, b) => differenceInDays(formatDate(a.expiryDate), formatDate(b.expiryDate)))
              : isLoadingCompanySurveys
                ? Array(10).fill({})
                : []
          }
          url="/surveys"
        />
      </TableDataContainer>
    </div>
  );
};
export default TableCompanySurveys;
