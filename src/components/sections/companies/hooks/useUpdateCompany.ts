import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';
import { AddUpdateCompanyPayload } from '@/backend/companies/validations/add-update-company';

export function useUpdateCompany(companyId: number) {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (data: AddUpdateCompanyPayload) => api.companies.updateCompany(companyId, data),
    onSuccess: async () => {
      toast.success('Company updated successfully');
      await queryClient.invalidateQueries({ queryKey: ['company', companyId] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    updateCompany: mutateAsync,
    isUpdating: isPending,
  };
}
