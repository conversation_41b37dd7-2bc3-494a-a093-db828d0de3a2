import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useDeleteCompany() {
  const queryClient = useQueryClient();
  const { mutateAsync, isPending, isError, error } = useMutation({
    mutationFn: (id: number) => api.companies.deleteCompany(id),
    onSuccess: async () => {
      toast.success('Company deleted successfully');
      await queryClient.invalidateQueries({ queryKey: ['companies'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    deleteCompany: mutateAsync,
    isDeleting: isPending,
    isDeleteError: isError,
    deleteError: error,
  };
}
