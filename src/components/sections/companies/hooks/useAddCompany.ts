import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';
import { AddUpdateCompanyPayload } from '@/backend/companies/validations/add-update-company';

export function useAddCompany() {
  const queryClient = useQueryClient();

  const { mutateAsync: addCompany, isPending: isAddingCompany } = useMutation({
    mutationFn: (data: AddUpdateCompanyPayload) => api.companies.addCompany(data),
    onSuccess: async () => {
      toast.success('Company created successfully');
      await queryClient.invalidateQueries({ queryKey: ['companies'] });
    },
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    addCompany,
    isAddingCompany,
  };
}
