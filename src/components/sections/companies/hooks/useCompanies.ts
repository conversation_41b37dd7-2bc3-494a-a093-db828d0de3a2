import { usePagination } from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useSortQuery } from '@/hooks/useSortQuery';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { Company } from '@/backend/companies/entities/Company';
import { useEffect } from 'react';

export function useCompanies(isPagination = true) {
  const { page, pageSize, previousPage } = usePagination();
  const { search } = useSearchQuery();
  const { sortBy, sortOrder } = useSortQuery();

  const queryKey = isPagination ? ['companies', page, pageSize, search, sortBy, sortOrder] : ['companies'];

  const {
    data: companies,
    isLoading,
    isError,
  } = useQuery({
    queryKey: queryKey,
    queryFn: async () => {
      if (isPagination) {
        return api.companies.getCompanies({
          page,
          pageSize,
          search,
          sortBy: sortBy as keyof Company,
          sortOrder: sortOrder === 'ASC' ? 'ASC' : 'DESC',
        });
      } else {
        return api.companies.getCompanies();
      }
    },
    placeholderData: previousData => previousData,
  });

  useEffect(() => {
    if (!companies?.data) return;
    if (isPagination && companies.data.totalPages < page && page > 1) {
      previousPage();
    }
  }, [isPagination, page, previousPage, companies]);

  return { companies, isLoadingCompanies: isLoading, isError };
}
