import React from 'react';
import * as z from 'zod';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Form, Input, Label } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { UseFormReturn } from 'react-hook-form';
import { useCheckCompanyName } from '@/components/sections/companies/hooks/useCheckCompanyName';
import { useAddCompany } from '@/components/sections/companies/hooks/useAddCompany';
import toast from 'react-hot-toast';
import { specificName } from '@/backend/shared/validations/name';

type CreateCompanyModalProps = {
  onClose: () => void;
};

const schema = z.object({
  name: specificName('Company'),
});

type FormData = z.infer<typeof schema>;

const CreateCompanyModal = ({ onClose }: CreateCompanyModalProps) => {
  const { checkCompanyName, isChecking } = useCheckCompanyName();
  const { addCompany, isAddingCompany } = useAddCompany();

  async function onSubmit(data: FormData, formHandler?: UseFormReturn<FormData>) {
    const resultCheckName = await checkCompanyName(data.name);
    if (resultCheckName.data?.isNameExist) {
      formHandler && formHandler.setError('name', { message: 'This company already exists' }, { shouldFocus: true });
      return;
    }

    if (resultCheckName.errors) {
      toast.error(resultCheckName.errors[0].message);
      return;
    }

    await addCompany(data);
    onClose();
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="flex flex-col md:!max-w-[80%] max-h-screen md:!w-fit md:h-fit p-0 overflow-auto md:top-1/2 md:left-1/2 md:-translate-y-1/2 md:-translate-x-[calc(50%-102.5px)] md:rounded-2xl">
        <div className="flex flex-1 p-8 w-full">
          <Form schema={schema} onSubmit={onSubmit} className="w-full flex flex-col flex-1">
            <h1 className="font-bold text-2xl mb-9">New Company</h1>
            <div className="flex flex-wrap gap-6 mb-10">
              <div className="w-[320px]">
                <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
                  Company Name
                </Label>
                <Input name="name" placeholder="Type Company Name" />
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                variant="secondary"
                className="flex-1 font-semibold border border-input bg-white rounded-lg"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1font-semibold rounded-lg"
                checkDirty={true}
                isLoading={isChecking || isAddingCompany}
              >
                Create Company
              </Button>
            </div>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default CreateCompanyModal;
