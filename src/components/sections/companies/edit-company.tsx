'use client';
import React, { useState } from 'react';
import { Form, Input, Label } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { z } from 'zod';
import { useCompany } from '@/components/sections/companies/hooks/useCompany';
import { formatDate } from '@/utils/date-format';
import { useUpdateCompany } from '@/components/sections/companies/hooks/useUpdateCompany';
import ModalConfirmDelete from '@/components/common/modal-confirm-delete';
import { useDeleteCompany } from '@/components/sections/companies/hooks/useDeleteCompany';
import { specificName } from '@/backend/shared/validations/name';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';

const schema = z.object({
  name: specificName('Company'),
});

type FormData = z.infer<typeof schema>;

type EditCompanyFormProps = {
  companyId: number;
};

const EditCompanyForm = ({ companyId }: EditCompanyFormProps) => {
  const router = useRouter();
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const { company, isLoadingCompany } = useCompany(companyId);
  const { updateCompany } = useUpdateCompany(companyId);
  const { deleteCompany, isDeleting } = useDeleteCompany();

  async function onSubmit(data: FormData) {
    const res = await updateCompany(data);
    if (!res.errors) setIsEditMode(false);
  }

  return (
    <Form
      schema={schema}
      onSubmit={onSubmit}
      key={`company-form-${isLoadingCompany}`}
      mode="onSubmit"
      className="w-full"
      defaultValues={company?.data}
    >
      <div className="flex w-full justify-between">
        {isLoadingCompany ? (
          <div className="w-full">
            <Skeleton className="w-1/4 h-7 mb-2" />
            <Skeleton className="w-1/2 h-5" />
          </div>
        ) : (
          <div className="flex flex-col">
            <div className="font-medium text-lg mb-2">{company?.data?.name ?? 'N/A'}</div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 lg:gap-6">
              <div className="text-sm">
                Surveys In Progress: <span className="font-medium">{company?.data?.surveysInProgress ?? 'N/A'}</span>
              </div>
              <div className="text-sm">
                Surveys Completed: <span className="font-medium">{company?.data?.surveysCompleted ?? 'N/A'}</span>
              </div>
              <div className="text-sm">
                Last Survey Date:{' '}
                <span className="font-medium">
                  {company?.data?.lastSurveyDate ? formatDate(company?.data?.lastSurveyDate) : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row items-center w-fit space-y-2 lg:space-y-0">
          <Button
            type="button"
            className="text-[#B42318] bg-inherit font-semibold whitespace-nowrap text-sm sm:text-base"
            disabled={isEditMode}
            onClick={e => {
              e.preventDefault();
              setOpenDialog(true);
            }}
          >
            Delete Company
          </Button>

          <ModalConfirmDelete
            openDialog={openDialog}
            setOpenDialog={setOpenDialog}
            onConfirm={async () => {
              await deleteCompany(companyId);
              router.replace('/companies');
            }}
            isConfirming={isDeleting}
            title="Delete Company"
            subtitle="Are you sure you want to delete this company? This action cannot be reversed."
          />

          {isEditMode ? (
            <Button type="submit" className="rounded-lg w-full lg:w-fit text-sm sm:text-base">
              Save Changes
            </Button>
          ) : (
            <Button
              type="button"
              className="rounded-lg w-full lg:w-fit text-sm sm:text-base"
              onClick={e => {
                e.preventDefault();
                setIsEditMode(true);
              }}
            >
              Edit Company
            </Button>
          )}
        </div>
      </div>

      <div className="w-full flex flex-col flex-1 mt-10">
        <div className="font-bold text-base mr-6">Company Info</div>
        <div className="flex flex-wrap gap-6 my-4">
          <div className="max-w-20 w-full">
            <Label htmlFor="name" className="mb-1.5 h-5 font-medium inline-block">
              Company Name
            </Label>
            {isLoadingCompany ? (
              <Skeleton className="h-11" />
            ) : (
              <Input name="name" placeholder="Type company name" disabled={!isEditMode} />
            )}
          </div>
        </div>
      </div>
    </Form>
  );
};
export default EditCompanyForm;
