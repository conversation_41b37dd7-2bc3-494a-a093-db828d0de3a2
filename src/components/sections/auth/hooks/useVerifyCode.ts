import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';

export function useVerifyCode(code: string) {
  const { data, isPending, isSuccess, isError } = useQuery({
    queryKey: ['verify-code', code],
    queryFn: async () => {
      return api.auth.verifyAdminPasswordResetCode(code);
    },
  });

  return {
    verifiedStatus: data,
    isVerifying: isPending,
    isVerified: isSuccess,
    isFailed: isError,
  };
}
