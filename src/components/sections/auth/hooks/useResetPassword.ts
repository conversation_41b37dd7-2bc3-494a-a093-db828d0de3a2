import { useMutation } from '@tanstack/react-query';
import { api } from '@/lib/http';
import { AdminResetPasswordPayload } from '@/backend/auth/validations/admin-reset-password';
import toast from 'react-hot-toast';

export function useResetPassword() {
  const { mutateAsync, isPending, isSuccess } = useMutation({
    mutationFn: (data: AdminResetPasswordPayload) => api.auth.resetPassword(data),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    resetPassword: mutateAsync,
    isResetting: isPending,
    isReset: isSuccess,
  };
}
