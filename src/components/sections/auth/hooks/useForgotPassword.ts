import { useMutation } from '@tanstack/react-query';
import { api } from '@/lib/http';
import toast from 'react-hot-toast';

export function useForgotPassword() {
  const { mutateAsync, isPending, isSuccess } = useMutation({
    mutationFn: (email: string) => api.auth.forgotPassword({ email }),
    onError: error => {
      const err = error as Error;
      toast.error(err.message);
    },
  });

  return {
    forgotPassword: mutateAsync,
    isSending: isPending,
    isSent: isSuccess,
  };
}
