'use client';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFilteredRowModel,
  SortingState,
  VisibilityState,
  Table as TableType,
  RowSelectionState,
  getSortedRowModel,
} from '@tanstack/table-core';
import { flexRender, useReactTable } from '@tanstack/react-table';
import { usePagination } from '@/hooks/usePagination';
import { useSortQuery } from '@/hooks/useSortQuery';
import { useRouter } from 'next/navigation';
import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { Loader2 } from 'lucide-react';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';

// Define custom column meta type
declare module '@tanstack/table-core' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData, TValue> {
    flexGrow?: number;
  }
}

export type TableInstance<TData> = {
  table: TableType<TData>;
  selectedRows: TData[];
};

export const defaultColumnSizing = {
  size: 150,
  minSize: 20,
  maxSize: Number.MAX_SAFE_INTEGER,
};

interface TableProps<TData, TValue> extends React.HTMLAttributes<HTMLTableElement> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  ref?: React.RefObject<HTMLTableElement>;
  containerClassName?: string;
  tableCellClassName?: string;
  url?: string;
  selectRowEdit?: (row: PublicReferralCode) => void;
  hasNextPage?: boolean;
  fetchNextPage?: () => void;
  isFetchingNextPage?: boolean;
  rowSelection?: RowSelectionState;
  onRowSelectionChange?: (rowSelection: RowSelectionState) => void;
  getRowId?: (row: TData) => string;
  stickyHeader?: boolean;
  useLocalSort?: boolean;
  onSortingChange?: (sorting: SortingState) => void;
  initialSorting?: SortingState;
}

const Table = <TData, TValue>({
  className,
  containerClassName,
  tableCellClassName,
  ref,
  data,
  columns,
  url,
  selectRowEdit,
  hasNextPage,
  fetchNextPage,
  rowSelection = {},
  onRowSelectionChange,
  isFetchingNextPage,
  getRowId,
  stickyHeader = false,
  useLocalSort = false,
  onSortingChange,
  initialSorting,
  ...props
}: TableProps<TData, TValue>) => {
  const { page, pageSize } = usePagination();
  const { sortBy, sortOrder, changeSort } = useSortQuery();
  const [sorting, setSorting] = useState<SortingState>(initialSorting || []);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const router = useRouter();

  // Update internal sorting state when initialSorting changes
  useEffect(() => {
    if (initialSorting && useLocalSort) {
      setSorting(initialSorting);
    }
  }, [initialSorting, useLocalSort]);

  const handleSortingChange = React.useCallback(
    (updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
      setSorting(updaterOrValue);

      if (onSortingChange) {
        const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue;
        onSortingChange(newSorting);
      }
    },
    [onSortingChange, sorting],
  );

  const table = useReactTable({
    data,
    columns,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    manualPagination: true,
    autoResetPageIndex: false,
    enableRowSelection: true,
    enableSorting: true,
    enableMultiSort: false,
    getRowId,
    onSortingChange: useLocalSort ? handleSortingChange : setSorting,
    onRowSelectionChange: updaterOrValue => {
      if (onRowSelectionChange) {
        const newSelection = typeof updaterOrValue === 'function' ? updaterOrValue(rowSelection) : updaterOrValue;
        onRowSelectionChange(newSelection);
      }
    },
    state: {
      sorting: useLocalSort ? sorting : sortBy ? [{ id: sortBy, desc: sortOrder === 'DESC' }] : [],
      columnFilters,
      columnVisibility,
      pagination: { pageIndex: page - 1, pageSize },
      rowSelection,
    },
    defaultColumn: defaultColumnSizing,
    columnResizeMode: 'onChange',
    enableColumnResizing: true,
  });

  useEffect(() => {
    table.setPageSize(pageSize);
  }, [table, pageSize]);

  useEffect(() => {
    table.setPageIndex(page - 1);
  }, [table, page]);

  useEffect(() => {
    if (sorting.length === 0 || useLocalSort) return;
    changeSort(sorting[0].id, sorting[0].desc ? 'DESC' : 'ASC');
  }, [changeSort, sorting, useLocalSort]);

  const loadMoreRef = useInfiniteScroll({
    hasNextPage,
    fetchNextPage,
    threshold: 0.1,
    isFetchingNextPage,
  });

  return (
    <div className={cn('relative w-full overflow-auto table-fixed', containerClassName)}>
      <table ref={ref} className={cn('w-full caption-bottom text-sm table-fixed', className)} {...props}>
        <TableHeader>
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id} className="!bg-white">
              {headerGroup.headers.map(header => {
                return (
                  <TableHead
                    key={header.id}
                    style={{
                      width: header.getSize(),
                      maxWidth: header.getSize(),
                      minWidth: header.getSize(),
                      ...(header.column.columnDef.meta?.flexGrow && {
                        flexGrow: header.column.columnDef.meta.flexGrow,
                      }),
                    }}
                    isSticky={stickyHeader}
                  >
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            <>
              {table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className={url && 'cursor-pointer'}
                  onClick={() => {
                    if (!url) return;

                    if (url === 'referral-codes') {
                      selectRowEdit && selectRowEdit(row.original as PublicReferralCode);
                    } else {
                      router.push(`${url}/${(row.original as any).id}`);
                    }
                  }}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell
                      key={cell.id}
                      className={tableCellClassName}
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                        minWidth: cell.column.getSize(),
                        ...(cell.column.columnDef.meta?.flexGrow && { flexGrow: cell.column.columnDef.meta.flexGrow }),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  ref={loadMoreRef as React.RefObject<HTMLTableCellElement>}
                  className={cn('p-0 text-center', isFetchingNextPage && 'p-4')}
                >
                  <div className="flex justify-center items-center">
                    {isFetchingNextPage && <Loader2 className="h-6 w-6 text-primary animate-spin" />}
                  </div>
                </TableCell>
              </TableRow>
            </>
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className={cn('h-24 text-center', tableCellClassName)}>
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </table>
    </div>
  );
};
Table.displayName = 'Table';

const TableHeader = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => (
    <thead ref={ref} className={cn('[&_tr]:border-b relative z-10', className)} {...props} />
  ),
);
TableHeader.displayName = 'TableHeader';

const TableBody = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => (
    <tbody ref={ref} className={cn('[&_tr:last-child]:border-0', className)} {...props} />
  ),
);
TableBody.displayName = 'TableBody';

const TableFooter = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => (
    <tfoot ref={ref} className={cn('border-t bg-muted/50 font-medium [&>tr]:last:border-b-0', className)} {...props} />
  ),
);
TableFooter.displayName = 'TableFooter';

const TableRow = React.forwardRef<HTMLTableRowElement, React.HTMLAttributes<HTMLTableRowElement>>(
  ({ className, ...props }, ref) => (
    <tr
      ref={ref}
      className={cn('border-b transform-gpu transition-colors odd:bg-primary-foreground hover:bg-muted/50', className)}
      {...props}
    />
  ),
);
TableRow.displayName = 'TableRow';

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement> & { isSticky?: boolean }
>(({ className, isSticky, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      'h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0  first:pl-4 lg:first:pl-6',
      isSticky && 'bg-white z-[99] sticky -top-[1px]',
      className,
    )}
    {...props}
  />
));
TableHead.displayName = 'TableHead';

const TableCell = React.forwardRef<HTMLTableCellElement, React.TdHTMLAttributes<HTMLTableCellElement>>(
  ({ className, ...props }, ref) => (
    <td
      ref={ref}
      className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0 first:pl-4 lg:first:pl-6', className)}
      {...props}
    />
  ),
);
TableCell.displayName = 'TableCell';

const TableCaption = React.forwardRef<HTMLTableCaptionElement, React.HTMLAttributes<HTMLTableCaptionElement>>(
  ({ className, ...props }, ref) => (
    <caption ref={ref} className={cn('mt-4 text-sm text-muted-foreground', className)} {...props} />
  ),
);
TableCaption.displayName = 'TableCaption';

export { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption };
