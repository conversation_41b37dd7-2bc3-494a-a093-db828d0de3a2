import { PropsWithChildren } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Row } from '@tanstack/table-core';
import { cn } from '@/lib/utils';

type SelectCellProps<T> = {
  row: Row<T>;
  onSelect?: () => void;
  disabled?: boolean;
  checked?: boolean;
};

const SelectCell = <T,>({ row, onSelect, disabled, checked }: PropsWithChildren<SelectCellProps<T>>) => (
  <Checkbox
    checked={checked || row.getIsSelected()}
    onCheckedChange={value => {
      row.toggleSelected(!!value);
      if (onSelect) {
        onSelect();
      }
    }}
    onClick={e => e.stopPropagation()}
    aria-label="Select row"
    className={cn(
      'w-5 h-5 mt-0.5 data-[state=checked]:bg-muted-foreground data-[state=checked]:text-white border-muted-foreground',
      disabled && 'opacity-50',
    )}
    disabled={disabled}
  />
);
export { SelectCell };
