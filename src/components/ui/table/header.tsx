import { PropsWithChildren } from 'react';
import { Column } from '@tanstack/table-core';
import { CaretSortIcon, CaretDownIcon, CaretUpIcon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';

const Header = <TData,>({ children, column }: PropsWithChildren<{ column: Column<TData> }>) => {
  return (
    <div
      onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      className={cn(
        'flex items-center whitespace-nowrap',
        column.getCanSort() ? 'cursor-pointer' : 'pointer-events-none',
      )}
    >
      {children}
      {column.getCanSort() ? (
        column.getIsSorted() ? (
          column.getIsSorted() === 'desc' ? (
            <CaretUpIcon className="ml-2 h-4 w-4" />
          ) : (
            <CaretDownIcon className="ml-2 h-4 w-4" />
          )
        ) : (
          <CaretSortIcon className="ml-2 h-4 w-4" />
        )
      ) : null}
    </div>
  );
};

export { Header };
