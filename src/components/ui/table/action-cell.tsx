import { PropsWithChildren, useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import Link from 'next/link';
import ModalConfirmDelete from '@/components/common/modal-confirm-delete';
import { Row } from '@tanstack/table-core';
import ModalConfirmVerification from '@/components/common/modal-confirm-verification';
import { VerificationStatus } from '@/backend/users/entities/User';
import { VerifyUserPayload } from '@/backend/users/validations/verify-user';

export type DeleteModalProps = {
  onDelete: (id: number) => void | Promise<void>;
  isDeleting?: boolean;
  deleteText?: string;
  title: string;
  subtitle?: React.ReactNode;
};

type VerificationPayload = { id: number } & VerifyUserPayload;

export type VerificationModalProps = {
  verificationStatus?: VerificationStatus;
  hasVerification?: boolean;
  onVerification?: (data: VerificationPayload) => void;
  isOnVerification?: boolean;
  title?: string;
  subtitle?: React.ReactNode;
};

type ActionsCellProps<TData> = {
  row: Row<TData>;
  urlDetail?: string;
  deleteModalProps: DeleteModalProps;
  verificationModalProps?: VerificationModalProps;
  customActions?: Array<{
    label: string;
    onClick: () => void;
  }>;
};

const ActionsCell = <TData extends { id: number }>({
  row,
  urlDetail,
  deleteModalProps,
  verificationModalProps = {},
  customActions,
  children,
}: PropsWithChildren<ActionsCellProps<TData>>) => {
  const { onDelete, isDeleting, deleteText, title: deleteTitle, subtitle: deleteSubtitle } = deleteModalProps;
  const {
    isOnVerification,
    onVerification,
    title: verificationTitle,
    subtitle: verificationSubtitle,
  } = verificationModalProps;

  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openVerificationModal, setOpenVerificationModal] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <DotsHorizontalIcon className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="p-0">
          {children && children}
          {!children && urlDetail && (
            <Link href={urlDetail}>
              <DropdownMenuItem className="text-foreground font-medium cursor-pointer rounded-none">
                View / Edit
              </DropdownMenuItem>
            </Link>
          )}

          {customActions?.map((action, index) => (
            <DropdownMenuItem
              key={index}
              className="rounded-none text-gray-text font-medium cursor-pointer"
              onClick={e => {
                e.stopPropagation();
                action.onClick();
              }}
            >
              {action.label}
            </DropdownMenuItem>
          ))}

          <DropdownMenuItem
            className="text-error-bold focus:text-error-bold font-medium rounded-none cursor-pointer"
            onClick={e => {
              e.stopPropagation();
              setOpenDeleteModal(true);
            }}
          >
            {deleteText ?? 'Delete'}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ModalConfirmDelete
        openDialog={openDeleteModal}
        setOpenDialog={setOpenDeleteModal}
        onConfirm={() => {
          onDelete(row.original.id);
        }}
        isConfirming={isDeleting}
        title={deleteTitle}
        subtitle={deleteSubtitle}
      />
      <ModalConfirmVerification
        openDialog={openVerificationModal}
        setOpenDialog={setOpenVerificationModal}
        onConfirm={() => {
          onVerification &&
            onVerification({
              id: row.original.id,
              verificationStatus: VerificationStatus.Verified,
            });
        }}
        isConfirming={isOnVerification}
        title={verificationTitle}
        subtitle={verificationSubtitle}
      />
    </>
  );
};

export { ActionsCell };
