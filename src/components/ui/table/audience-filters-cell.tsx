import { AudienceItem } from '@/lib/http/audiences/types';
import { Row } from '@tanstack/react-table';
import { Badge } from '../badge';
import { cn } from '@/lib/utils';

type AudienceFiltersCellProps = {
  row: Row<AudienceItem>;
};

export const AudienceFiltersCell = ({ row }: AudienceFiltersCellProps) => {
  const {
    cities = [],
    provinces = [],
    practiceSettings = [],
    completedSurveys = [],
    surveys = [],
    employmentStatuses = [],
    specialtyIds = [],
    specialties = [],
    userIds = [],
  } = row.original;

  const filters = [
    ...(cities ?? []),
    ...(provinces ?? []),
    ...(practiceSettings ?? []),
    ...(completedSurveys ? surveys.filter(s => completedSurveys.includes(s.id)).map(s => s.title) : []),
    ...(employmentStatuses ?? []),
    ...(specialtyIds ? specialties.filter(s => specialtyIds.includes(s.id)).map(s => s.name) : []),
  ];

  const badgeStyles = ['text-[#6941C6] bg-[#F9F5FF]', 'text-[#175CD3] bg-[#EFF8FF]', 'text-[#3538CD] bg-[#EEF4FF]'];

  const isShowSpecificUsers = userIds && userIds.length > 0;
  const isShowAllUsers = filters.length === 0 && !isShowSpecificUsers;

  return (
    <div className="flex flex-wrap gap-1 w-full">
      {isShowSpecificUsers && (
        <Badge variant="default" className={cn('text-[#344054] bg-[#F2F4F7] max-w-[200px] text-ellipsis line-clamp-1')}>
          Specific Users
        </Badge>
      )}
      {filters.slice(0, 3).map((filter, index) => (
        <Badge
          key={index}
          variant="default"
          className={cn(badgeStyles[index], 'max-w-[200px] text-ellipsis line-clamp-1')}
        >
          {filter}
        </Badge>
      ))}
      {filters.length > 3 && (
        <Badge variant="default" className={cn('bg-[#F2F4F7] text-[#344054] max-w-[200px] text-ellipsis line-clamp-1')}>
          +{filters.length - 3}
        </Badge>
      )}
      {isShowAllUsers && (
        <Badge variant="default" className={cn('text-[#6941C6] bg-[#F9F5FF] max-w-[200px] text-ellipsis line-clamp-1')}>
          All users
        </Badge>
      )}
    </div>
  );
};
