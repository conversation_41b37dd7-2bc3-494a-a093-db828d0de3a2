import { PropsWithChildren } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Table } from '@tanstack/table-core';

const SelectHeader = <TData,>({ table }: PropsWithChildren<{ table: Table<TData> }>) => (
  <Checkbox
    checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
    onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
    aria-label="Select all"
    className="w-5 h-5 mt-0.5 data-[state=checked]:bg-muted-foreground data-[state=checked]:text-white border-muted-foreground"
  />
);
export { SelectHeader };
