'use client';
import * as React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { DayPicker } from 'react-day-picker';
import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({ className, classNames, showOutsideDays = true, ...props }: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('py-6', className)}
      classNames={{
        months: 'flex flex-col sm:flex-row space-y-4 sm:space-y-0 divide-x',
        month: 'space-y-4 px-6',
        caption: 'flex justify-center pt-1 relative items-center',
        caption_label: 'text-gray-text font-medium',
        nav: 'space-x-1 flex items-center',
        nav_button: cn(buttonVariants({ variant: 'ghost' }), 'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'),
        nav_button_previous: 'absolute left-1',
        nav_button_next: 'absolute right-1',
        table: 'w-full border-collapse space-y-1',
        head_row: 'flex',
        head_cell: 'text-gray-text rounded-md w-10 font-medium text-sm',
        row: 'flex w-full mt-1',
        cell: 'h-10 w-10 text-center text-sm p-0 relative [&:has([aria-selected].day-range-start)]:rounded-l-full [&:has([aria-selected].day-range-end)]:rounded-r-full [&:has([aria-selected].day-range-middle)]:bg-[#F9F5FF] [&:has([aria-selected].day-range-start)]:bg-[#F9F5FF] [&:has([aria-selected].day-range-end)]:bg-[#F9F5FF] [&:has([aria-selected].day-range-middle.day-outside)]:bg-white [&:has([aria-selected].day-range-start.day-outside)]:bg-white [&:has([aria-selected].day-range-end.day-outside)]:bg-white [&:has([aria-selected].day-outside)]:bg-white [&:has([aria-selected])]:bg-white first:[&:has([aria-selected])]:rounded-l-full last:[&:has([aria-selected])]:rounded-r-full focus-within:relative focus-within:z-20',
        day: cn(
          buttonVariants({ variant: 'ghost' }),
          'h-10 w-10 p-0 font-normal aria-selected:opacity-100 aria-selected:font-medium rounded-full',
        ),
        day_range_start: 'day-range-start',
        day_range_end: 'day-range-end',
        day_selected:
          'bg-primary-brand text-primary-foreground hover:bg-primary-brand hover:text-primary-foreground focus:bg-primary-brand focus:text-primary-foreground',
        day_today: 'bg-[#F2F4F7] text-gray-text !font-medium',
        day_outside:
          'day-outside text-muted-foreground opacity-50 aria-selected:bg-white aria-selected:text-muted-foreground aria-selected:opacity-50 aria-selected:font-normal',
        day_disabled: 'text-muted-foreground opacity-50',
        day_range_middle:
          'day-range-middle aria-selected:bg-[#F9F5FF] aria-selected:text-primary-brand aria-selected:font-medium',
        day_hidden: 'invisible',
        ...classNames,
      }}
      components={{
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        IconLeft: ({ ...props }) => <ChevronLeft className="h-4 w-4" />,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        IconRight: ({ ...props }) => <ChevronRight className="h-4 w-4" />,
      }}
      {...props}
    />
  );
}
Calendar.displayName = 'Calendar';

export { Calendar };
