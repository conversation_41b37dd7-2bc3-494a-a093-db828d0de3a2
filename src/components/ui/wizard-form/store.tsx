import React, { createContext, useContext, useState } from 'react';
import { CreateUserPayload } from '@/backend/users/validations/create-user';

type FormCreateUserPayload = Partial<CreateUserPayload>;

interface WizardFormState {
  formState: FormCreateUserPayload;
  setFormState: React.Dispatch<React.SetStateAction<FormCreateUserPayload>>;
}

const WizardFormContext = createContext<WizardFormState>({
  formState: {},
  setFormState: () => {},
});

export function WizardFormProvider({ children }: React.PropsWithChildren) {
  const [formState, setFormState] = useState<FormCreateUserPayload>({});

  return <WizardFormContext.Provider value={{ formState, setFormState }}>{children}</WizardFormContext.Provider>;
}

export function useWizardFormState() {
  const context = useContext(WizardFormContext);

  if (!context) {
    throw new Error('useWizardFormState must be used within the WizardFormProvider');
  }

  return context;
}
