'use client';

import React, { useState } from 'react';
import { Calendar as CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import DateFilterOptions from '@/components/ui/date-picker/date-filter-options';
import FooterDatePicker from '@/components/ui/date-picker/footer-date-picker';
import { useDateRange } from '@/hooks/useDateRange';
import { useSearchParams } from 'next/navigation';

const DatePicker = ({ className }: React.HTMLAttributes<HTMLDivElement>) => {
  const searchParams = useSearchParams();
  const from = searchParams.get('from') as unknown as Date;
  const to = searchParams.get('to') as unknown as Date;
  const [date, setDate] = useState<DateRange | undefined>({ from, to });
  const [openCalendar, setOpenCalendar] = useState(false);
  const { changeDateRange } = useDateRange();

  const handleSetDate = (range: DateRange | undefined) => {
    setDate(range);
    if (range?.from) {
      setDate({ from: range.from, to: undefined });
    } else {
      setDate({ from: undefined, to: undefined });
    }

    if (range?.to && range.from) {
      setDate({ from: range.from, to: range.to });
    }
  };

  const onApply = () => {
    setOpenCalendar(false);
    if (!date || !date.from || !date.to) return;
    changeDateRange({ from: formatDate(date.from, 'yyyy-MM-dd'), to: formatDate(date.to, 'yyyy-MM-dd') });
  };

  const onCancel = () => {
    setOpenCalendar(false);
  };

  return (
    <div className={cn('grid gap-2', className)}>
      <Popover open={openCalendar} onOpenChange={open => setOpenCalendar(open)}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            className={cn(
              'w-[250px] justify-start text-left font-semibold text-sm text-gray-text',
              !date && 'text-muted-foreground',
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {formatDate(date.from, 'LLL dd, y')} - {formatDate(date.to, 'LLL dd, y')}
                </>
              ) : (
                formatDate(date.from, 'LLL dd, y')
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] sm:w-[600px] lg:w-auto p-0 flex flex-col lg:flex-row" align="start">
          <DateFilterOptions onSetDate={setDate} />
          <div className="flex flex-col">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={handleSetDate}
              numberOfMonths={2}
              pagedNavigation
              toDate={new Date()}
            />
            <FooterDatePicker date={date} onApply={onApply} onCancel={onCancel} />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default DatePicker;
