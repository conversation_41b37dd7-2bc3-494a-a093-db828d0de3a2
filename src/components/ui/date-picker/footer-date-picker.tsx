import { Button } from '@/components/ui/button';
import { formatDate } from '@/utils/date-format';
import { DateRange } from 'react-day-picker';

type FooterDatePickerProps = {
  date: DateRange | undefined;
  onApply: () => void;
  onCancel: () => void;
};

const FooterDatePicker = ({ date, onApply, onCancel }: FooterDatePickerProps) => {
  return (
    <div className="flex flex-1 flex-col sm:flex-row gap-4 justify-between items-center px-6 py-4 border-t border-t-[#EAECF0]">
      <div className="flex items-center gap-2">
        <div className="min-w-[136px] min-h-10 border py-2 px-4 rounded-lg">
          {date?.from ? formatDate(date?.from, 'LLL dd, y') : ''}
        </div>
        <div>&mdash;</div>
        <div className="min-w-[136px] min-h-10 border py-2 px-4 rounded-lg">
          {date?.to ? formatDate(date?.to, 'LLL dd, y') : ''}
        </div>
      </div>
      <div className="flex gap-3">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={onApply}>Apply</Button>
      </div>
    </div>
  );
};

export default FooterDatePicker;
