import {
  endOfMonth,
  endOfToday,
  endOfWeek,
  endOfYear,
  endOfYesterday,
  startOfMonth,
  startOfToday,
  startOfWeek,
  startOfYear,
  startOfYesterday,
  subMonths,
  subWeeks,
  subYears,
} from 'date-fns';

export const startToday = startOfToday();
export const endToday = endOfToday();
export const startYesterday = startOfYesterday();
export const endYesterday = endOfYesterday();
export const startOfThisWeek = startOfWeek(new Date());
export const endOfThisWeek = endOfWeek(new Date());
export const startOfLastWeek = subWeeks(startOfWeek(new Date()), 1);
export const endOfLastWeek = subWeeks(endOfWeek(new Date()), 1);
export const startOfThisMonth = startOfMonth(new Date());
export const endOfThisMonth = endOfMonth(new Date());
export const startOfLastMonth = subMonths(startOfMonth(new Date()), 1);
export const endOfLastMonth = endOfMonth(subMonths(endOfMonth(new Date()), 1));
export const startOfThisYear = startOfYear(new Date());
export const endOfThisYear = endOfYear(new Date());
export const startOfLastYear = subYears(startOfYear(new Date()), 1);
export const endOfLastYear = subYears(endOfYear(new Date()), 1);
export const startOfAllTime = new Date(2020, 0, 1);
export const endOfAllTime = endOfToday();
