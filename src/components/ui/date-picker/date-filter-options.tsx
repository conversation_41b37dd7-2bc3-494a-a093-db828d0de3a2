'use client';

import { SetStateAction, useCallback } from 'react';
import { DateRange } from 'react-day-picker';
import { useSearchParams } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { useDateRange } from '@/hooks/useDateRange';
import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import {
  endOfLastWeek,
  endOfLastMonth,
  endOfThisWeek,
  endToday,
  endYesterday,
  startOfLastMonth,
  startOfLastWeek,
  startOfThisMonth,
  startOfThisWeek,
  startToday,
  startYesterday,
  endOfThisMonth,
  startOfLastYear,
  endOfLastYear,
  startOfThisYear,
  endOfThisYear,
  endOfAllTime,
  startOfAllTime,
} from '@/components/ui/date-picker/date-range-calculation';

const templateDate = 'yyyy-MM-dd';

type DateFilterOptionProps = {
  name: string;
  isInRange: boolean;
  dateFrom: Date;
  dateTo: Date;
  onSetDate: React.Dispatch<SetStateAction<DateRange | undefined>>;
};

const DateFilterOption = ({ name, isInRange, dateFrom, dateTo, onSetDate }: DateFilterOptionProps) => {
  const { changeDateRange } = useDateRange();

  const buttonStyle = {
    default: 'w-fit lg:w-full justify-start hover:bg-[#F9F5FF] hover:text-primary-brand font-normal',
    focused: 'bg-[#F9F5FF] text-primary-brand font-medium',
  };

  return (
    <Button
      variant="ghost"
      className={cn(buttonStyle.default, isInRange && buttonStyle.focused)}
      onClick={() => {
        changeDateRange({
          from: formatDate(dateFrom, templateDate),
          to: formatDate(dateTo, templateDate),
        });
        onSetDate({ from: dateFrom, to: dateTo });
      }}
    >
      {name}
    </Button>
  );
};

const DateFilterOptions = ({ onSetDate }: { onSetDate: React.Dispatch<SetStateAction<DateRange | undefined>> }) => {
  const searchParams = useSearchParams();
  const fromDate = searchParams.get('from');
  const toDate = searchParams.get('to');

  const compareDateResult = useCallback(
    (start: Date, end: Date) => {
      return fromDate === formatDate(start, templateDate) && toDate === formatDate(end, templateDate);
    },
    [fromDate, toDate],
  );

  const today = compareDateResult(startToday, endToday);
  const yesterday = compareDateResult(startYesterday, endYesterday);
  const thisWeek = compareDateResult(startOfThisWeek, endOfThisWeek);
  const lastWeek = compareDateResult(startOfLastWeek, endOfLastWeek);
  const thisMonth = compareDateResult(startOfThisMonth, endOfThisMonth);
  const lastMonth = compareDateResult(startOfLastMonth, endOfLastMonth);
  const thisYear = compareDateResult(startOfThisYear, endOfThisYear);
  const lastYear = compareDateResult(startOfLastYear, endOfLastYear);
  const allTime = compareDateResult(startOfAllTime, endOfAllTime);

  return (
    <div className="w-full lg:w-[200px] border-b border-b-[#EAECF0] lg:border-b-0 lg:border-r lg:border-r-[#EAECF0]">
      <div className="flex flex-wrap gap-1 lg:flex-col px-4 py-6 text-sm text-gray-text">
        <DateFilterOption
          name="Today"
          isInRange={today}
          dateFrom={startToday}
          dateTo={endToday}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="Yesterday"
          isInRange={yesterday}
          dateFrom={startYesterday}
          dateTo={endYesterday}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="This week"
          isInRange={thisWeek}
          dateFrom={startOfThisWeek}
          dateTo={endOfThisWeek}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="Last week"
          isInRange={lastWeek}
          dateFrom={startOfLastWeek}
          dateTo={endOfLastWeek}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="This month"
          isInRange={thisMonth}
          dateFrom={startOfThisMonth}
          dateTo={endOfThisMonth}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="Last month"
          isInRange={lastMonth}
          dateFrom={startOfLastMonth}
          dateTo={endOfLastMonth}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="This year"
          isInRange={thisYear}
          dateFrom={startOfThisYear}
          dateTo={endOfThisYear}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="Last year"
          isInRange={lastYear}
          dateFrom={startOfLastYear}
          dateTo={endOfLastYear}
          onSetDate={onSetDate}
        />
        <DateFilterOption
          name="All time"
          isInRange={allTime}
          dateFrom={startOfAllTime}
          dateTo={endOfAllTime}
          onSetDate={onSetDate}
        />
      </div>
    </div>
  );
};

export default DateFilterOptions;
