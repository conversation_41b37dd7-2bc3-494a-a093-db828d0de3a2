import React, { useEffect } from 'react';
import { SelectMultipleProps } from './select-multiple';
import { SelectTrigger } from './select';
import * as SelectPrimitive from '@radix-ui/react-select';
import { useFilterDataStore } from '@/components/sections/audiences/hooks/useFilterDataStore';
import { useController, useFormContext } from 'react-hook-form';

interface SelectShowUserProps extends SelectMultipleProps {
  onClick?: () => void;
  className?: string;
}

const SelectShowLabel = ({ name, onClick, placeholder, ...props }: SelectShowUserProps) => {
  const [openPopover, setOpenPopover] = React.useState(false);
  const { specificUsers } = useFilterDataStore();

  const { control } = useFormContext();
  const { field } = useController({ control, name });

  useEffect(() => {
    const currentValues = field.value || [];
    const specificUserIds = specificUsers.map(user => user.id);

    const isDifferent =
      currentValues.length !== specificUserIds.length ||
      !currentValues.every((id: number) => specificUserIds.includes(id));

    if (isDifferent) {
      field.onChange(specificUserIds);
    }
  }, [field, specificUsers]);

  return (
    <SelectPrimitive.Root {...props} open={openPopover} onOpenChange={() => setOpenPopover(!openPopover)}>
      <SelectTrigger
        onClick={e => {
          e.preventDefault();
          onClick?.();
        }}
        className="px-3.5 py-2.5 text-start text-base h-11"
      >
        <span className="line-clamp-1 text-base text-start">
          {specificUsers.length > 0 ? specificUsers.map(i => `${i.firstName} ${i.lastName}`).join(', ') : placeholder}
        </span>
      </SelectTrigger>
    </SelectPrimitive.Root>
  );
};

export default SelectShowLabel;
