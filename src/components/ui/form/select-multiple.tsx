'use client';
import React, { useEffect, useRef } from 'react';
import * as SelectPrimitive from '@radix-ui/react-select';
import { SelectContent, SelectTrigger, SelectValue } from '@/components/ui/form/select';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { useController, useFormContext } from 'react-hook-form';
import { useVirtualizer } from '@tanstack/react-virtual';
import { Loader2 } from 'lucide-react';
import { ErrorMessage } from '@hookform/error-message';

export type SelectOptions = {
  label: string;
  value: string | number;
};

export interface SelectMultipleProps extends React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root> {
  name: string;
  placeholder: string;
  isShowError?: boolean;
  className?: string;
  options: SelectOptions[];
  defaultSelected?: SelectOptions[];
  isLoading?: boolean;
}

const SelectMultiple = ({
  name,
  className,
  placeholder,
  options,
  defaultSelected,
  isLoading,
  isShowError = true,
  ...props
}: SelectMultipleProps) => {
  const [selected, setSelected] = React.useState<SelectOptions[]>(defaultSelected ?? []);
  const [openPopover, setOpenPopover] = React.useState(false);
  const { control, formState } = useFormContext();
  const { field } = useController({ control, name });
  const ref = useRef(null);

  const rowVirtualize = useVirtualizer({
    count: options?.length + 1,
    getScrollElement: () => ref.current,
    estimateSize: () => 44,
  });

  useEffect(() => {
    if (selected.length === options.length) {
      field.onChange([]);
      return;
    }
    field.onChange(selected.map(i => i.value));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected]);

  return (
    <SelectPrimitive.Root {...props} open={openPopover} onOpenChange={() => setOpenPopover(!openPopover)}>
      <div className="relative">
        {isLoading ? (
          <div className="absolute top-1/2 right-0 -translate-x-1/2 -translate-y-1/2">
            <Loader2 className="h-4 w-4 animate-spin" />{' '}
          </div>
        ) : null}
        <SelectTrigger
          className={cn(
            'px-3.5 py-2.5 text-start text-base h-11',
            selected.length === 0 && 'text-placeholder',
            className,
          )}
        >
          <SelectValue
            placeholder={
              selected.length === options.length
                ? 'All'
                : selected.length > 0
                  ? selected.map(i => i.label).join(', ')
                  : placeholder
            }
          />
        </SelectTrigger>
        <SelectContent className="!p-0">
          {options.length > 0 ? (
            <div
              ref={ref}
              className="overflow-auto"
              style={{
                height: options.length < 8 ? `${(options.length + 1) * 44}px` : `384px`,
              }}
            >
              <div
                className="relative w-full"
                style={{
                  height: `${rowVirtualize.getTotalSize()}px`,
                }}
              >
                {rowVirtualize.getVirtualItems().map(virtualItem => {
                  if (virtualItem.index === options.length) {
                    return null;
                  }
                  const item = options[virtualItem.index];
                  return (
                    <>
                      {virtualItem.index === 0 && (
                        <div
                          className="px-4 py-2.5 flex items-center cursor-pointer absolute top-0 left-0 w-full"
                          onClick={() => {
                            const selectedAll = selected.length === options.length;
                            if (selectedAll) setSelected([]);
                            else setSelected(options);
                          }}
                          style={{
                            height: `${virtualItem.size}px`,
                            transform: `translateY(${virtualItem.start}px)`,
                          }}
                        >
                          <Checkbox className="border-[#D0D5DD]" checked={selected.length === options.length} />
                          <div className="text-base font-medium ml-3 text-gray-text">All</div>
                        </div>
                      )}
                      <div
                        key={item.value}
                        className="px-4 py-2.5 flex items-center cursor-pointer absolute top-0 left-0 w-full"
                        onClick={() => {
                          const existed = selected.findIndex(i => i.value === item.value) > -1;
                          if (!existed) setSelected([...selected, item]);
                          else setSelected(selected.filter(i => i.value !== item.value));
                        }}
                        style={{
                          height: `${virtualItem.size}px`,
                          transform: `translateY(${virtualItem.start + 44}px)`,
                        }}
                      >
                        <Checkbox
                          className="border-[#D0D5DD]"
                          checked={selected.findIndex(i => i.value === item.value) > -1}
                        />
                        <div className="text-sm font-medium ml-3 text-gray-text whitespace-nowrap text-ellipsis overflow-hidden">
                          {item.label}
                        </div>
                      </div>
                    </>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="px-4 py-2.5 text-sm text-center">No options</div>
          )}
        </SelectContent>
      </div>
      {isShowError && formState.errors[name] && (
        <div>
          <ErrorMessage
            name={name}
            render={({ message }) => {
              return <span className="text-red-500 text-sm">{message}</span>;
            }}
          />
        </div>
      )}
    </SelectPrimitive.Root>
  );
};
export default SelectMultiple;
