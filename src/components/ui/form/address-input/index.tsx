import { Controller, useFormContext } from 'react-hook-form';
import { cn } from '@/lib/utils';
import { ChevronRight, Info } from 'lucide-react';
import { ErrorMessage } from '@hookform/error-message';
import * as React from 'react';
import { useEffect, useRef, useState } from 'react';
import { InputProps } from '@/components/ui/form/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useAutoFillAddress } from '@/components/ui/form/address-input/useAutoFillAddress';
import { LocationResult, SearchResult } from '@/backend/canadapost/validations/canadapost';

type InputAddressProps = InputProps & {
  onDisabled: React.Dispatch<React.SetStateAction<boolean>>;
};

const InputAddress = ({ name, className, type, isShowError = true, onDisabled, ...props }: InputAddressProps) => {
  const { control, formState } = useFormContext();
  const ref = useRef<HTMLInputElement>(null);
  const [openSuggestions, setOpenSuggestions] = useState(false);
  const { searchTerm, setSearchTerm, setId, setLastId, addresses, isLoadingAddresses, completeAddress } =
    useAutoFillAddress();

  useEffect(() => {
    if (openSuggestions) {
      setTimeout(() => {
        ref.current?.focus();
      }, 200);
    }
  }, [openSuggestions]);

  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <Popover open={openSuggestions} onOpenChange={open => setOpenSuggestions(open)}>
          <PopoverTrigger asChild>
            <div className="w-full relative">
              <div className="w-full relative">
                <input
                  type={type}
                  autoComplete="off"
                  className={cn(
                    'z-[999] flex h-11 w-full rounded-md border border-input bg-background py-2.5 px-3.5 ring-offset-background placeholder:text-placeholder focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-primary-foreground',
                    formState.errors[name] &&
                      '!border-error-light ring-error-light ring-offset-error focus-visible:ring-error-light',
                    openSuggestions && 'outline-none ring-1 ring-ring ring-offset-0',
                    className,
                  )}
                  {...field}
                  {...props}
                  onChange={event => {
                    const value = event.target.value;
                    setSearchTerm(value);
                    setId(undefined);
                    setLastId(undefined);
                    onDisabled(false);
                    field.onChange(value);
                  }}
                  ref={ref}
                  onFocus={() => {
                    if (field.value) setSearchTerm(field.value);
                  }}
                  onClick={e => {
                    e.stopPropagation();
                    setOpenSuggestions(true);
                  }}
                />
                {isShowError && formState.errors[name] && (
                  <Info className="absolute top-1/2 right-2 -translate-x-1/2 -translate-y-1/2 h-4 w-4 text-error rotate-180" />
                )}
              </div>
              {isShowError && formState.errors[name] && (
                <div>
                  <ErrorMessage
                    name={name}
                    render={({ message }) => {
                      return <span className="text-red-500 text-sm">{message}</span>;
                    }}
                  />
                </div>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent align="start" className="w-[var(--radix-popover-trigger-width)] max-h-96 overflow-y-auto p-0">
            {addresses && addresses.data.Items.length > 0 && searchTerm ? (
              <div className="w-full flex flex-col text-sm">
                {addresses.data.Items.map(address => {
                  const next = (address as SearchResult).Next;
                  if (next) {
                    const searchAddress = address as SearchResult;
                    return (
                      <div
                        key={address.Id}
                        className="flex items-center justify-between space-x-2 px-3 py-1.5 first:pt-3 last:pb-3 cursor-pointer hover:bg-muted"
                        onClick={() => {
                          if (next === 'Retrieve') {
                            setId(searchAddress.Id);
                            setLastId(undefined);
                            setSearchTerm('');
                            setOpenSuggestions(false);
                            onDisabled(true);
                            return;
                          }
                          setId(undefined);
                          setLastId(searchAddress.Id);
                        }}
                      >
                        <div className="flex items-center space-x-2">
                          <div className="text-[12px]">{searchAddress.Text}</div>
                          <span className="text-xs italic text-muted-foreground text-[12px]">
                            {searchAddress.Description}
                          </span>
                        </div>
                        {next === 'Find' && <ChevronRight className="h-4 w-4 text-muted-foreground" />}
                      </div>
                    );
                  }

                  const err = address as LocationResult;
                  if (err.Error) {
                    return (
                      <div key={err.Error} className="text-center text-muted-foreground italic px-3 py-3">
                        Cannot use the service now, please check with the supplier.
                      </div>
                    );
                  }

                  const locationAddress = address as LocationResult;
                  return (
                    <div
                      key={`${locationAddress.Id}-${locationAddress.Language}`}
                      className="flex items-center justify-between space-x-2 px-3 py-1.5 first:pt-3 last:pb-3 cursor-pointer hover:bg-muted"
                      onClick={() => {
                        completeAddress(locationAddress);
                        setOpenSuggestions(false);
                      }}
                    >
                      <div className="">{locationAddress.Line1}</div>
                    </div>
                  );
                })}
              </div>
            ) : isLoadingAddresses ? (
              <div className="w-full px-3 py-3 text-center text-muted-foreground italic">Loading ...</div>
            ) : searchTerm.length === 0 ? (
              <div className="w-full px-3 py-3 text-center text-muted-foreground italic">Enter address to search</div>
            ) : (
              <div className="w-full px-3 py-3 text-center text-muted-foreground">No data</div>
            )}
          </PopoverContent>
        </Popover>
      )}
    />
  );
};
InputAddress.displayName = 'InputAddress';

export { InputAddress };
