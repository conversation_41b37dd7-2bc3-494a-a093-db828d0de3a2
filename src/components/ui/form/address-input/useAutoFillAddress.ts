import { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useDebounce } from '@/hooks/useDebounce';
import { useCanadapost } from '@/components/ui/form/address-input/useCanadapost';
import { LocationResult } from '@/backend/canadapost/validations/canadapost';

export function useAutoFillAddress() {
  const { setValue } = useFormContext();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [id, setId] = useState<string | undefined>();
  const [lastId, setLastId] = useState<string | undefined>();
  const debounceSearchTerm = useDebounce(searchTerm.trimStart());
  const { addresses, isLoadingAddresses } = useCanadapost(
    id ? { Id: id } : { SearchTerm: debounceSearchTerm, LastId: lastId },
  );

  const completeAddress = useCallback(
    (address: LocationResult) => {
      setValue('address', address.Line1, { shouldDirty: true });
      setValue('city', address.City, { shouldDirty: true });
      setValue('province', address.ProvinceName, { shouldDirty: true });
      setValue('postalCode', address.PostalCode, { shouldDirty: true });
      setValue('country', address.CountryName, { shouldDirty: true });
      setValue('canadaPostId', address.Id, { shouldDirty: true });
    },
    [setValue],
  );

  useEffect(() => {
    if (id && !isLoadingAddresses) {
      const items = addresses?.data?.Items;
      if (!items) return;
      let address = items[0];
      if (!address) return;

      if (items.length > 1) {
        const engAddress = items.find(item => (item as LocationResult).Language === 'ENG') as LocationResult;
        if (engAddress) address = engAddress;
      }

      if (address) completeAddress(address as LocationResult);
    }
  }, [addresses, id, isLoadingAddresses, setValue, completeAddress]);

  return {
    searchTerm,
    setSearchTerm,
    setId,
    setLastId,
    addresses,
    isLoadingAddresses,
    completeAddress,
  };
}
