import { RetrievePayload, SearchPayload } from '@/backend/canadapost/validations/canadapost';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/http';

export function useCanadapost(payload: SearchPayload | RetrievePayload) {
  const { SearchTerm, LastId } = payload as SearchPayload;
  const { Id } = payload as RetrievePayload;
  const { data, isFetching } = useQuery({
    queryKey: ['canadapost', payload],
    queryFn: async () => {
      if ((payload as RetrievePayload).Id) {
        return api.canadapost.retrieveAddress(payload as RetrievePayload);
      }
      return api.canadapost.searchAddress(payload as SearchPayload);
    },
    enabled: Boolean(SearchTerm || LastId || Id),
    placeholderData: previousData => previousData,
  });

  return {
    addresses: data,
    isLoadingAddresses: isFetching,
  };
}
