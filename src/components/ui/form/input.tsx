'use client';

import * as React from 'react';

import { cn, get } from '@/lib/utils';
import { Info, LucideIcon } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  StartIcon?: LucideIcon;
  EndIcon?: LucideIcon;
  name: string;
  isShowError?: boolean;
  iconClassName?: string;
  mask?: (value: string) => string;
  maxLength?: number;
}

export const postalCodeMask = (value?: string) => {
  if (!value) return '';

  let result = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  if (result.length >= 1 && result.length < 4) {
    result = result.slice(0, 3);
  } else {
    result = `${result.slice(0, 3)} ${result.slice(3, 6)}`;
  }

  return result;
};

const Input = ({
  name,
  className,
  iconClassName,
  type,
  StartIcon,
  EndIcon,
  isShowError = true,
  mask,
  maxLength,
  onChange,
  ...props
}: InputProps) => {
  const [isFocused, setIsFocused] = React.useState(false);
  const { control, formState } = useFormContext();
  const exceptThisSymbols = ['e', 'E', '+', '-', '.'];
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => {
        const isError = isShowError && get(formState.errors, name);
        return (
          <div className="w-full relative">
            <div className="w-full relative">
              {StartIcon && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <StartIcon size={18} className={cn('text-muted-foreground', iconClassName)} />
                </div>
              )}
              <input
                type={type}
                min={type === 'number' ? '0' : undefined}
                className={cn(
                  'flex h-11 w-full rounded-md border border-input bg-background py-2.5 px-3.5 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-placeholder focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-primary-foreground',
                  StartIcon ? 'pl-9' : '',
                  EndIcon ? 'pr-8' : '',
                  isError &&
                    '!border-error-light ring-error-light ring-offset-error focus-visible:ring-error-light pr-9',
                  className,
                )}
                {...field}
                onChange={event => {
                  let value = event.target?.value?.trimStart();
                  if (mask) value = mask(value);
                  if (type === 'number' && isNaN(Number(value))) return;
                  field.onChange(value);
                  onChange?.(event);
                }}
                onKeyDown={e => exceptThisSymbols.includes(e.key) && type === 'number' && e.preventDefault()}
                onFocus={() => setIsFocused(true)}
                onBlur={e => {
                  if (e.relatedTarget) {
                    (e.relatedTarget as HTMLInputElement).focus();
                  }
                  setIsFocused(false);
                }}
                {...props}
              />
              {isError && (
                <Info className="absolute top-1/2 right-2 -translate-x-1/2 -translate-y-1/2 h-4 w-4 text-error rotate-180" />
              )}
              {isFocused && maxLength && (
                <div className="absolute right-0 top-0 transform translate-y-[-115%] text-sm text-[#98A2B3]">
                  <span>
                    <span className={field.value?.length > maxLength ? '!text-red-600' : ''}>
                      {field.value ? field.value?.length : 0}/{maxLength}
                    </span>{' '}
                    Characters
                  </span>
                </div>
              )}
            </div>

            <div>
              {isError && (
                <ErrorMessage
                  name={name}
                  render={({ message }) => {
                    return <span className="text-red-500 text-sm">{message}</span>;
                  }}
                />
              )}
            </div>
            {EndIcon && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <EndIcon className={cn('text-muted-foreground', iconClassName)} size={18} />
              </div>
            )}
          </div>
        );
      }}
    />
  );
};
Input.displayName = 'Input';

export { Input };
