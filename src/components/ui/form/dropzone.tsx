/* eslint-disable @next/next/no-img-element */
'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';
import { readFile, getCroppedImg } from '@/utils/crop-image';
import { CircleCheck, CloudUpload, X } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import Cropper from 'react-easy-crop';
import { Area, Point } from 'react-easy-crop/types';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import { Button } from '../button';
import { useUpload } from '@/hooks/useUpload';
import toast from 'react-hot-toast';

export const DropzoneField = ({
  name,
  cropSize = { width: 343, height: 182 }, // Default crop size
  accept = {
    'image/png': ['.png'],
    'image/jpg': ['.jpg'],
    'image/jpeg': ['.jpeg'],
  },
  ...rest
}: {
  name: string;
  cropSize?: { width: number; height: number };
  accept?: { [key: string]: string[] };
}) => {
  const { control } = useFormContext();
  const fileSelected = useWatch({
    control,
    name: name,
  });

  return (
    <Controller
      render={({ field: { onChange } }) => (
        <Dropzone
          onChange={file => onChange(file)}
          accept={accept}
          fileSelected={fileSelected}
          cropSize={cropSize}
          {...rest}
        />
      )}
      name={name}
      control={control}
      defaultValue=""
    />
  );
};

const Dropzone = ({
  onChange,
  accept,
  fileSelected,
  cropSize,
  ...rest
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange: (...event: any[]) => void;
  accept?: { [key: string]: string[] };
  fileSelected?: string;
  cropSize: { width: number; height: number };
}) => {
  const { uploadFile, isUploading, isDeleting } = useUpload();

  const aspectRatio = cropSize.width / cropSize.height;
  const [preview, setPreview] = React.useState<string>(fileSelected ?? '');

  const { getRootProps, getInputProps, acceptedFiles, isDragActive } = useDropzone({
    onDrop: async (acceptedFiles, rejectedFiles) => {
      if (rejectedFiles.length > 0) {
        rejectedFiles.forEach(file => {
          file.errors.forEach(error => {
            toast.error(error.message);
          });
        });
        return;
      }
      if (acceptedFiles[0]) {
        const imageDataUrl = await readFile(acceptedFiles[0]);
        setPreview((imageDataUrl as string) || '');
      }
    },
    noClick: !!preview || isUploading || isDeleting,
    maxFiles: 1,
    accept: accept,
  });

  const onCancelPreview = useCallback(async () => {
    setPreview('');
    onChange('');
  }, [setPreview, onChange]);

  const renderContent = React.useMemo(() => {
    const handleUploadUrl = async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'surveys');
      const res = await uploadFile(formData);
      return res?.url || '';
    };

    const onConfirmPreview = async (imgPreview: string, file: File) => {
      const url = await handleUploadUrl(file);
      onChange(url);
      setPreview(imgPreview ?? '');
    };

    if (fileSelected) {
      return (
        <Completed
          preview={preview}
          filename={acceptedFiles?.[0]?.name}
          isDeleting={isDeleting}
          onCancelPreview={onCancelPreview}
        />
      );
    } else if (preview) {
      return (
        <Preview
          preview={preview}
          filename={acceptedFiles?.[0]?.name}
          onConfirmPreview={onConfirmPreview}
          onCancelPreview={onCancelPreview}
          aspectRatio={aspectRatio}
          isUploading={isUploading}
          resizedDimensions={{ width: cropSize.width, height: cropSize.height }}
        />
      );
    } else {
      return <Upload isDragActive={isDragActive} />;
    }
  }, [
    fileSelected,
    preview,
    uploadFile,
    onChange,
    acceptedFiles,
    isDeleting,
    onCancelPreview,
    aspectRatio,
    isUploading,
    isDragActive,
    cropSize,
  ]);

  return (
    <div
      className={cn(
        'min-w-[320px] min-h-[138px] overflow-hidden bg-[#EAECF0] rounded-lg border border-input flex flex-col justify-center items-center cursor-pointer',
        acceptedFiles.length > 0 && 'border-[#6149C4',
        (isDragActive || preview) && 'border-[#6149C4] text-[#6149C4] bg-[#F9F5FF]',
      )}
      {...getRootProps()}
    >
      <input {...getInputProps()} {...rest} />
      {renderContent}
    </div>
  );
};

const Preview = ({
  preview,
  filename,
  onConfirmPreview,
  onCancelPreview,
  aspectRatio,
  isUploading,
  resizedDimensions,
}: {
  preview: string;
  filename: string;
  onConfirmPreview: (imgCrop: string, file: File) => void;
  onCancelPreview: () => void;
  aspectRatio: number;
  isUploading: boolean;
  resizedDimensions: { width: number; height: number };
}) => {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState<number>(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  const onCropComplete = async (_: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };
  const handleConfirmPreview = async () => {
    const croppedImage = await getCroppedImg(preview, croppedAreaPixels as Area, resizedDimensions);
    const imageFile = new File([croppedImage?.file as Blob], `img-${Date.now()}.png`, {
      type: 'image/png',
    });
    onConfirmPreview(croppedImage?.url as string, imageFile);
  };
  return (
    <div className="flex flex-col p-4 w-full">
      <div
        className={cn(
          'h-[161px] relative w-full top-0 left-0 right-0 bottom-[80px]',
          isUploading && 'pointer-events-none',
        )}
      >
        <Cropper
          image={preview}
          crop={crop}
          aspect={aspectRatio}
          zoom={zoom}
          onCropChange={setCrop}
          onCropComplete={onCropComplete}
          onZoomChange={setZoom}
        />
      </div>
      <div className="flex flex-col items-center justify-center mt-2">
        <p className="text-sm text-[#101828] font-medium line-clamp-2" title={filename}>
          {filename}
        </p>
        <div className="flex space-x-3 w-full">
          <Button
            size="lg"
            variant="destructive"
            onClick={onCancelPreview}
            type="button"
            disabled={isUploading}
            className="w-full font-semibold mt-4 bg-white"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleConfirmPreview}
            isLoading={isUploading}
            size="lg"
            className="w-full font-semibold mt-4"
          >
            Confirm
          </Button>
        </div>
      </div>
    </div>
  );
};

export const Completed = ({
  preview,
  filename,
  onCancelPreview,
  isDeleting,
}: {
  preview: string;
  filename: string;
  onCancelPreview: () => void;
  isDeleting: boolean;
}) => {
  const [isDelete, setIsDelete] = useState(false);

  const PopoverDelete = React.useMemo(() => {
    const onConfirm = async () => {
      onCancelPreview();
    };
    return (
      <div className="flex w-full px-3 flex-col items-center justify-center mt-2 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30">
        <p className="text-sm text-white font-medium line-clamp-2">Remove Image?</p>
        <div className="flex space-x-3 w-full">
          <Button
            size="lg"
            variant="destructive"
            onClick={() => setIsDelete(false)}
            type="button"
            className="w-full font-semibold mt-4 h-10 bg-white"
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={onConfirm}
            isLoading={isDeleting}
            size="lg"
            className="w-full h-10 font-semibold mt-4"
          >
            Confirm
          </Button>
        </div>
      </div>
    );
  }, [setIsDelete, onCancelPreview, isDeleting]);

  return (
    <div className="w-full relative h-[138px]">
      <img className="w-full h-full object-cover" src={preview} alt="preview image" />
      {isDelete ? (
        PopoverDelete
      ) : (
        <>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 flex flex-col items-center">
            <CircleCheck className="w-6 h-6 mb-2 text-[#9F91DD]" />
            <p className="text-sm text-[#fff] font-medium">{filename}</p>
          </div>
        </>
      )}
      <div className="overlay bg-[#000000] absolute top-0 w-full opacity-65 h-full z-20"></div>
      {!isDelete && (
        <X className="w-6 h-6 text-white absolute top-[15px] right-[15px] z-20" onClick={() => setIsDelete(true)} />
      )}
    </div>
  );
};

const Upload = ({ isDragActive }: { isDragActive: boolean }) => (
  <>
    <CloudUpload className="w-6 h-6 mb-2" />
    <div>{isDragActive ? 'Release to upload' : 'Upload from computer'}</div>
  </>
);
