'use client';

import * as React from 'react';

import { cn, get } from '@/lib/utils';
import { Controller, useFormContext } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  name: string;
  isShowError?: boolean;
  maxLength?: number;
}

const Textarea = ({ className, name, isShowError = true, maxLength, ...props }: TextareaProps) => {
  const [isFocused, setIsFocused] = React.useState(false);
  const { control, formState } = useFormContext();

  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => {
        const isError = isShowError && get(formState.errors, name);
        return (
          <div className="w-full relative">
            <textarea
              className={cn(
                'flex min-h-[80px] placeholder:text-base w-full rounded-md overflow-hidden border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-placeholder focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50',
                className,
                isError ? '!border-error-light ring-error-light focus-visible:ring-error-light' : '',
              )}
              {...field}
              onFocus={() => setIsFocused(true)}
              onBlur={e => {
                if (e.relatedTarget) {
                  (e.relatedTarget as HTMLInputElement).focus();
                }
                setIsFocused(false);
              }}
              {...props}
            />
            {isError && (
              <div>
                <ErrorMessage
                  name={name}
                  render={({ message }) => {
                    return <span className="text-red-500 text-sm">{message}</span>;
                  }}
                />
              </div>
            )}
            {isFocused && maxLength && (
              <div className="absolute right-0 top-0 transform translate-y-[-115%] text-sm text-[#98A2B3]">
                <span>
                  <span className={field.value?.length > maxLength ? '!text-red-600' : ''}>
                    {field.value ? field.value?.length : 0}/{maxLength}
                  </span>{' '}
                  Characters
                </span>
              </div>
            )}
          </div>
        );
      }}
    />
  );
};
Textarea.displayName = 'Textarea';

export { Textarea };
