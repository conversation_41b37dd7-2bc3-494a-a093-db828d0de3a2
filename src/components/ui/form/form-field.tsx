'use client';

import {
  Controller,
  ControllerFieldState,
  ControllerRenderProps,
  FieldValues,
  useFormContext,
  UseFormStateReturn,
} from 'react-hook-form';
import { ReactElement } from 'react';

type FormFieldProps = {
  name: string;
  renderer: (props: {
    field: ControllerRenderProps<FieldValues, string>;
    fieldState: ControllerFieldState;
    formState: UseFormStateReturn<FieldValues>;
  }) => ReactElement;
};

const FormField = ({ name, renderer }: FormFieldProps) => {
  const { control } = useFormContext();
  return <Controller control={control} name={name} render={renderer} />;
};

export { FormField };
