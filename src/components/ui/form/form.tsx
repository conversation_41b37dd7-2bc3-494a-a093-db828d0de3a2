'use client';
import React, { useImperativeHandle, useMemo } from 'react';
import * as z from 'zod';
import { DefaultValues, FormProvider, useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { cn } from '@/lib/utils';

export interface FormActions<S extends z.ZodTypeAny> {
  formHandler: UseFormReturn<z.TypeOf<S>>;
}

export interface FormProps<S extends z.AnyZodObject, TDefault extends z.infer<S>>
  extends Omit<React.InputHTMLAttributes<HTMLFormElement>, 'onSubmit'> {
  schema?: S | z.ZodEffects<S>;
  onSubmit: (data: z.infer<S>, formHandler?: UseFormReturn<z.TypeOf<S>>) => void;
  defaultValues?: DefaultValues<TDefault>;
  formRef?: React.RefObject<FormActions<S> | FormActions<z.ZodEffects<S>>>;
  mode?: 'onSubmit' | 'onChange' | 'onBlur' | 'onTouched' | 'all';
}

const Form = <S extends z.AnyZodObject, TDefault extends z.infer<S>>(props: FormProps<S, TDefault>) => {
  const { schema, children, onSubmit, formRef, mode = 'onSubmit', defaultValues, className, ...rest } = props;
  const formHandler = useForm<z.infer<S>>({
    mode: mode || 'onChange',
    resolver: schema && zodResolver(schema),
    defaultValues: useMemo(() => defaultValues, [defaultValues]),
  });

  const handlePreventOneEnterKey = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  const isValid = formHandler.formState.isValid;
  const isDirty = Object.keys(formHandler.formState.dirtyFields).length > 0;

  useImperativeHandle(formRef, () => ({ formHandler }), [formHandler]);

  return (
    <FormProvider {...formHandler}>
      <form
        {...rest}
        onKeyDown={handlePreventOneEnterKey}
        onSubmit={formHandler.handleSubmit((data: z.infer<S>) => onSubmit(data, formHandler))}
        className={cn(className, !isValid && 'group is-invalid', !isDirty && 'group is-dirty')}
      >
        {children}
      </form>
    </FormProvider>
  );
};
export default Form;
