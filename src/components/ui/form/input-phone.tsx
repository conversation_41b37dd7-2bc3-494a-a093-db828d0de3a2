import React from 'react';
import { Input, InputProps } from '@/components/ui/form/input';
import { z } from 'zod';

export const inputPhoneSchema = z.preprocess(
  val => {
    if (!val) return '';
    return (val as string).replaceAll(/[() ]/g, '');
  },
  z.coerce.string().transform(value => {
    if (value.length === 0) return null;
    return value;
  }),
);

export const phoneMask = (value?: string) => {
  if (!value) return '';

  // Format for CA phone numbers
  let result = value.replace(/[^0-9]/g, '');
  result = result.replace('1', '');
  if (result.length >= 1 && result.length <= 3) {
    result = `+1 (${result}`;
  } else if (result.length >= 4 && result.length <= 6) {
    result = `+1 (${result.slice(0, 3)}) ${result.slice(3)}`;
  } else if (result.length > 6) {
    result = `+1 (${result.slice(0, 3)}) ${result.slice(3, 6)} ${result.slice(6, 10)}`;
  }
  return result;
};

const InputPhone = ({ ...props }: InputProps) => {
  return <Input mask={phoneMask} {...props} />;
};

export default InputPhone;
