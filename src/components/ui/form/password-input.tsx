'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';
import { Eye, EyeOff } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';
import { passwordSchema } from '@/backend/shared/validations/password';

export interface PasswordInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string;
  isShowError?: boolean;
  isShowGroupErrors?: boolean;
}

const PasswordInput = ({
  name,
  className,
  isShowError = true,
  isShowGroupErrors = false,
  ...props
}: PasswordInputProps) => {
  const { control, formState } = useFormContext();
  const [isViewPassword, setIsViewPassword] = React.useState(false);

  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => {
        let errors: Error[] = [];
        try {
          passwordSchema.parse({ password: field.value });
        } catch (err) {
          errors = JSON.parse(err as string) as Error[];
        }
        return (
          <div className="w-full">
            <div className="relative">
              <input
                type={isViewPassword ? 'text' : 'password'}
                className={cn(
                  'flex h-11 w-full rounded-md border border-input bg-background py-2.5 px-3.5 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-placeholder focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 pr-8',
                  formState.errors[name]
                    ? '!border-error-light ring-error-light ring-offset-error focus-visible:ring-error-light'
                    : '',
                  className,
                )}
                {...props}
                onChange={field.onChange}
              />
              <div
                className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                onClick={() => setIsViewPassword(pre => !pre)}
              >
                {isViewPassword ? (
                  <EyeOff
                    className={cn(formState.errors[name] ? 'text-error-light' : 'text-muted-foreground')}
                    size={18}
                  />
                ) : (
                  <Eye
                    className={cn(formState.errors[name] ? 'text-error-light' : 'text-muted-foreground')}
                    size={18}
                  />
                )}
              </div>
            </div>

            {isShowError && !isShowGroupErrors && formState.errors[name] && (
              <div>
                <ErrorMessage
                  name={name}
                  render={({ message }) => {
                    return <span className="text-red-500 text-sm">{message}</span>;
                  }}
                />
              </div>
            )}

            {isShowGroupErrors && formState.errors[name] && (
              <div className="text-sm text-error">
                <div className="mb-2">Password should meet the following criteria:</div>
                <ul className="list-disc pl-4">
                  {errors.map(e => (
                    <li key={e.message}>{e.message}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );
      }}
    />
  );
};
PasswordInput.displayName = 'PasswordInput';

export { PasswordInput };
