'use client';
import React, { useEffect, useRef } from 'react';
import * as SelectPrimitive from '@radix-ui/react-select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useController, useFormContext } from 'react-hook-form';
import { SelectOptions } from '@/components/ui/form/select-multiple';

export interface SelectSearchProps extends React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root> {
  name: string;
  placeholder: string;
  isShowError?: boolean;
  className?: string;
  options: SelectOptions[];
  defaultSelected?: SelectOptions[];
}

const SelectSearch = ({ name, placeholder, options, defaultSelected, disabled }: SelectSearchProps) => {
  const [selected, setSelected] = React.useState<SelectOptions[]>(defaultSelected ?? []);
  const [filtered, setFiltered] = React.useState<SelectOptions[]>(options);
  const { control } = useFormContext();
  const { field } = useController({ control, name });
  const [open, setOpen] = React.useState(false);
  const ref = useRef<HTMLInputElement>(null);
  const [showAll, setShowAll] = React.useState(true);

  useEffect(() => {
    if (selected.length === options.length) {
      field.onChange([]);
      return;
    }
    field.onChange(selected.map(i => i.value));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected]);

  useEffect(() => {
    setFiltered(options);
    if (open) {
      setTimeout(() => {
        ref.current?.focus();
      }, 200);
    }
  }, [open, options]);

  return (
    <Popover
      open={open}
      onOpenChange={open => {
        setOpen(open);
        setShowAll(true);
      }}
    >
      <PopoverTrigger asChild>
        <button
          type="button"
          role="combobox"
          aria-controls="radix-:r5:"
          aria-expanded={open ? 'true' : 'false'}
          aria-autocomplete="none"
          dir="ltr"
          data-state={open ? 'open' : 'closed'}
          className="flex text-base h-11 w-full items-center justify-between rounded-md border border-input bg-background ring-offset-background placeholder:text-placeholder focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;>span]:line-clamp-1 px-3.5 py-2.5"
          onClick={() => setOpen(!open)}
          disabled={disabled}
        >
          {open ? (
            <input
              placeholder="Start typing to search..."
              onChange={e => {
                setFiltered(options.filter(i => i.label.toLowerCase().includes(e.target.value.toLowerCase())));
                const allLabel = 'All';
                setShowAll(allLabel.toLowerCase().includes(e.target.value.toLowerCase()));
              }}
              ref={ref}
              onClick={event => event.stopPropagation()}
              className="flex-1 border-0 focus:outline-none focus:ring-0 text-base w-[inherit]"
            />
          ) : selected.length === options.length ? (
            'All'
          ) : selected.length > 0 ? (
            <span className="text-start text-base">{selected.map(i => i.label).join(', ')}</span>
          ) : (
            <span className="text-start text-base text-placeholder">{placeholder}</span>
          )}
          <ChevronDown className={cn('h-4 w-4 opacity-50', open ? 'rotate-180' : '')} />
        </button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[var(--radix-popover-trigger-width)] max-h-96 overflow-y-scroll p-0">
        {showAll && (
          <div
            className="px-4 py-2.5 flex items-center cursor-pointer"
            onClick={() => {
              if (selected.length === options.length) setSelected([]);
              else setSelected(options);
            }}
          >
            <Checkbox className="border-[#D0D5DD]" checked={selected.length === options.length} />
            <div className="text-sm font-medium ml-3 text-gray-text">All</div>
          </div>
        )}
        {filtered.length > 0 ? (
          filtered.map((item, idx) => {
            return (
              <div
                key={`${item.value}-${idx}`}
                className="px-4 py-2.5 flex items-center cursor-pointer"
                onClick={() => {
                  const existed = selected.findIndex(i => i.value === item.value) > -1;
                  if (!existed) setSelected([...selected, item]);
                  else setSelected(selected.filter(i => i.value !== item.value));
                }}
              >
                <Checkbox className="border-[#D0D5DD]" checked={selected.findIndex(i => i.value === item.value) > -1} />
                <div className="text-sm font-medium ml-3 text-gray-text">{item.label}</div>
              </div>
            );
          })
        ) : !showAll ? (
          <div className="px-4 py-2.5 text-sm text-center">No options</div>
        ) : null}
      </PopoverContent>
    </Popover>
  );
};
export default SelectSearch;
