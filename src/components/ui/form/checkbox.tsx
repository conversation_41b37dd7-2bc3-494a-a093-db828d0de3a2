'use client';

import * as React from 'react';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import { Check, LucideIcon } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Controller, useFormContext } from 'react-hook-form';
import { ErrorMessage } from '@hookform/error-message';

export interface CheckboxProps extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
  StartIcon?: LucideIcon;
  EndIcon?: LucideIcon;
  name: string;
  isShowError?: boolean;
}

const Checkbox = ({ className, name, isShowError = true, ...props }: CheckboxProps) => {
  const { control } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <CheckboxPrimitive.Root
          className={cn(
            'peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
            className,
          )}
          {...props}
          defaultChecked={field.value}
          onCheckedChange={checked => {
            props.onCheckedChange && props.onCheckedChange(checked);
            field.onChange(checked);
          }}
        >
          <CheckboxPrimitive.Indicator className={cn('flex items-center justify-center text-current')}>
            <Check className="h-4 w-4" />
          </CheckboxPrimitive.Indicator>
          {isShowError && fieldState.error?.message && (
            <div>
              <ErrorMessage
                name={name}
                render={({ message }) => {
                  return <span className="text-red-500 text-sm">{message}</span>;
                }}
              />
            </div>
          )}
        </CheckboxPrimitive.Root>
      )}
    />
  );
};

Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
