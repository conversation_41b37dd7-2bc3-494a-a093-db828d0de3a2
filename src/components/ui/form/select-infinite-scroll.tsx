import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import { Check, ChevronDown, Loader2 } from 'lucide-react';
import { ErrorMessage } from '@hookform/error-message';
import * as SelectPrimitive from '@radix-ui/react-select';
import { useInfiniteQuery } from '@tanstack/react-query';

import { SelectOptions } from '@/components/ui/form/select-multiple';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import { cn } from '@/lib/utils';
import { PaginationPayload } from '@/types/pagination';
import { useDebounce } from '@/hooks/useDebounce';

type QueryFunction = (params: PaginationPayload<unknown>) => Promise<SelectOptions[]>;

export type SelectSearchProps = React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root> & {
  name: string;
  placeholder?: string;
  isShowError?: boolean;
  className?: string;
  defaultSelected?: SelectOptions;
  apiFunction: QueryFunction;
  queryKey: string;
  onChange?: (value: SelectOptions) => void;
  searchable?: boolean;
  defaultOption?: SelectOptions | null; // Added defaultOption prop
};

const PAGE_SIZE = 25;

const SelectInfiniteScroll: React.FC<SelectSearchProps> = ({
  name,
  placeholder,
  defaultSelected,
  disabled,
  apiFunction,
  queryKey = 'infinite-scroll',
  isShowError = true,
  className,
  onChange,
  searchable = false,
  defaultOption = null,
}) => {
  const { control, formState } = useFormContext();
  const { field } = useController({ control, name });
  const [searchTerm, setSearchTerm] = useState('');
  const search = useDebounce(searchTerm);

  const [selected, setSelected] = useState<SelectOptions>(defaultSelected ?? ({} as SelectOptions));
  const [open, setOpen] = useState(false);
  const refSearchInput = useRef<HTMLInputElement>(null);

  const { data, fetchNextPage, hasNextPage, isFetching, isLoading, isFetchingNextPage } = useInfiniteQuery({
    queryKey: [queryKey, name, search],
    queryFn: async ({ pageParam = 1 }) => {
      return apiFunction({ page: pageParam, pageSize: PAGE_SIZE, search });
    },
    getNextPageParam: (lastPage: SelectOptions[], allPages: SelectOptions[][]) => {
      return lastPage.length >= PAGE_SIZE ? allPages.length + 1 : undefined;
    },
    initialPageParam: 1,
    gcTime: 0,
  });
  const { lastEntryRef, page } = useIntersectionObserver(isFetching);
  const pagePrev = useRef<number | null>(null);

  useEffect(() => {
    field.onChange(selected.value);
    onChange && onChange(selected);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected]);

  useEffect(() => {
    const findSelectedOption = () => data?.pages?.flat().find(option => option.value === +field.value);

    if (field.value && !isLoading) {
      const selectedOption = findSelectedOption();
      if (selectedOption) {
        setSelected(selectedOption);
      } else if (!selectedOption && hasNextPage) {
        fetchNextPage().then(() => {
          const newSelectedOption = findSelectedOption();
          if (newSelectedOption) {
            setSelected(newSelectedOption);
          }
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [field.value, data, isLoading]);

  useEffect(() => {
    if (page !== pagePrev?.current && !isFetching && !isFetchingNextPage && hasNextPage) {
      fetchNextPage();
      pagePrev.current = page;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  useEffect(() => {
    if (!searchable) return;
    if (open) {
      setTimeout(() => {
        refSearchInput.current?.focus();
      }, 200);
    }
  }, [searchable, open]);

  const mappedOptions = useMemo<SelectOptions[]>(() => {
    const options =
      data?.pages?.reduce((acc: SelectOptions[], page) => {
        return [...acc, ...page];
      }, []) || [];

    if (defaultOption) {
      if (searchTerm !== '') {
        return [...options];
      } else {
        return [defaultOption, ...options];
      }
    }

    return options;
  }, [data, defaultOption, searchTerm]);

  return (
    <Popover
      open={open}
      onOpenChange={open => {
        if (document.activeElement === refSearchInput.current) return;
        setOpen(open);
        setSearchTerm('');
      }}
    >
      <PopoverTrigger asChild>
        <div className="relative">
          <button
            type="button"
            role="combobox"
            aria-controls="radix-:r5:"
            aria-expanded={open ? 'true' : 'false'}
            aria-autocomplete="none"
            dir="ltr"
            data-state={open ? 'open' : 'closed'}
            onClick={() => setOpen(true)}
            disabled={disabled || isLoading}
            className={cn(
              'relative flex text-base h-11 w-full items-center justify-between rounded-md border border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&amp;>span]:line-clamp-1 px-3.5 py-2.5',
              formState.errors[name]
                ? '!border-error-light ring-error-light ring-offset-error focus-visible:ring-error-light focus:ring-error-light focus:ring-offset-1'
                : '',
              className,
            )}
          >
            {open && searchable ? (
              <input
                placeholder="Start typing to search..."
                onChange={e => {
                  setSearchTerm(e.target.value);
                }}
                ref={refSearchInput}
                onClick={event => event.stopPropagation()}
                className="flex-1 border-0 focus:outline-none focus:ring-0 text-base w-[inherit]"
              />
            ) : (
              <span
                className={cn(
                  'text-start text-base',
                  (!selected.label || selected.label === 'Select Custom Audience') && 'text-placeholder',
                )}
              >
                {selected.label || placeholder}
              </span>
            )}
            {isLoading || isFetching ? (
              <div className="absolute top-1/2 right-0 -translate-x-1/2 -translate-y-1/2">
                <Loader2 className="h-4 w-4 animate-spin" />{' '}
              </div>
            ) : (
              <ChevronDown className={cn('h-4 w-4 opacity-50', open ? 'rotate-180' : '')} />
            )}
          </button>
          {isShowError && formState.errors[name] && (
            <ErrorMessage
              name={name}
              render={({ message }) => {
                return <span className="absolute text-red-500 text-sm">{message}</span>;
              }}
            />
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="w-[var(--radix-popover-trigger-width)] max-h-96 overflow-y-auto p-0"
        onInteractOutside={() => {
          if (document.activeElement !== refSearchInput.current) {
            setOpen(false);
          }
        }}
      >
        {mappedOptions?.length > 0 ? (
          mappedOptions.map((item, index) => (
            <div
              key={item.value}
              className={`relative hover:bg-[#aaaaaa2b] flex w-full [&>span]:line-clamp-1 cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50`}
              onClick={() => {
                setSelected(item);
                setOpen(false);
                setSearchTerm('');
              }}
              ref={mappedOptions.length - 1 === index ? lastEntryRef : null}
            >
              {selected.value === item.value && (
                <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                  <Check className="h-4 w-4" />
                </span>
              )}
              <div className="text-sm font-medium text-gray-text cursor-pointer w-full">{item.label}</div>
            </div>
          ))
        ) : isFetching ? (
          <div className="px-4 py-2.5 text-sm text-center text-muted-foreground italic">Loading...</div>
        ) : (
          <div className="px-4 py-2.5 text-sm text-center">No options</div>
        )}
      </PopoverContent>
    </Popover>
  );
};

export default SelectInfiniteScroll;
