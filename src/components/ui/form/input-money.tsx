import { Input, InputProps } from '@/components/ui/form/input';
import { z } from 'zod';

export const inputMoneySchema = z.preprocess(
  val => {
    return (val as string)?.replaceAll('$', '');
  },
  z.coerce
    .number({
      invalid_type_error: 'Value is required',
    })
    .min(0, 'Value must be positive'),
);

const InputMoney = ({ ...props }: InputProps) => {
  return (
    <Input
      mask={value => {
        if (!value) return '$';
        return `$${value.replaceAll(/\D/g, '')}`;
      }}
      {...props}
    />
  );
};

export default InputMoney;
