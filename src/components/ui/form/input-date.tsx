'use client';
import * as React from 'react';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Controller, useFormContext } from 'react-hook-form';
import { DATE_FORMATS, formatDate } from '@/utils/date-format';
import { InputProps } from '@/components/ui/form/input';
import { ErrorMessage } from '@hookform/error-message';
import { Button } from '@/components/ui/button';
import { parse } from 'date-fns';

type InputDateProps = InputProps & {
  name: string;
  toDate?: Date;
  showLocalDate?: boolean;
};

export function InputDate({
  name,
  className,
  isShowError = true,
  toDate,
  disabled,
  showLocalDate = false,
  ...props
}: InputDateProps) {
  const { control, formState } = useFormContext();
  const [openCalendar, setOpenCalendar] = React.useState(false);

  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => {
        const selectedDate = field.value ? parse(field.value, 'yyyy-MM-dd', new Date()) : undefined;
        let localDate: Date | undefined = undefined;

        if (selectedDate) {
          localDate = new Date(selectedDate.getTime() - selectedDate.getTimezoneOffset() * 60_000);
        }

        const shouldShowError = isShowError && formState.errors[name];

        return (
          <Popover open={openCalendar} onOpenChange={open => setOpenCalendar(open)}>
            <div className="w-full relative">
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'flex justify-start items-center h-11 w-full rounded-md border border-input bg-background py-2.5 px-3.5 text-foreground font-normal ring-offset-background hover:bg-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-0',
                    disabled && 'cursor-not-allowed bg-primary-foreground',
                    formState.errors[name]
                      ? '!border-error-light ring-error-light ring-offset-error focus-visible:ring-error-light focus:ring-error-light focus:ring-offset-1'
                      : '',
                    className,
                  )}
                  disabled={disabled}
                >
                  <CalendarIcon size={18} className={cn('text-muted-foreground')} />
                  <input
                    type="date"
                    max="9999-12-31"
                    className={cn(
                      'pl-2 focus-visible:outline-none  focus-visible:ring-offset-0 cursor-pointer',
                      disabled && 'cursor-not-allowed bg-primary-foreground',
                    )}
                    {...props}
                    {...field}
                    value={field.value}
                    onChange={field.onChange}
                  />
                </Button>
              </PopoverTrigger>
            </div>
            {localDate && showLocalDate && !shouldShowError && (
              <div className="text-xs mt-1 text-gray-600 italic">
                Will be{' '}
                <span className="font-semibold">{localDate ? formatDate(localDate, DATE_FORMATS.ISO_DATE) : ''}</span>{' '}
                in your local time
              </div>
            )}
            {shouldShowError && (
              <div>
                <ErrorMessage
                  name={name}
                  render={({ message }) => {
                    return <span className="text-red-500 text-sm">{message}</span>;
                  }}
                />
              </div>
            )}
            <PopoverContent className="w-auto p-0 mt-0.5" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={date => {
                  field.onChange(date ? formatDate(date as Date, DATE_FORMATS.ISO_DATE) : '');
                }}
                initialFocus
                defaultMonth={field.value ? new Date(field.value) : new Date()}
                toDate={toDate}
              />
            </PopoverContent>
          </Popover>
        );
      }}
    />
  );
}
