'use client';
import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary-brand text-primary-foreground hover:bg-primary-brand/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2.5',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  isLoading?: boolean;
  checkDirty?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, isLoading, checkDirty, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        type="button"
        className={cn(
          buttonVariants({ variant, size, className }),
          isLoading && 'opacity-50',
          checkDirty &&
            'group-[.is-dirty]:opacity-50 group-[.is-dirty]:pointer-events-none group-[.is-dirty]:cursor-not-allowed',
        )}
        ref={ref}
        disabled={isLoading}
        {...props}
      >
        {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
        {props.children}
      </Comp>
    );
  },
);
Button.displayName = 'Button';

const ButtonReset = React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { reset } = useFormContext();
  return <Button ref={ref} {...props} onClick={() => reset()} />;
});
ButtonReset.displayName = 'ButtonReset';

type ButtonSubmitProps = ButtonProps & {
  disableOnError?: boolean;
};

const ButtonSubmit = React.forwardRef<HTMLButtonElement, ButtonSubmitProps>((props, ref) => {
  const {
    formState: { errors },
  } = useFormContext();
  return (
    <Button
      ref={ref}
      {...props}
      disabled={props.disabled || (props.disableOnError && Object.keys(errors).length > 0)}
    />
  );
});
ButtonSubmit.displayName = 'ButtonSubmit';

export { Button, ButtonReset, ButtonSubmit, buttonVariants };
