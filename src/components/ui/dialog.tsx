'use client';

import React, { useEffect, useState } from 'react';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { cn } from '@/lib/utils';

const DialogTrigger = DialogPrimitive.Trigger;

const DialogClose = DialogPrimitive.Close;

const Dialog: React.FC<DialogPrimitive.DialogProps> = ({ open, ...rest }: DialogPrimitive.DialogProps) => {
  useEffect(() => {
    document.body.classList.toggle('dialog-open', open);
    return () => {
      document.body.classList.remove('dialog-open');
    };
  }, [open]);

  return <DialogPrimitive.Root open={open} {...rest} />;
};

const DialogPortal = (props: React.PropsWithChildren) => {
  const [container, setContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    setContainer(document.querySelector('#portal-dialog') as HTMLElement);
  }, []);

  return <DialogPrimitive.Portal container={container} {...props} />;
};

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay ref={ref} className={cn('bg-black/20 absolute inset-0', className)} {...props} />
));
DialogOverlay.displayName = 'DialogOverlay';

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & { isWrapped?: boolean; onClickOutSide?: () => void }
>(({ className, children, isWrapped = true, onClickOutSide, ...props }, ref) => (
  <DialogPortal>
    {isWrapped ? (
      <DialogOverlay
        onClick={() => {
          if (!isWrapped && onClickOutSide) onClickOutSide();
        }}
        className="z-[10]"
      >
        <DialogPrimitive.Content
          ref={ref}
          className={cn(
            'fixed top-0 left-0 w-full h-full p-8 bg-white lg:rounded-2xl shadow-2xl outline-none z-40',
            'lg:top-1/2 lg:left-1/2 lg:-translate-y-1/2 lg:-translate-x-[calc(50%-102.5px)] lg:w-auto lg:h-auto',
            className,
          )}
          {...props}
        >
          {children}
        </DialogPrimitive.Content>
      </DialogOverlay>
    ) : (
      <div
        className="bg-black/20 absolute inset-0"
        onClick={() => {
          if (!isWrapped && onClickOutSide) onClickOutSide();
        }}
      >
        <div
          ref={ref}
          className={cn(
            'fixed top-0 left-0 w-full h-full p-8 bg-white lg:rounded-2xl shadow-2xl outline-none z-40',
            'lg:top-1/2 lg:left-1/2 lg:-translate-y-1/2 lg:-translate-x-[calc(50%-102.5px)] lg:w-auto lg:h-auto',
            className,
          )}
          {...props}
          onClick={event => event.stopPropagation()}
        >
          {children}
        </div>
      </div>
    )}
  </DialogPortal>
));
DialogContent.displayName = 'DialogContent';

const DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('flex flex-col space-y-1.5 text-center sm:text-left', className)} {...props} />
);
DialogHeader.displayName = 'DialogHeader';

const DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2', className)} {...props} />
);
DialogFooter.displayName = 'DialogFooter';

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn('text-lg font-semibold leading-none tracking-tight', className)}
    {...props}
  />
));
DialogTitle.displayName = DialogPrimitive.Title.displayName;

export { Dialog, DialogTrigger, DialogContent, DialogClose, DialogHeader, DialogFooter, DialogTitle };
