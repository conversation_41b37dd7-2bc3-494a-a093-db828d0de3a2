'use client';
import { createContext } from 'react';
import { DecodedIdToken } from 'firebase-admin/auth';

const AuthProvider = createContext({});

type AuthContextValues = {
  user: DecodedIdToken | null;
};

export const AuthContextProvider = ({ children, user }: React.PropsWithChildren<AuthContextValues>) => {
  return <AuthProvider.Provider value={{ user }}>{children}</AuthProvider.Provider>;
};
