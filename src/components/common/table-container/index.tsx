'use client';
import React, { PropsWithChildren, Suspense } from 'react';
import { cn } from '@/lib/utils';

type TableDataContainerProps = {
  showPagination?: boolean;
  className?: string;
};

const TableDataContainer = ({ children, className }: PropsWithChildren<TableDataContainerProps>) => {
  return (
    <Suspense>
      <div className={cn('w-full border rounded-lg pb-4 pt-4.5', className)}>{children}</div>
    </Suspense>
  );
};
export default TableDataContainer;
