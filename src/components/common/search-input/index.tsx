'use client';
import React from 'react';
import * as z from 'zod';
import { Form, Input } from '@/components/ui/form';
import { Search } from 'lucide-react';
import { useSearchQuery } from '@/hooks/useSearchQuery';

const schema = z.object({
  search: z.string().optional(),
});

const SearchInput = () => {
  const { search, changeSearch } = useSearchQuery();
  return (
    <Form schema={schema} onSubmit={() => {}}>
      <Input
        name="search"
        type="text"
        placeholder="Search"
        className="pl-[42px]"
        StartIcon={Search}
        iconClassName="w-5 h-5"
        defaultValue={search}
        onChange={e => {
          changeSearch(e.target.value?.trim());
        }}
      />
    </Form>
  );
};
export default SearchInput;
