'use client';
import React from 'react';
import ReactPaginate from 'react-paginate';
import Image from 'next/image';
import { usePagination } from '@/hooks/usePagination';
import { z } from 'zod';
import { Form, Select } from '@/components/ui/form';
import { SelectItem } from '@/components/ui/form/select';

const schema = z.object({});

type CustomPaginationProps = {
  totalPages: number;
};

const CustomPagination = ({ totalPages }: CustomPaginationProps) => {
  const { page, pageSize, goToPage, changePageSize } = usePagination();

  const handlePageClick = (event: { selected: number }) => {
    goToPage(event.selected + 1);
  };

  return (
    <div className="hidden peer-[.is-done]:!flex flex-col items-end lg:flex-row justify-between px-6 pt-4 border-t">
      <Form schema={schema} onSubmit={() => {}} className="flex w-full lg:w-fit justify-start mb-3 lg:mb-0">
        <Select
          name="pageSize"
          placeholder={`Show ${pageSize} items`}
          className="w-fit rounded-lg text-[#1D2939] font-medium gap-3"
          value={String(pageSize)}
          onValueChange={value => changePageSize(Number(value))}
        >
          <SelectItem value="10">Show 10 items</SelectItem>
          <SelectItem value="20">Show 20 items</SelectItem>
          <SelectItem value="50">Show 50 items</SelectItem>
          <SelectItem value="100">Show 100 items</SelectItem>
        </Select>
      </Form>
      <div className="flex w-fit border rounded-lg text-sm">
        <ReactPaginate
          className="flex items-center"
          pageLinkClassName="h-10 w-10 flex items-center justify-center text-[#1D2939] font-medium"
          pageClassName="text-[#1D2939] font-medium [&:nth-child(odd)]:border-x [&:nth-child(2)]:border-l [&:nth-last-child(2)]:border-r border-input"
          activeClassName="bg-primary-foreground"
          breakLabel="..."
          forcePage={page - 1}
          breakLinkClassName="text-[#1D2939] h-fit leading-none font-medium"
          breakClassName="text-[#1D2939] h-10 w-10 flex items-center justify-center font-medium [&:nth-child(odd)]:border-x [&:nth-child(2)]:border-l border-input"
          nextLabel={<NextPage />}
          onPageChange={handlePageClick}
          pageRangeDisplayed={3}
          pageCount={totalPages}
          previousLabel={<PreviousPage />}
          marginPagesDisplayed={1}
          renderOnZeroPageCount={null}
          nextLinkClassName="flex h-10 items-center justify-center py-2.5 px-4"
          previousLinkClassName="flex h-10 items-center justify-center py-2.5 px-4"
        />
      </div>
    </div>
  );
};

const NextPage = () => {
  return (
    <div className="flex sm:space-x-2">
      <div className="hidden sm:block">Next</div>
      <Image src="/icons/arrow-right.svg" width={20} height={20} alt="next-icon" />
    </div>
  );
};

const PreviousPage = () => {
  return (
    <div className="flex sm:space-x-2">
      <Image src="/icons/arrow-right.svg" width={20} height={20} alt="next-icon" className="rotate-180" />
      <div className="hidden sm:block">Previous</div>
    </div>
  );
};
export default CustomPagination;
