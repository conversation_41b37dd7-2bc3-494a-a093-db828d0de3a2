'use client';

import { getBasePath, useAsPathStore } from '@/hooks/usePathInitializer';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useMemo } from 'react';
import { useShallow } from 'zustand/react/shallow';

type Props = {
  name: string;
  path: string;
};

export const GoBack = ({ name, path }: Props) => {
  const pathname = usePathname();
  const { prevAsPath, pathHistory } = useAsPathStore(
    useShallow(state => ({
      prevAsPath: state.prevAsPath,
      pathHistory: state.pathHistory,
    })),
  );

  const basePathname = getBasePath(pathname);
  const basePrevAsPath = getBasePath(prevAsPath);

  const { href, linkText } = useMemo(() => {
    const key = `${basePathname}-${basePathname}`; //router from list to page detail
    const fallbackKey = `${basePathname}-${basePrevAsPath}`;

    const computedHref = pathHistory[key] || pathHistory[fallbackKey] || path;
    const computedLinkText =
      !basePrevAsPath || basePathname === basePrevAsPath || pathHistory[key] ? `Back to ${name}` : 'Go back';

    return { href: computedHref, linkText: computedLinkText };
  }, [basePathname, basePrevAsPath, pathHistory, path, name]);

  return (
    <Link href={href} className="flex w-fit">
      <Image src="/icons/arrow-left.svg" width={24} height={24} alt="prev-icon" />
      <div className="ml-2">{linkText}</div>
    </Link>
  );
};
