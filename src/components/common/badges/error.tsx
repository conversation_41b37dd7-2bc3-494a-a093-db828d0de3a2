import React, { PropsWithChildren } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

type Props = {
  className?: string;
};

const ErrorBadge = ({ children, className }: PropsWithChildren<Props>) => {
  return (
    <Badge variant="outline" className={cn('bg-[#FEF3F2] text-[#B42318] border-0', className)}>
      {children ?? 'Verified'}
    </Badge>
  );
};
export default ErrorBadge;
