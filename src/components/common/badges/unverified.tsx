import React, { PropsWithChildren } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

type Props = {
  className?: string;
};

const UnverifiedBadge = ({ children, className }: PropsWithChildren<Props>) => {
  return (
    <Badge variant="outline" className={cn('bg-[#F2F4F7] text-[#344054] border-0', className)}>
      {children ?? 'Unverified'}
    </Badge>
  );
};
export default UnverifiedBadge;
