import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { PropsWithChildren } from 'react';

type Props = {
  status: SurveyStatus;
  className?: string;
};

const StatusBadge = ({ status, className, children }: PropsWithChildren<Props>) => {
  let style = '';

  switch (status) {
    case SurveyStatus.Draft:
      style = 'text-[#175CD3] bg-[#EFF8FF]';
      break;
    case SurveyStatus.Active:
      style = 'text-[#027A48] bg-[#ECFDF3]';
      break;
    case SurveyStatus.Expired:
      style = 'text-[#344054] bg-[#F2F4F7]';
  }

  return (
    <Badge variant="secondary" className={cn(style, className)}>
      {children}
    </Badge>
  );
};

export default StatusBadge;
