import React, { PropsWithChildren } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

type Props = {
  className?: string;
};

const InformationBadge = ({ children, className }: PropsWithChildren<Props>) => {
  return (
    <Badge variant="outline" className={cn('bg-[#EFF8FF] text-[#175CD3] border-0', className)}>
      {children}
    </Badge>
  );
};
export default InformationBadge;
