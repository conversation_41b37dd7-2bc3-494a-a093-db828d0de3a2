import React, { PropsWithChildren } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

type Props = {
  className?: string;
};

const VerifiedBadge = ({ children, className }: PropsWithChildren<Props>) => {
  return (
    <Badge variant="outline" className={cn('bg-[#ECFDF3] text-[#027A48] border-0', className)}>
      {children ?? 'Verified'}
    </Badge>
  );
};
export default VerifiedBadge;
