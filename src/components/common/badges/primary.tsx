import React, { PropsWithChildren } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

type Props = {
  className?: string;
  isLoading?: boolean;
};

const PrimaryBadge = ({ children, className, isLoading = false }: PropsWithChildren<Props>) => {
  if (isLoading) {
    return <Skeleton className={cn('min-w-20 h-4 rounded-full', className)} />;
  }

  return (
    <Badge variant="outline" className={cn('bg-[#F9F5FF] text-primary-brand border-0', className)}>
      {children}
    </Badge>
  );
};
export default PrimaryBadge;
