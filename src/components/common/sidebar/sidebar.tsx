'use client';
import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { AlignJustify, X } from 'lucide-react';

import MenuItem from '@/components/common/sidebar/menu-item';
import { cn } from '@/lib/utils';
import { useLogout } from '@/hooks/useLogout';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { Skeleton } from '@/components/ui/skeleton';
const MenuItems = () => {
  const { user, isLoadingCurrentUser } = useCurrentUser();
  const isAdmin = user?.role === AdminRole.Admin;
  const isAccountManager = user?.role === AdminRole.AccountManager;

  if (isLoadingCurrentUser) {
    return Array.from({ length: 5 }).map((_, index) => (
      <Skeleton key={index} className="h-[40px] w-full bg-primary-bold" />
    ));
  }

  if (isAccountManager) {
    return (
      <Link href="/surveys">
        <MenuItem url="/surveys" img="/icons/survey.svg">
          Surveys
        </MenuItem>
      </Link>
    );
  }

  return (
    <>
      <Link href="/users">
        <MenuItem url="/users" img="/icons/users.svg">
          Users
        </MenuItem>
      </Link>

      <Link href="/companies">
        <MenuItem url="/companies" img="/icons/company.svg">
          Companies
        </MenuItem>
      </Link>

      <Link href="/specialties">
        <MenuItem url="/specialties" img="/icons/speciality.svg">
          Specialties
        </MenuItem>
      </Link>

      <Link href="/audiences">
        <MenuItem url="/audiences" img="/icons/audience.svg">
          Audiences
        </MenuItem>
      </Link>

      <Link href="/surveys">
        <MenuItem url="/surveys" img="/icons/survey.svg">
          Surveys
        </MenuItem>
      </Link>

      {isAdmin && (
        <Link href="/available-funds">
          <MenuItem url="/available-funds" img="/icons/fund.svg">
            Available Funds
          </MenuItem>
        </Link>
      )}

      <Link href="/support">
        <MenuItem url="/support" img="/icons/support.svg">
          Support
        </MenuItem>
      </Link>

      <Link href="/referral-codes">
        <MenuItem url="/referral-codes" img="/icons/gift.svg">
          Referral Codes
        </MenuItem>
      </Link>
    </>
  );
};

const FooterMenuItems = () => {
  const { logout } = useLogout();
  const { user, isLoadingCurrentUser } = useCurrentUser();

  if (isLoadingCurrentUser) {
    return Array.from({ length: 5 }).map((_, index) => (
      <Skeleton key={index} className="h-[40px] w-full bg-primary-bold" />
    ));
  }

  return (
    <>
      {user?.role !== AdminRole.AccountManager && (
        <Link href="/activity-log">
          <MenuItem url="/activity-log" img="/icons/activity-log.svg">
            Activity Log
          </MenuItem>
        </Link>
      )}

      <Link href="/settings">
        <MenuItem url="/settings" img="/icons/setting.svg">
          Settings
        </MenuItem>
      </Link>

      <div className="px-4 !my-[18px]">
        <div className="border-b border-[#9F91DD]"></div>
      </div>
      <div className="!mt-0" onClick={() => logout()}>
        <MenuItem url="/logout" img="/icons/log-out.svg">
          Logout
        </MenuItem>
      </div>
    </>
  );
};

const DefaultMenu = () => {
  return (
    <div className="bg-primary-brand hidden sm:block">
      <div className="h-screen fixed bg-primary-brand py-6 flex flex-col justify-between">
        <div className="w-full">
          <div className="px-6">
            <Image src="/icons/logo-text.svg" alt="logo-text" height={35} width={157} className="min-w-[157px]" />
          </div>
          <div className="mt-[107px] px-2 flex flex-col space-y-1.5">
            <MenuItems />
          </div>
        </div>
        <div className="px-2 flex flex-col space-y-1.5">
          <FooterMenuItems />
        </div>
      </div>
    </div>
  );
};

const MobileMenu = () => {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = React.useState(false);

  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  return (
    <>
      <div
        className={cn(
          'absolute h-full bg-primary-brand z-20 transition-width duration-500 ease-in-out overflow-hidden',
          isOpen ? 'w-full' : 'w-0',
        )}
      >
        <div className="w-full h-full m-4">
          <X size={40} className="text-white mb-4 ml-1.5 cursor-pointer" onClick={() => setIsOpen(!isOpen)} />
          <MenuItems />
          <FooterMenuItems />
        </div>
      </div>
      <div className="flex sm:hidden p-4 py-4 items-center justify-between bg-primary-brand">
        <AlignJustify size={40} className="text-white cursor-pointer" onClick={() => setIsOpen(!isOpen)} />
        <Image src="/icons/logo-text.svg" alt="logo" height={35} width={157} className="h-[40px] z-10" />
      </div>
    </>
  );
};

const Sidebar = () => {
  return (
    <>
      <DefaultMenu />
      <MobileMenu />
    </>
  );
};
export default Sidebar;
