'use client';
import React, { PropsWithChildren } from 'react';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';
import Image from 'next/image';

type MenuItemProps = {
  url?: string;
  img: string;
};

const MenuItem = ({ children, url, img }: PropsWithChildren<MenuItemProps>) => {
  const pathname = usePathname();
  return (
    <div
      className={cn(
        'flex items-center text-white py-1.5 px-4 rounded-[5px] cursor-pointer mb-3 last:mb-0',
        url && pathname.startsWith(url) ? 'bg-primary-bold' : 'bg-inherit',
      )}
    >
      <Image src={img} width={18} height={18} alt="menu-icon" className="min-w-1.125" />
      <div className="ml-4 whitespace-nowrap">{children}</div>
    </div>
  );
};
export default MenuItem;
