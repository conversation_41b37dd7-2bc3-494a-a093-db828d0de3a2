import React from 'react';
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { CircleAlert } from 'lucide-react';

type ModalConfirmDeleteProps = {
  openDialog: boolean;
  setOpenDialog: React.Dispatch<React.SetStateAction<boolean>>;
  onConfirm?: () => Promise<void> | void;
  isConfirming?: boolean;
  title?: string;
  subtitle?: React.ReactNode;
  customConfirmButtonLabel?: string;
};

const ModalConfirmDelete = ({
  openDialog,
  setOpenDialog,
  onConfirm,
  isConfirming,
  title,
  subtitle,
  customConfirmButtonLabel,
}: ModalConfirmDeleteProps) => {
  return (
    <Dialog open={openDialog} onOpenChange={() => setOpenDialog(false)}>
      <DialogContent
        className="top-1/2 left-1/2 -translate-y-1/2 p-6 -translate-x-1/2 sm:-translate-x-[calc(50%-100px)] w-full sm:w-auto sm:max-w-[400px] h-auto"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex flex-col justify-center items-start">
          <div className="w-16 h-16 mb-5 rounded-full flex justify-center items-center bg-[#FEF3F2]">
            <div className="w-10 h-10 rounded-full flex justify-center items-center bg-[#FEE4E2]">
              <CircleAlert className="text-[#D92D20]" width={20} height={20} />
            </div>
          </div>
          <div className="text-foreground font-semibold text-lg mb-2">{title ?? 'Are you sure?'}</div>
          <div className="text-sm text-muted-foreground">{subtitle ?? 'This action cannot be reversed.'}</div>
        </div>
        <DialogFooter className="mt-8 flex justify-between">
          <Button
            type="button"
            variant="outline"
            className="h-10 flex-1 text-gray-text font-semibold"
            onClick={e => {
              e.stopPropagation();
              setOpenDialog(false);
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="h-10 bg-red-600 flex-1 font-semibold"
            disabled={isConfirming}
            isLoading={isConfirming}
            onClick={async e => {
              e.stopPropagation();
              onConfirm && (await onConfirm());
              setOpenDialog(false);
            }}
          >
            {customConfirmButtonLabel ?? 'Delete'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default ModalConfirmDelete;
