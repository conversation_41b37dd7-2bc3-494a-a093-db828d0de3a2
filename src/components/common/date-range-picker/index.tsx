import { Button } from '@/components/ui/button';
import { Calendar, CalendarProps } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { DATE_FORMAT, TIME_RANGE_OPTIONS } from '@/utils/constants';
import { PopoverContentProps } from '@radix-ui/react-popover';
import { endOfDay, format, isAfter } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React from 'react';
import { DateRange } from 'react-day-picker';

type DateRangePickerProps = Omit<CalendarProps, 'onDateChange'> & {
  onDateChange: (date?: DateRange) => void;
  initialDate?: DateRange;
  align?: PopoverContentProps['align'];
  sideOffset?: PopoverContentProps['sideOffset'];
  side?: PopoverContentProps['side'];
  disabledDateButton?: boolean;
};

const DateRangePicker = ({
  onDateChange,
  initialDate,
  align = 'start',
  sideOffset = 5,
  side = 'bottom',
  disabledDateButton = false,
}: DateRangePickerProps) => {
  const [date, setDate] = React.useState<DateRange | undefined>(initialDate);
  const [tempDate, setTempDate] = React.useState<DateRange | undefined>(initialDate);
  const [isOpen, setIsOpen] = React.useState(false);

  const handleTimeRangeClick = (option: (typeof TIME_RANGE_OPTIONS)[0]) => {
    const newRange = option.getValue();
    setTempDate(newRange);
  };

  const handleDateChange = (date: DateRange | undefined) => {
    if (date) {
      setDate(date);
      onDateChange(date);
    }
    setIsOpen(false);
  };

  const onApply = () => {
    // If the from date is selected but the to date is not,
    // set the to date to the end of the day
    if (tempDate && tempDate.from && !tempDate.to) {
      const newDate = { ...tempDate, to: endOfDay(tempDate.from) };
      setTempDate(newDate);
      handleDateChange(newDate);
      return;
    }
    handleDateChange(tempDate);
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      setTempDate(date);
    }
    setIsOpen(open);
  };

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          id="date"
          variant={'outline'}
          className={cn('w-[300px] gap-2 justify-start text-left font-semibold', !date && 'text-muted-foreground')}
          disabled={disabledDateButton}
        >
          <CalendarIcon className="h-4 w-4" />
          {date?.from ? (
            date.to ? (
              <>
                {format(date.from, DATE_FORMAT)} - {format(date.to, DATE_FORMAT)}
              </>
            ) : (
              format(date.from, DATE_FORMAT)
            )
          ) : (
            <span>Select Date</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-auto p-0 flex --radix-popover-content-transform-origin"
        sideOffset={sideOffset}
        align={align}
        side={side}
      >
        <div className="border-r border-border">
          <div className="p-2 flex flex-col space-y-1">
            {TIME_RANGE_OPTIONS.map(option => (
              <Button
                key={option.label}
                variant="ghost"
                className={cn('justify-start text-sm font-normal', {
                  'bg-[#F9F5FF] text-primary hover:bg-[#F9F5FF] hover:text-primary':
                    tempDate?.from?.toISOString() === option.getValue().from.toISOString(),
                })}
                onClick={() => handleTimeRangeClick(option)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <Calendar
            key={tempDate?.from?.toISOString()}
            initialFocus
            mode="range"
            defaultMonth={tempDate?.from}
            selected={tempDate}
            onSelect={setTempDate}
            numberOfMonths={2}
            disabled={date => isAfter(date, new Date())}
          />
          <div className="flex justify-between items-center flex-1 gap-2 px-4 pb-4 pt-2 border-t border-border">
            <div className="flex items-center gap-2 ">
              <Button variant="outline" className="text-base font-normal cursor-default hover:bg-transparent">
                {tempDate?.from ? format(tempDate?.from, DATE_FORMAT) : 'Start Date'}
              </Button>{' '}
              -
              <Button variant="outline" className="text-base font-normal cursor-default hover:bg-transparent">
                {tempDate?.to ? format(tempDate?.to, DATE_FORMAT) : 'End Date'}
              </Button>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="outline" className="text-sm" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button onClick={onApply} disabled={!tempDate} className="text-sm">
                Apply
              </Button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default DateRangePicker;
