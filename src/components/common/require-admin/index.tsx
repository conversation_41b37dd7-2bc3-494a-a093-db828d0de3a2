'use client';

import { useCurrentUser } from '@/hooks/useCurrentUser';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { useRouter } from 'next/navigation';
import { ReactNode, useEffect } from 'react';
import LoadingSpinner from '../loading-spinner';

interface RequireAdminProps {
  children: ReactNode;
}

const RequireAdmin = ({ children }: RequireAdminProps) => {
  const { user, isLoadingCurrentUser } = useCurrentUser();
  const router = useRouter();

  useEffect(() => {
    if (!isLoadingCurrentUser && user?.role !== AdminRole.Admin) {
      router.push('/not-found');
    }
  }, [user, isLoadingCurrentUser, router]);

  if (isLoadingCurrentUser) {
    return (
      <div className="flex w-full items-center relative justify-center h-[100dvh]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (user?.role !== AdminRole.Admin) {
    return null;
  }

  return <>{children}</>;
};

export default RequireAdmin;
