const config = {
  NODE_ENV: process.env.NODE_ENV,

  // Firebase configuration
  FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
  FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
  FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
  FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
  FIREBASE_MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID,
  FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
  FIREBASE_CREDENTIALS: process.env.FIREBASE_CREDENTIALS,

  // Token
  ACCESS_TOKEN_SECRET: process.env.ACCESS_TOKEN_SECRET || 'access-token-secret',
  REFRESH_TOKEN_SECRET: process.env.REFRESH_TOKEN_SECRET || 'refresh-token-secret',
  ACCESS_TOKEN_EXPIRE_TIME: Number(process.env.ACCESS_TOKEN_EXPIRE_TIME) || 3900, // 1 hour 5 minutes
  REFRESH_TOKEN_EXPIRE_TIME: Number(process.env.REFRESH_TOKEN_EXPIRE_TIME) || 30 * 24 * 60 * 60, // 30 days

  // Hubspot
  HUBSPOT_ACCESS_TOKEN: process.env.HUBSPOT_ACCESS_TOKEN || '',
  HUBSPOT_CLIENT_SECRET: process.env.HUBSPOT_CLIENT_SECRET || '',
  HUBSPOT_WEBHOOK_URL: process.env.HUBSPOT_WEBHOOK_URL || '',
  HUBSPOT_PORTAL_ID: Number(process.env.HUBSPOT_PORTAL_ID),
  HUBSPOT_CTC_PRODUCT_OBJECT_ID: process.env.HUBSPOT_CTC_PRODUCT_OBJECT_ID || '',
  HUBSPOT_PROGRAM_OBJECT_ID: process.env.HUBSPOT_PROGRAM_OBJECT_ID || '',

  //  Hubspot subscription type IDs
  HUBSPOT_CTC_PRODUCTS_SERVICES_SUBSCRIPTION_ID: process.env.HUBSPOT_CTC_PRODUCTS_SERVICES_SUBSCRIPTION_ID || '',
  HUBSPOT_MARKETING_INFORMATION_SUBSCRIPTION_ID: process.env.HUBSPOT_MARKETING_INFORMATION_SUBSCRIPTION_ID || '',
  HUBSPOT_EDUCATIONAL_PROGRAM_SUBSCRIPTION_ID: process.env.HUBSPOT_EDUCATIONAL_PROGRAM_SUBSCRIPTION_ID || '',
  HUBSPOT_PROGRAM_NOTIFICATIONS_SUBSCRIPTION_ID: process.env.HUBSPOT_PROGRAM_NOTIFICATIONS_SUBSCRIPTION_ID || '',
  HUBSPOT_CUSTOMER_SERVICE_SUBSCRIPTION_ID: process.env.HUBSPOT_CUSTOMER_SERVICE_SUBSCRIPTION_ID || '',
  HUBSPOT_ONE_TO_ONE_SUBSCRIPTION_ID: process.env.HUBSPOT_ONE_TO_ONE_SUBSCRIPTION_ID || '',

  // DB
  POSTGRES_URL: process.env.POSTGRES_URL,

  // API
  API_URL: process.env.API_URL || 'http://localhost:3000/api',

  // Plaid
  PLAID_CLIENT_ID: process.env.PLAID_CLIENT_ID || '',
  PLAID_SECRET: process.env.PLAID_SECRET || '',
  PLAID_ENV: process.env.PLAID_ENV || 'sandbox',

  // Vopay
  VOPAY_ACCOUNT_ID: process.env.VOPAY_ACCOUNT_ID || '',
  VOPAY_API_KEY: process.env.VOPAY_API_KEY || '',
  VOPAY_API_URL: process.env.VOPAY_API_URL || 'https://earthnode-dev.vopay.com/api/v2',
  VOPAY_SECRET: process.env.VOPAY_SECRET || '',

  // Canadapost
  CANADAPOST_API_KEY: process.env.CANADAPOST_API_KEY || '',

  // Vercel Blob
  BLOB_READ_WRITE_TOKEN: process.env.BLOB_READ_WRITE_TOKEN,
  BLOB_URL: process.env.BLOB_URL,

  // Branch
  BRANCH_KEY: process.env.BRANCH_KEY || '',
  BRANCH_API_URL: process.env.BRANCH_API_URL || 'https://api2.branch.io/v1/url',
  BRANCH_DOMAIN: process.env.BRANCH_DOMAIN || '',
  INDUSTRII_USER_VERIFICATION_LINK: process.env.INDUSTRII_USER_VERIFICATION_LINK || '',

  // Cron
  CRON_SECRET: process.env.CRON_SECRET || '',

  // Webapp
  WEBAPP_URL: process.env.WEBAPP_URL || 'http://localhost:4000',

  // Mobile app version
  ANDROID_CURRENT_VERSION: process.env.ANDROID_CURRENT_VERSION || '1.0.0',
  ANDROID_MIN_VERSION: process.env.ANDROID_MIN_VERSION || '1.0.0',
  IOS_CURRENT_VERSION: process.env.IOS_CURRENT_VERSION || '1.0.0',
  IOS_MIN_VERSION: process.env.IOS_MIN_VERSION || '1.0.0',

  // Temp survey id in QR Code
  SURVEY_SLUG_1: process.env.SURVEY_SLUG_1 || '',
  SURVEY_SLUG_2: process.env.SURVEY_SLUG_2 || '',
  SURVEY_ID_IN_SLUG_1: Number(process.env.SURVEY_ID_IN_SLUG_1) || 0,
  SURVEY_ID_IN_SLUG_2: Number(process.env.SURVEY_ID_IN_SLUG_2) || 0,
};

// Bank logo url
export const bankLogos = {
  ins_37: `${config.BLOB_URL}/bank-logos/cibc.png`, // CIBC
  ins_38: `${config.BLOB_URL}/bank-logos/scotiabank.png`, // Scotiabank
  ins_39: `${config.BLOB_URL}/bank-logos/rbc.png`, // RBC Royal Bank
  ins_40: `${config.BLOB_URL}/bank-logos/tangerine.png`, // Tangerine - Personal
  ins_42: `${config.BLOB_URL}/bank-logos/bmo.png`, // BMO Bank of Montreal
  ins_46: `${config.BLOB_URL}/bank-logos/desjardins.png`, // Desjardins
  ins_48: `${config.BLOB_URL}/bank-logos/nbc.png`, // National Bank of Canada
  ins_115575: null, // Vancity - Personal Banking
  ins_120010: `${config.BLOB_URL}/bank-logos/atb.png`, // ATB Online - Personal
  etransfer: `${config.BLOB_URL}/bank-logos/etransfer.png`, // Interac e-Transfer
} as Record<string, string | null>;

export const isDevelopment = config.NODE_ENV === 'development' ? true : false;

export default config;
