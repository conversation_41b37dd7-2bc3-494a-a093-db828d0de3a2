import { Audience } from '@/backend/audiences/entities/Audience';
import { Company } from '@/backend/companies/entities/Company';
import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { SpecialtyTranslation } from '@/backend/specialties/entities/SpecialtyTranslation';
import { SupportRequest } from '@/backend/support/entities/SupportRequest';
import { ScreeningResponse } from '@/backend/surveys/entities/ScreeningResponse';
import { ScreeningResult } from '@/backend/surveys/entities/ScreeningResult';
import { Survey } from '@/backend/surveys/entities/Survey';
import { SurveyAudience } from '@/backend/surveys/entities/SurveyAudience';
import { SurveyQr } from '@/backend/surveys/entities/SurveyQr';
import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import { SurveyQuestionOption } from '@/backend/surveys/entities/SurveyQuestionOption';
import { SurveyQuestionOptionTranslation } from '@/backend/surveys/entities/SurveyQuestionOptionTranslation';
import { SurveyQuestionTranslation } from '@/backend/surveys/entities/SurveyQuestionTranslation';
import { SurveyTranslation } from '@/backend/surveys/entities/SurveyTranslation';
import { City } from '@/backend/users/entities/City';
import { Country } from '@/backend/users/entities/Country';
import { EmploymentStatus } from '@/backend/users/entities/EmploymentStatus';
import { PracticeSetting } from '@/backend/users/entities/PracticeSetting';
import { Province } from '@/backend/users/entities/Province';
import { PublicSurveyAnswer } from '@/backend/users/entities/PublicSurveyAnswer';
import { User } from '@/backend/users/entities/User';
import { UserAction } from '@/backend/users/entities/UserAction';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserAudience } from '@/backend/users/entities/UserAudience';
import { UserDevice } from '@/backend/users/entities/UserDevice';
import { UserImportTask } from '@/backend/users/entities/UserImportTask';
import { UserPaymentMethod } from '@/backend/users/entities/UserPaymentMethods';
import { UserReferralReward } from '@/backend/users/entities/UserReferralReward';
import { UserSubmission } from '@/backend/users/entities/UserSubmission';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import { UserSurveyAnswer } from '@/backend/users/entities/UserSurveyAnswer';
import { UserTransaction } from '@/backend/users/entities/UserTransaction';
import { MobileAppVersion } from '@/backend/versions/entities/MobileAppVersion';

export const entities = [
  Audience,
  City,
  Company,
  Country,
  EmploymentStatus,
  MobileAppVersion,
  PracticeSetting,
  Province,
  PublicReferralCode,
  Specialty,
  SpecialtyTranslation,
  SupportRequest,
  Survey,
  SurveyAudience,
  SurveyQuestion,
  SurveyQuestionOption,
  SurveyQuestionOptionTranslation,
  SurveyQuestionTranslation,
  SurveyTranslation,
  User,
  UserAction,
  UserAdmin,
  UserAudience,
  UserDevice,
  UserImportTask,
  UserPaymentMethod,
  UserReferralReward,
  UserSubmission,
  UserSurvey,
  UserSurveyAnswer,
  UserTransaction,
  PublicSurveyAnswer,
  SurveyQr,
  ScreeningResult,
  ScreeningResponse,
];
