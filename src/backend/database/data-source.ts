import { entities } from '@/backend/database';
import { loadEnvConfig } from '@next/env';
import { DataSource, DataSourceOptions } from 'typeorm';

loadEnvConfig(process.cwd(), process.env.NODE_ENV !== 'production');

const databaseConfig = {
  type: 'postgres',
  url: process.env.POSTGRES_URL,
  entities: [...entities],
  migrations: ['.dist/backend/database/migrations/*.js'],
  synchronize: false,
  migrationsRun: false,
} as DataSourceOptions;

export const dataSource = new DataSource(databaseConfig);

export const openConnection = async () => {
  if (!dataSource.isInitialized) {
    await dataSource.initialize();
  }
  return dataSource;
};

export const closeConnection = async () => {
  if (dataSource.isInitialized) {
    await dataSource.destroy();
  }
};
