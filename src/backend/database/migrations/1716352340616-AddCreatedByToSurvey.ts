import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCreatedByToSurvey1716352340616 implements MigrationInterface {
  name = 'AddCreatedByToSurvey1716352340616';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ADD "createdBy" integer`);
    await queryRunner.query(
      `ALTER TABLE "surveys" ADD CONSTRAINT "FK_0e287721421e8c5bda5df7554ef" FOREIGN KEY ("createdBy") REFERENCES "user_admins"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" DROP CONSTRAINT "FK_0e287721421e8c5bda5df7554ef"`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "createdBy"`);
  }
}
