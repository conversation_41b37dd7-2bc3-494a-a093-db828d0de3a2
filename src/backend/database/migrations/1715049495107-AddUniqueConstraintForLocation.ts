import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueConstraintForLocation1715049495107 implements MigrationInterface {
  name = 'AddUniqueConstraintForLocation1715049495107';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove duplicate cities based on name and provinceId
    await queryRunner.query(`DELETE FROM cities a USING (
            SELECT MIN(ctid) as ctid, name, "provinceId"
            FROM cities 
            GROUP BY (name, "provinceId") HAVING COUNT(*) > 1
        ) b
        WHERE a.name = b.name
        AND a."provinceId" = b."provinceId"
        AND a.ctid <> b.ctid;`);
    await queryRunner.query(`ALTER TABLE "countries" ADD CONSTRAINT "UQ_fa1376321185575cf2226b1491d" UNIQUE ("name")`);
    await queryRunner.query(
      `ALTER TABLE "provinces" ADD CONSTRAINT "UQ_2eaf52711fc36b358f6e3e4943e" UNIQUE ("name", "countryId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "cities" ADD CONSTRAINT "UQ_d8260d8964eee69f5ec90bec88b" UNIQUE ("name", "provinceId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "UQ_d8260d8964eee69f5ec90bec88b"`);
    await queryRunner.query(`ALTER TABLE "provinces" DROP CONSTRAINT "UQ_2eaf52711fc36b358f6e3e4943e"`);
    await queryRunner.query(`ALTER TABLE "countries" DROP CONSTRAINT "UQ_fa1376321185575cf2226b1491d"`);
  }
}
