import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712713256575 implements MigrationInterface {
  name = 'Migrations1712713256575';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_questions" ADD "isMultiLang" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "survey_questions" ADD "locale" character varying(10) NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "locale"`);
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "isMultiLang"`);
  }
}
