import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEnumSurveyStatus1717404546693 implements MigrationInterface {
  name = 'UpdateEnumSurveyStatus1717404546693';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TYPE "public"."surveys_status_enum" RENAME TO "surveys_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."surveys_status_enum" AS ENUM('Active', 'Draft', 'Expired')`);
    await queryRunner.query(
      `ALTER TABLE "surveys" ALTER COLUMN "status" TYPE "public"."surveys_status_enum" USING "status"::text::"public"."surveys_status_enum"`,
    );
    await queryRunner.query(`DROP TYPE "surveys_status_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."surveys_status_enum_old" AS ENUM('Draft', 'Active', 'Expired')`);
    await queryRunner.query(
      `ALTER TABLE "surveys" ALTER COLUMN "status" TYPE "public"."surveys_status_enum_old" USING "status"::text::"public"."surveys_status_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."surveys_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."surveys_status_enum_old" RENAME TO "surveys_status_enum"`);
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" SET DEFAULT 'Draft'`);
  }
}
