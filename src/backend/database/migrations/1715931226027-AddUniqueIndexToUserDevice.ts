import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUniqueIndexToUserDevice1715931226027 implements MigrationInterface {
  name = 'AddUniqueIndexToUserDevice1715931226027';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_devices" DROP CONSTRAINT "UQ_e81c41e04269a2d2152f0d60b5c"`);
    await queryRunner.query(
      `ALTER TABLE "user_devices" ADD CONSTRAINT "UQ_a3699a8ee632fc3527fec16a7d2" UNIQUE ("userId", "deviceId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_devices" DROP CONSTRAINT "UQ_a3699a8ee632fc3527fec16a7d2"`);
    await queryRunner.query(
      `ALTER TABLE "user_devices" ADD CONSTRAINT "UQ_e81c41e04269a2d2152f0d60b5c" UNIQUE ("deviceId")`,
    );
  }
}
