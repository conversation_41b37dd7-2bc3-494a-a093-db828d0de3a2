import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIndexesForSurvey1722496053145 implements MigrationInterface {
  name = 'AddIndexesForSurvey1722496053145';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_a49f081327b9e5442f03221b73" ON "survey_question_options" ("questionId") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_f5ea0adc954a9272d4cce09c59" ON "survey_question_options" ("surveyId") `);
    await queryRunner.query(`CREATE INDEX "IDX_cf60ce3c6d65af9e9493e93a3d" ON "survey_questions" ("surveyId") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_f257a773d63e105c169faec301" ON "survey_question_option_translations" ("questionOptionId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d44ad016a3fbf64d39539a5cff" ON "survey_question_option_translations" ("surveyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_84427c67b0b851aa81a0936029" ON "survey_question_translations" ("questionId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e00d76d097b278cd51a8624777" ON "survey_question_translations" ("surveyId") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_7484892a203cc00dfa7c278319" ON "survey_translations" ("surveyId") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_7484892a203cc00dfa7c278319"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e00d76d097b278cd51a8624777"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_84427c67b0b851aa81a0936029"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d44ad016a3fbf64d39539a5cff"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f257a773d63e105c169faec301"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cf60ce3c6d65af9e9493e93a3d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f5ea0adc954a9272d4cce09c59"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a49f081327b9e5442f03221b73"`);
  }
}
