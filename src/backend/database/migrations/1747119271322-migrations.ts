import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1747119271322 implements MigrationInterface {
  name = 'Migrations1747119271322';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "public_survey_answers" ADD "compensation" numeric(15,2) NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "compensation"`);
  }
}
