import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddVopayTransactionStatusToUserTransaction1717041130864 implements MigrationInterface {
  name = 'AddVopayTransactionStatusToUserTransaction1717041130864';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_transactions_vopaytransactionstatus_enum" AS ENUM('requested', 'pending', 'in progress', 'successful', 'failed', 'cancelled')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transactions" ADD "vopayTransactionStatus" "public"."user_transactions_vopaytransactionstatus_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "expiryDate" TYPE date`);
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "startDate" TYPE TIMESTAMP`);
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "startDate" TYPE date`);
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "vopayTransactionStatus"`);
    await queryRunner.query(`DROP TYPE "public"."user_transactions_vopaytransactionstatus_enum"`);
  }
}
