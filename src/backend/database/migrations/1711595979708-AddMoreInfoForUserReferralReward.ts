import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMoreInfoForUserReferralReward1711595979708 implements MigrationInterface {
  name = 'AddMoreInfoForUserReferralReward1711595979708';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" ADD "isUserRewarded" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" ADD "isReferralUserRewarded" boolean`);
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" ADD "amount" numeric(15,2) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "firebaseUserId" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "firebaseUserId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" DROP COLUMN "amount"`);
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" DROP COLUMN "isReferralUserRewarded"`);
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" DROP COLUMN "isUserRewarded"`);
  }
}
