import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1742133426737 implements MigrationInterface {
  name = 'Migrations1742133426737';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_79ce179c4ac5d606074cd68cf6"`);
    await queryRunner.query(
      `CREATE TYPE "public"."users_verification_status_enum" AS ENUM('Verified', 'Denied', 'Unverified')`,
    );
    await queryRunner.query(`ALTER TABLE "users" ADD "verificationStatus" "public"."users_verification_status_enum"`);
    await queryRunner.query(`ALTER TABLE "users" ADD "note" character varying(500)`);
    await queryRunner.query(
      `CREATE TYPE "public"."users_contact_status_enum" AS ENUM('WSPending', 'WaitForInfo', 'ReviewInfo', 'Complete')`,
    );
    await queryRunner.query(`ALTER TABLE "users" ADD "contactStatus" "public"."users_contact_status_enum"`);
    await queryRunner.query(`ALTER TABLE "users" ADD "isCompleteWS" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "users" ADD "lastLogin" TIMESTAMP`);
    await queryRunner.query(
      `CREATE INDEX "IDX_5b5e9add27dae326ac746e16f6" ON "users" ("deletedAt", "verificationStatus", "city", "practiceSetting", "employmentStatus", "province", "specialtyId") WHERE ("deletedAt" IS NULL AND "verificationStatus" = 'Verified')`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_5b5e9add27dae326ac746e16f6"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "lastLogin"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "isCompleteWS"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "contactStatus"`);
    await queryRunner.query(`DROP TYPE "public"."users_contact_status_enum"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "note"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "verificationStatus"`);
    await queryRunner.query(`DROP TYPE "public"."users_verification_status_enum"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_79ce179c4ac5d606074cd68cf6" ON "users" ("isVerified", "city", "province", "practiceSetting", "employmentStatus", "specialtyId", "deletedAt") WHERE (("deletedAt" IS NULL) AND ("isVerified" = true))`,
    );
  }
}
