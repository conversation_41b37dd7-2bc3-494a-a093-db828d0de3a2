import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeletedAtToSpecialty1712203103284 implements MigrationInterface {
  name = 'AddDeletedAtToSpecialty1712203103284';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "specialties" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "specialties" DROP CONSTRAINT "UQ_565f38f8b0417c7dbd40e429782"`);
    await queryRunner.query(`ALTER TABLE "companies" DROP CONSTRAINT "UQ_3dacbb3eb4f095e29372ff8e131"`);
    await queryRunner.query(`CREATE INDEX "IDX_565f38f8b0417c7dbd40e42978" ON "specialties" ("name") `);
    await queryRunner.query(`CREATE INDEX "IDX_3dacbb3eb4f095e29372ff8e13" ON "companies" ("name") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_3dacbb3eb4f095e29372ff8e13"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_565f38f8b0417c7dbd40e42978"`);
    await queryRunner.query(`ALTER TABLE "companies" ADD CONSTRAINT "UQ_3dacbb3eb4f095e29372ff8e131" UNIQUE ("name")`);
    await queryRunner.query(
      `ALTER TABLE "specialties" ADD CONSTRAINT "UQ_565f38f8b0417c7dbd40e429782" UNIQUE ("name")`,
    );
    await queryRunner.query(`ALTER TABLE "specialties" DROP COLUMN "deletedAt"`);
  }
}
