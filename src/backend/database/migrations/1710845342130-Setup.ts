import { MigrationInterface, QueryRunner } from "typeorm";

export class Setup1710845342130 implements MigrationInterface {
    name = 'Setup1710845342130'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "specialties" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "isEnabled" boolean NOT NULL DEFAULT false, "numberOfUsers" integer NOT NULL DEFAULT '0', "numberOfSurveys" integer NOT NULL DEFAULT '0', "lastSurveyDate" date, "referralValue" numeric(15,2) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_565f38f8b0417c7dbd40e429782" UNIQUE ("name"), CONSTRAINT "PK_ba01cec5aa8ac48778a1d097e98" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "public_referral_codes" ("id" SERIAL NOT NULL, "code" character varying(50) NOT NULL, "value" numeric(15,2) NOT NULL, "expiryDate" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_958c9e890dfa5eeec086661a5de" UNIQUE ("code"), CONSTRAINT "PK_5ce6881cb5483b47142df4c4b1c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_referral_rewards" ("id" SERIAL NOT NULL, "publicReferralCodeId" integer, "userId" integer NOT NULL, "referredBy" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_7ac0cde2675a5aa054aafaaec32" UNIQUE ("userId"), CONSTRAINT "REL_7ac0cde2675a5aa054aafaaec3" UNIQUE ("userId"), CONSTRAINT "PK_3273a9b4304ca3f980acaef8442" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0fe58234388f333ace54b2cce2" ON "user_referral_rewards" ("publicReferralCodeId") `);
        await queryRunner.query(`CREATE INDEX "IDX_2a747dc1dafbc2346646efa454" ON "user_referral_rewards" ("referredBy") `);
        await queryRunner.query(`CREATE TABLE "users" ("id" SERIAL NOT NULL, "firstName" character varying(50), "lastName" character varying(50), "email" character varying(50), "phone" character varying(50), "firebaseUserId" character varying(150) NOT NULL, "licenseNumber" character varying(100), "isVerified" boolean NOT NULL DEFAULT false, "address" character varying(255), "city" character varying(50), "province" character varying(50), "country" character varying(50), "postalCode" character varying(50), "birthday" date, "practiceSetting" character varying(50), "employmentStatus" character varying(50), "specialtyId" integer, "balance" numeric(15,2) NOT NULL DEFAULT '0', "referralCode" character varying(50), "deletedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_1ebf192be0cd17d1c6f88367855" UNIQUE ("firebaseUserId"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_transactions_type_enum" AS ENUM('Withdrawal', 'Compensation', 'ReferralSuccess', 'ReferralReward')`);
        await queryRunner.query(`CREATE TABLE "user_transactions" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "type" "public"."user_transactions_type_enum" NOT NULL, "description" character varying(255), "amount" numeric(15,2) NOT NULL DEFAULT '0', "refId" character varying(255), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_21325240e8a1f55f22a6f35df4f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_75b6267235a8239114c21d9251" ON "user_transactions" ("userId") `);
        await queryRunner.query(`CREATE TABLE "companies" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "surveysInProgress" integer NOT NULL DEFAULT '0', "surveysCompleted" integer NOT NULL DEFAULT '0', "lastSurveyDate" date, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_3dacbb3eb4f095e29372ff8e131" UNIQUE ("name"), CONSTRAINT "PK_d4bc3e82a314fa9e29f652c2c22" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."surveys_status_enum" AS ENUM('Draft', 'Active', 'Expired', 'Cancelled')`);
        await queryRunner.query(`CREATE TABLE "surveys" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "locale" character varying(10) NOT NULL, "title" character varying(500) NOT NULL, "description" text, "compensation" numeric(15,2) NOT NULL, "maxParticipants" integer NOT NULL, "time" integer NOT NULL, "expiryDate" TIMESTAMP NOT NULL, "image" text, "backgroundImage" text, "isPinned" boolean NOT NULL DEFAULT false, "status" "public"."surveys_status_enum" NOT NULL DEFAULT 'Draft', "successfulCompletions" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_1b5e3d4aaeb2321ffa98498c971" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."survey_questions_questiontype_enum" AS ENUM('SingleSelection', 'MultipleSelection', 'Text', 'Number', 'Date', 'Slider', 'Rank')`);
        await queryRunner.query(`CREATE TABLE "survey_questions" ("id" SERIAL NOT NULL, "surveyId" integer NOT NULL, "questionType" "public"."survey_questions_questiontype_enum" NOT NULL, "title" character varying(4000) NOT NULL, "subtitle" character varying(4000), "minValue" integer, "maxValue" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_131815624efb0f0e15a220102de" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "survey_question_options" ("id" SERIAL NOT NULL, "questionId" integer NOT NULL, "title" character varying(4000) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_96b36bfeadb4f9c25bfb6348dcc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_surveys" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "surveyId" integer NOT NULL, "isComplete" boolean NOT NULL DEFAULT false, "completeDate" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_a716de9b9109324b0978f8adc7f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0a1c058cfeedff5939951fe158" ON "user_surveys" ("userId") `);
        await queryRunner.query(`CREATE INDEX "IDX_b41534a2c2a65b1f7f8abe7ef8" ON "user_surveys" ("surveyId") `);
        await queryRunner.query(`CREATE TABLE "user_survey_answers" ("id" SERIAL NOT NULL, "userSurveyId" integer NOT NULL, "questionId" integer NOT NULL, "questionOptionId" integer, "value" text NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_1cbb6b8fbc3d7f64a6d9bd8eff3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_1c2bdbd66f7b39daa0ced72d76" ON "user_survey_answers" ("userSurveyId") `);
        await queryRunner.query(`CREATE INDEX "IDX_dcbb1f21d077a273302b7a0124" ON "user_survey_answers" ("questionId") `);
        await queryRunner.query(`CREATE INDEX "IDX_f84a69e5758dda09df2bb72959" ON "user_survey_answers" ("questionOptionId") `);
        await queryRunner.query(`CREATE TYPE "public"."user_payment_methods_type_enum" AS ENUM('Plaid', 'Etransfer')`);
        await queryRunner.query(`CREATE TABLE "user_payment_methods" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "type" "public"."user_payment_methods_type_enum" NOT NULL, "token" character varying(150) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_3baaffbd29bfd03e2c9e9a1fd24" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "audiences" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "specialtyId" integer NOT NULL, "isVisible" boolean NOT NULL DEFAULT false, "numberOfUsers" integer NOT NULL DEFAULT '0', "lastSurveyDate" date, "filter" jsonb NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_14afbc90dad7bb98168f3c58985" UNIQUE ("name"), CONSTRAINT "PK_d87c8fe1be471372dd505a9dc1f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_5983e8858a3b1ad37047dfdca2" ON "audiences" ("specialtyId") `);
        await queryRunner.query(`CREATE TABLE "user_audiences" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "audienceId" integer NOT NULL, CONSTRAINT "PK_00b086720b4f1fd758faef1c3d6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0caa2dc4994e3c7ad07b203ed5" ON "user_audiences" ("userId") `);
        await queryRunner.query(`CREATE INDEX "IDX_c88600f94252fa6ba89a3ce3cb" ON "user_audiences" ("audienceId") `);
        await queryRunner.query(`CREATE TYPE "public"."user_admins_role_enum" AS ENUM('Admin', 'Editor')`);
        await queryRunner.query(`CREATE TABLE "user_admins" ("id" SERIAL NOT NULL, "firstName" character varying(50), "lastName" character varying(50), "email" character varying(50), "phone" character varying(50), "firebaseUserId" character varying(150) NOT NULL, "role" "public"."user_admins_role_enum" NOT NULL DEFAULT 'Admin', "deletedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_790799066f595507f9c66095010" UNIQUE ("firebaseUserId"), CONSTRAINT "PK_d2acc43f0f0d6dad76c4299da5e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_actions" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "description" character varying(500) NOT NULL, "previousMetadata" jsonb NOT NULL, "newMetadata" jsonb NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_3c8a683381b553ee59ce5b7b13a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "survey_translations" ("id" SERIAL NOT NULL, "surveyId" integer NOT NULL, "title" character varying(500) NOT NULL, "description" text, "locale" character varying(10) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_36cf263cef8fbbcb6807d2e2ff0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "survey_question_option_translations" ("id" SERIAL NOT NULL, "questionOptionId" integer NOT NULL, "locale" character varying(10) NOT NULL, "title" character varying(4000) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_6c004457d806cbffd1342bd6ccc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "survey_question_translations" ("id" SERIAL NOT NULL, "questionId" integer NOT NULL, "locale" character varying(10) NOT NULL, "title" character varying(4000) NOT NULL, "subtitle" character varying(4000), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_eed84839d6d5707f88919e804c3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "survey_audiences" ("id" SERIAL NOT NULL, "surveyId" integer NOT NULL, "audienceId" integer NOT NULL, CONSTRAINT "PK_06ad0a176da5f04e1d6f0f02658" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_5fafdb5558fbf8a41024660fc2" ON "survey_audiences" ("surveyId") `);
        await queryRunner.query(`CREATE INDEX "IDX_6efa61166a9ea123225010e830" ON "survey_audiences" ("audienceId") `);
        await queryRunner.query(`CREATE TYPE "public"."support_requests_status_enum" AS ENUM('Open', 'Closed')`);
        await queryRunner.query(`CREATE TABLE "support_requests" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "email" character varying(50) NOT NULL, "title" character varying(500) NOT NULL, "description" text, "status" "public"."support_requests_status_enum" NOT NULL DEFAULT 'Open', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_a9ce3cdbad0970597a07ab5db97" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_bf7c96e552aaed8dd4f691ccea" ON "support_requests" ("userId") `);
        await queryRunner.query(`CREATE TABLE "specialty_translations" ("id" SERIAL NOT NULL, "specialtyId" integer NOT NULL, "name" character varying NOT NULL, "locale" character varying(10) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_fa0289730bcb20a96d81be1d4a6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_563e60ae38cc35aab8abada141" ON "specialty_translations" ("specialtyId") `);
        await queryRunner.query(`CREATE TABLE "countries" ("id" SERIAL NOT NULL, "name" character varying(50) NOT NULL, "code" character varying(20) NOT NULL, CONSTRAINT "PK_b2d7006793e8697ab3ae2deff18" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "provinces" ("id" SERIAL NOT NULL, "countryId" integer NOT NULL, "name" character varying(50) NOT NULL, "code" character varying(20) NOT NULL, CONSTRAINT "PK_2e4260eedbcad036ec53222e0c7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "practice_settings" ("id" SERIAL NOT NULL, "name" character varying(50) NOT NULL, CONSTRAINT "PK_c205d95b91f7faa0495b75ab834" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "employment_status" ("id" SERIAL NOT NULL, "name" character varying(50) NOT NULL, CONSTRAINT "PK_ef8372c9f94ca3ceb0a2983db15" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "cities" ("id" SERIAL NOT NULL, "provinceId" integer NOT NULL, "name" character varying(50) NOT NULL, CONSTRAINT "PK_4762ffb6e5d198cfec5606bc11e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "user_referral_rewards" ADD CONSTRAINT "FK_0fe58234388f333ace54b2cce26" FOREIGN KEY ("publicReferralCodeId") REFERENCES "public_referral_codes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_referral_rewards" ADD CONSTRAINT "FK_7ac0cde2675a5aa054aafaaec32" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_referral_rewards" ADD CONSTRAINT "FK_2a747dc1dafbc2346646efa4543" FOREIGN KEY ("referredBy") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_1dc69d0524c62da152bc8131539" FOREIGN KEY ("specialtyId") REFERENCES "specialties"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_transactions" ADD CONSTRAINT "FK_75b6267235a8239114c21d92519" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "surveys" ADD CONSTRAINT "FK_8a1ac2a8cde86f0343745cd23e6" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "survey_questions" ADD CONSTRAINT "FK_cf60ce3c6d65af9e9493e93a3de" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "survey_question_options" ADD CONSTRAINT "FK_a49f081327b9e5442f03221b731" FOREIGN KEY ("questionId") REFERENCES "survey_questions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_surveys" ADD CONSTRAINT "FK_0a1c058cfeedff5939951fe1581" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_surveys" ADD CONSTRAINT "FK_b41534a2c2a65b1f7f8abe7ef8b" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_survey_answers" ADD CONSTRAINT "FK_1c2bdbd66f7b39daa0ced72d763" FOREIGN KEY ("userSurveyId") REFERENCES "user_surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_survey_answers" ADD CONSTRAINT "FK_dcbb1f21d077a273302b7a0124a" FOREIGN KEY ("questionId") REFERENCES "survey_questions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_survey_answers" ADD CONSTRAINT "FK_f84a69e5758dda09df2bb729599" FOREIGN KEY ("questionOptionId") REFERENCES "survey_question_options"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD CONSTRAINT "FK_de52ee40aabfbe3eef30992bf65" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "audiences" ADD CONSTRAINT "FK_5983e8858a3b1ad37047dfdca20" FOREIGN KEY ("specialtyId") REFERENCES "specialties"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_audiences" ADD CONSTRAINT "FK_0caa2dc4994e3c7ad07b203ed55" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_audiences" ADD CONSTRAINT "FK_c88600f94252fa6ba89a3ce3cbc" FOREIGN KEY ("audienceId") REFERENCES "audiences"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_actions" ADD CONSTRAINT "FK_e65a8053e5b02e0b89947b6bac9" FOREIGN KEY ("userId") REFERENCES "user_admins"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "survey_translations" ADD CONSTRAINT "FK_7484892a203cc00dfa7c2783190" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "survey_question_option_translations" ADD CONSTRAINT "FK_f257a773d63e105c169faec3015" FOREIGN KEY ("questionOptionId") REFERENCES "survey_question_options"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "survey_question_translations" ADD CONSTRAINT "FK_84427c67b0b851aa81a09360299" FOREIGN KEY ("questionId") REFERENCES "survey_questions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "survey_audiences" ADD CONSTRAINT "FK_5fafdb5558fbf8a41024660fc2f" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "survey_audiences" ADD CONSTRAINT "FK_6efa61166a9ea123225010e830e" FOREIGN KEY ("audienceId") REFERENCES "audiences"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "support_requests" ADD CONSTRAINT "FK_bf7c96e552aaed8dd4f691ccead" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "specialty_translations" ADD CONSTRAINT "FK_563e60ae38cc35aab8abada1411" FOREIGN KEY ("specialtyId") REFERENCES "specialties"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "provinces" ADD CONSTRAINT "FK_0a994c2ff2af686951495418a3b" FOREIGN KEY ("countryId") REFERENCES "countries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cities" ADD CONSTRAINT "FK_a7c1a801700048901c8e86d1a9e" FOREIGN KEY ("provinceId") REFERENCES "provinces"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "FK_a7c1a801700048901c8e86d1a9e"`);
        await queryRunner.query(`ALTER TABLE "provinces" DROP CONSTRAINT "FK_0a994c2ff2af686951495418a3b"`);
        await queryRunner.query(`ALTER TABLE "specialty_translations" DROP CONSTRAINT "FK_563e60ae38cc35aab8abada1411"`);
        await queryRunner.query(`ALTER TABLE "support_requests" DROP CONSTRAINT "FK_bf7c96e552aaed8dd4f691ccead"`);
        await queryRunner.query(`ALTER TABLE "survey_audiences" DROP CONSTRAINT "FK_6efa61166a9ea123225010e830e"`);
        await queryRunner.query(`ALTER TABLE "survey_audiences" DROP CONSTRAINT "FK_5fafdb5558fbf8a41024660fc2f"`);
        await queryRunner.query(`ALTER TABLE "survey_question_translations" DROP CONSTRAINT "FK_84427c67b0b851aa81a09360299"`);
        await queryRunner.query(`ALTER TABLE "survey_question_option_translations" DROP CONSTRAINT "FK_f257a773d63e105c169faec3015"`);
        await queryRunner.query(`ALTER TABLE "survey_translations" DROP CONSTRAINT "FK_7484892a203cc00dfa7c2783190"`);
        await queryRunner.query(`ALTER TABLE "user_actions" DROP CONSTRAINT "FK_e65a8053e5b02e0b89947b6bac9"`);
        await queryRunner.query(`ALTER TABLE "user_audiences" DROP CONSTRAINT "FK_c88600f94252fa6ba89a3ce3cbc"`);
        await queryRunner.query(`ALTER TABLE "user_audiences" DROP CONSTRAINT "FK_0caa2dc4994e3c7ad07b203ed55"`);
        await queryRunner.query(`ALTER TABLE "audiences" DROP CONSTRAINT "FK_5983e8858a3b1ad37047dfdca20"`);
        await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP CONSTRAINT "FK_de52ee40aabfbe3eef30992bf65"`);
        await queryRunner.query(`ALTER TABLE "user_survey_answers" DROP CONSTRAINT "FK_f84a69e5758dda09df2bb729599"`);
        await queryRunner.query(`ALTER TABLE "user_survey_answers" DROP CONSTRAINT "FK_dcbb1f21d077a273302b7a0124a"`);
        await queryRunner.query(`ALTER TABLE "user_survey_answers" DROP CONSTRAINT "FK_1c2bdbd66f7b39daa0ced72d763"`);
        await queryRunner.query(`ALTER TABLE "user_surveys" DROP CONSTRAINT "FK_b41534a2c2a65b1f7f8abe7ef8b"`);
        await queryRunner.query(`ALTER TABLE "user_surveys" DROP CONSTRAINT "FK_0a1c058cfeedff5939951fe1581"`);
        await queryRunner.query(`ALTER TABLE "survey_question_options" DROP CONSTRAINT "FK_a49f081327b9e5442f03221b731"`);
        await queryRunner.query(`ALTER TABLE "survey_questions" DROP CONSTRAINT "FK_cf60ce3c6d65af9e9493e93a3de"`);
        await queryRunner.query(`ALTER TABLE "surveys" DROP CONSTRAINT "FK_8a1ac2a8cde86f0343745cd23e6"`);
        await queryRunner.query(`ALTER TABLE "user_transactions" DROP CONSTRAINT "FK_75b6267235a8239114c21d92519"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_1dc69d0524c62da152bc8131539"`);
        await queryRunner.query(`ALTER TABLE "user_referral_rewards" DROP CONSTRAINT "FK_2a747dc1dafbc2346646efa4543"`);
        await queryRunner.query(`ALTER TABLE "user_referral_rewards" DROP CONSTRAINT "FK_7ac0cde2675a5aa054aafaaec32"`);
        await queryRunner.query(`ALTER TABLE "user_referral_rewards" DROP CONSTRAINT "FK_0fe58234388f333ace54b2cce26"`);
        await queryRunner.query(`DROP TABLE "cities"`);
        await queryRunner.query(`DROP TABLE "employment_status"`);
        await queryRunner.query(`DROP TABLE "practice_settings"`);
        await queryRunner.query(`DROP TABLE "provinces"`);
        await queryRunner.query(`DROP TABLE "countries"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_563e60ae38cc35aab8abada141"`);
        await queryRunner.query(`DROP TABLE "specialty_translations"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bf7c96e552aaed8dd4f691ccea"`);
        await queryRunner.query(`DROP TABLE "support_requests"`);
        await queryRunner.query(`DROP TYPE "public"."support_requests_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6efa61166a9ea123225010e830"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5fafdb5558fbf8a41024660fc2"`);
        await queryRunner.query(`DROP TABLE "survey_audiences"`);
        await queryRunner.query(`DROP TABLE "survey_question_translations"`);
        await queryRunner.query(`DROP TABLE "survey_question_option_translations"`);
        await queryRunner.query(`DROP TABLE "survey_translations"`);
        await queryRunner.query(`DROP TABLE "user_actions"`);
        await queryRunner.query(`DROP TABLE "user_admins"`);
        await queryRunner.query(`DROP TYPE "public"."user_admins_role_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c88600f94252fa6ba89a3ce3cb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0caa2dc4994e3c7ad07b203ed5"`);
        await queryRunner.query(`DROP TABLE "user_audiences"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5983e8858a3b1ad37047dfdca2"`);
        await queryRunner.query(`DROP TABLE "audiences"`);
        await queryRunner.query(`DROP TABLE "user_payment_methods"`);
        await queryRunner.query(`DROP TYPE "public"."user_payment_methods_type_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f84a69e5758dda09df2bb72959"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dcbb1f21d077a273302b7a0124"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1c2bdbd66f7b39daa0ced72d76"`);
        await queryRunner.query(`DROP TABLE "user_survey_answers"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b41534a2c2a65b1f7f8abe7ef8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0a1c058cfeedff5939951fe158"`);
        await queryRunner.query(`DROP TABLE "user_surveys"`);
        await queryRunner.query(`DROP TABLE "survey_question_options"`);
        await queryRunner.query(`DROP TABLE "survey_questions"`);
        await queryRunner.query(`DROP TYPE "public"."survey_questions_questiontype_enum"`);
        await queryRunner.query(`DROP TABLE "surveys"`);
        await queryRunner.query(`DROP TYPE "public"."surveys_status_enum"`);
        await queryRunner.query(`DROP TABLE "companies"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_75b6267235a8239114c21d9251"`);
        await queryRunner.query(`DROP TABLE "user_transactions"`);
        await queryRunner.query(`DROP TYPE "public"."user_transactions_type_enum"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2a747dc1dafbc2346646efa454"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0fe58234388f333ace54b2cce2"`);
        await queryRunner.query(`DROP TABLE "user_referral_rewards"`);
        await queryRunner.query(`DROP TABLE "public_referral_codes"`);
        await queryRunner.query(`DROP TABLE "specialties"`);
    }

}
