import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1749803808019 implements MigrationInterface {
  name = 'Migrations1749803808019';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_referral_rewards" ADD "isCongratulationDisplayed" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" ALTER COLUMN "amount" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" ALTER COLUMN "amount" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_referral_rewards" DROP COLUMN "isCongratulationDisplayed"`);
  }
}
