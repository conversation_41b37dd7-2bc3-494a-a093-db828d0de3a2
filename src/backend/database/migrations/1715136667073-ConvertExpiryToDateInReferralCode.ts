import { MigrationInterface, QueryRunner } from 'typeorm';

export class ConvertExpiryToDateInReferralCode1715136667073 implements MigrationInterface {
  name = 'ConvertExpiryToDateInReferralCode1715136667073';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_referral_codes" DROP COLUMN "expiryDate"`);
    await queryRunner.query(`ALTER TABLE "public_referral_codes" ADD "expiryDate" date`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_referral_codes" DROP COLUMN "expiryDate"`);
    await queryRunner.query(`ALTER TABLE "public_referral_codes" ADD "expiryDate" TIMESTAMP`);
  }
}
