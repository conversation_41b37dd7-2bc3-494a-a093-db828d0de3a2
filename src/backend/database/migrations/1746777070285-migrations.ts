import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1746777070285 implements MigrationInterface {
  name = 'Migrations1746777070285';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."user_admins_role_enum" RENAME TO "user_admins_role_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."user_admins_role_enum" AS ENUM('Admin', 'Editor', 'AccountManager')`,
    );
    await queryRunner.query(`ALTER TABLE "user_admins" ALTER COLUMN "role" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "user_admins" ALTER COLUMN "role" TYPE "public"."user_admins_role_enum" USING "role"::"text"::"public"."user_admins_role_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "user_admins" ALTER COLUMN "role" SET DEFAULT 'Admin'`);
    await queryRunner.query(`DROP TYPE "public"."user_admins_role_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."user_admins_role_enum_old" AS ENUM('Admin', 'Editor')`);
    await queryRunner.query(`ALTER TABLE "user_admins" ALTER COLUMN "role" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "user_admins" ALTER COLUMN "role" TYPE "public"."user_admins_role_enum_old" USING "role"::"text"::"public"."user_admins_role_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "user_admins" ALTER COLUMN "role" SET DEFAULT 'Admin'`);
    await queryRunner.query(`DROP TYPE "public"."user_admins_role_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."user_admins_role_enum_old" RENAME TO "user_admins_role_enum"`);
  }
}
