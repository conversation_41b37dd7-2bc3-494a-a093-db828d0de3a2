import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712723810210 implements MigrationInterface {
  name = 'Migrations1712723810210';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_a6f49103285c12efb32943f8cc"`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "name"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ADD "name" character varying(255) NOT NULL`);
    await queryRunner.query(`CREATE INDEX "IDX_a6f49103285c12efb32943f8cc" ON "surveys" ("name") `);
  }
}
