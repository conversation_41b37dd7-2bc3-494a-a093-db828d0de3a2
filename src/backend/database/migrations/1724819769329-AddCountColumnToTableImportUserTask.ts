import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCountColumnToTableImportUserTask1724819769329 implements MigrationInterface {
  name = 'AddCountColumnToTableImportUserTask1724819769329';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_import_tasks" ADD "count" integer NOT NULL DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_import_tasks" DROP COLUMN "count"`);
  }
}
