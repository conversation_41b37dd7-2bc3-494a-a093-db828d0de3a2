import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1750925879553 implements MigrationInterface {
  name = 'Migrations1750925879553';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."users_usertype_enum" AS ENUM('ActualUser', 'Client', 'ClientImported', 'UserImported', 'NotVerified', 'Denied', 'Internal')`,
    );
    await queryRunner.query(`ALTER TABLE "users" ADD "userType" "public"."users_usertype_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "userType"`);
    await queryRunner.query(`DROP TYPE "public"."users_usertype_enum"`);
  }
}
