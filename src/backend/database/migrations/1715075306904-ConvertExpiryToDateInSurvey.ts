import { MigrationInterface, QueryRunner } from 'typeorm';

export class ConvertExpiryToDateInSurvey1715075306904 implements MigrationInterface {
  name = 'ConvertExpiryToDateInSurvey1715075306904';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "expiryDate" TYPE date`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
  }
}
