import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHubspotTicketInfoToSupportRequest1712736060384 implements MigrationInterface {
  name = 'AddHubspotTicketInfoToSupportRequest1712736060384';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "support_requests" ADD "hubspotTicketId" character varying(50)`);
    await queryRunner.query(`ALTER TABLE "support_requests" ADD "hubspotTicketUrl" character varying(50)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "support_requests" DROP COLUMN "hubspotTicketUrl"`);
    await queryRunner.query(`ALTER TABLE "support_requests" DROP COLUMN "hubspotTicketId"`);
  }
}
