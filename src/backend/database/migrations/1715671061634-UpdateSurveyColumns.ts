import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSurveyColumns1715671061634 implements MigrationInterface {
  name = 'UpdateSurveyColumns1715671061634';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_surveys" DROP CONSTRAINT "FK_b41534a2c2a65b1f7f8abe7ef8b"`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "isUnlimited"`);
    await queryRunner.query(`CREATE TYPE "public"."survey_question_options_locale_enum" AS ENUM('En', 'Fr')`);
    await queryRunner.query(
      `ALTER TABLE "survey_question_options" ADD "locale" "public"."survey_question_options_locale_enum" NOT NULL DEFAULT 'En'`,
    );
    await queryRunner.query(`ALTER TABLE "audiences" ALTER COLUMN "name" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "audiences" ALTER COLUMN "isVisible" SET DEFAULT true`);
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "maxParticipants" DROP NOT NULL`);
    await queryRunner.query(`ALTER TYPE "public"."surveys_status_enum" RENAME TO "surveys_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."surveys_status_enum" AS ENUM('Draft', 'Active', 'Expired')`);
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "surveys" ALTER COLUMN "status" TYPE "public"."surveys_status_enum" USING "status"::"text"::"public"."surveys_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" SET DEFAULT 'Draft'`);
    await queryRunner.query(`DROP TYPE "public"."surveys_status_enum_old"`);
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "locale"`);
    await queryRunner.query(`CREATE TYPE "public"."survey_questions_locale_enum" AS ENUM('En', 'Fr')`);
    await queryRunner.query(
      `ALTER TABLE "survey_questions" ADD "locale" "public"."survey_questions_locale_enum" NOT NULL DEFAULT 'En'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_surveys" ADD CONSTRAINT "FK_b41534a2c2a65b1f7f8abe7ef8b" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_surveys" DROP CONSTRAINT "FK_b41534a2c2a65b1f7f8abe7ef8b"`);
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "locale"`);
    await queryRunner.query(`DROP TYPE "public"."survey_questions_locale_enum"`);
    await queryRunner.query(`ALTER TABLE "survey_questions" ADD "locale" character varying(10) NOT NULL`);
    await queryRunner.query(
      `CREATE TYPE "public"."surveys_status_enum_old" AS ENUM('Draft', 'Active', 'Expired', 'Cancelled')`,
    );
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "surveys" ALTER COLUMN "status" TYPE "public"."surveys_status_enum_old" USING "status"::"text"::"public"."surveys_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" SET DEFAULT 'Draft'`);
    await queryRunner.query(`DROP TYPE "public"."surveys_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."surveys_status_enum_old" RENAME TO "surveys_status_enum"`);
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "maxParticipants" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "audiences" ALTER COLUMN "isVisible" SET DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "audiences" ALTER COLUMN "name" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_question_options" DROP COLUMN "locale"`);
    await queryRunner.query(`DROP TYPE "public"."survey_question_options_locale_enum"`);
    await queryRunner.query(`ALTER TABLE "surveys" ADD "isUnlimited" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `ALTER TABLE "user_surveys" ADD CONSTRAINT "FK_b41534a2c2a65b1f7f8abe7ef8b" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
