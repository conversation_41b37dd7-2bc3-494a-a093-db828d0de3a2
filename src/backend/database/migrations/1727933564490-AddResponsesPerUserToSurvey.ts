import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddResponsesPerUserToSurvey1727933564490 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ADD COLUMN "responsesPerUser" integer NOT NULL DEFAULT 1`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "responsesPerUser"`);
  }
}
