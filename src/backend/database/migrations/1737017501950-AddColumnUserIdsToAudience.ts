import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnUserIdsToAudience1737017501950 implements MigrationInterface {
  name = 'AddColumnUserIdsToAudience1737017501950';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_surveys" DROP COLUMN "completeDate"`);
    await queryRunner.query(`ALTER TABLE "user_surveys" DROP COLUMN "hubspotProgramId"`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "userIds" integer array`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "userIds"`);
    await queryRunner.query(`ALTER TABLE "user_surveys" ADD "hubspotProgramId" character varying(50)`);
    await queryRunner.query(`ALTER TABLE "user_surveys" ADD "completeDate" TIMESTAMP`);
  }
}
