import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserDeviceTable1715239882974 implements MigrationInterface {
  name = 'AddUserDeviceTable1715239882974';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."user_devices_tokenprovider_enum" AS ENUM('Firebase')`);
    await queryRunner.query(`CREATE TYPE "public"."user_devices_platform_enum" AS ENUM('Android', 'iOS')`);
    await queryRunner.query(
      `CREATE TABLE "user_devices" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "token" character varying NOT NULL, "deviceId" character varying NOT NULL, "enabled" boolean NOT NULL DEFAULT true, "tokenProvider" "public"."user_devices_tokenprovider_enum" NOT NULL DEFAULT 'Firebase', "platform" "public"."user_devices_platform_enum" NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_e81c41e04269a2d2152f0d60b5c" UNIQUE ("deviceId"), CONSTRAINT "PK_c9e7e648903a9e537347aba4371" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "users" ADD "notificationEnabled" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `ALTER TABLE "user_devices" ADD CONSTRAINT "FK_e12ac4f8016243ac71fd2e415af" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_devices" DROP CONSTRAINT "FK_e12ac4f8016243ac71fd2e415af"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "notificationEnabled"`);
    await queryRunner.query(`DROP TABLE "user_devices"`);
    await queryRunner.query(`DROP TYPE "public"."user_devices_platform_enum"`);
    await queryRunner.query(`DROP TYPE "public"."user_devices_tokenprovider_enum"`);
  }
}
