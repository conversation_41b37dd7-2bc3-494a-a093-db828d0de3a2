import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1749543327488 implements MigrationInterface {
  name = 'Migrations1749543327488';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "screening_results" ("id" SERIAL NOT NULL, "surveyId" integer NOT NULL, "userId" integer, "email" character varying(200), "isPassed" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_71818947d4264f0b79e0a5bae42" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_6cc64db46a361ca95e36bbfc74" ON "screening_results" ("surveyId") `);
    await queryRunner.query(
      `CREATE TABLE "screening_responses" ("id" SERIAL NOT NULL, "screeningResultId" integer NOT NULL, "questionId" integer NOT NULL, "questionOptionId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_03aca12e62415d0dde7961f9c43" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "user_surveys" ADD "isPassScreening" boolean NOT NULL DEFAULT true`);
    await queryRunner.query(`ALTER TABLE "survey_question_options" ADD "isEligible" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `ALTER TABLE "survey_questions" ADD "isMultiSelectionEnabled" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."survey_questions_questiontype_enum" RENAME TO "survey_questions_questiontype_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."survey_questions_questiontype_enum" AS ENUM('SingleSelection', 'MultipleSelection', 'Text', 'Number', 'Date', 'Slider', 'Rank', 'Screening')`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_questions" ALTER COLUMN "questionType" TYPE "public"."survey_questions_questiontype_enum" USING "questionType"::"text"::"public"."survey_questions_questiontype_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."survey_questions_questiontype_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "screening_results" ADD CONSTRAINT "FK_6a5374cd4d161cf76206d0a3a21" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "screening_responses" ADD CONSTRAINT "FK_36672ade5625601d6f5248956cc" FOREIGN KEY ("screeningResultId") REFERENCES "screening_results"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "screening_responses" DROP CONSTRAINT "FK_36672ade5625601d6f5248956cc"`);
    await queryRunner.query(`ALTER TABLE "screening_results" DROP CONSTRAINT "FK_6a5374cd4d161cf76206d0a3a21"`);
    await queryRunner.query(
      `CREATE TYPE "public"."survey_questions_questiontype_enum_old" AS ENUM('SingleSelection', 'MultipleSelection', 'Text', 'Number', 'Date', 'Slider', 'Rank')`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_questions" ALTER COLUMN "questionType" TYPE "public"."survey_questions_questiontype_enum_old" USING "questionType"::"text"::"public"."survey_questions_questiontype_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."survey_questions_questiontype_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."survey_questions_questiontype_enum_old" RENAME TO "survey_questions_questiontype_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "isMultiSelectionEnabled"`);
    await queryRunner.query(`ALTER TABLE "survey_question_options" DROP COLUMN "isEligible"`);
    await queryRunner.query(`ALTER TABLE "user_surveys" DROP COLUMN "isPassScreening"`);
    await queryRunner.query(`DROP TABLE "screening_responses"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6cc64db46a361ca95e36bbfc74"`);
    await queryRunner.query(`DROP TABLE "screening_results"`);
  }
}
