import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCompletionDateToUserTransaction1713423006941 implements MigrationInterface {
  name = 'AddCompletionDateToUserTransaction1713423006941';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" ADD "completionDate" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "completionDate"`);
  }
}
