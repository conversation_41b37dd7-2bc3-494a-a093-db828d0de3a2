import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712650979992 implements MigrationInterface {
  name = 'Migrations1712650979992';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ADD "isUnlimited" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "isUnlimited"`);
  }
}
