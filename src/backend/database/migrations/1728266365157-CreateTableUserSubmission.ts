import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableUserSubmission1728266365157 implements MigrationInterface {
  name = 'CreateTableUserSubmission1728266365157';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_submissions" ("id" SERIAL NOT NULL, "userSurveyId" integer NOT NULL, "surveyId" integer NOT NULL, "userId" integer NOT NULL, "responseId" integer NOT NULL, "completionDate" TIMESTAMP NOT NULL, "hubspotProgramId" character varying(50) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_69b8ae3aabcd766d49270be20ad" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "user_survey_answers" ADD "submissionId" integer`);
    await queryRunner.query(
      `ALTER TABLE "user_submissions" ADD CONSTRAINT "FK_bae5bfe7db46a3f2e1ad144d4ba" FOREIGN KEY ("userSurveyId") REFERENCES "user_surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_submissions" ADD CONSTRAINT "FK_1642c55ea0cc81f9c2b29e0322e" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_submissions" ADD CONSTRAINT "FK_2aed9398db4d69e584a013b2912" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_survey_answers" ADD CONSTRAINT "FK_fc9f61832f132f7b6af1fba4aa1" FOREIGN KEY ("submissionId") REFERENCES "user_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(_queryRunner: QueryRunner): Promise<void> {}
}
