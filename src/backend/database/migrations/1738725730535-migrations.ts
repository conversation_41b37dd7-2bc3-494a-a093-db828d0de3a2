import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1738725730535 implements MigrationInterface {
  name = 'Migrations1738725730535';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_question_options" ADD "isOther" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "survey_questions" ADD "hasOtherOption" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "hasOtherOption"`);
    await queryRunner.query(`ALTER TABLE "survey_question_options" DROP COLUMN "isOther"`);
  }
}
