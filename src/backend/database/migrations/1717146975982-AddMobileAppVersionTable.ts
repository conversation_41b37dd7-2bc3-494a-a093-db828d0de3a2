import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMobileAppVersionTable1717146975982 implements MigrationInterface {
  name = 'AddMobileAppVersionTable1717146975982';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "mobile_app_versions" ("id" SERIAL NOT NULL, "os" character varying(10) NOT NULL, "minVersion" character varying(10) NOT NULL, "currentVersion" character varying(10) NOT NULL, CONSTRAINT "PK_6757832f70e8ea93062e6bcd569" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "mobile_app_versions"`);
  }
}
