import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHubspotProgramIdToUserSurvey1718355992107 implements MigrationInterface {
  name = 'AddHubspotProgramIdToUserSurvey1718355992107';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_surveys" ADD "hubspotProgramId" character varying(50)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_surveys" DROP COLUMN "hubspotProgramId"`);
  }
}
