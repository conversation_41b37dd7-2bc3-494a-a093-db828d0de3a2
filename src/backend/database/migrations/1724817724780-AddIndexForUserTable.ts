import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIndexForUserTable1724817724780 implements MigrationInterface {
  name = 'AddIndexForUserTable1724817724780';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_c4dd678058708647766157c6e4"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_79ce179c4ac5d606074cd68cf6" ON "users" ("deletedAt", "isVerified", "city", "practiceSetting", "employmentStatus", "province", "specialtyId") WHERE ("deletedAt" IS NULL AND "isVerified" = true)`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d55ed9d0097cfe99cfd5972dfe" ON "users" ("email", "deletedAt") WHERE ("deletedAt" IS NULL)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d55ed9d0097cfe99cfd5972dfe"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_79ce179c4ac5d606074cd68cf6"`);
    await queryRunner.query(`CREATE INDEX "IDX_c4dd678058708647766157c6e4" ON "users" ("email", "deletedAt") `);
  }
}
