import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCascadeDeleteForSurvey1715067438078 implements MigrationInterface {
  name = 'AddCascadeDeleteForSurvey1715067438078';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_audiences" DROP CONSTRAINT "FK_5fafdb5558fbf8a41024660fc2f"`);
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP CONSTRAINT "FK_cf60ce3c6d65af9e9493e93a3de"`);
    await queryRunner.query(`ALTER TABLE "survey_question_options" DROP CONSTRAINT "FK_a49f081327b9e5442f03221b731"`);
    await queryRunner.query(
      `ALTER TABLE "survey_question_option_translations" DROP CONSTRAINT "FK_f257a773d63e105c169faec3015"`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_translations" DROP CONSTRAINT "FK_84427c67b0b851aa81a09360299"`,
    );
    await queryRunner.query(`ALTER TABLE "survey_translations" DROP CONSTRAINT "FK_7484892a203cc00dfa7c2783190"`);
    await queryRunner.query(
      `ALTER TABLE "survey_audiences" ADD CONSTRAINT "FK_5fafdb5558fbf8a41024660fc2f" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_questions" ADD CONSTRAINT "FK_cf60ce3c6d65af9e9493e93a3de" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_options" ADD CONSTRAINT "FK_a49f081327b9e5442f03221b731" FOREIGN KEY ("questionId") REFERENCES "survey_questions"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_option_translations" ADD CONSTRAINT "FK_f257a773d63e105c169faec3015" FOREIGN KEY ("questionOptionId") REFERENCES "survey_question_options"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_translations" ADD CONSTRAINT "FK_84427c67b0b851aa81a09360299" FOREIGN KEY ("questionId") REFERENCES "survey_questions"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_translations" ADD CONSTRAINT "FK_7484892a203cc00dfa7c2783190" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_translations" DROP CONSTRAINT "FK_7484892a203cc00dfa7c2783190"`);
    await queryRunner.query(
      `ALTER TABLE "survey_question_translations" DROP CONSTRAINT "FK_84427c67b0b851aa81a09360299"`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_option_translations" DROP CONSTRAINT "FK_f257a773d63e105c169faec3015"`,
    );
    await queryRunner.query(`ALTER TABLE "survey_question_options" DROP CONSTRAINT "FK_a49f081327b9e5442f03221b731"`);
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP CONSTRAINT "FK_cf60ce3c6d65af9e9493e93a3de"`);
    await queryRunner.query(`ALTER TABLE "survey_audiences" DROP CONSTRAINT "FK_5fafdb5558fbf8a41024660fc2f"`);
    await queryRunner.query(
      `ALTER TABLE "survey_translations" ADD CONSTRAINT "FK_7484892a203cc00dfa7c2783190" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_translations" ADD CONSTRAINT "FK_84427c67b0b851aa81a09360299" FOREIGN KEY ("questionId") REFERENCES "survey_questions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_option_translations" ADD CONSTRAINT "FK_f257a773d63e105c169faec3015" FOREIGN KEY ("questionOptionId") REFERENCES "survey_question_options"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_question_options" ADD CONSTRAINT "FK_a49f081327b9e5442f03221b731" FOREIGN KEY ("questionId") REFERENCES "survey_questions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_questions" ADD CONSTRAINT "FK_cf60ce3c6d65af9e9493e93a3de" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "survey_audiences" ADD CONSTRAINT "FK_5fafdb5558fbf8a41024660fc2f" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
