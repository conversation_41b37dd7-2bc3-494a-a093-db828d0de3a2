import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnTotalSubmissionsToUserSurvey1728357368003 implements MigrationInterface {
  name = 'AddColumnTotalSubmissionsToUserSurvey1728357368003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_surveys" ADD "totalSubmissions" integer NOT NULL DEFAULT '0'`);

    await queryRunner.query(`UPDATE "user_surveys" us 
    SET "totalSubmissions" = (
        SELECT COUNT(*)
        FROM "user_submissions" usb
        WHERE usb."userSurveyId" = us.id
    )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_surveys" DROP COLUMN "totalSubmissions"`);
  }
}
