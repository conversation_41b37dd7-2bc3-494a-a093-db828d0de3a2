import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGenderToUser1711874175535 implements MigrationInterface {
  name = 'AddGenderToUser1711874175535';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."users_gender_enum" AS ENUM('Male', 'Female', 'Other')`);
    await queryRunner.query(`ALTER TABLE "users" ADD "gender" "public"."users_gender_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "gender"`);
    await queryRunner.query(`DROP TYPE "public"."users_gender_enum"`);
  }
}
