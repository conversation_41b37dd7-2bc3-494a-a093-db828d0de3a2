import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1747040354617 implements MigrationInterface {
  name = 'Migrations1747040354617';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isEmailOptIn" SET DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "isEmailOptIn" SET DEFAULT true`);
  }
}
