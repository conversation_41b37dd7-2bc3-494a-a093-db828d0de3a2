import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnUsagesToPublicReferralCode1713941897805 implements MigrationInterface {
  name = 'AddColumnUsagesToPublicReferralCode1713941897805';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_referral_codes" ADD "usages" integer NOT NULL DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_referral_codes" DROP COLUMN "usages"`);
  }
}
