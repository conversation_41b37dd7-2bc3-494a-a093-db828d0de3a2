import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1747196926913 implements MigrationInterface {
  name = 'Migrations1747196926913';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ADD "firstName" character varying(50) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ADD "lastName" character varying(50) NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "lastName"`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "firstName"`);
  }
}
