import { MigrationInterface, QueryRunner } from 'typeorm';

export class IncreaseEmailAndPhoneLengthInUser1716197983533 implements MigrationInterface {
  name = 'IncreaseEmailAndPhoneLengthInUser1716197983533';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "email" TYPE character varying(200)`);
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "phone" TYPE character varying(200)`);
  }

  public async down(_queryRunner: QueryRunner): Promise<void> {}
}
