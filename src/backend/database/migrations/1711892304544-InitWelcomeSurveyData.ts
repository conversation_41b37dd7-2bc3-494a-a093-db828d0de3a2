import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitWelcomeSurveyData1711892304544 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`INSERT INTO practice_settings (name) VALUES ('Hospital'), ('Community'), ('Other');`);
    await queryRunner.query(`INSERT INTO employment_status (name) VALUES ('Self-employed'), ('Employee');`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM practice_settings WHERE name IN ('Hospital', 'Community', 'Other');`);
    await queryRunner.query(`DELETE FROM employment_status WHERE name IN ('Self-employed', 'Employee');`);
  }
}
