import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHubspotInfoToUser1712566404925 implements MigrationInterface {
  name = 'AddHubspotInfoToUser1712566404925';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ADD "preferredLanguage" character varying(10)`);
    await queryRunner.query(`ALTER TABLE "users" ADD "hubspotContactId" character varying(50)`);
    await queryRunner.query(`ALTER TABLE "users" ADD "hubspotProductId" character varying(50)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "hubspotProductId"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "hubspotContactId"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "preferredLanguage"`);
  }
}
