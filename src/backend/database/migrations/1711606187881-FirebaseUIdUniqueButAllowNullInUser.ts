import { MigrationInterface, QueryRunner } from 'typeorm';

export class FirebaseUIdUniqueButAllowNullInUser1711606187881 implements MigrationInterface {
  name = 'FirebaseUIdUniqueButAllowNullInUser1711606187881';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "UQ_1ebf192be0cd17d1c6f88367855"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_d84eb60dd4667c09ddbb8f23c7" ON "users" ("firebaseUserId") WHERE "firebaseUserId" IS NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d84eb60dd4667c09ddbb8f23c7"`);
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "UQ_1ebf192be0cd17d1c6f88367855" UNIQUE ("firebaseUserId")`,
    );
  }
}
