import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNewColumnsToPublicReferralCode1713496346563 implements MigrationInterface {
  name = 'AddNewColumnsToPublicReferralCode1713496346563';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_referral_codes" ADD "name" character varying(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "public_referral_codes" ADD "description" text`);
    await queryRunner.query(`CREATE TYPE "public"."public_referral_codes_status_enum" AS ENUM('Enabled', 'Disabled')`);
    await queryRunner.query(
      `ALTER TABLE "public_referral_codes" ADD "status" "public"."public_referral_codes_status_enum" NOT NULL DEFAULT 'Enabled'`,
    );
    await queryRunner.query(`ALTER TABLE "public_referral_codes" ADD "maxUses" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_referral_codes" DROP COLUMN "maxUses"`);
    await queryRunner.query(`ALTER TABLE "public_referral_codes" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."public_referral_codes_status_enum"`);
    await queryRunner.query(`ALTER TABLE "public_referral_codes" DROP COLUMN "description"`);
    await queryRunner.query(`ALTER TABLE "public_referral_codes" DROP COLUMN "name"`);
  }
}
