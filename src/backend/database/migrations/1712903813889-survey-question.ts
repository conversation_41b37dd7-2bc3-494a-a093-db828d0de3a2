import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712903813889 implements MigrationInterface {
  name = 'Migrations1712903813889';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_questions" ADD "order" integer NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "order"`);
  }
}
