import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnsToAudiences1713173614813 implements MigrationInterface {
  name = 'AddColumnsToAudiences1713173614813';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "filter"`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "cities" text array`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "completedSurveys" integer array`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "employmentStatuses" text array`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "practiceSettings" text array`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "provinces" text array`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "specialtyIds" integer array`);
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "specialtyIds"`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "provinces"`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "practiceSettings"`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "employmentStatuses"`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "completedSurveys"`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "cities"`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD "filter" jsonb NOT NULL`);
  }
}
