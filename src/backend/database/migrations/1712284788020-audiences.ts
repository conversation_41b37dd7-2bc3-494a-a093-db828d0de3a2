import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712284788020 implements MigrationInterface {
  name = 'Migrations1712284788020';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audiences" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP CONSTRAINT "UQ_14afbc90dad7bb98168f3c58985"`);
    await queryRunner.query(`CREATE INDEX "IDX_14afbc90dad7bb98168f3c5898" ON "audiences" ("name") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_14afbc90dad7bb98168f3c5898"`);
    await queryRunner.query(`ALTER TABLE "audiences" ADD CONSTRAINT "UQ_14afbc90dad7bb98168f3c58985" UNIQUE ("name")`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "deletedAt"`);
  }
}
