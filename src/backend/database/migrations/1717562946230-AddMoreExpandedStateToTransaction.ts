import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMoreExpandedStateToTransaction1717562946230 implements MigrationInterface {
  name = 'AddMoreExpandedStateToTransaction1717562946230';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" ADD "referredBy" character varying(100)`);
    await queryRunner.query(`ALTER TABLE "user_transactions" ADD "referred" character varying(100)`);
    await queryRunner.query(`ALTER TABLE "user_transactions" ADD "code" character varying(100)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "referred"`);
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "referredBy"`);
  }
}
