import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712544095211 implements MigrationInterface {
  name = 'Migrations1712544095211';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audiences" DROP CONSTRAINT "FK_5983e8858a3b1ad37047dfdca20"`);
    await queryRunner.query(`ALTER TABLE "audiences" ALTER COLUMN "specialtyId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "audiences" ADD CONSTRAINT "FK_5983e8858a3b1ad37047dfdca20" FOREIGN KEY ("specialtyId") REFERENCES "specialties"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audiences" DROP CONSTRAINT "FK_5983e8858a3b1ad37047dfdca20"`);
    await queryRunner.query(`ALTER TABLE "audiences" ALTER COLUMN "specialtyId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "audiences" ADD CONSTRAINT "FK_5983e8858a3b1ad37047dfdca20" FOREIGN KEY ("specialtyId") REFERENCES "specialties"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
