import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1750153457611 implements MigrationInterface {
  name = 'Migrations1750153457611';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "survey_qrs" ("id" SERIAL NOT NULL, "surveyId" integer, "slug" character varying NOT NULL, "qrCodeUrl" character varying NOT NULL, "surveyUrl" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_42ba89ecef7a8d5359bd79cfac0" UNIQUE ("slug"), CONSTRAINT "PK_f78763ce5ffb57e33affdf9961a" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "survey_qrs"`);
  }
}
