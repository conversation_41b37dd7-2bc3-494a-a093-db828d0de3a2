import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStartDateToSurvey1716432381013 implements MigrationInterface {
  name = 'AddStartDateToSurvey1716432381013';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ADD "startDate" date`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "startDate"`);
  }
}
