import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnDescriptionToUserTransaction1715309191292 implements MigrationInterface {
  name = 'AddColumnDescriptionToUserTransaction1715309191292';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" ADD "description" character varying(255)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "description"`);
  }
}
