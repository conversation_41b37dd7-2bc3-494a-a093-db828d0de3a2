import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1748935993575 implements MigrationInterface {
  name = 'Migrations1748935993575';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "firstName"`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "lastName"`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "phone"`);
    await queryRunner.query(`ALTER TABLE "user_submissions" ADD "startDate" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ADD "startDate" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "user_submissions" ALTER COLUMN "hubspotProgramId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ALTER COLUMN "email" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ALTER COLUMN "email" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_submissions" ALTER COLUMN "hubspotProgramId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "startDate"`);
    await queryRunner.query(`ALTER TABLE "user_submissions" DROP COLUMN "startDate"`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ADD "phone" character varying(200) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ADD "lastName" character varying(50) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ADD "firstName" character varying(50) NOT NULL`);
  }
}
