import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712562344575 implements MigrationInterface {
  name = 'Migrations1712562344575';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ADD "name" character varying(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "surveys" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "survey_translations" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "locale"`);
    await queryRunner.query(`CREATE TYPE "public"."surveys_locale_enum" AS ENUM('En', 'Fr')`);
    await queryRunner.query(`ALTER TABLE "surveys" ADD "locale" "public"."surveys_locale_enum" NOT NULL DEFAULT 'En'`);
    await queryRunner.query(`ALTER TABLE "survey_translations" DROP COLUMN "locale"`);
    await queryRunner.query(`CREATE TYPE "public"."survey_translations_locale_enum" AS ENUM('En', 'Fr')`);
    await queryRunner.query(
      `ALTER TABLE "survey_translations" ADD "locale" "public"."survey_translations_locale_enum" NOT NULL DEFAULT 'Fr'`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_a6f49103285c12efb32943f8cc" ON "surveys" ("name") `);
    await queryRunner.query(`CREATE INDEX "IDX_8a1ac2a8cde86f0343745cd23e" ON "surveys" ("companyId") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_8a1ac2a8cde86f0343745cd23e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a6f49103285c12efb32943f8cc"`);
    await queryRunner.query(`ALTER TABLE "survey_translations" DROP COLUMN "locale"`);
    await queryRunner.query(`DROP TYPE "public"."survey_translations_locale_enum"`);
    await queryRunner.query(`ALTER TABLE "survey_translations" ADD "locale" character varying(10) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "locale"`);
    await queryRunner.query(`DROP TYPE "public"."surveys_locale_enum"`);
    await queryRunner.query(`ALTER TABLE "surveys" ADD "locale" character varying(10) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_translations" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "name"`);
  }
}
