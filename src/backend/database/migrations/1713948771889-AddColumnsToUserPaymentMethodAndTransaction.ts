import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnsToUserPaymentMethodAndTransaction1713948771889 implements MigrationInterface {
  name = 'AddColumnsToUserPaymentMethodAndTransaction1713948771889';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "token"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "accessToken" character varying(150)`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "processorToken" character varying(150)`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "institutionId" character varying(50)`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "bankName" character varying(100)`);
    await queryRunner.query(`ALTER TABLE "user_transactions" ADD "paidTo" character varying(100)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "paidTo"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "bankName"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "institutionId"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "processorToken"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "accessToken"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "token" character varying(150)`);
  }
}
