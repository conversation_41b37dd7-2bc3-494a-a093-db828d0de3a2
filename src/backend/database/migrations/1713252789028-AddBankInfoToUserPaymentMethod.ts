import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBankInfoToUserPaymentMethod1713252789028 implements MigrationInterface {
  name = 'AddBankInfoToUserPaymentMethod1713252789028';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "email" character varying(50)`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "mask" character varying(4)`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ADD "deletedAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "survey_questions" ALTER COLUMN "order" SET DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ALTER COLUMN "token" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_payment_methods" ALTER COLUMN "token" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_questions" ALTER COLUMN "order" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "deletedAt"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "mask"`);
    await queryRunner.query(`ALTER TABLE "user_payment_methods" DROP COLUMN "email"`);
  }
}
