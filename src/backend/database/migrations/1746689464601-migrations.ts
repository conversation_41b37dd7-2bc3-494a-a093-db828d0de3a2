import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1746689464601 implements MigrationInterface {
  name = 'Migrations1746689464601';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."public_survey_answers_status_enum" AS ENUM('Open', 'InProgress', 'Done')`,
    );
    await queryRunner.query(
      `CREATE TABLE "public_survey_answers" ("id" SERIAL NOT NULL, "email" character varying(200) NOT NULL, "phone" character varying(200) NOT NULL, "surveyId" integer NOT NULL, "answers" jsonb NOT NULL, "status" "public"."public_survey_answers_status_enum" NOT NULL DEFAULT 'Open', "error" text, "count" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_6b9bac309e6384f3e5e2cf17d6a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_fb97d74626fd67642b0c0b3240" ON "public_survey_answers" ("status") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_38e1420b15526faf2ab0f78e67" ON "user_surveys" ("userId", "surveyId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "public_survey_answers" ADD CONSTRAINT "FK_70c7395fd9aebf140920e803519" FOREIGN KEY ("surveyId") REFERENCES "surveys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP CONSTRAINT "FK_70c7395fd9aebf140920e803519"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_38e1420b15526faf2ab0f78e67"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fb97d74626fd67642b0c0b3240"`);
    await queryRunner.query(`DROP TABLE "public_survey_answers"`);
    await queryRunner.query(`DROP TYPE "public"."public_survey_answers_status_enum"`);
  }
}
