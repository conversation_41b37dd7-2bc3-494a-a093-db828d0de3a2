import { MigrationInterface, QueryRunner } from 'typeorm';

export class DefaultValueForSurveyStatus1717404960262 implements MigrationInterface {
  name = 'DefaultValueForSurveyStatus1717404960262';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" SET DEFAULT 'Draft'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "status" DROP DEFAULT`);
  }
}
