import { MigrationInterface, QueryRunner } from 'typeorm';

export class ConvertStartDateToTimestamp1716956257107 implements MigrationInterface {
  name = 'ConvertStartDateToTimestamp1716956257107';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "expiryDate" TYPE TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "surveys" ALTER COLUMN "expiryDate" TYPE date`);
  }
}
