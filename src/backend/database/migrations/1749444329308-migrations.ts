import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1749444329308 implements MigrationInterface {
  name = 'Migrations1749444329308';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" ADD "userId" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "public_survey_answers" DROP COLUMN "userId"`);
  }
}
