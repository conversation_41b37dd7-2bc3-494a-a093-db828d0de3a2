import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1746522057880 implements MigrationInterface {
  name = 'Migrations1746522057880';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d55ed9d0097cfe99cfd5972dfe"`);
    await queryRunner.query(`ALTER TABLE "users" ADD "isPublic" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "surveys" ADD "isPublic" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "surveys" ADD "publicQRCode" text`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_d55ed9d0097cfe99cfd5972dfe" ON "users" ("email", "deletedAt") WHERE ("deletedAt" IS NULL)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d55ed9d0097cfe99cfd5972dfe"`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "publicQRCode"`);
    await queryRunner.query(`ALTER TABLE "surveys" DROP COLUMN "isPublic"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "isPublic"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_d55ed9d0097cfe99cfd5972dfe" ON "users" ("email", "deletedAt") WHERE ("deletedAt" IS NULL)`,
    );
  }
}
