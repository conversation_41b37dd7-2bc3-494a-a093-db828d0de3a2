import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1714116638962 implements MigrationInterface {
  name = 'Migrations1714116638962';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_question_option_translations" ADD "questionId" integer NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_question_option_translations" DROP COLUMN "questionId"`);
  }
}
