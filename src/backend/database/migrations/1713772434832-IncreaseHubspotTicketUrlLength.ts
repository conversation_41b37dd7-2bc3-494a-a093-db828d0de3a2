import { MigrationInterface, QueryRunner } from 'typeorm';

export class IncreaseHubspotTicketUrlLength1713772434832 implements MigrationInterface {
  name = 'IncreaseHubspotTicketUrlLength1713772434832';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "support_requests" ALTER COLUMN "hubspotTicketUrl" TYPE character varying(150)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "support_requests" ALTER COLUMN "hubspotTicketUrl" TYPE character varying(50)`,
    );
  }
}
