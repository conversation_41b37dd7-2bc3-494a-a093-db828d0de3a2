import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1753850198601 implements MigrationInterface {
  name = 'Migrations1753850198601';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."users_usertype_enum" RENAME TO "users_usertype_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."users_usertype_enum" AS ENUM('HCP User', 'Client', 'Client - Imported', 'HCP User - Imported', 'Unverified', 'Denied', 'Internal')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "userType" TYPE "public"."users_usertype_enum" USING "userType"::"text"::"public"."users_usertype_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_usertype_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."users_usertype_enum_old" AS ENUM('ActualUser', 'Client', 'ClientImported', 'UserImported', 'NotVerified', 'Denied', 'Internal')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "userType" TYPE "public"."users_usertype_enum_old" USING "userType"::"text"::"public"."users_usertype_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_usertype_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."users_usertype_enum_old" RENAME TO "users_usertype_enum"`);
  }
}
