import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateValueColumnForUserSurveyAnswerTable1715054382128 implements MigrationInterface {
  name = 'UpdateValueColumnForUserSurveyAnswerTable1715054382128';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_survey_answers" ALTER COLUMN "value" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_survey_answers" ALTER COLUMN "value" SET NOT NULL`);
  }
}
