import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739433830773 implements MigrationInterface {
  name = 'Migrations1739433830773';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ADD "isEmailOptIn" boolean NOT NULL DEFAULT true`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "isEmailOptIn"`);
  }
}
