import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712213227402 implements MigrationInterface {
  name = 'Migrations1712213227402';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "companies" ADD "deletedAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "deletedAt"`);
  }
}
