import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameColumnAndAddNewTypeToEnumInUserTransaction1715308243387 implements MigrationInterface {
  name = 'RenameColumnAndAddNewTypeToEnumInUserTransaction1715308243387';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" RENAME COLUMN "description" TO "title"`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_transactions_type_enum" RENAME TO "user_transactions_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_transactions_type_enum" AS ENUM('Withdrawal', 'Compensation', 'ReferralSuccess', 'ReferralReward', 'Credit', 'Adjustment')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transactions" ALTER COLUMN "type" TYPE "public"."user_transactions_type_enum" USING "type"::"text"::"public"."user_transactions_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_transactions_type_enum_old"`);
    await queryRunner.query(`ALTER TABLE "user_transactions" ALTER COLUMN "title" SET NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" ALTER COLUMN "title" DROP NOT NULL`);
    await queryRunner.query(
      `CREATE TYPE "public"."user_transactions_type_enum_old" AS ENUM('Withdrawal', 'Compensation', 'ReferralSuccess', 'ReferralReward')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_transactions" ALTER COLUMN "type" TYPE "public"."user_transactions_type_enum_old" USING "type"::"text"::"public"."user_transactions_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_transactions_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_transactions_type_enum_old" RENAME TO "user_transactions_type_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "user_transactions" RENAME COLUMN "title" TO "description"`);
  }
}
