import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateColumnsForUserAction1715312454221 implements MigrationInterface {
  name = 'UpdateColumnsForUserAction1715312454221';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_actions" ADD "entity" character varying(50) NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_actions" ALTER COLUMN "previousMetadata" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_actions" ALTER COLUMN "newMetadata" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_actions" ALTER COLUMN "newMetadata" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_actions" ALTER COLUMN "previousMetadata" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_actions" DROP COLUMN "entity"`);
  }
}
