import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1714099913340 implements MigrationInterface {
  name = 'Migrations1714099913340';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_question_options" ADD "surveyId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_question_option_translations" ADD "surveyId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_question_translations" ADD "surveyId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_questions" ALTER COLUMN "locale" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "survey_question_option_translations" ALTER COLUMN "locale" DROP DEFAULT`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_question_option_translations" ALTER COLUMN "locale" SET DEFAULT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_questions" ALTER COLUMN "locale" SET DEFAULT NULL`);
    await queryRunner.query(`ALTER TABLE "survey_question_translations" DROP COLUMN "surveyId"`);
    await queryRunner.query(`ALTER TABLE "survey_question_option_translations" DROP COLUMN "surveyId"`);
    await queryRunner.query(`ALTER TABLE "survey_question_options" DROP COLUMN "surveyId"`);
  }
}
