import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1753783225303 implements MigrationInterface {
  name = 'Migrations1753783225303';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "screening_results" ADD "startDate" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "screening_results" DROP COLUMN "startDate"`);
  }
}
