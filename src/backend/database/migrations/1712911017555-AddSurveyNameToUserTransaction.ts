import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSurveyNameToUserTransaction1712911017555 implements MigrationInterface {
  name = 'AddSurveyNameToUserTransaction1712911017555';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" ADD "surveyName" character varying(500)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_transactions" DROP COLUMN "surveyName"`);
  }
}
