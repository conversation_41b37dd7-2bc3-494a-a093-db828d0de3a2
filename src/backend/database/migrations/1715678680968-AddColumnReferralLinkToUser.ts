import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnReferralLinkToUser1715678680968 implements MigrationInterface {
  name = 'AddColumnReferralLinkToUser1715678680968';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ADD "referralLink" character varying(150)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "referralLink"`);
  }
}
