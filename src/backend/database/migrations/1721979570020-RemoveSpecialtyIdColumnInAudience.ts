import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveSpecialtyIdColumnInAudience1721979570020 implements MigrationInterface {
  name = 'RemoveSpecialtyIdColumnInAudience1721979570020';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_5983e8858a3b1ad37047dfdca2"`);
    await queryRunner.query(`ALTER TABLE "audiences" DROP COLUMN "specialtyId"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audiences" ADD "specialtyId" integer`);
    await queryRunner.query(`CREATE INDEX "IDX_5983e8858a3b1ad37047dfdca2" ON "audiences" ("specialtyId") `);
  }
}
