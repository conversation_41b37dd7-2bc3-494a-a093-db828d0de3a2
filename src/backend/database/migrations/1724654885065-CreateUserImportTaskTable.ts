import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserImportTaskTable1724654885065 implements MigrationInterface {
  name = 'CreateUserImportTaskTable1724654885065';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_import_tasks_status_enum" AS ENUM('Open', 'InProgress', 'Done')`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_import_tasks" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "status" "public"."user_import_tasks_status_enum" NOT NULL DEFAULT 'Open', "error" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_0dd35928a50ab4cbc725738ec5e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_a4dca6a25e118929f8f2df2529" ON "user_import_tasks" ("status") `);
    await queryRunner.query(`CREATE INDEX "IDX_c4dd678058708647766157c6e4" ON "users" ("email", "deletedAt") `);
    await queryRunner.query(`CREATE INDEX "IDX_de52ee40aabfbe3eef30992bf6" ON "user_payment_methods" ("userId") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_de52ee40aabfbe3eef30992bf6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c4dd678058708647766157c6e4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a4dca6a25e118929f8f2df2529"`);
    await queryRunner.query(`DROP TABLE "user_import_tasks"`);
    await queryRunner.query(`DROP TYPE "public"."user_import_tasks_status_enum"`);
  }
}
