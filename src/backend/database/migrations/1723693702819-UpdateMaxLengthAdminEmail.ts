import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateMaxLengthAdminEmail1723693702819 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_admins" ALTER COLUMN "email" TYPE character varying(200)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_admins" ALTER COLUMN "email" TYPE character varying(50)`);
  }
}
