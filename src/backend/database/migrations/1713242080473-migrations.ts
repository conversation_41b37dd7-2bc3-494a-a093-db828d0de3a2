import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1713242080473 implements MigrationInterface {
  name = 'Migrations1713242080473';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_questions" DROP COLUMN "isMultiLang"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "survey_questions" ADD "isMultiLang" boolean NOT NULL DEFAULT false`);
  }
}
