import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1740993722295 implements MigrationInterface {
  name = 'Migrations1740993722295';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "FK_a7c1a801700048901c8e86d1a9e"`);
    await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "UQ_d8260d8964eee69f5ec90bec88b"`);
    await queryRunner.query(`ALTER TABLE "cities" ALTER COLUMN "provinceId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "cities" ADD CONSTRAINT "UQ_d8260d8964eee69f5ec90bec88b" UNIQUE ("name", "provinceId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "cities" ADD CONSTRAINT "FK_a7c1a801700048901c8e86d1a9e" FOREIGN KEY ("provinceId") REFERENCES "provinces"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "FK_a7c1a801700048901c8e86d1a9e"`);
    await queryRunner.query(`ALTER TABLE "cities" DROP CONSTRAINT "UQ_d8260d8964eee69f5ec90bec88b"`);
    await queryRunner.query(`ALTER TABLE "cities" ALTER COLUMN "provinceId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "cities" ADD CONSTRAINT "UQ_d8260d8964eee69f5ec90bec88b" UNIQUE ("provinceId", "name")`,
    );
    await queryRunner.query(
      `ALTER TABLE "cities" ADD CONSTRAINT "FK_a7c1a801700048901c8e86d1a9e" FOREIGN KEY ("provinceId") REFERENCES "provinces"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
