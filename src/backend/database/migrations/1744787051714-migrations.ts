import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1744787051714 implements MigrationInterface {
  name = 'Migrations1744787051714';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "isVerified"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e67cfb1579b0e91b94d0ca2519"`);
    await queryRunner.query(
      `ALTER TYPE "public"."users_verification_status_enum" RENAME TO "users_verification_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."users_verificationstatus_enum" AS ENUM('Denied', 'Unverified', 'Verified')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "verificationStatus" TYPE "public"."users_verificationstatus_enum" USING "verificationStatus"::"text"::"public"."users_verificationstatus_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_verification_status_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."users_contact_status_enum" RENAME TO "users_contact_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."users_contactstatus_enum" AS ENUM('Complete', 'ReviewInfo', 'WaitForInfo', 'WSPending')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "contactStatus" TYPE "public"."users_contactstatus_enum" USING "contactStatus"::"text"::"public"."users_contactstatus_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_contact_status_enum_old"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_e67cfb1579b0e91b94d0ca2519" ON "users" ("deletedAt", "verificationStatus", "city", "practiceSetting", "employmentStatus", "province", "specialtyId") WHERE ("deletedAt" IS NULL AND "verificationStatus" = 'Verified')`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_e67cfb1579b0e91b94d0ca2519"`);
    await queryRunner.query(
      `CREATE TYPE "public"."users_contact_status_enum_old" AS ENUM('Complete', 'ReviewInfo', 'WaitForInfo', 'WSPending')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "contactStatus" TYPE "public"."users_contact_status_enum_old" USING "contactStatus"::"text"::"public"."users_contact_status_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_contactstatus_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."users_contact_status_enum_old" RENAME TO "users_contact_status_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."users_verification_status_enum_old" AS ENUM('Denied', 'Unverified', 'Verified')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "verificationStatus" TYPE "public"."users_verification_status_enum_old" USING "verificationStatus"::"text"::"public"."users_verification_status_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_verificationstatus_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."users_verification_status_enum_old" RENAME TO "users_verification_status_enum"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e67cfb1579b0e91b94d0ca2519" ON "users" ("city", "province", "practiceSetting", "employmentStatus", "specialtyId", "deletedAt", "verificationStatus") WHERE (("deletedAt" IS NULL) AND ("verificationStatus" = 'Verified'::users_verification_status_enum))`,
    );
    await queryRunner.query(`ALTER TABLE "users" ADD "isVerified" boolean NOT NULL DEFAULT false`);
  }
}
