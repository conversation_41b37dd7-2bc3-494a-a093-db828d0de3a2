import * as fs from 'fs';
import * as path from 'path';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { City } from '../../users/entities/City';
import { Country } from '../../users/entities/Country';
import { Province } from '../../users/entities/Province';

export class Migrations1713168384996 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Read data from the file
    const filePath = path.join(process.cwd(), '/data/region.json');
    const jsonData = fs.readFileSync(filePath, 'utf-8');
    const countryData = JSON.parse(jsonData);

    // Insert countries
    const country = new Country();
    country.id = countryData.id;
    country.name = countryData.name;
    country.code = countryData.code;
    await queryRunner.manager.save(country);

    // Insert provinces for each country
    for (const provinceData of countryData.provinces) {
      const province = new Province();
      province.id = provinceData.id;
      province.countryId = countryData.id;
      province.name = provinceData.name;
      province.code = provinceData.code;
      await queryRunner.manager.save(province);

      // Insert cities for each province
      for (const cityData of provinceData.cities) {
        const city = new City();
        city.id = cityData.id;
        city.provinceId = provinceData.id;
        city.name = cityData.name;
        await queryRunner.manager.save(city);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.manager.delete(City, {});
    await queryRunner.manager.delete(Province, {});
    await queryRunner.manager.delete(Country, {});
  }
}
