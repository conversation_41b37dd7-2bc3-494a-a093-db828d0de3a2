import { SummariesPayload } from '@/app/api/available-funds/summaries/route';
import { dataSource } from '@/backend/database/data-source';
import { PaymentService } from '@/backend/payments/services/payment-service';
import { logger } from '@/backend/shared/utils/logger';
import { Survey } from '@/backend/surveys/entities/Survey';
import { SurveyRepository } from '@/backend/surveys/repositories/survey-repository';
import { VerificationStatus } from '@/backend/users/entities/User';
import { TransactionType } from '@/backend/users/entities/UserTransaction';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { UserTransactionRepository } from '@/backend/users/repositories/user-transaction-repository';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';
import { endOfDay, startOfYear } from 'date-fns';
import { Logger } from 'pino';

export class AvailableFundsService {
  private readonly userRepository = new UserRepository(dataSource);
  private readonly surveyRepository = new SurveyRepository(dataSource);
  private readonly userTransactionRepository = new UserTransactionRepository(dataSource);
  private readonly paymentService = new PaymentService();
  private readonly log: Logger = logger.child({ description: 'AvailableFundsService' });

  constructor() {}

  public async getSummaries(data: SummariesPayload): Promise<Record<string, number>> {
    const today = new Date();

    const validateDateRange = (from?: string, to?: string) => {
      if (!from || !to || new Date(from) > new Date(to)) {
        return { from: startOfYear(today), to: endOfDay(today) };
      }
      return { from: new Date(from), to: new Date(to) };
    };

    const { from: cashedOutFrom, to: cashedOutTo } = validateDateRange(
      data.cashedOutFrom as string,
      data.cashedOutTo as string,
    );

    const { from: creditedFrom, to: creditedTo } = validateDateRange(
      data.creditedFrom as string,
      data.creditedTo as string,
    );

    const [availableFunds, walletFunds, interacCashedOut, bankCashedOut, surveyCredited, referralCredited] =
      await Promise.all([
        this.paymentService.getAccountBalance(),
        this.getWalletFunds(),
        this.getCashedOutTotal('Interac Withdrawal', cashedOutFrom, cashedOutTo),
        this.getCashedOutTotal('Bank Withdrawal', cashedOutFrom, cashedOutTo),
        this.getCreditedTotal(TransactionType.Compensation, creditedFrom, creditedTo),
        this.getCreditedTotal(
          [TransactionType.ReferralReward, TransactionType.ReferralSuccess],
          creditedFrom,
          creditedTo,
        ),
      ]);

    return {
      availableFunds: Number(availableFunds.AvailableFunds) || 0,
      walletFunds,
      interacCashedOut,
      bankCashedOut,
      surveyCredited,
      referralCredited,
    };
  }

  private async getCashedOutTotal(title: string, from: Date, to: Date): Promise<number> {
    const result = await this.userTransactionRepository
      .createQueryBuilder('userTransaction')
      .select('SUM(ABS(userTransaction.amount))', 'total')
      .innerJoin('userTransaction.user', 'user')
      .where('userTransaction.type = :type', { type: TransactionType.Withdrawal })
      .andWhere('userTransaction.title = :title', { title })
      .andWhere('userTransaction.createdAt BETWEEN :from AND :to', { from, to })
      .andWhere('user.verificationStatus = :verificationStatus', { verificationStatus: VerificationStatus.Verified })
      .andWhere('user.deletedAt IS NULL')
      .getRawOne();

    return result.total ? Number(result.total) : 0;
  }

  private async getCreditedTotal(types: TransactionType | TransactionType[], from: Date, to: Date): Promise<number> {
    const result = await this.userTransactionRepository
      .createQueryBuilder('userTransaction')
      .select('SUM(userTransaction.amount)', 'total')
      .innerJoin('userTransaction.user', 'user')
      .where('userTransaction.type IN (:...types)', { types: Array.isArray(types) ? types : [types] })
      .andWhere('userTransaction.createdAt BETWEEN :from AND :to', { from, to })
      .andWhere('user.verificationStatus = :verificationStatus', { verificationStatus: VerificationStatus.Verified })
      .andWhere('user.deletedAt IS NULL')
      .getRawOne();

    return result.total ? Number(result.total) : 0;
  }

  public async getSurveys(data: PaginationPayload<Survey>): Promise<PaginationResponseData<Survey>> {
    try {
      const { page, pageSize, search } = data;

      const queryBuilder = this.surveyRepository.createQueryBuilder('survey').orderBy('survey.createdAt', 'DESC');

      if (search) {
        const searchTerm = `%${search.trim()}%`;
        queryBuilder.andWhere('survey.title ILIKE :search', { search: searchTerm });
      }

      const [walletFunds, paginatedResult] = await Promise.all([
        this.getWalletFunds(),
        withPagination<Survey>(queryBuilder, page, pageSize),
      ]);

      if (paginatedResult.data.length > 0) {
        const surveyIds: number[] = paginatedResult.data.map(survey => survey.id);

        const walletPayouts = await this.userTransactionRepository
          .createQueryBuilder('ut')
          .select('us.surveyId', 'surveyId')
          .addSelect('COALESCE(SUM(ut.amount), 0)', 'walletPayout')
          .innerJoin('user_submissions', 'us', 'us.id = CAST(ut.refId AS INTEGER)')
          .where('ut.type = :type', { type: TransactionType.Compensation })
          .andWhere('us.surveyId IN (:...surveyIds)', { surveyIds })
          .groupBy('us.surveyId')
          .getRawMany();

        const payoutMap: Map<number, number> = new Map(
          walletPayouts.map(item => [Number(item.surveyId), Number(item.walletPayout)]),
        );

        paginatedResult.data = paginatedResult.data.map(survey => {
          const surveyWithFinancials = {
            ...survey,
            walletPayout: payoutMap.get(survey.id) || 0,
            estimatedFunds: this.getSurveyEstimatedFunds(survey),
          };
          return surveyWithFinancials;
        });
      }

      return {
        ...paginatedResult,
        walletFunds,
      };
    } catch (error) {
      this.log.error({ error }, 'Failed to get surveys with financial data');
      throw error;
    }
  }

  private getSurveyEstimatedFunds(survey: Survey): number | null {
    if (survey.compensation === 0) {
      return 0;
    }

    return survey.maxParticipants !== null ? survey.compensation * survey.maxParticipants : null;
  }

  private async getWalletFunds(): Promise<number> {
    const result = await this.userRepository
      .createQueryBuilder('user')
      .select('SUM(user.balance)', 'total')
      .where('user.deletedAt IS NULL')
      .andWhere('user.verificationStatus = :verificationStatus', { verificationStatus: VerificationStatus.Verified })
      .getRawOne();

    return result.total ? Number(result.total) : 0;
  }
}
