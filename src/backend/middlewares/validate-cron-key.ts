import { unauthorizedError } from '@/backend/middlewares/capture-errors';
import config from '@/config';
import { PipeFunction } from 'next-route-handler-pipe';
import { headers } from 'next/headers';

export const validateCronKey: PipeFunction = async (_req, _event, next) => {
  const token = headers().get('authorization')?.split('Bearer ')[1] ?? null;

  if (!token || token !== config.CRON_SECRET) {
    throw unauthorizedError('Invalid token');
  }

  return await next();
};
