import { logger } from '@/backend/shared/utils/logger';
import createError from 'http-errors';
import { PipeFunction } from 'next-route-handler-pipe';
import { NextResponse } from 'next/server';

export enum ErrorCode {
  InvalidCredential = 'invalid_credential',
  NotFound = 'not_found',
  AlreadyExists = 'already_exists',
  BadRequest = 'bad_request',
  Unauthorized = 'unauthorized',
  Forbidden = 'forbidden',
  InternalServerError = 'internal_server_error',
  HubspotError = 'hubspot_error',
  PlaidError = 'plaid_error',
  VopayError = 'vopay_error',
}

export const badRequestError = (message: string, errorCode?: ErrorCode) => {
  return customHttpError(400, message, errorCode ?? ErrorCode.BadRequest);
};

export const notFoundError = (message: string) => {
  return customHttpError(404, message, ErrorCode.NotFound);
};

export const unauthorizedError = (message: string) => {
  return customHttpError(401, message, ErrorCode.Unauthorized);
};

export const forbiddenError = (message: string) => {
  return customHttpError(403, message, ErrorCode.Forbidden);
};

export const customHttpError = (statusCode: number, message: string, errorCode: ErrorCode) => {
  const error = createError(statusCode, message);
  error.code = errorCode;
  return error;
};

export const captureErrors: PipeFunction = async (_req, _event, next) => {
  try {
    return await next();
  } catch (error: unknown) {
    if (createError.isHttpError(error) && error.expose) {
      const { statusCode, message, code } = error;

      return NextResponse.json(
        {
          status: 'Error',
          errors: [
            {
              code,
              message,
            },
          ],
        },
        { status: statusCode },
      );
    }

    const log = logger.child({ description: 'UnhandledException' });
    log.error(error);
    return NextResponse.json(
      {
        status: 'Error',
        errors: [
          {
            code: ErrorCode.InternalServerError,
            message: 'Internal server error',
          },
        ],
      },
      { status: 500 },
    );
  }
};
