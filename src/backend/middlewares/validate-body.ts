import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { PipeFunction } from 'next-route-handler-pipe';
import { NextResponse } from 'next/server';
import QueryString from 'qs';
import { z } from 'zod';

export const validateBody = (zodSchema: z.ZodSchema): PipeFunction<{ data: unknown }> => {
  return async function (req, _event, next) {
    let validation;

    if (req.method === 'GET') {
      try {
        const query = QueryString.parse(req.nextUrl.search ? req.nextUrl.search.slice(1) : '');
        validation = { ...query, ...zodSchema.parse(query) };
      } catch (error) {
        throw customHttpError(400, 'Invalid query parameters', ErrorCode.BadRequest);
      }
    } else {
      try {
        const query = req.nextUrl.searchParams;
        const body = await req.json();
        const combinedData = { ...Object.fromEntries(query), ...body };
        validation = zodSchema.safeParse(combinedData);
      } catch (error) {
        throw customHttpError(400, 'Invalid body', ErrorCode.BadRequest);
      }
    }

    if (req.method === 'GET') {
      req.data = validation;
      return await next();
    }

    if (validation.success) {
      req.data = validation.data;
      return await next();
    }

    const { errors } = validation.error;
    return NextResponse.json({ status: 'Error', errors }, { status: 400 });
  };
};
