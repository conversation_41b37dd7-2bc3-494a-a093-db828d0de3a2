import { authorize } from '@/backend/middlewares/authorize';
import { captureErrors } from '@/backend/middlewares/capture-errors';
import { connectDatabase } from '@/backend/middlewares/connect-database';
import { loadFirebaseAdmin } from '@/backend/middlewares/load-firebase-admin';
import { loadFirebaseApp } from '@/backend/middlewares/load-firebase-app';
import { validateBody } from '@/backend/middlewares/validate-body';
import { validateCronKey } from '@/backend/middlewares/validate-cron-key';
import { validatePathParams } from '@/backend/middlewares/validate-path-params';
import { NonEmptyArray } from '@/backend/shared/types/app';
import { Role } from '@/backend/users/types/user';
import { PipeFunctionOrHandler, pipe } from 'next-route-handler-pipe';
import { z } from 'zod';

interface WrapperPayload {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handler: PipeFunctionOrHandler<any> | any;
  schema?: z.ZodSchema;
  roles?: NonEmptyArray<Role>;
  firebase?: boolean;
  validatePathParams?: boolean;
}

export const wrapper = (payload: WrapperPayload) => {
  const { handler, schema, roles, firebase } = payload;
  let isValidatePathParams = payload.validatePathParams;

  // Always validate path params by default
  // If path params are not number, set `validatePathParams` to false and create custom validation
  if (typeof isValidatePathParams === 'undefined') {
    isValidatePathParams = true;
  }

  const pipeFunctions = [
    captureErrors,
    connectDatabase,
    ...(isValidatePathParams ? [validatePathParams] : []),
    ...(roles ? [authorize(roles)] : []),
    ...(firebase ? [loadFirebaseAdmin, loadFirebaseApp] : []),
    ...(schema ? [validateBody(schema)] : []),
    handler,
  ];

  return pipe(...pipeFunctions);
};

export const cronWrapper = (payload: Pick<WrapperPayload, 'handler'>) => {
  const { handler } = payload;
  const pipeFunctions = [captureErrors, connectDatabase, validateCronKey, handler];

  return pipe(...pipeFunctions);
};
