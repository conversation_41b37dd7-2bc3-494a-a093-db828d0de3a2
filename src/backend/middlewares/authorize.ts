import { TokenService } from '@/backend/auth/services/token-service';
import { AuthInfo, IndustriiTokens } from '@/backend/auth/types/auth';
import { dataSource } from '@/backend/database/data-source';
import { forbiddenError, unauthorizedError } from '@/backend/middlewares/capture-errors';
import { AuthorizedRequest, NonEmptyArray, Nullable } from '@/backend/shared/types/app';
import { User } from '@/backend/users/entities/User';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserAdminRepository } from '@/backend/users/repositories/user-admin-repository';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { Role, UserRole } from '@/backend/users/types/user';
import config from '@/config';
import jwt from 'jsonwebtoken';
import { PipeFunction } from 'next-route-handler-pipe';
import { cookies, headers } from 'next/headers';
import { IsNull } from 'typeorm';

const handleToken = (): Nullable<IndustriiTokens> => {
  try {
    const accessToken =
      headers().get('authorization')?.split('Bearer ')[1] ?? cookies().get('accessToken')?.value ?? null;

    const refreshToken = cookies().get('refreshToken')?.value ?? null;

    return { accessToken, refreshToken };
  } catch (err) {
    return { accessToken: null, refreshToken: null };
  }
};

const validatePermission = (allowedRoles: NonEmptyArray<Role>, userRole: Role): boolean => {
  // Allow all roles
  if (allowedRoles.includes('*')) {
    return true;
  }

  if (allowedRoles.includes(userRole)) {
    return true;
  }

  return false;
};

export const authorize = (roles: NonEmptyArray<Role>): PipeFunction<AuthorizedRequest> => {
  return async (req: AuthorizedRequest, _event, next) => {
    const { accessToken, refreshToken } = handleToken();

    if (!accessToken && !refreshToken) {
      throw unauthorizedError('Access token not found');
    }

    let accessTokenDecoded = null;
    try {
      accessTokenDecoded = jwt.verify(accessToken as string, config.ACCESS_TOKEN_SECRET) as AuthInfo;
    } catch (error) {
      // Access token verification failed
      if (!refreshToken) {
        throw unauthorizedError('Invalid access token');
      }
    }

    let user: User | UserAdmin | null;
    const userRepository = new UserRepository(dataSource);
    const userAdminRepository = new UserAdminRepository(dataSource);

    // Access token is valid
    if (accessTokenDecoded) {
      const isValid = validatePermission(roles, accessTokenDecoded.role);
      if (!isValid) {
        throw forbiddenError('Permission denied');
      }

      user =
        accessTokenDecoded.role === UserRole.User
          ? await userRepository.findOneBy({
              id: accessTokenDecoded.userId,
              deletedAt: IsNull(),
            })
          : await userAdminRepository.findOneBy({
              id: accessTokenDecoded.userId,
              deletedAt: IsNull(),
            });

      if (!user) {
        throw unauthorizedError('User not found');
      }

      if (user && accessTokenDecoded.role === UserRole.User) {
        if (!user.phone || user.phone !== accessTokenDecoded.phone) {
          throw unauthorizedError('User not found');
        }
      }

      if (user && 'role' in user && user.role !== accessTokenDecoded.role) {
        throw unauthorizedError('Invalid user role');
      }

      req.user = {
        id: accessTokenDecoded.userId,
        role: accessTokenDecoded.role,
      };

      return await next();
    }

    // Refresh access token
    let refreshTokenDecoded = null;
    try {
      refreshTokenDecoded = jwt.verify(refreshToken as string, config.REFRESH_TOKEN_SECRET) as AuthInfo;
    } catch (error) {
      // Refresh token verification failed
      throw unauthorizedError('Invalid refresh token');
    }

    // Refresh token is valid
    const isValid = validatePermission(roles, refreshTokenDecoded.role);
    if (!isValid) {
      throw forbiddenError('Permission denied');
    }

    user =
      refreshTokenDecoded.role === UserRole.User
        ? await userRepository.findOneBy({
            id: refreshTokenDecoded.userId,
            deletedAt: IsNull(),
          })
        : await userAdminRepository.findOneBy({
            id: refreshTokenDecoded.userId,
            deletedAt: IsNull(),
          });

    if (!user) {
      throw unauthorizedError('User not found');
    }

    if (user && refreshTokenDecoded.role === UserRole.User) {
      if (!user.phone || user.phone !== refreshTokenDecoded.phone) {
        throw unauthorizedError('User not found');
      }
    }

    if (user && 'role' in user && user.role !== refreshTokenDecoded.role) {
      throw unauthorizedError('Invalid user role');
    }

    const tokenService = new TokenService();
    const newAccessToken = tokenService.generateAccessToken({
      userId: refreshTokenDecoded.userId,
      email: refreshTokenDecoded.email,
      phone: refreshTokenDecoded.phone,
      role: refreshTokenDecoded.role,
    });

    cookies().set({
      name: 'accessToken',
      value: newAccessToken,
      maxAge: config.ACCESS_TOKEN_EXPIRE_TIME,
      httpOnly: true,
      secure: true,
    });

    req.user = {
      id: refreshTokenDecoded.userId,
      role: refreshTokenDecoded.role,
    };

    return await next();
  };
};
