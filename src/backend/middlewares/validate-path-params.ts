import { badRequestError } from '@/backend/middlewares/capture-errors';
import { ensureNumber } from '@/utils/number';
import { Params, PipeFunction } from 'next-route-handler-pipe';

export const validatePathParams: PipeFunction = async (_req, event: Params | undefined, next) => {
  if (event && event.params) {
    for (const key in event.params) {
      const [value, error] = ensureNumber(event.params[key]);

      if (error) {
        throw badRequestError(error.message);
      }

      event.params[key] = value;
    }
  }

  return await next();
};
