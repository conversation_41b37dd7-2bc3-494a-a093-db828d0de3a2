import { Input, stringify } from 'csv-stringify';

export function exportCsv(data: Input) {
  if (data.length === 0) return '';
  return new Promise((resolve, reject) => {
    stringify(data, { header: true }, (err, output) => {
      if (err) {
        reject(err);
      } else {
        resolve(output);
      }
    });
  });
}

export const getCsvHeaders = (nameFile: string) => {
  return {
    'Content-Type': 'text/csv; charset=utf-8',
    'Content-Disposition': `attachment; filename=${nameFile}.csv`,
  };
};

export const stringToBlob = (data: string) => {
  // Add BOM to support special characters
  const BOM = '\uFEFF';
  return new Blob([BOM + data], { type: 'text/csv' });
};
