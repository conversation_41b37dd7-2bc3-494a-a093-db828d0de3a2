import { badRequestError, customHttpError } from '@/backend/middlewares/capture-errors';
import { logger } from '@/backend/shared/utils/logger';
import createError from 'http-errors';

export const handleTransactionError = (error: unknown, msg: string): void => {
  const log = logger.child({ description: 'TransactionError' });
  log.warn(error);

  if (createError.isHttpError(error) && error.expose) {
    const { statusCode, message, code } = error;
    throw customHttpError(statusCode, message, code);
  }

  throw badRequestError(msg);
};
