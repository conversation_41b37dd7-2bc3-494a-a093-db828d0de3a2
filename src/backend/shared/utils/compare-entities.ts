import { ObjectLiteral } from 'typeorm';

export const compareEntities = <TEntity1 extends ObjectLiteral, TEntity2 extends ObjectLiteral>(
  e1: TEntity1,
  e2: TEntity2,
  exceptColumns: string[] = ['createdAt', 'updatedAt', 'deletedAt'],
) => {
  const keys1 = Object.keys(e1).filter(key => !exceptColumns.includes(key));
  const keys2 = Object.keys(e2).filter(key => !exceptColumns.includes(key));

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (typeof e1[key] !== typeof e2[key]) return false;
    if (Array.isArray(e1[key]) && Array.isArray(e2[key])) {
      return JSON.stringify(e1[key]) === JSON.stringify(e2[key]);
    }

    if (typeof e1[key] === 'object' && typeof e2[key] === 'object' && e1[key] !== null && e2[key] !== null) {
      if (!compareEntities(e1[key], e2[key])) return false;
    }
    if (e1[key] !== e2[key]) {
      return false;
    }
  }

  return true;
};
