export enum MessagesApi {
  SUCCESS = 'Success',
  COMPANY_EXISTS = 'The company already exists',
  COMPANY_VALID = 'The company not available',
  SPECIALTY_VALID = 'The Specialty not available',
  NAME_COMPANY_SAME_CURRENTLY = 'The name update for the company is the same as currently',
  NAME_COMPANY_VALID = 'The name for company not available',
  DELL_COMPANY_VALID = 'Can not be deleted because the company has applied to the survey',
  INVALID_ID = 'Invalid ID',
  INVALID_BODY = 'Invalid body',
  AUDIENCE_EXISTS = 'The Audience already exists',
  AUDIENCE_VALID = 'The Audience not available',
  NAME_AUDIENCE_VALID = 'The name for audience not available',
  SURVEY_AUDIENCE_EXISTS = 'The survey has added an audience',
  PROVINCE_VALID = 'The province not available',
  PUBLISH_VALID = `Can not publish this survey because it don't have questions`,
  PUBLISH_EMPTY = 'Do not have users match with condition for this survey',
  DATA_NOT_FOUND = 'Data not found',

  // ZOD
  ID_REQUIRED = 'ID is required',
  NAME_REQUIRED = 'Name is required',
  VALUE_REQUIRED = 'Value is required',
  NAME_NOT_EMPTY = 'Name must not be empty',
  VALUE_NONE_NEGATIVE = 'Value must be nonnegative',
  SURVEY_EXISTS = 'The Survey already exists',
  SURVEY_VALID = 'The Survey not available',
  SURVEY_EXPIRED = 'The Survey has expired',
  NAME_SURVEY_VALID = 'The name for Survey not available',
  URL_IMG_VALID = 'The url for image not available',
  UNLIMITED_TRUE = 'If select Unlimited, maxParticipants must be -1',
  UNLIMITED_FALSE = 'If none select Unlimited, maxParticipants must be greater than 0',
  QUESTION_LIST_VALID = 'Question option list must not be empty',

  // AWS
  IMG_REQUIRED = 'Images field is required',
  TYPE_REQUIRED = 'Type field is required or invalid',
  TYPE_FILE_REQUIRED = 'Only PNG or JPG file types are allowed',
  MAX_FILE_SIZE = 'File size exceeds the maximum limit of 3.5 MB',

  // Referral Code
  REFERRAL_CODE_EXISTS = 'Code already exists',
  REFERRAL_CODE_VALID = 'Code not found',
  REFERRAL_MAX_USES_TOO_LOW = 'Max uses can’t be lower than current usage',
  REFERRAL_CAN_NOT_BE_DELETED = 'Cannot delete referral code because it has been used by users',
}
