import { Audience } from '@/backend/audiences/entities/Audience';
import { Company } from '@/backend/companies/entities/Company';
import { Survey } from '@/backend/surveys/entities/Survey';
import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import { SurveyQuestionOption } from '@/backend/surveys/entities/SurveyQuestionOption';
import { SurveyQuestionOptionTranslation } from '@/backend/surveys/entities/SurveyQuestionOptionTranslation';
import { SurveyQuestionTranslation } from '@/backend/surveys/entities/SurveyQuestionTranslation';
import { SurveyTranslation } from '@/backend/surveys/entities/SurveyTranslation';
import { IsNull } from 'typeorm';

export const AUDIENCE_SORTABLE: Array<keyof Audience> = ['id', 'name', 'numberOfUsers', 'lastSurveyDate'];

export const SURVEY_SORTABLE: Array<keyof Survey> = [
  'id',
  'title',
  'description',
  'companyId',
  'expiryDate',
  'status',
  'updatedAt',
  'maxParticipants',
  'successfulCompletions',
];

export const COMPANY_SORTABLE: Array<keyof Company> = [
  'id',
  'name',
  'surveysInProgress',
  'surveysCompleted',
  'lastSurveyDate',
];

export const SURVEY_FIELDS: Array<keyof Survey> = [
  ...SURVEY_SORTABLE,
  'locale',
  'compensation',
  'maxParticipants',
  'responsesPerUser',
  'time',
  'image',
  'backgroundImage',
  'isPinned',
  'status',
  'successfulCompletions',
  'createdAt',
  'createdBy',
  'isPublic',
  'publicQRCode',
];

export const SURVEY_TRANSL_FIELDS: Array<keyof SurveyTranslation> = [
  'id',
  'surveyId',
  'locale',
  'title',
  'description',
];

export const AUDIENCE_FIELDS: Array<keyof Audience> = [
  'id',
  'cities',
  'completedSurveys',
  'employmentStatuses',
  'practiceSettings',
  'provinces',
  'specialtyIds',
  'createdAt',
];

export const SURVEY_QUESTION_KEYS: Array<keyof SurveyQuestion> = [
  'id',
  'surveyId',
  'order',
  'questionType',
  'locale',
  'title',
  'subtitle',
  'minValue',
  'maxValue',
  'maxValue',
  'hasOtherOption',
  'isMultiSelectionEnabled',
];
export const SURVEY_QUESTION_TRANSLATION_KEYS: Array<keyof SurveyQuestionTranslation> = [
  'id',
  'questionId',
  'locale',
  'title',
  'subtitle',
];

export const SURVEY_QUESTION_OPTION_KEYS: Array<keyof SurveyQuestionOption> = [
  'id',
  'questionId',
  'title',
  'isOther',
  'isEligible',
];

export const SURVEY_QUESTION_OPTION_TRANSLATION_KEYS: Array<keyof SurveyQuestionOptionTranslation> = [
  'id',
  'questionOptionId',
  'locale',
  'title',
];

// *** EXTENSION*** //
export const NOT_DELETED = { deletedAt: IsNull() };
