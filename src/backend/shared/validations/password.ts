import { z } from 'zod';

const minLengthRegex = /^.{8,}$/;
const includedUppercaseRegex = /[A-Z]/;
const includedLowercaseRegex = /[a-z]/;
const includeNumericRegex = /\d/;
const includedSpecialCharRegex = /[@$!%*?&]/;

export const passwordShape = z
  .string({ required_error: 'Minimum 8 characters long' })
  .optional()
  .refine(val => val && minLengthRegex.test(val), {
    message: 'Minimum 8 characters long',
  })
  .refine(val => val && includedUppercaseRegex.test(val), {
    message: 'At least 1 uppercase character',
  })
  .refine(val => val && includedLowercaseRegex.test(val), {
    message: 'At least 1 lowercase character',
  })
  .refine(val => val && includeNumericRegex.test(val), {
    message: 'At least 1 number',
  })
  .refine(val => val && includedSpecialCharRegex.test(val), {
    message: 'At least 1 special character (e.g. @)',
  });

export const passwordSchema = z.object({
  password: passwordShape,
});
