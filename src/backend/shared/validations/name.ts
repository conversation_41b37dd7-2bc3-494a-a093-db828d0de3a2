import { z } from 'zod';

export const nameShape = z
  .string({ required_error: 'Name is required' })
  .trim()
  .min(1, { message: 'Name can not be empty' })
  .max(50, { message: 'Name must be less than 50 characters' });

export const specificName = (type: string) => {
  return z
    .string({ required_error: `${type} Name is required` })
    .trim()
    .min(1, { message: `${type} Name can not be empty` })
    .max(50, { message: `${type} Name must be less than 50 characters` });
};

export const nameSchema = z.object({
  name: nameShape,
});
