import { Audience } from '@/backend/audiences/entities/Audience';
import { Survey } from '@/backend/surveys/entities/Survey';
import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, type Relation } from 'typeorm';

@Entity('survey_audiences')
export class SurveyAudience {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  surveyId: number;

  @Index()
  @Column({ type: 'int' })
  audienceId: number;

  @ManyToOne(() => Survey, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'surveyId' })
  survey: Relation<Survey>;

  @ManyToOne(() => Audience)
  @JoinColumn({ name: 'audienceId' })
  audience: Relation<Audience>;
}
