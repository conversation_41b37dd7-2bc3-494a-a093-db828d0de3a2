import { ScreeningResponse } from '@/backend/surveys/entities/ScreeningResponse';
import { User } from '@/backend/users/entities/User';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('screening_results')
export class ScreeningResult {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  surveyId: number;

  @Column({ type: 'int', nullable: true })
  userId: number | null;

  @Column({ type: 'varchar', length: 200, nullable: true })
  email: string | null;

  @Column({ type: 'boolean', default: false })
  isPassed: boolean;

  @Column({ type: 'timestamp', nullable: true })
  startDate: Date | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userId' })
  user: Relation<User> | null;

  @OneToMany(() => ScreeningResponse, response => response.screeningResult)
  screeningResponses: Relation<ScreeningResponse>[];
}
