import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('survey_question_translations')
export class SurveyQuestionTranslation {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  questionId: number;

  @Index()
  @Column({ type: 'int' })
  surveyId: number;

  @Column({ type: 'varchar', length: 10 })
  locale: string;

  @Column({ type: 'varchar', length: 4000 })
  title: string;

  @Column({ type: 'varchar', length: 4000, nullable: true })
  subtitle: string | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => SurveyQuestion, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'questionId' })
  surveyQuestion: Relation<SurveyQuestion>;
}
