import { Locale } from '@/backend/surveys/entities/Survey';
import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';
import { SurveyQuestionOptionTranslation } from './SurveyQuestionOptionTranslation';

@Entity('survey_question_options')
export class SurveyQuestionOption {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  questionId: number;

  @Index()
  @Column({ type: 'int' })
  surveyId: number;

  @Column({ type: 'varchar', length: 4000 })
  title: string;

  @Column({ type: 'boolean', default: false })
  isOther: boolean;

  @Column({ type: 'boolean', default: false })
  isEligible: boolean;

  @Column({
    type: 'enum',
    enum: Locale,
    default: Locale.EN,
  })
  locale: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => SurveyQuestion, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'questionId' })
  surveyQuestion: Relation<SurveyQuestion>;

  translation: SurveyQuestionOptionTranslation;
}
