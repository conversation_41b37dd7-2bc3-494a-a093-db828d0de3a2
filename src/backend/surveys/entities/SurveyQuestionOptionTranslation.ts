import { SurveyQuestionOption } from '@/backend/surveys/entities/SurveyQuestionOption';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('survey_question_option_translations')
export class SurveyQuestionOptionTranslation {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  questionOptionId: number;

  @Column({ type: 'int' })
  questionId: number;

  @Index()
  @Column({ type: 'int' })
  surveyId: number;

  @Column({ type: 'varchar', length: 10 })
  locale: string;

  @Column({ type: 'varchar', length: 4000 })
  title: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => SurveyQuestionOption, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'questionOptionId' })
  surveyQuestionOption: Relation<SurveyQuestionOption>;
}
