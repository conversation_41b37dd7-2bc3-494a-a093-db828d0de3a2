import { Locale, Survey } from '@/backend/surveys/entities/Survey';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';
import { SurveyQuestionOption } from './SurveyQuestionOption';
import { SurveyQuestionTranslation } from './SurveyQuestionTranslation';

export enum SurveyQuestionType {
  SingleSelection = 'SingleSelection',
  MultipleSelection = 'MultipleSelection',
  Text = 'Text',
  Number = 'Number',
  Date = 'Date',
  Slider = 'Slider',
  Rank = 'Rank',
  Screening = 'Screening',
}

@Entity('survey_questions')
export class SurveyQuestion {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  surveyId: number;

  @Column({ type: 'int', default: 0 })
  order: number;

  @Column({
    type: 'enum',
    enum: SurveyQuestionType,
  })
  questionType: SurveyQuestionType;

  @Column({
    type: 'enum',
    enum: Locale,
    default: Locale.EN,
  })
  locale: string;

  @Column({ type: 'varchar', length: 4000 })
  title: string;

  @Column({ type: 'varchar', length: 4000, nullable: true })
  subtitle: string | null;

  @Column({ type: 'int', nullable: true })
  minValue: number | null;

  @Column({ type: 'int', nullable: true })
  maxValue: number | null;

  @Column({ type: 'boolean', default: false })
  hasOtherOption: boolean;

  /**
   * Indicates whether multiple options can be selected for Screening questions.
   * When true, users can select multiple choices in screening questions.
   * When false, users can only select a single option.
   */
  @Column({ type: 'boolean', default: false })
  isMultiSelectionEnabled: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => Survey, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'surveyId' })
  survey: Relation<Survey>;

  @OneToMany(() => SurveyQuestionOption, option => option.surveyQuestion)
  options: SurveyQuestionOption[];

  translation: SurveyQuestionTranslation;
}
