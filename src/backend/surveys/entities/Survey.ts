import { Audience } from '@/backend/audiences/entities/Audience';
import { Company } from '@/backend/companies/entities/Company';
import { DecimalColumnTransformer } from '@/backend/shared/utils/data-transform';
import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';
import { SurveyTranslation } from './SurveyTranslation';

export enum Locale {
  EN = 'En',
  FR = 'Fr',
}

export enum SurveyStatus {
  Active = 'Active',
  Draft = 'Draft',
  Expired = 'Expired',
}

@Entity('surveys')
export class Survey {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  companyId: number;

  @Column({
    type: 'enum',
    enum: Locale,
    default: Locale.EN,
  })
  locale: string;

  @Column({ type: 'varchar', length: 500 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: new DecimalColumnTransformer(),
  })
  compensation: number;

  @Column({ type: 'int', nullable: true })
  maxParticipants: number | null;

  @Column({ type: 'int' })
  time: number;

  @Column({ type: 'date' })
  expiryDate: Date;

  @Column({ type: 'text', nullable: true })
  image: string | null;

  @Column({ type: 'text', nullable: true })
  backgroundImage: string | null;

  @Column({ type: 'boolean', default: false })
  isPinned: boolean;

  @Column({
    type: 'enum',
    enum: SurveyStatus,
    default: SurveyStatus.Draft,
  })
  status: SurveyStatus;

  @Column({ type: 'int', default: 1 })
  responsesPerUser: number;

  @Column({ type: 'int', default: 0 })
  successfulCompletions: number;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date | null;

  @Column({ type: 'int', nullable: true })
  createdBy: number;

  @Column({ type: 'timestamp', nullable: true })
  startDate: Date | null;

  @Column({ type: 'boolean', default: false })
  isPublic: boolean;

  @Column({ type: 'text', nullable: true })
  publicQRCode: string | null;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  company: Relation<Company>;

  @ManyToOne(() => UserAdmin)
  @JoinColumn({ name: 'createdBy' })
  userAdmin: Relation<UserAdmin>;

  @OneToMany(() => UserSurvey, userSurvey => userSurvey.survey)
  userSurveys: UserSurvey[];

  audience: Audience;

  translation: SurveyTranslation;

  questions: SurveyQuestion[];

  walletPayout: number | null;

  estimatedFunds: number | null;
}
