import { ScreeningResult } from '@/backend/surveys/entities/ScreeningResult';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('screening_responses')
export class ScreeningResponse {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  screeningResultId: number;

  @Column({ type: 'int' })
  questionId: number;

  @Column({ type: 'int' })
  questionOptionId: number;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => ScreeningResult, result => result.screeningResponses)
  @JoinColumn({ name: 'screeningResultId' })
  screeningResult: Relation<ScreeningResult>;
}
