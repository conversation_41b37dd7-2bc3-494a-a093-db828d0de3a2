import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import { SurveyQuestionOption } from '@/backend/surveys/entities/SurveyQuestionOption';
import { UserSurveyAnswer } from '@/backend/users/entities/UserSurveyAnswer';

type OptionCalculate = {
  count: number;
  percentage: number;
};

export type QuestionUserAnswers = SurveyQuestion & {
  answers: UserSurveyAnswer[];
  optionsCalculate: Record<number, OptionCalculate>;
  optionsRanking: Record<number, number>;
  totalUsers: number;
  options: SurveyQuestionOption[];
};
