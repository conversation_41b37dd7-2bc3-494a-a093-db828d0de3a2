import { PublishSurveyPayload } from '@/backend/surveys/validations/publish-survey';

export interface TranslationPayload {
  id?: number;
  surveyId?: number;
  questionOptionId?: number;
  locale?: string;
  title?: string;
  description?: string;
}

export interface SurveyQuestionOptionPayload {
  id?: number;
  surveyId?: number;
  title?: string;
}

export type AudienceSettings = Omit<PublishSurveyPayload, 'isPublish' | 'audienceId'>;
