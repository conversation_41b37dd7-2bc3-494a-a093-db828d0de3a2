import { dataSource } from '@/backend/database/data-source';
import { SurveyAudienceRepository } from '../repositories/survey-audiences-repository';

export class SurveyAudienceService {
  private surveyAudienceRepository = new SurveyAudienceRepository(dataSource);
  constructor() {}

  public async delByAudienceId(audienceId: number): Promise<void> {
    await this.surveyAudienceRepository.delByAudienceId(audienceId);
  }
}
