import { dataSource } from '@/backend/database/data-source';
import { badRequestError } from '@/backend/middlewares/capture-errors';
import { CurrentUser } from '@/backend/shared/types/app';
import { stringToBlob } from '@/backend/shared/utils/export-csv';
import { ScreeningResult } from '@/backend/surveys/entities/ScreeningResult';
import { ScreeningResponseRepository } from '@/backend/surveys/repositories/screening-response-repository';
import { ScreeningResultRepository } from '@/backend/surveys/repositories/screening-result-repository';
import { SurveyRepository } from '@/backend/surveys/repositories/survey-repository';
import { User } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { UserSurveyAnswer } from '@/backend/users/entities/UserSurveyAnswer';
import { UserSurveyAnswerRepository } from '@/backend/users/repositories/user-survey-answers';
import { convertToEstTimezone } from '@/utils/date-format';
import { randomUUID } from 'crypto';
import { format } from 'date-fns';
import { In } from 'typeorm';
import { Survey } from '../entities/Survey';
import { SurveyQuestion, SurveyQuestionType } from '../entities/SurveyQuestion';
import { SurveyQuestionOption } from '../entities/SurveyQuestionOption';
import { SurveyQuestionTranslation } from '../entities/SurveyQuestionTranslation';
import { SurveyQuestionOptionRepository } from '../repositories/survey-questions-option-repository';
import { SurveyQuestionsRepository } from '../repositories/survey-questions-repository';

type SurveyExportData = {
  questions: SurveyQuestion[];
  questionOptions: SurveyQuestionOption[];
  answers: UserSurveyAnswer[];
  screeningResults: ScreeningResult[];
};

type FormattedAnswer = {
  timestamp: Date;
  answersMap: Map<number, UserSurveyAnswer[]>;
  user: User;
  responseId: number;
  startDate: Date | null;
};

type OptionInfo = {
  title: string;
  isMultiple: boolean;
};

const DATE_FORMAT = 'yyyy-MM-dd HH:mm:ss';

export class SurveyExportService {
  private readonly surveyQuestionRepository = new SurveyQuestionsRepository(dataSource);
  private readonly surveyQuestionOptionRepository = new SurveyQuestionOptionRepository(dataSource);
  private readonly userSurveyAnswerRepository = new UserSurveyAnswerRepository(dataSource);
  private readonly surveyRepository = new SurveyRepository(dataSource);
  private readonly screeningResultRepository = new ScreeningResultRepository(dataSource);
  private readonly screeningResponseRepository = new ScreeningResponseRepository(dataSource);

  constructor() {}

  public async exportSurvey(id: number, user: CurrentUser): Promise<Blob> {
    if (user.role === AdminRole.AccountManager) {
      const survey = await this.surveyRepository.findOneBy({
        id,
        createdBy: user.id,
        isPublic: true,
      });

      if (!survey) {
        throw badRequestError('Survey not found or you do not have permission to access it');
      }
    }

    const data = await this.fetchSurveyData(id);
    const answers = [...data.answers, ...this.mapScreeningResultsToUserSurveyAnswers(data.screeningResults)].sort(
      (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
    );
    const formattedAnswers = this.formatAnswers(answers);
    const optionsMap = this.createOptionsMap(data.questionOptions);
    const header = this.createHeader(data.questions);
    const content = this.createContent(data.questions, formattedAnswers, optionsMap);

    return stringToBlob(header + '\n' + content);
  }

  private async fetchSurveyData(id: number): Promise<SurveyExportData> {
    const questions = await this.fetchQuestions(id);
    const questionIds = questions.map(item => item.id);
    const [questionOptions, answers, screeningResults] = await Promise.all([
      this.fetchQuestionOptions(questionIds),
      this.fetchAnswers(id, questionIds),
      this.fetchScreeningResults(id),
    ]);

    return { questions, questionOptions, answers, screeningResults };
  }

  private async fetchQuestions(id: number): Promise<SurveyQuestion[]> {
    return this.surveyQuestionRepository
      .createQueryBuilder('question')
      .where('question.surveyId = :id', { id })
      .leftJoinAndMapOne(
        'question.translation',
        SurveyQuestionTranslation,
        'translation',
        'translation.questionId = question.id',
      )
      .leftJoinAndMapMany('question.options', SurveyQuestionOption, 'option', 'option.questionId = question.id')
      .leftJoinAndMapOne('question.survey', Survey, 'survey', 'survey.id = question.surveyId')
      .orderBy('question.order', 'ASC')
      .addOrderBy('option.id', 'ASC')
      .getMany();
  }

  private async fetchQuestionOptions(questionIds: number[]): Promise<SurveyQuestionOption[]> {
    return this.surveyQuestionOptionRepository.find({
      where: { questionId: In(questionIds) },
      relations: {
        surveyQuestion: true,
      },
    });
  }

  private async fetchAnswers(surveyId: number, questionIds: number[]): Promise<UserSurveyAnswer[]> {
    return this.userSurveyAnswerRepository.find({
      where: {
        questionId: In(questionIds),
        userSubmission: {
          surveyId,
        },
      },
      relations: {
        userSubmission: {
          user: {
            specialty: true,
          },
        },
      },
      order: {
        createdAt: 'ASC',
        questionOptionId: 'ASC',
      },
    });
  }

  private formatAnswers(answers: UserSurveyAnswer[]): Record<number, FormattedAnswer> {
    return answers.reduce(
      (acc, answer) => {
        if (!acc[answer.submissionId]) {
          acc[answer.submissionId] = {
            timestamp: answer.createdAt,
            answersMap: new Map<number, UserSurveyAnswer[]>(),
            user: answer.userSubmission.user,
            responseId: answer.userSubmission.responseId,
            startDate: answer.userSubmission.startDate,
          };
        }
        const { answersMap } = acc[answer.submissionId];
        const arrAnswers = answersMap.get(answer.questionId) || [];
        arrAnswers.push(answer);
        acc[answer.submissionId].answersMap.set(answer.questionId, arrAnswers);
        return acc;
      },
      {} as Record<number, FormattedAnswer>,
    );
  }

  private createOptionsMap(options: SurveyQuestionOption[]): Map<number, OptionInfo> {
    return new Map(
      options.map(option => [
        option.id,
        {
          title: option.title,
          isMultiple:
            option.surveyQuestion.questionType === SurveyQuestionType.MultipleSelection ||
            (option.surveyQuestion.questionType === SurveyQuestionType.Screening &&
              option.surveyQuestion.isMultiSelectionEnabled),
        },
      ]),
    );
  }

  private createHeader(questions: SurveyQuestion[]): string {
    const baseHeader =
      'User ID,First Name,Last Name,Gender,Specialty,User Role,Response ID,Email,Birthday,Province,City,Postal Code,Practice Setting,Employment Status,Time submitted survey,Survey published time,User survey start time,';
    const questionHeaders = questions.map((ques, idx) => this.formatQuestionHeader(ques, idx));
    return baseHeader + questionHeaders.join(',');
  }

  private formatQuestionHeader(question: SurveyQuestion, index: number): string {
    let title = '';
    if (question.questionType === SurveyQuestionType.Screening) {
      title = 'Screening - ';
    }

    title += `Question ${index + 1} - [EN] ${question.title}`;
    if (question.translation) {
      title += ` - [FR] ${question.translation.title}`;
    }

    if (
      question.questionType === SurveyQuestionType.Rank ||
      question.questionType === SurveyQuestionType.MultipleSelection ||
      (question.questionType === SurveyQuestionType.Screening && question.isMultiSelectionEnabled)
    ) {
      const optionHeaders = question.options.map(option => {
        const headerTitle = option.isOther ? `${title}-Other` : `${title}-${option.title}`;
        return this.formatWithComma(headerTitle);
      });
      return optionHeaders.join(',');
    }
    return this.formatWithComma(title);
  }

  private createContent(
    questions: SurveyQuestion[],
    formattedAnswers: Record<number, FormattedAnswer>,
    optionsMap: Map<number, OptionInfo>,
  ): string {
    const rows = Object.entries(formattedAnswers).map(([_, value]) => {
      const row = this.createRow(questions, value, optionsMap);
      return [value.timestamp, row] as [Date, string];
    });

    rows.sort((a, b) => a[0].getTime() - b[0].getTime());
    return rows.map(row => row[1]).join('\n');
  }

  private createRow(
    questions: SurveyQuestion[],
    formattedAnswer: FormattedAnswer,
    optionsMap: Map<number, OptionInfo>,
  ): string {
    const { user, timestamp, answersMap, responseId, startDate: userSurveyStartTime } = formattedAnswer;
    const userInfo = this.formatUserInfo(user, timestamp, responseId?.toString() ?? '');
    const surveyInfo = this.formatSurveyInfo(questions[0].survey, userSurveyStartTime);

    const answersString = questions
      .map(question => {
        const answers = answersMap.get(question.id) || [];
        return this.formatQuestionAnswers(question, answers, optionsMap);
      })
      .join(',');

    return `${userInfo},${surveyInfo},${answersString}`;
  }

  private formatUserInfo(user: User, timestamp: Date, responseId: string): string {
    const {
      id,
      firstName,
      lastName,
      gender,
      specialty,
      email,
      birthday,
      province,
      city,
      postalCode,
      practiceSetting,
      employmentStatus,
      userType,
    } = user;
    const timeStr = convertToEstTimezone(timestamp, DATE_FORMAT);
    const dob = birthday ? format(birthday, 'yyyy-MM-dd') : '';

    return [
      id,
      this.formatWithComma(firstName ?? ''),
      this.formatWithComma(lastName ?? ''),
      gender,
      this.formatWithComma(specialty ? specialty.name : ''),
      userType,
      responseId,
      email,
      dob,
      this.formatWithComma(province ?? ''),
      this.formatWithComma(city ?? ''),
      this.formatWithComma(postalCode ?? ''),
      this.formatWithComma(practiceSetting ?? ''),
      this.formatWithComma(employmentStatus ?? ''),
      timeStr,
    ].join(',');
  }

  private formatQuestionAnswers(
    question: SurveyQuestion,
    answers: UserSurveyAnswer[],
    optionsMap: Map<number, OptionInfo>,
  ): string {
    let mappedAnswers: (string | undefined)[] = [];

    if (
      question.questionType === SurveyQuestionType.MultipleSelection ||
      (question.questionType === SurveyQuestionType.Screening && question.isMultiSelectionEnabled)
    ) {
      mappedAnswers = question.options.map(option => {
        const selectedAnswer = answers.find(answer => answer.questionOptionId === option.id);
        if (!selectedAnswer) return '';

        if (option.isOther) {
          return this.formatWithComma(selectedAnswer.value || '');
        }
        return 'true';
      });
    } else if (question.questionType === SurveyQuestionType.Rank) {
      mappedAnswers = question.options.map(option => {
        const rankAnswer = answers.find(answer => answer.questionOptionId === option.id);
        return rankAnswer ? this.formatWithComma(rankAnswer.value) : '';
      });
    } else {
      mappedAnswers = answers.map(answer => this.formatAnswer(answer, question, optionsMap));
    }

    mappedAnswers = mappedAnswers.filter(answer => answer !== undefined);
    return mappedAnswers.join(',');
  }

  private formatAnswer(
    answer: UserSurveyAnswer,
    question: SurveyQuestion,
    optionsMap: Map<number, OptionInfo>,
  ): string {
    if (!answer.questionOptionId) return this.formatWithComma(answer.value);
    if (question.questionType === SurveyQuestionType.SingleSelection && answer.value) {
      return this.formatWithComma(answer.value);
    }

    return this.formatWithComma(optionsMap.get(answer.questionOptionId)?.title ?? '');
  }

  private formatWithComma(value: string): string {
    if (!value) {
      return '';
    }

    // Check if the value contains a comma, double quote, or newline
    // If it does, we need to wrap it in double quotes
    const needsQuoting = /[",\r\n]/.test(value);

    if (needsQuoting) {
      const escaped = value.replace(/"/g, '""');
      return `"${escaped}"`;
    }

    return value;
  }

  private formatSurveyInfo(survey: Survey, userSurveyStartTime: Date | null): string {
    return [
      survey.startDate ? convertToEstTimezone(survey.startDate, DATE_FORMAT) : '',
      userSurveyStartTime ? convertToEstTimezone(userSurveyStartTime, DATE_FORMAT) : '',
    ].join(',');
  }

  private async fetchScreeningResults(surveyId: number): Promise<ScreeningResult[]> {
    const survey = await this.surveyRepository.findOneBy({ id: surveyId });
    if (!survey) {
      return [];
    }

    return await this.screeningResultRepository.find({
      where: { surveyId, isPassed: false },
      relations: {
        screeningResponses: true,
        user: {
          specialty: true,
        },
      },
    });
  }

  private mapScreeningResultsToUserSurveyAnswers(screeningResults: ScreeningResult[]): UserSurveyAnswer[] {
    return screeningResults.flatMap(screeningResult => {
      const id = randomUUID();

      return (screeningResult.screeningResponses || []).map(response => ({
        questionId: response.questionId,
        questionOptionId: response.questionOptionId,
        value: null,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt,
        submissionId: id,
        userSubmission: {
          id: id,
          surveyId: screeningResult.surveyId,
          userId: screeningResult.userId,
          createdAt: response.createdAt,
          updatedAt: response.updatedAt,
          user: screeningResult.user || undefined,
          startDate: screeningResult.startDate || undefined,
        },
      }));
    }) as unknown as UserSurveyAnswer[];
  }
}
