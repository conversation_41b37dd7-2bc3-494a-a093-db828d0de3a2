import { dataSource } from '@/backend/database/data-source';
import { badRequestError, forbiddenError, notFoundError } from '@/backend/middlewares/capture-errors';
import {
  SURVEY_QUESTION_OPTION_KEYS,
  SURVEY_QUESTION_OPTION_TRANSLATION_KEYS,
  SURVEY_QUESTION_TRANSLATION_KEYS,
} from '@/backend/shared/common/keyof';
import { CurrentUser } from '@/backend/shared/types/app';
import { handleTransactionError } from '@/backend/shared/utils/handle-transaction-error';
import { Locale, Survey } from '@/backend/surveys/entities/Survey';
import { SurveyQuestionOption } from '@/backend/surveys/entities/SurveyQuestionOption';
import { SurveyQuestionOptionTranslation } from '@/backend/surveys/entities/SurveyQuestionOptionTranslation';
import { SurveyQuestionTranslation } from '@/backend/surveys/entities/SurveyQuestionTranslation';
import { SurveyRepository } from '@/backend/surveys/repositories/survey-repository';
import { CreateQuestionPayload, QuestionOptionPayload } from '@/backend/surveys/validations/create-survey';
import {
  UpdateQuestionOrderPayload,
  UpdateSurveyQuestionPayload,
} from '@/backend/surveys/validations/update-survey-question';
import { UserAction } from '@/backend/users/entities/UserAction';
import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { PublicSurveyAnswerRepository } from '@/backend/users/repositories/public-survey-answer-repository';
import { UserSurveyRepository } from '@/backend/users/repositories/user-survey-repository';
import { EntitiesName } from '@/backend/users/services/user-action-service';
import { UserRole } from '@/backend/users/types/user';
import { createPayloadModel } from '@/lib/utils';
import { MoreThan, QueryRunner } from 'typeorm';
import { SurveyQuestion, SurveyQuestionType } from '../entities/SurveyQuestion';
import { SurveyTranslation } from '../entities/SurveyTranslation';
import { SurveyQuestionOptionRepository } from '../repositories/survey-questions-option-repository';
import { SurveyQuestionOptionTranslationRepository } from '../repositories/survey-questions-option-translation-repository';
import { SurveyQuestionsRepository } from '../repositories/survey-questions-repository';
import { SurveyQuestionTranslationRepository } from '../repositories/survey-questions-translation-repository';
import { SurveyQuestionOptionPayload, TranslationPayload } from '../types/survey';

export class SurveyQuestionService {
  private readonly surveyQuestionRepository = new SurveyQuestionsRepository(dataSource);
  private readonly questionTranslationRepository = new SurveyQuestionTranslationRepository(dataSource);
  private readonly questionOptionRepository = new SurveyQuestionOptionRepository(dataSource);
  private readonly questionOptionTranslationRepository = new SurveyQuestionOptionTranslationRepository(dataSource);
  private readonly userSurveyRepository = new UserSurveyRepository(dataSource);
  private readonly surveyRepository = new SurveyRepository(dataSource);
  private readonly publicSurveyAnswerRepository = new PublicSurveyAnswerRepository(dataSource);

  constructor() {}

  public async listQuestions(surveyId: number, user: CurrentUser): Promise<SurveyQuestion[]> {
    const survey = await this.surveyRepository.findOneBy({
      id: surveyId,
      createdBy: user.role === AdminRole.AccountManager ? user.id : undefined,
      isPublic: user.role === AdminRole.AccountManager ? true : undefined,
    });

    if (!survey) {
      throw notFoundError('Survey not found');
    }

    const questions = await this.surveyQuestionRepository.questionsBySurveyId(surveyId);

    if (user.role !== UserRole.User) {
      for (const question of questions) {
        if (question.hasOtherOption) {
          question.options = question.options.filter(option => !option.isOther);
        }
      }
    } else {
      // For regular users, remove the field `isEligible` from options
      for (const question of questions) {
        question.options = question.options.map(option => {
          const { isEligible: _isEligible, ...rest } = option;
          return rest as SurveyQuestionOption;
        });
      }

      // For regular users, sort screening questions by order
      questions.sort((a, b) => {
        if (a.questionType === SurveyQuestionType.Screening && b.questionType !== SurveyQuestionType.Screening) {
          return -1; // Screening questions come first
        }
        if (b.questionType === SurveyQuestionType.Screening && a.questionType !== SurveyQuestionType.Screening) {
          return 1; // Screening questions come first
        }
        return a.order - b.order; // Otherwise, sort by order
      });
    }

    return questions;
  }

  public async checkCanAddTranslation(surveyId: number): Promise<boolean> {
    const survey = await this.surveyRepository.validSoftDelById(surveyId);
    return Boolean(survey && survey.translation);
  }

  public async update(data: UpdateSurveyQuestionPayload): Promise<void> {
    const hasResponses = await this.checkIfSurveyHasResponses(data.surveyId);
    if (hasResponses) {
      data = {
        id: data.id,
        surveyId: data.surveyId,
        title: data.title,
        subtitle: data.subtitle,
        translation: data.translation,
        options: data.options
          ? data.options.map(opt => ({
              id: opt.id,
              title: opt.title,
              translation: opt.translation
                ? {
                    id: opt.translation.id,
                    title: opt.translation.title,
                    locale: opt.translation.locale,
                  }
                : undefined,
            }))
          : undefined,
        locale: data.locale,
      };
    }

    const question = await this.surveyQuestionRepository.findOneBy({
      id: data.id,
      surveyId: data.surveyId,
    });

    if (!question) {
      throw notFoundError('Question not found');
    }

    // Validate question data based on question type
    this.validateQuestionData(data, question.questionType);

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await this.surveyQuestionRepository.updateQuestion(data, queryRunner);
      const canAddTranslation = await this.checkCanAddTranslation(data.surveyId);

      // Handle question translation
      await this.handleQuestionTranslation(data, canAddTranslation, queryRunner);

      // Handle question type specific processing
      if (data.questionType && data.questionType !== question.questionType) {
        // Question type has changed
        await this.handleQuestionTypeChange(question, data, canAddTranslation, queryRunner);
      } else {
        // Question type remains the same, handle "Other" option for selection types
        await this.handleOtherOption(question, data, canAddTranslation, queryRunner);
      }

      // Process question options
      await this.handleQuestionOptions(data, canAddTranslation, queryRunner);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();

      handleTransactionError(error, 'Failed to update survey question');
      throw error;
    } finally {
      await queryRunner.release();
    }

    await this.bumpScreeningQuestionsToTop(data.surveyId);
  }

  private async validateQuestionData(
    data: UpdateSurveyQuestionPayload,
    originalType: SurveyQuestionType,
  ): Promise<void> {
    const isSelectionType = (type: string): boolean => {
      return type === SurveyQuestionType.SingleSelection || type === SurveyQuestionType.MultipleSelection;
    };

    const needsOptions = (type: string): boolean => {
      return isSelectionType(type) || type === SurveyQuestionType.Screening;
    };

    if (data.questionType) {
      // If changing to a question type that requires options, validate them
      if (needsOptions(data.questionType) && !needsOptions(originalType)) {
        if (!data.options || data.options.length === 0) {
          throw badRequestError(`Question requires options`);
        }
      }

      // Validate options for selection types and screening questions
      if (needsOptions(data.questionType) && data.options) {
        // Special validation for screening questions
        if (data.questionType === SurveyQuestionType.Screening) {
          // At least one option must be marked as eligible
          const hasEligibleOption = data.options.some(opt => opt.isEligible);
          if (!hasEligibleOption) {
            throw badRequestError('Screening questions must have at least one eligible option');
          }
        }
      }

      // For Slider type, validate min and max values
      if (data.questionType === SurveyQuestionType.Slider && originalType !== SurveyQuestionType.Slider) {
        if (data.minValue === undefined || data.maxValue === undefined) {
          throw badRequestError('Slider questions require minimum and maximum values');
        }

        if (data.minValue >= data.maxValue) {
          throw badRequestError('Minimum value must be less than maximum value for slider questions');
        }
      }
    }
  }

  private async handleQuestionTranslation(
    data: UpdateSurveyQuestionPayload,
    canAddTranslation: boolean,
    queryRunner: QueryRunner,
  ): Promise<void> {
    if (data.translation && canAddTranslation) {
      const payload: TranslationPayload = createPayloadModel(data.translation, SURVEY_QUESTION_TRANSLATION_KEYS);
      if (payload.id) {
        await this.questionTranslationRepository.updateQuestionTranslation(payload, queryRunner);
      } else {
        await this.questionTranslationRepository.createQuestionTranslation(
          payload,
          data.id,
          data.surveyId,
          queryRunner,
        );
      }
    } else {
      await this.questionTranslationRepository.deleteAllTranslationOfQuestion(data.id, queryRunner);
    }
  }

  private async handleQuestionTypeChange(
    question: SurveyQuestion,
    data: UpdateSurveyQuestionPayload,
    canAddTranslation: boolean,
    queryRunner: QueryRunner,
  ): Promise<void> {
    const needsOptions = (type: string): boolean => {
      return (
        type === SurveyQuestionType.SingleSelection ||
        type === SurveyQuestionType.MultipleSelection ||
        type === SurveyQuestionType.Screening
      );
    };

    // When changing from a type that had options to one that doesn't, remove all options
    if (needsOptions(question.questionType) && !needsOptions(data.questionType!)) {
      const currentOptions = await queryRunner.manager.find(SurveyQuestionOption, {
        where: { questionId: data.id, surveyId: data.surveyId },
      });

      if (currentOptions.length) {
        const optionIds = currentOptions.map(option => option.id);
        await this.questionOptionRepository.deleteQuestionOptions(optionIds, queryRunner);
        await this.questionOptionTranslationRepository.deleteTranslationForQuestionOptions(optionIds, queryRunner);
      }

      // Reset option-related flags
      await this.surveyQuestionRepository.updateQuestion(
        {
          ...data,
          hasOtherOption: false,
          isMultiSelectionEnabled: false,
        },
        queryRunner,
      );
    }

    // When changing to selection type, add "Other" option if requested
    if (
      data.questionType === SurveyQuestionType.SingleSelection ||
      data.questionType === SurveyQuestionType.MultipleSelection ||
      data.questionType === SurveyQuestionType.Screening
    ) {
      // Check if "Other" option already exists
      const existingOtherOption = await queryRunner.manager.findOne(SurveyQuestionOption, {
        where: { questionId: data.id, isOther: true },
      });

      // If "Other" option does not exist, create it
      if (!existingOtherOption) {
        if (data.hasOtherOption && data.questionType !== SurveyQuestionType.Screening) {
          // Only create "Other" option if it is requested and not a Screening type

          const newOtherOption = await this.questionOptionRepository.createNewQuestionOption(
            { title: 'Other', surveyId: data.surveyId, questionId: data.id, isOther: true, isEligible: false },
            data.id,
            queryRunner,
          );

          if (data.translation && canAddTranslation) {
            await this.questionOptionTranslationRepository.createNewQuestionOptionTranslation(
              {
                title: 'Autre',
                locale: data.translation.locale,
                surveyId: data.surveyId,
                questionId: data.id,
                questionOptionId: newOtherOption!.id,
              },
              queryRunner,
            );
          }
        }
      } else {
        // If "Other" option exists but is not requested, delete it
        if (data.hasOtherOption === false || data.questionType === SurveyQuestionType.Screening) {
          await this.questionOptionRepository.deleteQuestionOptions([existingOtherOption.id], queryRunner);
          await this.questionOptionTranslationRepository.deleteTranslationForQuestionOptions(
            [existingOtherOption.id],
            queryRunner,
          );
          await this.surveyQuestionRepository.updateQuestion(
            {
              ...data,
              hasOtherOption: false,
            },
            queryRunner,
          );
        }
      }
    }

    // When changing to or from Screening type, handle specific properties
    if (data.questionType === SurveyQuestionType.Screening || question.questionType === SurveyQuestionType.Screening) {
      // If changing to a non-Screening type, reset isMultiSelectionEnabled
      if (
        question.questionType === SurveyQuestionType.Screening &&
        data.questionType !== SurveyQuestionType.Screening
      ) {
        await this.surveyQuestionRepository.updateQuestion(
          {
            ...data,
            isMultiSelectionEnabled: false,
          },
          queryRunner,
        );
      }

      // If changing from a non-Screening type to Screening type
      if (
        question.questionType !== SurveyQuestionType.Screening &&
        data.questionType === SurveyQuestionType.Screening
      ) {
        // Ensure isMultiSelectionEnabled is set properly
        if (data.isMultiSelectionEnabled === undefined) {
          await this.surveyQuestionRepository.updateQuestion(
            {
              ...data,
              isMultiSelectionEnabled: false, // Default value
            },
            queryRunner,
          );
        }
      }
    }
  }

  private async handleOtherOption(
    question: SurveyQuestion,
    data: UpdateSurveyQuestionPayload,
    canAddTranslation: boolean,
    queryRunner: QueryRunner,
  ): Promise<void> {
    // Only handle Other option for selection types, not for Screening type
    if (
      question.questionType === SurveyQuestionType.MultipleSelection ||
      question.questionType === SurveyQuestionType.SingleSelection
    ) {
      const otherOption = await queryRunner.manager.findOne(SurveyQuestionOption, {
        where: { questionId: data.id, isOther: true },
      });

      // Add "Other" option if it doesn't exist but is requested
      if (data.hasOtherOption && !otherOption) {
        const newOtherOption = await this.questionOptionRepository.createNewQuestionOption(
          { title: 'Other', surveyId: data.surveyId, questionId: data.id, isOther: true, isEligible: false },
          data.id,
          queryRunner,
        );

        if (data.translation && canAddTranslation) {
          await this.questionOptionTranslationRepository.createNewQuestionOptionTranslation(
            {
              title: 'Autre',
              locale: data.translation.locale,
              surveyId: data.surveyId,
              questionId: data.id,
              questionOptionId: newOtherOption!.id,
            },
            queryRunner,
          );
        }
        // Remove "Other" option if it exists but is no longer requested
      } else if (data.hasOtherOption !== undefined && !data.hasOtherOption && otherOption) {
        await this.questionOptionRepository.deleteQuestionOptions([otherOption.id], queryRunner);
        await this.questionOptionTranslationRepository.deleteTranslationForQuestionOptions(
          [otherOption.id],
          queryRunner,
        );
      }
    }
  }

  private async handleQuestionOptions(
    data: UpdateSurveyQuestionPayload,
    canAddTranslation: boolean,
    queryRunner: QueryRunner,
  ): Promise<void> {
    const options = data.options;
    if (!options || !options.length) {
      return;
    }

    const currentOptions = await queryRunner.manager.find(SurveyQuestionOption, {
      where: { questionId: data.id, surveyId: data.surveyId },
    });

    // Find options that need to be removed
    const removedOptionIds = currentOptions
      .filter(option => !option.isOther && !options.some(opt => opt.id === option.id))
      .map(option => option.id);

    // Filter out any attempt to modify "Other" option through the options array
    const filteredOptions = options.filter(option => !currentOptions.some(opt => opt.isOther && opt.id === option.id));

    // Delete removed options
    if (removedOptionIds.length) {
      await this.questionOptionRepository.deleteQuestionOptions(removedOptionIds, queryRunner);
    }

    // Process each option
    await Promise.all(
      filteredOptions.map(async item => {
        // For non-screening questions, isEligible should always be false
        if (data.questionType && data.questionType !== SurveyQuestionType.Screening) {
          item.isEligible = false;
        }

        const payload: SurveyQuestionOptionPayload = createPayloadModel(item, SURVEY_QUESTION_OPTION_KEYS);
        let optionId = payload.id;

        // Update or create option
        if (payload.id) {
          await this.questionOptionRepository.updateQuestionOptions(payload, queryRunner);
        } else {
          const option = await this.questionOptionRepository.createNewQuestionOption(
            { ...payload, surveyId: data.surveyId },
            data.id,
            queryRunner,
          );

          if (option) {
            optionId = option.id;
          }
        }

        if (!optionId) {
          return;
        }

        // Handle option translation
        if (!item.translation || !canAddTranslation) {
          await this.questionOptionTranslationRepository.deleteTranslationForQuestionOptions([optionId], queryRunner);
          return;
        }

        if (item.translation.id) {
          const payload: TranslationPayload = createPayloadModel(
            item.translation,
            SURVEY_QUESTION_OPTION_TRANSLATION_KEYS,
          );
          await this.questionOptionTranslationRepository.updateQuestionOptionTranslations(
            { ...payload, questionOptionId: optionId },
            queryRunner,
          );
        } else {
          await this.questionOptionTranslationRepository.createNewQuestionOptionTranslation(
            {
              ...item.translation,
              questionOptionId: optionId,
              surveyId: data.surveyId,
              questionId: data.id,
            },
            queryRunner,
          );
        }
      }),
    );
  }

  public async updateOrder(surveyId: number, data: UpdateQuestionOrderPayload, user: CurrentUser): Promise<void> {
    const survey = await this.surveyRepository.getAvailableSurveyForEditing(surveyId);
    if (user.role === AdminRole.AccountManager && survey.createdBy !== user.id && survey.isPublic) {
      throw forbiddenError('You are not allowed to update this survey question');
    }

    const questions = await this.surveyQuestionRepository.findBy({
      surveyId,
    });

    const questionIds = new Set(data.questions.map(item => item.id));

    if (
      questions.length !== data.questions.length ||
      questions.length !== questionIds.size ||
      questions.some(item => !questionIds.has(item.id))
    ) {
      throw badRequestError('Invalid question list');
    }

    const indexes = data.questions.map(item => item.order).sort((a, b) => a - b);
    for (let i = 0; i < indexes.length; i++) {
      if (indexes[i] !== i + 1) {
        throw badRequestError('Invalid question order');
      }
    }

    await this.surveyQuestionRepository.save(data.questions);
    await this.bumpScreeningQuestionsToTop(surveyId);
  }

  public async delete(surveyId: number, questionId: number, user: CurrentUser): Promise<void> {
    const [survey, surveyQuestion] = await Promise.all([
      this.surveyRepository.findOneBy({ id: surveyId }),
      this.surveyQuestionRepository.findOneBy({ id: questionId }),
    ]);

    if (!survey) {
      throw notFoundError('Survey not found');
    }

    if (!surveyQuestion) {
      throw notFoundError('Question not found');
    }

    if (survey.id !== surveyQuestion.surveyId) {
      throw badRequestError('Question does not belong to this survey');
    }

    if (user.role === AdminRole.AccountManager && survey.createdBy !== user.id && survey.isPublic) {
      throw forbiddenError('You are not allowed to delete this survey question');
    }

    const hasResponses = await this.checkIfSurveyHasResponses(surveyId);
    if (hasResponses) {
      throw badRequestError("You can't delete a question that has responses");
    }

    await this.surveyQuestionRepository.delete({ id: surveyQuestion.id });
    await this.bumpScreeningQuestionsToTop(surveyId);
  }

  public async createCompleteQuestionSet(
    data: CreateQuestionPayload[],
    surveyId: number,
    queryRunner: QueryRunner,
  ): Promise<void> {
    const questions = data;
    const survey = await queryRunner.manager
      .getRepository(Survey)
      .createQueryBuilder('survey')
      .leftJoinAndMapOne(
        'survey.translation',
        SurveyTranslation,
        'survey_translation',
        'survey_translation.surveyId = survey.id',
      )
      .where('survey.id = :surveyId', { surveyId })
      .getOne();
    const canAddTranslation = Boolean(survey && survey.translation);

    // Promise.all doesn't work with TypeORM transactions and not rollback if one of the promises fails
    const questionsSettled = await Promise.allSettled(
      questions.map(async question => {
        // Save question
        const newQuestion = await queryRunner.manager
          .getRepository(SurveyQuestion)
          .createQueryBuilder()
          .insert()
          .values({
            surveyId,
            locale: Locale.EN,
            ...question,
          })
          .execute();

        const questionId = newQuestion.identifiers[0].id;

        // Save question translation
        if (question.translation && canAddTranslation) {
          await queryRunner.manager
            .getRepository(SurveyQuestionTranslation)
            .createQueryBuilder()
            .insert()
            .values({
              surveyId,
              questionId,
              ...question.translation,
            })
            .execute();
        }

        if (question.options) {
          for (const option of question.options) {
            const newOption = await queryRunner.manager
              .getRepository(SurveyQuestionOption)
              .createQueryBuilder()
              .insert()
              .values({
                surveyId,
                questionId,
                title: option.title,
                isOther: (option as QuestionOptionPayload & { isOther?: boolean }).isOther || false,
                isEligible: option.isEligible,
              })
              .execute();

            const optionId = newOption.identifiers[0].id;

            if (option.translation && canAddTranslation) {
              await queryRunner.manager
                .getRepository(SurveyQuestionOptionTranslation)
                .createQueryBuilder()
                .insert()
                .values({
                  surveyId,
                  questionId,
                  questionOptionId: optionId,
                  ...option.translation,
                })
                .execute();
            }
          }
        }
      }),
    );

    for (const res of questionsSettled) {
      if (res.status === 'rejected') {
        throw res.reason;
      }
    }
  }

  public async create(user: CurrentUser, surveyId: number, data: CreateQuestionPayload): Promise<void> {
    const survey = await this.surveyRepository.getAvailableSurveyForEditing(surveyId);

    if (user.role === AdminRole.AccountManager && survey.createdBy !== user.id && survey.isPublic) {
      throw forbiddenError('You are not allowed to create question for this survey');
    }

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const questions = await queryRunner.manager.find(SurveyQuestion, {
        where: {
          surveyId: survey.id,
        },
        order: {
          order: 'DESC',
        },
        take: 1,
      });

      const lastOrder = questions.length ? questions[0].order + 1 : 1;

      if (lastOrder !== data.order) {
        throw badRequestError('Invalid question order');
      }

      if (
        data.hasOtherOption &&
        (data.questionType === SurveyQuestionType.SingleSelection ||
          data.questionType === SurveyQuestionType.MultipleSelection)
      ) {
        // Add other option to question
        data.options!.push({
          title: 'Other',
          translation: data.translation ? { title: 'Autre', locale: data.translation.locale } : undefined,
          isOther: true,
        } as unknown as QuestionOptionPayload);
      }

      await this.createCompleteQuestionSet([data], survey.id, queryRunner);
      await queryRunner.manager
        .getRepository(UserAction)
        .createQueryBuilder()
        .insert()
        .values({
          userId: user.id,
          description: `New Question for Survey - ${survey.title}`,
          entity: EntitiesName.Surveys,
        })
        .execute();

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();

      handleTransactionError(error, 'Failed to create question');
    } finally {
      await queryRunner.release();
    }

    await this.bumpScreeningQuestionsToTop(surveyId);
  }

  private async checkIfSurveyHasResponses(surveyId: number): Promise<boolean> {
    const [userSurvey, publicSurveyResponse] = await Promise.all([
      this.userSurveyRepository.findOneBy([
        { surveyId, isComplete: true },
        { surveyId, isComplete: false, totalSubmissions: MoreThan(0) },
      ]),
      this.publicSurveyAnswerRepository.findOneBy({ surveyId }),
    ]);

    return Boolean(userSurvey || publicSurveyResponse);
  }

  public async bumpScreeningQuestionsToTop(surveyId: number): Promise<void> {
    const questions = await this.surveyQuestionRepository.findBy({
      surveyId,
    });

    const screening = questions
      .filter(q => q.questionType === SurveyQuestionType.Screening)
      .sort((a, b) => a.order - b.order);
    const nonScreening = questions
      .filter(q => q.questionType !== SurveyQuestionType.Screening)
      .sort((a, b) => a.order - b.order);

    const reordered = [...screening, ...nonScreening].map((q, index) => {
      return {
        id: q.id,
        order: index + 1,
      };
    });

    await this.surveyQuestionRepository.save(reordered);
  }
}
