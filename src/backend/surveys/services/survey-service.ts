import { Audience } from '@/backend/audiences/entities/Audience';
import { AudienceRepository } from '@/backend/audiences/repositories/audience-repository';
import { AudienceService } from '@/backend/audiences/services/audience-service';
import { UsersIncludedPayload } from '@/backend/audiences/validations/add-update-audience';
import { Company } from '@/backend/companies/entities/Company';
import { CompanyRepository } from '@/backend/companies/repositories/company-repository';
import { CompanyService } from '@/backend/companies/services/company-service';
import { dataSource } from '@/backend/database/data-source';
import { HubspotService } from '@/backend/hubspot/services/hubspot-service';
import { CompleteWS, HCPVerificationStatus, HubspotContactStatus } from '@/backend/hubspot/types/contact';
import { ProgramProgress } from '@/backend/hubspot/types/program';
import { badRequestError, forbiddenError, notFoundError } from '@/backend/middlewares/capture-errors';
import { NotificationService } from '@/backend/notifications/services';
import { CurrentUser } from '@/backend/shared/types/app';
import { handleTransactionError } from '@/backend/shared/utils/handle-transaction-error';
import { logger } from '@/backend/shared/utils/logger';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { SpecialtyRepository } from '@/backend/specialties/repositories/specialty-repository';
import { ScreeningResponse } from '@/backend/surveys/entities/ScreeningResponse';
import { ScreeningResult } from '@/backend/surveys/entities/ScreeningResult';
import { SurveyAudience } from '@/backend/surveys/entities/SurveyAudience';
import { SurveyQuestion, SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { SurveyQuestionOption } from '@/backend/surveys/entities/SurveyQuestionOption';
import { SurveyQuestionOptionTranslation } from '@/backend/surveys/entities/SurveyQuestionOptionTranslation';
import { SurveyQuestionTranslation } from '@/backend/surveys/entities/SurveyQuestionTranslation';
import { SurveyTranslation } from '@/backend/surveys/entities/SurveyTranslation';
import { ScreeningResponseRepository } from '@/backend/surveys/repositories/screening-response-repository';
import { ScreeningResultRepository } from '@/backend/surveys/repositories/screening-result-repository';
import { SurveyAudienceRepository } from '@/backend/surveys/repositories/survey-audiences-repository';
import { SurveyQrRepository } from '@/backend/surveys/repositories/survey-qr-repository';
import { SurveyQuestionsRepository } from '@/backend/surveys/repositories/survey-questions-repository';
import { SurveyQuestionService } from '@/backend/surveys/services/survey-question-service';
import { QuestionUserAnswers } from '@/backend/surveys/types/question-user-answers';
import { inputQuestions } from '@/backend/surveys/types/survey-question';
import { UserAnswer } from '@/backend/surveys/types/user-answer';
import { CreateSurveyPayload, QuestionOptionPayload } from '@/backend/surveys/validations/create-survey';
import { PublishSurveyPayload } from '@/backend/surveys/validations/publish-survey';
import { SubmitScreeningPayload } from '@/backend/surveys/validations/submit-screening';
import {
  SubmitPrivateSurveyPayload,
  SubmitPublicSurveyPayload,
  SubmitSurveyPayload,
  SurveyAnswer,
} from '@/backend/surveys/validations/submit-survey';
import { UpdateSurveyPayload } from '@/backend/surveys/validations/update-survey';
import { PublicSurveyAnswer, PublicSurveyProgress } from '@/backend/users/entities/PublicSurveyAnswer';
import { ContactStatus, User, VerificationStatus } from '@/backend/users/entities/User';
import { UserAction } from '@/backend/users/entities/UserAction';
import { AdminRole, UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserSubmission } from '@/backend/users/entities/UserSubmission';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import { UserSurveyAnswer } from '@/backend/users/entities/UserSurveyAnswer';
import { TransactionType, UserTransaction } from '@/backend/users/entities/UserTransaction';
import { PublicSurveyAnswerRepository } from '@/backend/users/repositories/public-survey-answer-repository';
import { UserAdminRepository } from '@/backend/users/repositories/user-admin-repository';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { UserSubmissionRepository } from '@/backend/users/repositories/user-submissions';
import { UserSurveyRepository } from '@/backend/users/repositories/user-survey-repository';
import { EntitiesName, UserActionService } from '@/backend/users/services/user-action-service';
import { Role, UserRole } from '@/backend/users/types/user';
import config from '@/config';
import { createPayloadUserIncluded } from '@/lib/utils';
import { PaginationPayload, PaginationResponseData } from '@/types/pagination';
import { put } from '@vercel/blob';
import { waitUntil } from '@vercel/functions';
import { randomUUID } from 'crypto';
import { format, isValid, startOfTomorrow } from 'date-fns';
import createError from 'http-errors';
import _ from 'lodash';
import QRCode, { QRCodeToBufferOptions } from 'qrcode';
import { Brackets, ILike, In, IsNull, LessThan, MoreThan, Not, QueryRunner } from 'typeorm';
import { Locale, Survey, SurveyStatus } from '../entities/Survey';
import { SurveyQuestionOptionTranslationRepository } from '../repositories/survey-questions-option-translation-repository';
import { SurveyQuestionTranslationRepository } from '../repositories/survey-questions-translation-repository';
import { SurveyRepository } from '../repositories/survey-repository';
import { SurveyTranslationRepository } from '../repositories/survey-translation-repository';
import { AudienceSettings } from '../types/survey';

export class SurveyService {
  private readonly surveyRepository = new SurveyRepository(dataSource);
  private readonly surveyTranslationRepository = new SurveyTranslationRepository(dataSource);
  private readonly surveyQuestionRepository = new SurveyQuestionsRepository(dataSource);
  private readonly surveyQuestionTranslationRepository = new SurveyQuestionTranslationRepository(dataSource);
  private readonly surveyQuestionOptionTranslationRepository = new SurveyQuestionOptionTranslationRepository(
    dataSource,
  );
  private readonly userSurveyRepository = new UserSurveyRepository(dataSource);
  private readonly userRepository = new UserRepository(dataSource);
  private readonly userAdminRepository = new UserAdminRepository(dataSource);
  private readonly companyRepository = new CompanyRepository(dataSource);
  private readonly specialtyRepository = new SpecialtyRepository(dataSource);
  private readonly surveyAudienceRepository = new SurveyAudienceRepository(dataSource);
  private readonly audienceRepository = new AudienceRepository(dataSource);
  private readonly publicSurveyAnswerRepository = new PublicSurveyAnswerRepository(dataSource);
  private readonly surveyQrRepository = new SurveyQrRepository(dataSource);
  private readonly screeningResultRepository = new ScreeningResultRepository(dataSource);
  private readonly screeningResponseRepository = new ScreeningResponseRepository(dataSource);

  private readonly audienceService = new AudienceService();
  private readonly surveyQuestionService = new SurveyQuestionService();
  private readonly notificationService = new NotificationService();
  private readonly userActionService = new UserActionService();
  private readonly hubspotService = new HubspotService();
  private readonly companyService = new CompanyService();
  private readonly userSubmissionRepository = new UserSubmissionRepository(dataSource);

  private readonly log = logger.child({ description: 'SurveyService' });

  constructor() {}

  public async update(user: UserAdmin, surveyId: number, data: UpdateSurveyPayload): Promise<Survey> {
    const survey = await this.surveyRepository.getAvailableSurveyForEditing(surveyId);
    this.validateAccountManagerUpdatePublicSurvey(user.role, survey, data);

    if (survey.status === SurveyStatus.Active && user.role === AdminRole.Editor) {
      throw badRequestError('Editor cannot update a live survey');
    }

    if ((data.expiryDate && data.expiryDate < startOfTomorrow()) || survey.expiryDate <= new Date()) {
      throw badRequestError('Survey expiry date should be greater than current date');
    }

    if (data.maxParticipants && data.maxParticipants <= survey.successfulCompletions) {
      throw badRequestError('Max responses should be greater than successful completions');
    }

    if (data.companyId && survey.companyId !== data.companyId) {
      const company = await this.companyRepository.findOneBy({ id: data.companyId, deletedAt: IsNull() });

      if (!company) {
        throw notFoundError('Company not found');
      }

      const count = survey.status === SurveyStatus.Active ? 1 : 0;
      await Promise.all([
        this.companyRepository.update(
          { id: survey.companyId },
          {
            surveysInProgress: () => `surveysInProgress - ${count}`,
          },
        ),
        this.companyRepository.update(
          { id: data.companyId },
          { surveysInProgress: () => `surveysInProgress + ${count}` },
        ),
      ]);
    }

    const updatedSurvey = await this.surveyRepository.save({ id: survey.id, ...data });

    if (data.isPublic !== undefined) {
      if (data.isPublic === true && !survey.publicQRCode) {
        await this.generatePublicSurveyQRCode(survey);
      }

      if (data.isPublic !== survey.isPublic) {
        const audience = await this.getAudienceBySurveyId(surveyId);

        if (audience) {
          const [users] = await this.audienceService.usersIncluded(audience);
          await this.audienceService.regenerateUserSurveys(surveyId, users);
          await this.removeUnusedAudience(survey, audience);
        }
      }
    }

    if (
      (data.maxParticipants && survey.maxParticipants && data.maxParticipants !== survey.maxParticipants) ||
      (data.responsesPerUser && data.responsesPerUser !== survey.responsesPerUser)
    ) {
      const audience = await this.getAudienceBySurveyId(surveyId);

      if (audience) {
        const [users] = await this.audienceService.usersIncluded(audience);
        await this.audienceService.regenerateUserSurveys(surveyId, users);
      }
    }

    if (data.translation) {
      const surveyTranslation = await this.surveyTranslationRepository.findOneBy({ surveyId });
      if (!surveyTranslation) {
        await this.surveyTranslationRepository.save({ surveyId, ...data.translation });
      } else {
        await this.surveyTranslationRepository.update({ surveyId }, data.translation);
      }
    } else {
      await this.surveyTranslationRepository.delete({ surveyId });
      await this.surveyQuestionTranslationRepository.delete({ surveyId });
      await this.surveyQuestionOptionTranslationRepository.delete({ surveyId });
    }

    await this.userActionService.logAction({
      userId: user.id,
      description: `Updated Survey - ${updatedSurvey.title}`,
      entity: EntitiesName.Surveys,
    });

    return updatedSurvey;
  }

  public async findCompany(companyId: number): Promise<void> {
    return await this.surveyRepository.findCompany(companyId);
  }

  public async listSurveys(
    data: PaginationPayload<Survey>,
    user: CurrentUser,
  ): Promise<PaginationResponseData<Survey>> {
    return await this.surveyRepository.listSurveys(data, user);
  }

  public async getPublishedSurveys(): Promise<Survey[]> {
    return await this.surveyRepository.getPublishedSurveys();
  }

  public async surveysByAudienceId(audienceId: number): Promise<Survey[]> {
    return await this.surveyRepository.surveysByAudienceId(audienceId);
  }

  public async surveysByCompanyId(companyId: number): Promise<Survey[]> {
    return await this.surveyRepository.surveysByCompanyId(companyId);
  }

  public async findSurveyExpired(id: number): Promise<Survey> {
    return await this.surveyRepository.findSurveyExpired(id);
  }

  public async surveyResults(id: number, user: CurrentUser) {
    if (user.role === AdminRole.AccountManager) {
      const survey = await this.surveyRepository.findOneBy({
        id,
        createdBy: user.id,
        isPublic: true,
      });

      if (!survey) {
        throw badRequestError('Survey not found or you do not have permission to access it');
      }
    }

    const queryBuilder = this.surveyQuestionRepository.createQueryBuilder('survey_questions');
    queryBuilder
      .where('survey_questions.surveyId = :surveyId', { surveyId: id })
      .leftJoinAndMapMany(
        'survey_questions.answers',
        UserSurveyAnswer,
        'answers',
        'answers.questionId = survey_questions.id',
      )
      .leftJoinAndMapMany(
        'survey_questions.options',
        SurveyQuestionOption,
        'options',
        'options.questionId = survey_questions.id',
      );

    const questionAnswers = (await queryBuilder.getMany()) as QuestionUserAnswers[];

    questionAnswers.forEach(question => {
      const answers = question.answers;
      const users = new Set();
      question.optionsCalculate = {};
      question.optionsRanking = {};

      for (const answer of answers) {
        users.add(answer.submissionId);
        /* Only Single Select, Multiple Select and Rank questions have questionOptionId */
        if (!answer.questionOptionId) continue;

        /* Only Rank questions have both questionOptionId and value */
        if (answer.value && question.questionType === SurveyQuestionType.Rank) {
          if (isNaN(Number(answer.value))) continue;
          if (!question.optionsRanking[answer.questionOptionId]) {
            question.optionsRanking[answer.questionOptionId] = 0;
          }
          question.optionsRanking[answer.questionOptionId] += Number(answer.value);
          continue;
        }

        /* Calculate percentage and count respondents for Single or Multiple Select questions*/
        if (!question.optionsCalculate[answer.questionOptionId]) {
          question.optionsCalculate[answer.questionOptionId] = {
            count: 0,
            percentage: 0,
          };
        }
        question.optionsCalculate[answer.questionOptionId].count++;
      }
      question.totalUsers = users.size;
    });

    questionAnswers.forEach(question => {
      Object.keys(question.optionsCalculate).forEach(key => {
        question.optionsCalculate[Number(key)].percentage =
          (question.optionsCalculate[Number(key)].count / question.totalUsers) * 100;
      });
    });

    return questionAnswers.map(questionAns => ({ ...questionAns, answers: undefined }));
  }

  private async checkIfSurveyHasResponses(surveyId: number): Promise<void> {
    const [userSurvey, publicSurveyResponse] = await Promise.all([
      this.userSurveyRepository.findOneBy([
        { surveyId, isComplete: true },
        { surveyId, isComplete: false, totalSubmissions: MoreThan(0) },
      ]),
      this.publicSurveyAnswerRepository.findOneBy({ surveyId }),
    ]);

    if (userSurvey || publicSurveyResponse) {
      throw badRequestError("This survey already has responses and can't be deleted in order to preserve data");
    }
  }

  public async delete(surveyId: number, user: CurrentUser): Promise<Survey> {
    const survey = await this.surveyRepository.getAvailableSurveyForEditing(surveyId);

    if (survey.status === SurveyStatus.Active && user.role === AdminRole.Editor) {
      throw badRequestError('Editor cannot delete a live survey');
    }

    if (user.role === AdminRole.AccountManager && survey.createdBy !== user.id && survey.isPublic) {
      throw forbiddenError('You are not allowed to delete this survey');
    }

    await this.checkIfSurveyHasResponses(surveyId);

    const lastSurveyOfCompany = await this.getCompanyLastSurvey(survey.companyId, surveyId);
    await this.companyRepository.update(
      {
        id: survey.companyId,
      },
      {
        lastSurveyDate: lastSurveyOfCompany?.startDate ?? null,
        surveysInProgress: () => `surveysInProgress - ${survey.status === SurveyStatus.Active ? 1 : 0}`,
      },
    );

    const audience = await this.getAudienceBySurveyId(surveyId);
    if (audience) {
      const lastSurveyOfAudience = await this.surveyRepository.getAudienceLastSurvey(audience.id, surveyId);
      const startDate = lastSurveyOfAudience?.startDate ?? null;

      await Promise.all([
        this.updateSpecialtyStatistics(audience.specialtyIds, {
          numberOfSurveys: () => `numberOfSurveys - 1`,
        }),
        this.audienceRepository.update({ id: audience.id }, { lastSurveyDate: startDate }),
      ]);
    }

    await this.surveyRepository.delete(surveyId);
    return survey;
  }

  private validateAnswers(question: SurveyQuestion, answer: SurveyAnswer): UserAnswer[] {
    if (inputQuestions.includes(question.questionType) && !answer.value) {
      throw badRequestError(`Answer is required for question ${question.title}`);
    }

    if (question.questionType === SurveyQuestionType.Text) {
      return [{ questionId: question.id, value: answer.value }];
    }

    if (question.questionType === SurveyQuestionType.Slider) {
      const value = Number(answer.value);
      if (isNaN(value) || value < question.minValue! || value > question.maxValue!) {
        throw badRequestError(`Invalid answer for question ${question.title}`);
      }

      return [{ questionId: question.id, value: answer.value }];
    }

    if (question.questionType === SurveyQuestionType.Number) {
      const value = Number(answer.value);
      if (isNaN(value)) {
        throw badRequestError(`Invalid answer for question ${question.title}`);
      }

      return [{ questionId: question.id, value: answer.value }];
    }

    if (question.questionType === SurveyQuestionType.Date) {
      const date = new Date(answer.value!);
      if (!isValid(date)) {
        throw badRequestError(`Invalid answer for question ${question.title}`);
      }

      return [{ questionId: question.id, value: format(date, 'yyyy/MM/dd') }];
    }

    if (question.questionType === SurveyQuestionType.SingleSelection) {
      const option = question.options.find(option => option.id === (answer.questionOptionIds ?? [])[0]);
      if (!option || (answer.questionOptionIds && answer.questionOptionIds.length > 1)) {
        throw badRequestError(`Invalid answer for question ${question.title}`);
      }

      if (option.isOther) {
        if (!answer.value) {
          // throw badRequestError(`Answer is required for question ${question.title}`);
          throw badRequestError(
            `Your app version is no longer supported. Please update to continue or try it again with the new survey!`,
          );
        }
      }

      return [{ questionId: question.id, questionOptionId: option.id, value: answer.value }];
    }

    if (question.questionType === SurveyQuestionType.MultipleSelection) {
      const optionIds = question.options.map(option => option.id);
      const answerOptionIds = answer.questionOptionIds ?? [];
      const uniqueIds = new Set(answerOptionIds);

      if (
        answerOptionIds.length === 0 ||
        uniqueIds.size !== answerOptionIds.length ||
        _.difference(answerOptionIds, optionIds).length > 0
      ) {
        throw badRequestError(`Invalid answer for question ${question.title}`);
      }

      let otherOptionId = null;
      for (const optionId of answerOptionIds) {
        const option = question.options.find(option => option.id === optionId);
        if (option && option.isOther) {
          if (!answer.value) {
            // throw badRequestError(`Answer is required for question ${question.title}`);
            throw badRequestError(
              `Your app version is no longer supported. Please update to continue or try it again with the new survey!`,
            );
          }

          otherOptionId = optionId;
        }
      }

      return answerOptionIds.map(optionId => ({
        questionId: question.id,
        questionOptionId: optionId,
        value: optionId === otherOptionId ? answer.value : undefined,
      }));
    }

    if (question.questionType === SurveyQuestionType.Rank) {
      const optionIds = question.options.map(option => option.id);
      const answerOptionIds = answer.questionOptionIds ?? [];
      const uniqueIds = new Set(answerOptionIds);

      if (
        answerOptionIds.length === 0 ||
        uniqueIds.size !== answerOptionIds.length ||
        _.xor(answerOptionIds, optionIds).length > 0
      ) {
        throw badRequestError(`Invalid answer for question ${question.title}`);
      }

      return answerOptionIds.map((optionId, index) => ({
        questionId: question.id,
        questionOptionId: optionId,
        value: (index + 1).toString(),
      }));
    }

    return [];
  }

  private validateUserAnswers(data: SubmitSurveyPayload, questions: SurveyQuestion[]): UserAnswer[] {
    const questionMap = new Map(questions.map(question => [question.id, question]));
    const answers = data.surveyAnswers;
    const questionIds = answers.map(answer => answer.questionId);
    const uniqueIds = new Set(questionIds);

    if (questionMap.size !== questionIds.length || uniqueIds.size !== questionIds.length) {
      throw badRequestError('Invalid survey questions');
    }

    const userAnswers: UserAnswer[] = [];
    for (const answer of answers) {
      const question = questionMap.get(answer.questionId);
      if (!question) {
        throw badRequestError('Invalid survey questions');
      }
      userAnswers.push(...this.validateAnswers(question, answer));
    }

    return userAnswers;
  }

  public async submit(userId: number, surveyId: number, data: SubmitPrivateSurveyPayload): Promise<void> {
    const survey = await this.surveyRepository.findOneBy({ id: surveyId });
    if (!survey) {
      throw notFoundError('Survey not found');
    }

    const user = await this.userRepository.getVerifiedUser(userId);

    const questions = await this.surveyQuestionRepository.find({
      where: { surveyId },
      relations: ['options'],
    });

    const screeningQuestions = questions.filter(q => q.questionType === SurveyQuestionType.Screening);
    const nonScreeningQuestions = questions.filter(q => q.questionType !== SurveyQuestionType.Screening);

    const userAnswers: UserAnswer[] = [];

    // Filter out screening answers from the submitted data
    const nonScreeningAnswers = data.surveyAnswers.filter(answer => {
      const question = questions.find(q => q.id === answer.questionId);
      return question && question.questionType !== SurveyQuestionType.Screening;
    });

    // Use the filtered answers for validation
    const filteredData = {
      surveyAnswers: nonScreeningAnswers,
    } as SubmitSurveyPayload;

    userAnswers.push(...this.validateUserAnswers(filteredData, nonScreeningQuestions));

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let isSyncCompanyStats = false;

    try {
      const userSurvey = await queryRunner.manager
        .getRepository(UserSurvey)
        .createQueryBuilder('user_surveys')
        .setLock('pessimistic_write')
        .where('user_surveys.userId = :userId', { userId })
        .andWhere('user_surveys.surveyId = :surveyId', { surveyId })
        .andWhere('user_surveys.isPassScreening = true')
        .getOne();

      if (!userSurvey) {
        throw badRequestError('Survey is not assigned to user');
      }

      if (userSurvey.isComplete) {
        throw badRequestError('User has already completed this survey');
      }

      const submissions = await queryRunner.manager
        .getRepository(UserSubmission)
        .createQueryBuilder('user_submissions')
        .where('user_submissions.userSurveyId = :userSurveyId', { userSurveyId: userSurvey.id })
        .getMany();

      if (submissions.length >= survey.responsesPerUser) {
        throw badRequestError('Response limit exceeded for this survey');
      }

      if (screeningQuestions.length > 0) {
        const screeningResult = await queryRunner.manager
          .getRepository(ScreeningResult)
          .createQueryBuilder('screening_results')
          .leftJoinAndMapMany(
            'screening_results.screeningResponses',
            ScreeningResponse,
            'screening_responses',
            'screening_responses.screeningResultId = screening_results.id',
          )
          .where('screening_results.userId = :userId', { userId })
          .andWhere('screening_results.surveyId = :surveyId', { surveyId })
          .orderBy('screening_results.createdAt', 'DESC')
          .getMany();

        if (!screeningResult.length) {
          throw badRequestError('Please complete screening questions before submitting the survey');
        }

        const lastScreeningResult = screeningResult[0];
        if (!lastScreeningResult.isPassed) {
          throw badRequestError('User has not passed screening questions for this survey');
        }

        if (screeningResult.length <= submissions.length) {
          throw badRequestError('Please complete screening questions before submitting the survey');
        }

        const screeningResponseQuestionIds = new Set(
          lastScreeningResult.screeningResponses.map(answer => answer.questionId),
        );

        if (screeningResponseQuestionIds.size !== screeningQuestions.length) {
          throw badRequestError('Please complete all screening questions before submitting the survey');
        }

        userAnswers.push(
          ...lastScreeningResult.screeningResponses.map(answer => ({
            questionId: answer.questionId,
            questionOptionId: answer.questionOptionId,
          })),
        );
      }

      const isFullSubmission = submissions.length + 1 === survey.responsesPerUser;
      const result = isFullSubmission
        ? await queryRunner.manager.update(
            UserSurvey,
            {
              id: userSurvey.id,
              isComplete: false,
            },
            {
              isComplete: true,
            },
          )
        : null;

      if ((result && result.affected) || !isFullSubmission) {
        const responseId = submissions.length ? submissions.length + 1 : 1;
        let hubspotProgramId = submissions.length ? submissions[0].hubspotProgramId : null;
        if (!hubspotProgramId) {
          hubspotProgramId = await this.hubspotService.syncNewSurveyCompletion(
            user.hubspotContactId!,
            user.hubspotProductId!,
            {
              program_name: survey.title,
              ctcproduct_name: 'Industrii',
              program_progress: ProgramProgress.Complete,
              amount_earned: survey.compensation.toString(),
              submission_count: responseId.toString(),
            },
          );
        } else {
          await this.hubspotService.syncExistingSurveyCompletion(hubspotProgramId, {
            program_name: survey.title,
            ctcproduct_name: 'Industrii',
            program_progress: ProgramProgress.Complete,
            amount_earned: survey.compensation.toString(),
            submission_count: responseId.toString(),
          });
        }

        const completionDate = new Date();
        const newSubmission = await queryRunner.manager
          .getRepository(UserSubmission)
          .createQueryBuilder()
          .insert()
          .values({
            userSurveyId: userSurvey.id,
            surveyId,
            userId,
            responseId,
            completionDate,
            startDate: data.startDate,
            hubspotProgramId,
          })
          .execute();

        const submissionId = newSubmission.identifiers[0].id;
        const mappedUserAnswers = userAnswers.map(answer => ({
          userSurveyId: userSurvey.id,
          questionId: answer.questionId,
          questionOptionId: answer.questionOptionId,
          value: answer.value,
          submissionId,
        }));

        await queryRunner.manager
          .getRepository(UserSurveyAnswer)
          .createQueryBuilder()
          .insert()
          .values(mappedUserAnswers)
          .execute();

        await queryRunner.manager
          .getRepository(UserTransaction)
          .createQueryBuilder()
          .insert()
          .values({
            userId: user.id,
            type: TransactionType.Compensation,
            title: 'Earned from Survey',
            amount: survey.compensation,
            surveyName: survey.title,
            completionDate,
            refId: submissionId.toString(),
          })
          .execute();

        await queryRunner.manager.update(
          User,
          {
            id: user.id,
          },
          {
            balance: () => `balance + ${survey.compensation}`,
          },
        );

        /**
         * We intentionally allow incrementing `successfulCompletions` without strictly checking
         * `successfulCompletions < maxParticipants` at the time of submission.
         * This is because CTC wants users who see a survey in their dashboard to still be able
         * to submit it, even if the max participant limit is technically reached during submission.
         */
        await queryRunner.manager.increment(Survey, { id: survey.id }, 'successfulCompletions', 1);
        await queryRunner.manager.increment(UserSurvey, { id: userSurvey.id }, 'totalSubmissions', 1);
        const updateSurveyResult = await queryRunner.manager
          .createQueryBuilder()
          .update(Survey)
          .set({ status: SurveyStatus.Expired })
          .where('id = :id', { id: survey.id })
          .andWhere('successfulCompletions >= maxParticipants')
          .andWhere('maxParticipants IS NOT NULL')
          .execute();

        if (updateSurveyResult.affected) {
          isSyncCompanyStats = true;
        }
      } else {
        throw badRequestError('User has already completed this survey');
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      const message = createError.isHttpError(error) ? error.message : 'Failed to submit survey';
      handleTransactionError(error, message);
    } finally {
      await queryRunner.release();

      if (isSyncCompanyStats) {
        waitUntil(this.companyService.syncStats(survey.companyId));
      }
    }
  }

  private async validateSurveyInfo(data: CreateSurveyPayload): Promise<Audience | null> {
    const company = await this.companyRepository.findOneBy({
      id: data.companyId,
      deletedAt: IsNull(),
    });

    if (!company) {
      throw notFoundError('Company not found');
    }

    let audience = null;
    if (data.audience) {
      if (data.audience.audienceId) {
        audience = await this.audienceRepository.findOneBy({
          id: data.audience.audienceId,
          deletedAt: IsNull(),
          isVisible: true,
        });

        if (!audience) {
          throw notFoundError('Audience not found');
        }
      }

      if (data.audience.completedSurveys) {
        const surveyIds = data.audience.completedSurveys;
        const count = await this.surveyRepository.count({
          where: {
            id: In(surveyIds),
            status: Not(SurveyStatus.Draft),
          },
        });

        if (count !== surveyIds.length) {
          throw badRequestError('Invalid survey IDs');
        }
      }

      if (data.audience.specialtyIds) {
        const specialtyIds = data.audience.specialtyIds;
        const count = await this.specialtyRepository.count({
          where: {
            id: In(specialtyIds),
            deletedAt: IsNull(),
          },
        });

        if (count !== specialtyIds.length) {
          throw badRequestError('Invalid specialty IDs');
        }
      }

      if (data.audience.userIds) {
        const userIds = data.audience.userIds;
        const count = await this.userRepository.count({
          where: {
            id: In(userIds),
            deletedAt: IsNull(),
          },
        });

        if (count !== userIds.length) {
          throw badRequestError('Invalid user IDs');
        }
      }
    }

    return audience;
  }

  public async create(userId: number, data: CreateSurveyPayload, isDuplicate = false): Promise<Survey> {
    let surveyId = null;
    let audience;
    if (!isDuplicate) {
      audience = await this.validateSurveyInfo(data);
    }

    let users: User[] = [];
    let count: number = 0;
    // If survey is public, audience is not required
    if (data.audience && !data.isPublic) {
      const payload: UsersIncludedPayload = audience ? audience : createPayloadUserIncluded(data.audience);

      [users, count] = await this.audienceService.usersIncluded(payload);
    }

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const startDate = data.status === SurveyStatus.Active ? new Date() : undefined;

      // Handle Survey Info section
      const surveyEntity = queryRunner.manager.create(Survey, {
        ...data,
        createdBy: userId,
        startDate,
      });
      const newSurvey = await queryRunner.manager.save(Survey, surveyEntity);
      surveyId = newSurvey.id;

      if (data.translation) {
        await queryRunner.manager
          .getRepository(SurveyTranslation)
          .createQueryBuilder()
          .insert()
          .values({ surveyId: newSurvey.id, ...data.translation })
          .execute();
      }

      // Handle Questions section
      if (data.questions) {
        for (const question of data.questions) {
          if (
            question.hasOtherOption &&
            (question.questionType === SurveyQuestionType.SingleSelection ||
              question.questionType === SurveyQuestionType.MultipleSelection)
          ) {
            // Add other option to question
            question.options!.push({
              title: 'Other',
              translation: data.translation ? { title: 'Autre', locale: data.translation.locale } : undefined,
              isOther: true,
            } as unknown as QuestionOptionPayload);
          }
        }

        await this.surveyQuestionService.createCompleteQuestionSet(data.questions, newSurvey.id, queryRunner);
      }

      // Handle company statistics
      await queryRunner.manager.update(
        Company,
        { id: data.companyId },
        { lastSurveyDate: startDate, surveysInProgress: () => `surveysInProgress + ${startDate ? 1 : 0}` },
      );

      let audienceId;
      // Handle Audience section
      if (data.audience && !data.isPublic) {
        audienceId = data.audience.audienceId;

        // Handle specialty statistics
        const specialtyIds = audience?.specialtyIds ?? data.audience.specialtyIds;
        const updateSpecialtyCond = specialtyIds?.length ? { id: In(specialtyIds) } : {};

        await queryRunner.manager.update(
          Specialty,
          { ...updateSpecialtyCond, deletedAt: IsNull() },
          {
            numberOfSurveys: () => `numberOfSurveys + 1`,
            lastSurveyDate: startDate,
          },
        );

        if (!audienceId) {
          const newAudience = await queryRunner.manager
            .getRepository(Audience)
            .createQueryBuilder()
            .insert()
            .values({
              ...data.audience,
              isVisible: false,
            })
            .execute();

          audienceId = newAudience.identifiers[0].id;
        }

        queryRunner.manager
          .getRepository(SurveyAudience)
          .createQueryBuilder()
          .insert()
          .values({
            surveyId: newSurvey.id,
            audienceId,
          })
          .execute();

        if (data.status === SurveyStatus.Active) {
          // Handle Audience statistics
          await queryRunner.manager.update(Audience, { id: audienceId }, { lastSurveyDate: startDate });

          // Notification
          if (count) {
            const userSurveys = users.map(user => {
              return {
                userId: user.id,
                surveyId: newSurvey.id,
                isComplete: false,
              };
            });

            await queryRunner.manager
              .getRepository(UserSurvey)
              .createQueryBuilder()
              .insert()
              .values(userSurveys)
              .execute();

            if (data.audience.isPublish && count > 0) {
              const userIds = users.map(user => user.id);
              await this.notificationService.notifySurveyAvailable(userIds, newSurvey.id);
            }
          }
        }
      }

      let publicQRCode: string | null = null;
      if (data.isPublic) {
        publicQRCode = await this.generatePublicSurveyQRCode(newSurvey, queryRunner);
      }

      const actionDescription = `Survey - ${newSurvey.title}`;
      await queryRunner.manager
        .getRepository(UserAction)
        .createQueryBuilder()
        .insert()
        .values({
          userId,
          description: `${isDuplicate ? 'Duplicated' : 'Created'} ${actionDescription}`,
          entity: EntitiesName.Surveys,
        })
        .execute();

      await queryRunner.commitTransaction();
      return { ...newSurvey, publicQRCode };
    } catch (error) {
      await queryRunner.rollbackTransaction();

      handleTransactionError(error, 'Failed to create survey');
      throw error; // Throw error to satisfy function return type
    } finally {
      await queryRunner.release();

      if (surveyId) {
        await this.surveyQuestionService.bumpScreeningQuestionsToTop(surveyId);
      }
    }
  }

  public async getUserAvailableSurvey(surveyId: number, currentUser: CurrentUser): Promise<Survey> {
    const queryBuilder = this.surveyRepository
      .createQueryBuilder('surveys')
      .where('surveys.id = :surveyId', { surveyId })
      .andWhere('surveys.deletedAt IS NULL');

    let userSubmissions: number | null = null;

    if (currentUser.role === UserRole.User) {
      const user = await this.userRepository.findOneById(currentUser.id);

      if (
        !user.gender ||
        !user.birthday ||
        !user.address ||
        !user.city ||
        !user.province ||
        !user.country ||
        !user.postalCode ||
        !user.licenseNumber ||
        !user.specialtyId ||
        !user.practiceSetting ||
        !user.employmentStatus ||
        user.verificationStatus !== VerificationStatus.Verified
      ) {
        throw notFoundError('Survey not found');
      }

      const userSurvey = await this.userSurveyRepository.findOne({
        where: { userId: currentUser.id, surveyId, isComplete: false, isPassScreening: true },
      });

      if (!userSurvey) {
        throw notFoundError('Survey not found');
      }

      userSubmissions = userSurvey.totalSubmissions || null;

      queryBuilder
        .andWhere('surveys.status = :status', { status: SurveyStatus.Active })
        .andWhere('surveys.expiryDate > :expiryDate', { expiryDate: new Date() })
        .andWhere('surveys.isPublic = false')
        .andWhere(
          new Brackets(qb => {
            qb.where('surveys.maxParticipants IS NULL').orWhere(
              'surveys.maxParticipants > surveys.successfulCompletions',
            );
          }),
        );
    }

    if (currentUser.role === AdminRole.AccountManager) {
      queryBuilder.andWhere('surveys.createdBy = :userId', { userId: currentUser.id });
      queryBuilder.andWhere('surveys.isPublic = true');
    }

    queryBuilder.innerJoinAndMapOne('surveys.company', Company, 'company', 'company.id = surveys.companyId');
    queryBuilder.leftJoinAndMapOne(
      'surveys.translation',
      SurveyTranslation,
      'suTrans',
      'suTrans.surveyId = surveys.id',
    );
    queryBuilder.innerJoinAndMapOne('surveys.userAdmin', UserAdmin, 'admin', 'admin.id = surveys.createdBy');

    const survey = await queryBuilder.getOne();

    if (!survey) {
      throw notFoundError('Survey not found');
    }

    return { ...survey, userSubmissions } as Survey;
  }

  public async getUserAvailableSurveys(userId: number): Promise<Survey[]> {
    const user = await this.userRepository.findOneById(userId);

    if (
      !user.gender ||
      !user.birthday ||
      !user.address ||
      !user.city ||
      !user.province ||
      !user.country ||
      !user.postalCode ||
      !user.licenseNumber ||
      !user.specialtyId ||
      !user.practiceSetting ||
      !user.employmentStatus ||
      user.verificationStatus !== VerificationStatus.Verified
    ) {
      return [];
    }

    const surveys = await this.surveyRepository
      .createQueryBuilder('surveys')
      .leftJoinAndMapOne('surveys.translation', SurveyTranslation, 'translation', 'translation.surveyId = surveys.id')
      .innerJoin(UserSurvey, 'user_surveys', 'surveys.id = user_surveys.surveyId')
      .where('user_surveys.userId = :userId', { userId })
      .andWhere('user_surveys.isComplete = false')
      .andWhere('user_surveys.isPassScreening = true')
      .andWhere('surveys.status = :status', { status: SurveyStatus.Active })
      .andWhere('surveys.expiryDate > :expiryDate', { expiryDate: new Date() })
      .andWhere('surveys.isPublic = false')
      .andWhere(
        new Brackets(qb => {
          qb.where('surveys.maxParticipants IS NULL').orWhere(
            'surveys.maxParticipants > surveys.successfulCompletions',
          );
        }),
      )
      .addSelect('user_surveys.totalSubmissions', 'totalSubmissions')
      .orderBy('surveys.isPinned', 'DESC')
      .addOrderBy('surveys.expiryDate', 'ASC')
      .getRawAndEntities();

    return surveys.entities.map((survey, index) => ({
      ...survey,
      userSubmissions: parseInt(surveys.raw[index].totalSubmissions) || null,
    }));
  }

  public async duplicate(surveyId: number, user: CurrentUser): Promise<void> {
    // Validate survey for duplication
    const survey = await this.surveyRepository.getAvailableSurveyForEditing(surveyId);
    if (user.role === AdminRole.AccountManager && survey.createdBy !== user.id && !survey.isPublic) {
      throw badRequestError('Account manager can only duplicate their own surveys');
    }

    const surveyDetails = (await this.surveyRepository
      .createQueryBuilder('surveys')
      .where('surveys.id = :surveyId', { surveyId })
      .leftJoinAndMapOne('surveys.translation', SurveyTranslation, 'translation', 'translation.surveyId = surveys.id')
      .leftJoin('survey_audiences', 'survey_audiences', 'survey_audiences.surveyId = surveys.id')
      .leftJoinAndMapOne('surveys.audience', Audience, 'audience', 'audience.id = survey_audiences.audienceId')
      .leftJoinAndMapMany('surveys.questions', SurveyQuestion, 'questions', 'questions.surveyId = surveys.id')
      .leftJoinAndMapOne(
        'questions.translation',
        SurveyQuestionTranslation,
        'questionTranslation',
        'questionTranslation.questionId = questions.id',
      )
      .leftJoinAndMapMany('questions.options', SurveyQuestionOption, 'options', 'options.questionId = questions.id')
      .leftJoinAndMapOne(
        'options.translation',
        SurveyQuestionOptionTranslation,
        'optionTranslation',
        'optionTranslation.questionOptionId = options.id',
      )
      .getOne()) as Survey;

    const data = await this.buildCreateSurveyPayload(surveyDetails);
    await this.create(user.id, data, true);
  }

  private async buildCreateSurveyPayload(survey: Survey): Promise<CreateSurveyPayload> {
    if (survey.questions && survey.questions.length) {
      for (const question of survey.questions) {
        if (question.options && question.options.length) {
          question.options.sort((a, b) => a.id - b.id);
        }
      }
    }

    const questions = survey.questions.length
      ? survey.questions.map(question => {
          return {
            title: question.title,
            order: question.order,
            questionType: question.questionType,
            maxValue: question.maxValue ?? undefined,
            minValue: question.minValue ?? undefined,
            subtitle: question.subtitle ?? undefined,
            hasOtherOption: question.hasOtherOption,
            isMultiSelectionEnabled: question.isMultiSelectionEnabled,
            translation: question.translation
              ? {
                  locale: Locale.FR,
                  title: question.translation.title,
                  subtitle: question.translation.subtitle ?? undefined,
                }
              : undefined,
            options: question.options.length
              ? question.options
                  .filter(option => !option.isOther)
                  .map(option => {
                    return {
                      title: option.title,
                      isOther: option.isOther,
                      isEligible: option.isEligible,
                      translation: option.translation
                        ? {
                            locale: Locale.FR,
                            title: option.translation.title,
                          }
                        : undefined,
                    };
                  })
              : undefined,
          };
        })
      : undefined;

    // Find all surveys that has been duplicated
    const similarSurveys = await this.surveyRepository.find({
      where: {
        title: ILike(`${survey.title} (%)`),
      },
    });

    // Ex: last duplicated survey is name (1), then the next duplicated survey is name (2)
    const numericSuffixes = similarSurveys.map(survey => {
      const match = survey.title.match(/\((\d+)\)$/);
      return match ? parseInt(match[1]) : 0;
    });
    const maxSuffix = Math.max(0, ...numericSuffixes);

    const data: CreateSurveyPayload = {
      title: `${survey.title} (${maxSuffix + 1})`,
      companyId: survey.companyId,
      status: SurveyStatus.Draft,
      description: survey.description ?? undefined,
      image: survey.image ?? undefined,
      backgroundImage: survey.backgroundImage ?? undefined,
      isPinned: survey.isPinned,
      maxParticipants: survey.maxParticipants,
      time: survey.time,
      expiryDate: survey.expiryDate,
      compensation: survey.compensation,
      translation: survey.translation?.title
        ? {
            locale: Locale.FR,
            title: `${survey.translation.title} (${maxSuffix + 1})`,
            description: survey.translation.description ?? undefined,
          }
        : undefined,
      audience: survey.audience
        ? survey.audience.name === null
          ? {
              isPublish: false,
              completedSurveys: survey.audience.completedSurveys ?? undefined,
              specialtyIds: survey.audience.specialtyIds ?? undefined,
              cities: survey.audience.cities ?? undefined,
              employmentStatuses: survey.audience.employmentStatuses ?? undefined,
              practiceSettings: survey.audience.practiceSettings ?? undefined,
              provinces: survey.audience.provinces ?? undefined,
              userIds: survey.audience.userIds ?? undefined,
            }
          : {
              audienceId: survey.audience.id,
              isPublish: false,
            }
        : undefined,
      questions: questions as never,
      responsesPerUser: survey.responsesPerUser,
      isPublic: survey.isPublic,
    };

    return data;
  }

  private isSameAudienceSettings(target: AudienceSettings, base: Audience): boolean {
    const keys: (keyof AudienceSettings)[] = [
      'cities',
      'provinces',
      'specialtyIds',
      'employmentStatuses',
      'practiceSettings',
      'completedSurveys',
      'userIds',
    ];

    for (const key of keys) {
      const targetValue = target[key];
      const baseValue = base[key];

      if (targetValue === null && Array.isArray(baseValue)) {
        return false;
      }

      if (Array.isArray(targetValue) && baseValue === null) {
        return false;
      }

      if (
        Array.isArray(targetValue) &&
        Array.isArray(baseValue) &&
        _.differenceWith(
          targetValue.length > baseValue.length ? targetValue : baseValue,
          (targetValue.length > baseValue.length ? baseValue : targetValue) as never,
          _.isEqual,
        ).length
      ) {
        return false;
      }
    }

    return true;
  }

  private isChangeAudienceType(audience: Audience, data: PublishSurveyPayload): boolean {
    if (audience.isVisible && !data.audienceId) {
      return true;
    }

    if (!audience.isVisible && data.audienceId) {
      return true;
    }

    return false;
  }

  private async removeUnusedAudience(survey: Survey, audience?: Audience | null): Promise<void> {
    if (audience) {
      await this.surveyAudienceRepository.delete({
        surveyId: survey.id,
        audienceId: audience.id,
      });

      if (!audience.isVisible) {
        await this.audienceRepository.delete({
          id: audience.id,
        });
      }
    }
  }

  public async publish(surveyId: number, data: PublishSurveyPayload): Promise<void> {
    const survey = await this.surveyRepository.getAvailableSurveyForEditing(surveyId);

    const startDate = new Date();
    await this.companyRepository.update(
      { id: survey.companyId },
      {
        lastSurveyDate: startDate,
        surveysInProgress: () => `surveysInProgress + ${survey.status === SurveyStatus.Active ? 0 : 1}`,
      },
    );

    if (survey.status !== SurveyStatus.Active) {
      await this.surveyRepository.update({ id: surveyId }, { status: SurveyStatus.Active, startDate });
    }

    if (survey.isPublic) {
      return;
    }

    const publicAudience = data.audienceId ? await this.audienceRepository.getPublicAudience(data.audienceId) : null;

    const surveyAudience = await this.surveyAudienceRepository.findOne({
      where: { surveyId },
      relations: ['audience'],
    });

    let payload: UsersIncludedPayload;
    if (surveyAudience && !this.isChangeAudienceType(surveyAudience.audience, data)) {
      const currentAudience = surveyAudience.audience;

      // Do nothing
      if (survey.status === SurveyStatus.Active) {
        if (data.audienceId && currentAudience.id === data.audienceId) {
          return;
        }

        if (!data.audienceId && this.isSameAudienceSettings(data, currentAudience)) {
          return;
        }
      }

      await this.updateSurveyAudience(surveyAudience, data, publicAudience, startDate);
      payload = data.audienceId ? publicAudience! : createPayloadUserIncluded(data);
    } else {
      await this.createSurveyAudience(survey, surveyAudience, data, publicAudience, startDate);
      payload = data.audienceId ? publicAudience! : createPayloadUserIncluded(data);
    }

    const [users] = await this.audienceService.usersIncluded(payload);
    const notificationIds = await this.audienceService.regenerateUserSurveys(surveyId, users);

    if (data.isPublish && notificationIds.length > 0) {
      await this.notificationService.notifySurveyAvailable(notificationIds, surveyId);
    }
  }

  public async updateSurveyStatistics(
    audienceId: number,
    startDate: Date | undefined,
    newSpecialtyIds: number[] | null,
    oldSpecialtyIds?: number[] | null,
  ): Promise<void> {
    await Promise.all([
      startDate ? this.audienceRepository.update({ id: audienceId }, { lastSurveyDate: startDate }) : undefined,
      this.updateSpecialtyStatistics(newSpecialtyIds, {
        numberOfSurveys: () => `numberOfSurveys + 1`,
      }),
      typeof oldSpecialtyIds !== 'undefined'
        ? this.updateSpecialtyStatistics(oldSpecialtyIds, {
            numberOfSurveys: () => `numberOfSurveys - 1`,
          })
        : undefined,
    ]);
  }

  public async unpublish(surveyId: number, user: CurrentUser): Promise<void> {
    const survey = await this.surveyRepository.getAvailableSurveyForEditing(surveyId);
    if (user.role === AdminRole.AccountManager && survey.createdBy !== user.id && !survey.isPublic) {
      throw forbiddenError('You are not allowed to unpublish this survey');
    }

    if (survey.status !== SurveyStatus.Active) {
      throw badRequestError('Survey is not published');
    }

    await Promise.all([
      this.updateSurveyStatisticsWhenUnpublish(surveyId, survey.companyId),
      this.surveyRepository.update({ id: surveyId }, { status: SurveyStatus.Draft }),
      this.userSurveyRepository.delete({
        surveyId,
        isComplete: false,
        totalSubmissions: 0,
      }),
    ]);
  }

  public async updateSurveyStatisticsWhenUnpublish(surveyId: number, companyId: number): Promise<void> {
    const lastSurveyOfCompany = await this.getCompanyLastSurvey(companyId, surveyId);
    await this.companyRepository.update(
      {
        id: companyId,
      },
      {
        surveysInProgress: () => `surveysInProgress - 1`,
        lastSurveyDate: lastSurveyOfCompany?.startDate ?? null,
      },
    );

    const audience = await this.getAudienceBySurveyId(surveyId);
    if (!audience) {
      return;
    }

    const lastSurveyOfAudience = await this.surveyRepository.getAudienceLastSurvey(audience.id, surveyId);
    const startDate = lastSurveyOfAudience?.startDate ?? null;

    await Promise.all([
      this.updateSpecialtyStatistics(audience.specialtyIds),
      this.audienceRepository.update({ id: audience.id }, { lastSurveyDate: startDate }),
    ]);
  }

  public async updateSpecialtyStatistics(specialtyIds: number[] | null, additionalCondition = {}): Promise<void> {
    const whereCond = specialtyIds && specialtyIds.length ? { id: In(specialtyIds) } : {};

    const specialties = await this.specialtyRepository.find({
      where: { ...whereCond, deletedAt: IsNull() },
    });

    const promises = specialties.map(async specialty => {
      const survey = await this.surveyRepository
        .createQueryBuilder('surveys')
        .innerJoin(SurveyAudience, 'survey_audiences', 'surveys.id = survey_audiences.surveyId')
        .innerJoin(Audience, 'audiences', 'audiences.id = survey_audiences.audienceId')
        .where('audiences.deletedAt IS NULL')
        .andWhere(
          new Brackets(qb => {
            qb.where('array_length(audiences.specialtyIds, 1) IS NULL')
              .orWhere('audiences.specialtyIds IS NULL')
              .orWhere(':specialtyId = ANY(audiences.specialtyIds)', { specialtyId: specialty.id });
          }),
        )
        .andWhere('surveys.startDate IS NOT NULL')
        .orderBy('surveys.startDate', 'DESC')
        .getOne();

      await this.specialtyRepository.update(
        { id: specialty.id },
        { ...additionalCondition, lastSurveyDate: survey ? survey.startDate : null },
      );
    });

    await Promise.all(promises);
  }

  public async getUnassignedSurveysForUser(userId: number, audienceIds: number[]): Promise<Survey[]> {
    const surveys = await this.surveyRepository
      .createQueryBuilder('surveys')
      .leftJoin(SurveyAudience, 'survey_audiences', 'surveys.id = survey_audiences.surveyId')
      .where('surveys.status = :status', { status: SurveyStatus.Active })
      .andWhere('surveys.expiryDate > :expiryDate', { expiryDate: new Date() })
      .andWhere(
        new Brackets(qb => {
          qb.where('surveys.maxParticipants IS NULL').orWhere(
            'surveys.maxParticipants > surveys.successfulCompletions',
          );
        }),
      )
      .andWhere('survey_audiences.audienceId IN (:...audienceIds)', { audienceIds })
      .getMany();

    if (!surveys.length) {
      return [];
    }

    const userSurveys = await this.userSurveyRepository.find({
      where: {
        userId,
        surveyId: In(surveys.map(survey => survey.id)),
      },
    });

    const assignedSurveyIds = userSurveys.map(userSurvey => userSurvey.surveyId);
    return surveys.filter(survey => !assignedSurveyIds.includes(survey.id));
  }

  public async createUserSurveys(userId: number, surveyIds: number[]): Promise<void> {
    const userSurveys = surveyIds.map(surveyId => {
      return {
        userId,
        surveyId,
        isComplete: false,
      };
    });

    await this.userSurveyRepository.createQueryBuilder().insert().values(userSurveys).execute();
  }

  public async getAudienceBySurveyId(surveyId: number): Promise<Audience | null> {
    return await this.audienceRepository
      .createQueryBuilder('audiences')
      .innerJoin(SurveyAudience, 'survey_audiences', 'audiences.id = survey_audiences.audienceId')
      .where('survey_audiences.surveyId = :surveyId', { surveyId })
      .getOne();
  }

  // Retrieve last published survey for company
  public async getCompanyLastSurvey(companyId: number, surveyId?: number): Promise<Survey | null> {
    const additionalCondition = surveyId ? { id: Not(surveyId) } : {};

    const result = await this.surveyRepository.find({
      where: {
        ...additionalCondition,
        companyId,
        startDate: Not(IsNull()),
      },
      order: {
        startDate: 'DESC',
      },
      take: 1,
    });

    return result.length ? result[0] : null;
  }

  public async saveDraftAudience(surveyId: number, data: PublishSurveyPayload): Promise<void> {
    const survey = await this.surveyRepository.findOneBy({
      id: surveyId,
      status: SurveyStatus.Draft,
    });

    if (!survey) {
      throw notFoundError('Survey not found');
    }

    const publicAudience = data.audienceId ? await this.audienceRepository.getPublicAudience(data.audienceId) : null;

    const surveyAudience = await this.surveyAudienceRepository.findOne({
      where: { surveyId },
      relations: ['audience'],
    });

    if (surveyAudience && !this.isChangeAudienceType(surveyAudience.audience, data)) {
      await this.updateSurveyAudience(surveyAudience, data, publicAudience);
    } else {
      await this.createSurveyAudience(survey, surveyAudience, data, publicAudience);
    }
  }

  private async updateSurveyAudience(
    surveyAudience: SurveyAudience,
    data: PublishSurveyPayload,
    publicAudience: Audience | null,
    startDate?: Date,
  ): Promise<void> {
    const currentAudience = surveyAudience.audience;
    const surveyId = surveyAudience.surveyId;

    // Use new public audience if audienceId is provided
    if (data.audienceId && currentAudience.id !== data.audienceId) {
      await this.surveyAudienceRepository.delete({ surveyId, audienceId: currentAudience.id });
      await this.surveyAudienceRepository.insertWithQueryBuilder({ surveyId, audienceId: publicAudience!.id });

      await this.updateSurveyStatistics(
        publicAudience!.id,
        startDate,
        publicAudience!.specialtyIds,
        currentAudience.specialtyIds,
      );
    }

    // Update private audience settings if audienceId is not provided and settings are different
    if (!data.audienceId && !this.isSameAudienceSettings(data, currentAudience)) {
      const { isPublish: _isPublish, audienceId: _audienceId, ...clonedData } = data;
      await this.audienceRepository.update({ id: currentAudience.id }, clonedData);

      await this.updateSurveyStatistics(
        currentAudience.id,
        startDate,
        clonedData.specialtyIds ?? null,
        currentAudience.specialtyIds,
      );
    }
  }

  private async createSurveyAudience(
    survey: Survey,
    surveyAudience: SurveyAudience | null,
    data: PublishSurveyPayload,
    publicAudience: Audience | null,
    startDate?: Date,
  ): Promise<void> {
    await this.removeUnusedAudience(survey, surveyAudience?.audience);
    const oldSpecialtyIds = surveyAudience?.audience.specialtyIds;

    // Use public audience
    if (data.audienceId) {
      await this.surveyAudienceRepository.insertWithQueryBuilder({
        surveyId: survey.id,
        audienceId: publicAudience!.id,
      });

      const specialtyIds = publicAudience!.specialtyIds;
      await this.updateSurveyStatistics(publicAudience!.id, startDate, specialtyIds, oldSpecialtyIds);
    }

    // Create private audience and use it
    if (!data.audienceId) {
      const privateAudience = await this.audienceRepository.insertWithQueryBuilder({ ...data, isVisible: false });
      await this.surveyAudienceRepository.insertWithQueryBuilder({
        surveyId: survey.id,
        audienceId: privateAudience.identifiers[0].id,
      });

      const specialtyIds = data.specialtyIds ?? null;
      await this.updateSurveyStatistics(privateAudience.identifiers[0].id, startDate, specialtyIds, oldSpecialtyIds);
    }
  }

  public async generatePublicSurveyQRCode(survey: Survey, queryRunner?: QueryRunner): Promise<string> {
    try {
      const url = `${config.WEBAPP_URL}/public/surveys/${survey.id}`;

      const options = {
        type: 'png',
        margin: 1,
        width: 300,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      } as QRCodeToBufferOptions;

      const qrBuffer = await QRCode.toBuffer(url, options);
      const filename = `public-surveys/${randomUUID()}.png`;

      const blob = await put(filename, qrBuffer, {
        contentType: 'image/png',
        access: 'public',
      });

      if (queryRunner) {
        await queryRunner.manager
          .getRepository(Survey)
          .createQueryBuilder()
          .update()
          .set({ publicQRCode: blob.url })
          .where('id = :id', { id: survey.id })
          .execute();
      } else {
        await this.surveyRepository.update(
          {
            id: survey.id,
          },
          {
            publicQRCode: blob.url,
          },
        );
      }

      return blob.url;
    } catch (error) {
      this.log.error('Failed to generate public survey QR code', error);
      throw error;
    }
  }

  public async submitPublicSurvey(surveyId: number, data: SubmitPublicSurveyPayload): Promise<void> {
    const survey = await this.surveyRepository.findOneBy({ id: surveyId });
    if (!survey || !survey.isPublic) {
      throw notFoundError('Survey not found');
    }

    if (data.email) {
      const admin = await this.userAdminRepository.findOneBy({
        email: data.email,
      });

      if (admin) {
        throw badRequestError('Admin cannot submit public survey');
      }
    }

    const questions = await this.surveyQuestionRepository.find({
      where: { surveyId },
      relations: ['options'],
    });

    const screeningQuestions = questions.filter(q => q.questionType === SurveyQuestionType.Screening);
    const nonScreeningQuestions = questions.filter(q => q.questionType !== SurveyQuestionType.Screening);

    const userAnswers: UserAnswer[] = [];

    // Filter out screening answers from the submitted data
    const nonScreeningAnswers = data.surveyAnswers.filter(answer => {
      const question = questions.find(q => q.id === answer.questionId);
      return question && question.questionType !== SurveyQuestionType.Screening;
    });

    const screeningAnswers = data.surveyAnswers.filter(answer => {
      const question = questions.find(q => q.id === answer.questionId);
      return question && question.questionType === SurveyQuestionType.Screening;
    });

    userAnswers.push(
      ...this.validateUserAnswers(
        {
          surveyAnswers: nonScreeningAnswers,
        } as SubmitPublicSurveyPayload,
        nonScreeningQuestions,
      ),
    );

    const [isPassed, userScreeningAnswers] = this.validateScreeningAnswers(
      {
        surveyAnswers: screeningAnswers,
      } as SubmitScreeningPayload,
      screeningQuestions,
    );

    if (!isPassed) {
      throw badRequestError('User has not passed screening questions for this survey');
    }

    userAnswers.push(...userScreeningAnswers);

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let isSyncCompanyStats = false;

    try {
      await queryRunner.manager
        .createQueryBuilder()
        .update(Survey)
        .set({
          successfulCompletions: () => `successfulCompletions + 1`,
        })
        .where('id = :id', { id: surveyId })
        .execute();

      if (data.email) {
        const user = await queryRunner.manager
          .getRepository(User)
          .createQueryBuilder('users')
          .where('users.email = :email', { email: data.email })
          .leftJoinAndMapMany(
            'users.userSubmissions',
            UserSubmission,
            'submissions',
            'submissions.surveyId = :surveyId AND submissions.userId = users.id',
            { surveyId },
          )
          .getOne();

        if (user && user.userSubmissions.length >= survey.responsesPerUser) {
          throw badRequestError('User has already submitted the survey');
        }

        const publicAnswers = await queryRunner.manager
          .getRepository(PublicSurveyAnswer)
          .createQueryBuilder('answers')
          .where('answers.surveyId = :surveyId', { surveyId })
          .andWhere('answers.email = :email', { email: data.email })
          .getMany();

        const filteredAnswers = publicAnswers.filter(answer => answer.status !== PublicSurveyProgress.Done);

        if (filteredAnswers.length >= survey.responsesPerUser) {
          throw badRequestError('User has already submitted the survey');
        }
      }

      await queryRunner.manager
        .getRepository(PublicSurveyAnswer)
        .createQueryBuilder()
        .insert()
        .values({
          email: data.email,
          surveyId,
          compensation: survey.compensation,
          status: PublicSurveyProgress.Open,
          answers: userAnswers,
          startDate: data.startDate,
        })
        .execute();

      const markSurveyAsExpired = await queryRunner.manager
        .createQueryBuilder()
        .update(Survey)
        .set({ status: SurveyStatus.Expired })
        .where('id = :id', { id: survey.id })
        .andWhere('successfulCompletions >= maxParticipants')
        .andWhere('maxParticipants IS NOT NULL')
        .andWhere('status != :status', { status: SurveyStatus.Expired })
        .execute();

      if (markSurveyAsExpired.affected) {
        isSyncCompanyStats = true;
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      const message = createError.isHttpError(error) ? error.message : 'Failed to submit public survey';
      handleTransactionError(error, message);
    } finally {
      await queryRunner.release();

      if (isSyncCompanyStats) {
        waitUntil(this.companyService.syncStats(survey.companyId));
      }
    }
  }

  public async handlePublicSurveyAnswer(): Promise<void> {
    const MAX_TASKS_PER_RUN = 30;
    const MAX_RETRIES = 5;

    const inProgressAnswer = await this.publicSurveyAnswerRepository.findOneBy({
      status: PublicSurveyProgress.InProgress,
    });

    if (inProgressAnswer) {
      this.log.info('Public survey answer is already in progress, skipping this run');
      return;
    }

    const answers = await this.publicSurveyAnswerRepository.find({
      where: {
        status: PublicSurveyProgress.Open,
        count: LessThan(MAX_RETRIES),
      },
      take: MAX_TASKS_PER_RUN,
    });

    if (answers.length === 0) {
      return;
    }

    const answerIds = answers.map(task => task.id);
    await this.publicSurveyAnswerRepository.update(
      { id: In(answerIds) },
      { status: PublicSurveyProgress.InProgress, count: () => 'count + 1' },
    );

    for (const answer of answers) {
      await this.processPublicSurveyAnswer(answer);
    }
  }

  public async processPublicSurveyAnswer(answer: PublicSurveyAnswer): Promise<void> {
    let user = answer.email
      ? await this.userRepository.findOneBy({
          email: answer.email,
          deletedAt: IsNull(),
        })
      : null;

    try {
      if (!user) {
        user = await this.createPublicSurveyUser(answer);
      }
    } catch (error) {
      await this.publicSurveyAnswerRepository.update(
        { id: answer.id },
        { status: PublicSurveyProgress.Open, error: JSON.stringify(error) },
      );

      return;
    }

    await this.userSurveyRepository
      .createQueryBuilder()
      .insert()
      .into(UserSurvey)
      .values({
        userId: user.id,
        surveyId: answer.surveyId,
        isComplete: false,
      })
      .orIgnore()
      .execute();

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const survey = await queryRunner.manager.getRepository(Survey).findOneBy({
        id: answer.surveyId,
      });

      if (!survey) {
        throw notFoundError('Survey not found');
      }

      const userSurvey = await queryRunner.manager
        .getRepository(UserSurvey)
        .createQueryBuilder('user_surveys')
        .setLock('pessimistic_write')
        .where('user_surveys.userId = :userId', { userId: user.id })
        .andWhere('user_surveys.surveyId = :surveyId', { surveyId: answer.surveyId })
        .getOne();

      if (!userSurvey) {
        throw notFoundError('User survey not found');
      }

      const submissions = await queryRunner.manager
        .getRepository(UserSubmission)
        .createQueryBuilder('user_submissions')
        .where('user_submissions.userSurveyId = :userSurveyId', { userSurveyId: userSurvey.id })
        .getMany();

      const responseId = submissions.length ? submissions.length + 1 : 1;
      let hubspotProgramId = submissions.length ? submissions[0].hubspotProgramId : null;
      if (answer.email) {
        if (!hubspotProgramId) {
          hubspotProgramId = await this.hubspotService.syncNewSurveyCompletion(
            user.hubspotContactId!,
            user.hubspotProductId!,
            {
              program_name: survey.title,
              ctcproduct_name: 'Industrii',
              program_progress: ProgramProgress.Complete,
              amount_earned: answer.compensation.toString(),
              submission_count: responseId.toString(),
            },
          );
        } else {
          await this.hubspotService.syncExistingSurveyCompletion(hubspotProgramId, {
            program_name: survey.title,
            ctcproduct_name: 'Industrii',
            program_progress: ProgramProgress.Complete,
            amount_earned: answer.compensation.toString(),
            submission_count: responseId.toString(),
          });
        }
      }

      const completionDate = answer.createdAt;
      const newSubmission = await queryRunner.manager
        .getRepository(UserSubmission)
        .createQueryBuilder()
        .insert()
        .values({
          userSurveyId: userSurvey.id,
          surveyId: survey.id,
          userId: user.id,
          responseId,
          completionDate,
          startDate: answer.startDate,
          hubspotProgramId: answer.email ? hubspotProgramId : null,
        })
        .execute();

      const submissionId = newSubmission.identifiers[0].id;
      const mappedUserAnswers = answer.answers.map(answer => ({
        userSurveyId: userSurvey.id,
        questionId: answer.questionId,
        questionOptionId: answer.questionOptionId,
        value: answer.value,
        submissionId,
      }));

      await queryRunner.manager
        .getRepository(UserSurveyAnswer)
        .createQueryBuilder()
        .insert()
        .values(mappedUserAnswers)
        .execute();

      await queryRunner.manager
        .getRepository(UserTransaction)
        .createQueryBuilder()
        .insert()
        .values({
          userId: user.id,
          type: TransactionType.Compensation,
          title: 'Earned from Survey',
          amount: answer.compensation,
          surveyName: survey.title,
          completionDate,
          refId: submissionId.toString(),
        })
        .execute();

      await queryRunner.manager.update(
        User,
        {
          id: user.id,
        },
        {
          balance: () => `balance + ${answer.compensation}`,
        },
      );

      await queryRunner.manager.increment(UserSurvey, { id: userSurvey.id }, 'totalSubmissions', 1);
      const isFullSubmission = submissions.length + 1 === survey.responsesPerUser;
      if (isFullSubmission) {
        await queryRunner.manager.update(
          UserSurvey,
          {
            id: userSurvey.id,
            isComplete: false,
          },
          {
            isComplete: true,
          },
        );
      }

      await queryRunner.commitTransaction();

      await this.publicSurveyAnswerRepository.update(
        { id: answer.id },
        {
          status: PublicSurveyProgress.Done,
          userId: user.id,
        },
      );

      return;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      await this.publicSurveyAnswerRepository.update(
        { id: answer.id },
        { status: PublicSurveyProgress.Open, error: JSON.stringify(error) },
      );
    } finally {
      await queryRunner.release();
    }
  }

  private async createPublicSurveyUser(answer: PublicSurveyAnswer): Promise<User> {
    const { email } = answer;
    let userId = null;

    if (!email) {
      userId = await this.generateAnonymousUser();
    } else {
      const firstName = 'Public Survey';
      const lastName = 'User';

      const { contactId, ctcProductId } = await this.hubspotService.syncPublicSurveyUser({
        email,
        firstname: firstName,
        lastname: lastName,
        industrii_contact_status: HubspotContactStatus.WSPending,
        verification_status: HCPVerificationStatus.NotStarted,
        industrii_complete_ws: CompleteWS.False,
      });

      const newUser = await this.userRepository
        .createQueryBuilder()
        .insert()
        .into(User)
        .values({
          email,
          firstName,
          lastName,
          contactStatus: ContactStatus.WSPending,
          verificationStatus: VerificationStatus.Unverified,
          isCompleteWS: false,
          isPublic: true,
          hubspotContactId: contactId,
          hubspotProductId: ctcProductId,
          isEmailOptIn: false,
        })
        .orIgnore()
        .execute();

      userId = newUser.identifiers[0].id;
    }

    const user = await this.userRepository.findOneBy({ id: userId });
    return user!;
  }

  public async getPublicSurvey(surveyId: number): Promise<Survey> {
    const survey = await this.surveyRepository
      .createQueryBuilder('surveys')
      .leftJoinAndMapOne(
        'surveys.translation',
        SurveyTranslation,
        'survey_translation',
        'survey_translation.surveyId = surveys.id',
      )
      .where('surveys.id = :surveyId', { surveyId })
      .andWhere('surveys.deletedAt IS NULL')
      .andWhere('surveys.isPublic = true')
      .andWhere('surveys.status = :status', { status: SurveyStatus.Active })
      .andWhere('surveys.expiryDate > :expiryDate', { expiryDate: new Date() })
      .andWhere(
        new Brackets(qb => {
          qb.where('surveys.maxParticipants IS NULL').orWhere(
            'surveys.maxParticipants > surveys.successfulCompletions',
          );
        }),
      )
      .getOne();

    if (!survey) {
      throw notFoundError('Survey not found');
    }

    return survey;
  }

  public async getPublicSurveyQuestions(surveyId: number): Promise<SurveyQuestion[]> {
    await this.getPublicSurvey(surveyId);
    return await this.surveyQuestionRepository.questionsBySurveyId(surveyId);
  }

  public validateAccountManagerCreatePublicSurvey(role: Role, data: CreateSurveyPayload): void {
    if (role !== AdminRole.AccountManager) {
      return;
    }

    if (!data.isPublic) {
      throw badRequestError('Account manager can only create public surveys');
    }
  }

  public validateAccountManagerUpdatePublicSurvey(role: Role, survey: Survey, data: UpdateSurveyPayload): void {
    if (role !== AdminRole.AccountManager) {
      return;
    }

    if (!survey.isPublic && data.isPublic) {
      throw badRequestError('Account manager can only update public surveys');
    }

    if (survey.isPublic && !data.isPublic) {
      throw badRequestError('Account manager can only update public surveys');
    }
  }

  public async resolveSurveySlug(slug: string): Promise<number> {
    if (slug === config.SURVEY_SLUG_1) {
      if (config.SURVEY_ID_IN_SLUG_1 !== 0) {
        return config.SURVEY_ID_IN_SLUG_1;
      }
    }

    if (slug === config.SURVEY_SLUG_2) {
      if (config.SURVEY_ID_IN_SLUG_2 !== 0) {
        return config.SURVEY_ID_IN_SLUG_2;
      }
    }

    throw notFoundError('Survey not found');
  }

  public async surveyUsersCompleted(surveyId: number) {
    const queryBuilder = this.userSubmissionRepository
      .createQueryBuilder('us')
      .where('us.surveyId = :surveyId', { surveyId })
      .leftJoinAndMapOne('us.user', User, 'user', 'user.id = us.userId')
      .leftJoinAndMapOne('user.specialty', Specialty, 'specialty', 'specialty.id = user.specialtyId');

    return await queryBuilder.getMany();
  }

  public validateScreeningAnswers(data: SubmitScreeningPayload, questions: SurveyQuestion[]): [boolean, UserAnswer[]] {
    const questionMap = new Map(
      questions
        .filter(question => question.questionType === SurveyQuestionType.Screening)
        .map(question => [question.id, question]),
    );
    const answers = data.surveyAnswers;

    const userAnswers: UserAnswer[] = [];
    let isPassed = true;

    for (const answer of answers) {
      const question = questionMap.get(answer.questionId);
      if (!question) {
        throw badRequestError('Invalid survey questions');
      }

      if (question.isMultiSelectionEnabled) {
        // Handle multi selection screening questions
        const selectedOptionIds = answer.questionOptionIds;

        // Validate that all selected options exist in the question
        const validOptions = question.options.filter(option => selectedOptionIds.includes(option.id));

        if (validOptions.length !== selectedOptionIds.length) {
          throw badRequestError(`Invalid options selected for screening multi selection question`);
        }

        // For multi selection, add each selected option as a separate answer
        for (const optionId of selectedOptionIds) {
          userAnswers.push({
            questionId: question.id,
            questionOptionId: optionId,
          });
        }

        // Check if all selected options are eligible
        // If any option is not eligible, the screening fails
        const hasAnyEligibleOption = validOptions.every(option => option.isEligible === true);
        if (!hasAnyEligibleOption) {
          isPassed = false;
        }
      } else {
        // Handle single selection screening questions
        if (answer.questionOptionIds.length !== 1) {
          throw badRequestError(`Single selection question requires exactly one answer`);
        }

        const questionOptionId = answer.questionOptionIds[0];
        const option = question.options.find(option => option.id === questionOptionId);

        if (!option) {
          throw badRequestError(`Invalid option selected for screening single selection question`);
        }

        userAnswers.push({
          questionId: question.id,
          questionOptionId: questionOptionId,
        });

        // For single selection, the selected option must be eligible
        if (option.isEligible !== true) {
          isPassed = false;
        }
      }
    }

    return [isPassed, userAnswers];
  }

  public async submitScreeningQuestions(
    surveyId: number,
    data: SubmitScreeningPayload,
    userId?: number,
  ): Promise<boolean> {
    const survey = await this.surveyRepository.findOneBy({ id: surveyId });
    if (!survey || survey.status !== SurveyStatus.Active) {
      throw notFoundError('Survey not found');
    }

    const questions = await this.surveyQuestionRepository.find({
      where: { surveyId },
      relations: ['options'],
    });

    const screeningQuestions = questions.filter(q => q.questionType === SurveyQuestionType.Screening);
    if (screeningQuestions.length === 0) {
      throw badRequestError('No screening questions found for this survey');
    }

    const [isPassed, userAnswers] = this.validateScreeningAnswers(data, questions);

    if (!userId) {
      return await this.handleAnonymousSubmission(surveyId, isPassed, userAnswers, data.startDate);
    }

    return await this.handleUserSubmission(surveyId, userId, isPassed, userAnswers, data.startDate);
  }

  private async handleUserSubmission(
    surveyId: number,
    userId: number,
    isPassed: boolean,
    userAnswers: UserAnswer[],
    startDate?: Date,
  ): Promise<boolean> {
    const user = await this.userRepository.findOneBy({ id: userId, deletedAt: IsNull() });
    if (!user || user.verificationStatus !== VerificationStatus.Verified) {
      throw notFoundError('User not found or not verified');
    }

    const userSurvey = await this.userSurveyRepository.findOneBy({
      userId,
      surveyId,
      isComplete: false,
      isPassScreening: true,
    });

    if (!userSurvey) {
      throw notFoundError('Survey is not assigned to user');
    }

    const screeningResults = await this.screeningResultRepository.find({
      where: { userId, surveyId },
      order: { createdAt: 'DESC' },
    });

    const lastResult = screeningResults[0];

    if (lastResult) {
      if (!lastResult.isPassed) {
        return false;
      }

      if (screeningResults.length > userSurvey.totalSubmissions) {
        await this.screeningResponseRepository.delete({ screeningResultId: lastResult.id });
        await this.insertScreeningResponses(lastResult.id, userAnswers);

        await this.screeningResultRepository.update({ id: lastResult.id }, { isPassed, startDate });
        await this.userSurveyRepository.update({ id: userSurvey.id }, { isPassScreening: isPassed });

        return isPassed;
      }
    }

    const newResult = await this.screeningResultRepository.save({
      userId,
      surveyId,
      isPassed,
      startDate,
    });
    await this.insertScreeningResponses(newResult.id, userAnswers);

    if (!isPassed) {
      await this.userSurveyRepository.update({ id: userSurvey.id }, { isPassScreening: false });
    }

    return isPassed;
  }

  private async handleAnonymousSubmission(
    surveyId: number,
    isPassed: boolean,
    userAnswers: UserAnswer[],
    startDate?: Date,
  ): Promise<boolean> {
    if (!isPassed) {
      const anonymousUserId = await this.generateAnonymousUser();

      const result = await this.screeningResultRepository.save({
        userId: anonymousUserId,
        surveyId,
        isPassed,
        startDate,
      });

      await this.insertScreeningResponses(result.id, userAnswers);
    }

    return isPassed;
  }

  private async insertScreeningResponses(screeningResultId: number, userAnswers: UserAnswer[]): Promise<void> {
    await this.screeningResponseRepository
      .createQueryBuilder()
      .insert()
      .values(
        userAnswers.map(answer => ({
          screeningResultId,
          questionId: answer.questionId,
          questionOptionId: answer.questionOptionId,
        })),
      )
      .execute();
  }

  private async generateAnonymousUser(): Promise<number> {
    const tempEmail = `anonymous-${randomUUID()}@industrii.com`;
    const newAnonymousUser = await this.userRepository
      .createQueryBuilder()
      .insert()
      .into(User)
      .values({
        email: tempEmail,
        firstName: 'Anonymous',
        lastName: 'User',
        isPublic: true,
        deletedAt: new Date(),
      })
      .execute();

    return newAnonymousUser.identifiers[0].id;
  }
}
