import { z } from 'zod';

const screeningAnswerSchema = z.object({
  questionId: z.number().int().min(1),
  questionOptionIds: z.number().int().min(1).array().nonempty(),
});

export const submitScreeningSchema = z.object({
  surveyAnswers: z.array(screeningAnswerSchema).nonempty(),
  startDate: z.coerce.date().optional(),
});

export type SubmitScreeningPayload = z.infer<typeof submitScreeningSchema>;
