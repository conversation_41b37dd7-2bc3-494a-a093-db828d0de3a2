import { Locale } from '@/backend/surveys/entities/Survey';
import { z } from 'zod';

export const baseTranslationSchema = z.object({
  locale: z.enum([Locale.FR]),
  title: z.string().min(1),
});

export const surveyTranslationSchema = baseTranslationSchema.extend({
  description: z.string().optional(),
});

export const questionTranslationSchema = baseTranslationSchema.extend({
  subtitle: z.string().optional(),
});

export const optionTranslationSchema = baseTranslationSchema;
