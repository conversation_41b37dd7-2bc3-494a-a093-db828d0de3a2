import { z } from 'zod';

const surveyAnswerSchema = z.object({
  questionId: z.number().int().min(1),
  questionOptionIds: z.number().int().min(1).array().optional(),
  value: z.string().optional(),
});

export const submitPrivateSurveySchema = z.object({
  startDate: z.coerce.date().optional(), // Old mobile app compatibility
  surveyAnswers: z.array(surveyAnswerSchema).nonempty(),
});

export const submitPublicSurveySchema = z.object({
  surveyAnswers: z.array(surveyAnswerSchema).nonempty(),
  email: z
    .string({
      required_error: 'Email is required',
    })
    .toLowerCase()
    .email('Invalid email address')
    .nullable()
    .optional(),
  startDate: z.coerce.date(),
});

export type SubmitPrivateSurveyPayload = z.infer<typeof submitPrivateSurveySchema>;
export type SubmitPublicSurveyPayload = z.infer<typeof submitPublicSurveySchema>;
export type SurveyAnswer = z.infer<typeof surveyAnswerSchema>;
export type SubmitSurveyPayload = SubmitPrivateSurveyPayload | SubmitPublicSurveyPayload;
