import { surveyTranslationSchema } from '@/backend/surveys/validations/translation';
import { z } from 'zod';

export const updateSurveySchema = z
  .object({
    companyId: z.number().int().min(1).optional(),
    title: z.string().min(1).optional(),
    description: z.string().optional().nullable(),
    compensation: z.number().int().min(0).optional(),
    maxParticipants: z.number().int().min(1).nullable().optional(),
    time: z.number().int().min(1).optional(),
    expiryDate: z.coerce.date().optional(),
    image: z.string().optional(),
    backgroundImage: z.string().optional(),
    isPinned: z.boolean().optional(),
    translation: surveyTranslationSchema.partial().optional(),
    responsesPerUser: z.number().int().min(1).optional(),
    isPublic: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.maxParticipants && data.responsesPerUser && data.maxParticipants < data.responsesPerUser) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Responses per user must be less than max responses',
        path: ['responsesPerUser'],
      });
    }
  });

export type UpdateSurveyPayload = z.infer<typeof updateSurveySchema>;
