import { z } from 'zod';

export const publishSurveySchema = z.object({
  audienceId: z.number().int().min(1).optional(),
  isPublish: z.boolean().optional(),
  cities: z.string().array().nullable().optional(),
  completedSurveys: z.number().array().nullable().optional(),
  employmentStatuses: z.string().array().nullable().optional(),
  practiceSettings: z.string().array().nullable().optional(),
  provinces: z.string().array().nullable().optional(),
  specialtyIds: z.number().array().nullable().optional(),
  userIds: z.number().array().nullable().optional(),
});

export type PublishSurveyPayload = z.infer<typeof publishSurveySchema>;
