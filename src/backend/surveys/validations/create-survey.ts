import { isNumber } from '@/backend/shared/utils/primitive';
import { SurveyQuestionType } from '@/backend/surveys/entities/SurveyQuestion';
import { inputQuestions, selectionQuestions } from '@/backend/surveys/types/survey-question';
import {
  optionTranslationSchema,
  questionTranslationSchema,
  surveyTranslationSchema,
} from '@/backend/surveys/validations/translation';
import { z } from 'zod';
import { SurveyStatus } from '../entities/Survey';

const surveyAudienceSchema = z.object({
  audienceId: z.number().int().min(1).optional(),
  isPublish: z.boolean(),
  cities: z.string().array().optional(),
  completedSurveys: z.number().array().optional(),
  employmentStatuses: z.string().array().optional(),
  practiceSettings: z.string().array().optional(),
  provinces: z.string().array().optional(),
  specialtyIds: z.number().array().optional(),
  userIds: z.number().array().optional(),
});

export const questionOptionSchema = z.object({
  title: z.string().min(1),
  translation: optionTranslationSchema.optional(),
  isEligible: z.boolean().optional(),
});

export const createQuestionSchema = z
  .object({
    order: z.number().int().min(1),
    questionType: z.nativeEnum(SurveyQuestionType),
    title: z.string().min(1),
    subtitle: z.string().optional(),
    translation: questionTranslationSchema.optional(),
    minValue: z.number().int().min(0).optional(),
    maxValue: z.number().int().min(1).optional(),
    options: z.array(questionOptionSchema).nonempty().optional(),
    hasOtherOption: z.boolean().optional(),
    isMultiSelectionEnabled: z.boolean().optional(),
  })
  .superRefine((val, ctx) => {
    validateQuestionSchema(val, ctx);
  });

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function validateQuestionSchema(val: any, ctx: z.RefinementCtx) {
  if (val.questionType === SurveyQuestionType.Slider) {
    if (!isNumber(val.minValue) || !isNumber(val.maxValue)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['questionType', 'minValue', 'maxValue'],
        message: 'Min and max values are required for Slider questions',
        fatal: true,
      });

      return z.NEVER;
    }

    if (val.minValue! >= val.maxValue!) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['questionType', 'minValue', 'maxValue'],
        message: 'Min value must be less than max value',
        fatal: true,
      });

      return z.NEVER;
    }
  } else {
    if (isNumber(val.minValue) || isNumber(val.maxValue)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['questionType', 'minValue', 'maxValue'],
        message: 'Min and max values are not allowed for non-Slider questions',
        fatal: true,
      });

      return z.NEVER;
    }
  }

  if (inputQuestions.includes(val.questionType) && val.options) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['questionType', 'questionOptions'],
      message: 'Options are not allowed for Text, Date and Number questions',
      fatal: true,
    });

    return z.NEVER;
  }

  if (selectionQuestions.includes(val.questionType) && (!val.options || val.options.length === 0)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['questionType', 'questionOptions'],
      message: 'Options are required for Single Selection, Multiple Selection, Rank and Screening questions',
      fatal: true,
    });

    return z.NEVER;
  }

  if (
    val.questionType !== SurveyQuestionType.SingleSelection &&
    val.questionType !== SurveyQuestionType.MultipleSelection &&
    val.hasOtherOption
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['questionType', 'hasOtherOption'],
      message: 'Other option is only allowed for Single Selection and Multiple Selection questions',
      fatal: true,
    });

    return z.NEVER;
  }

  if (val.questionType !== SurveyQuestionType.Screening && val.isMultiSelectionEnabled) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['questionType', 'isMultiSelectionEnabled'],
      message: 'Multi-selection is only allowed for Screening questions',
      fatal: true,
    });

    return z.NEVER;
  }

  if (val.questionType === SurveyQuestionType.Screening) {
    for (const option of val.options) {
      if (option.isEligible === undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['questionType', 'questionOptions', 'isEligible'],
          message: 'Mark as Eligible is required for Screening question options',
          fatal: true,
        });

        return z.NEVER;
      }
    }

    const countEligible = val.options.filter((o: QuestionOptionPayload) => o.isEligible).length;
    if (countEligible === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['questionType', 'questionOptions', 'isEligible'],
        message: 'At least one option must be marked as Eligible for Screening questions',
        fatal: true,
      });

      return z.NEVER;
    }
  }

  if (val.options) {
    for (let i = 0; i < val.options.length; i++) {
      if (val.translation && !val.options[i].translation) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['questionOptions', 'optionTranslation'],
          message: 'Option translation is required',
          fatal: true,
        });

        return z.NEVER;
      }

      if (!val.translation && val.options[i].translation) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['questionOptions', 'optionTranslation'],
          message: 'Option translation is not allowed',
          fatal: true,
        });

        return z.NEVER;
      }
    }
  }
}

export const createSurveySchema = z
  .object({
    companyId: z.number().int().min(1),
    title: z.string().min(1),
    description: z.string().optional(),
    compensation: z.number().int().min(0),
    maxParticipants: z.number().int().min(1).nullable(),
    time: z.number().int().min(1),
    expiryDate: z.coerce.date(),
    image: z.string().optional(),
    backgroundImage: z.string().optional(),
    isPinned: z.boolean(),
    status: z.enum([SurveyStatus.Active, SurveyStatus.Draft]),
    translation: surveyTranslationSchema.optional(),
    questions: z.array(createQuestionSchema).nonempty().optional(),
    audience: surveyAudienceSchema.optional(),
    responsesPerUser: z.number().int().min(1),
    isPublic: z.boolean(),
  })
  .superRefine((val, ctx) => {
    if (val.responsesPerUser && val.maxParticipants && val.responsesPerUser > val.maxParticipants) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['responsesPerUser'],
        message: 'Responses per user must be less than max responses',
        fatal: true,
      });

      return z.NEVER;
    }

    if (val.questions) {
      const sortedOrders = val.questions.map(q => q.order).sort((a, b) => a - b);

      for (let i = 0; i < sortedOrders.length; i++) {
        if (sortedOrders[i] !== i + 1) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['questions'],
            message: 'Questions must have consecutive order numbers starting from 1',
            fatal: true,
          });

          return z.NEVER;
        }
      }
    }

    if (!val.translation && val.questions && val.questions.find(q => q.translation)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['translation'],
        message: 'Translation is not allowed for questions',
        fatal: true,
      });

      return z.NEVER;
    }

    if (val.audience && val.isPublic) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['audience', 'isPublic'],
        message: 'Public surveys cannot have Audience',
        fatal: true,
      });

      return z.NEVER;
    }

    if (val.status === SurveyStatus.Active) {
      if (!val.questions) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['status', 'questions'],
          message: 'Questions are required for active surveys',
          fatal: true,
        });

        return z.NEVER;
      }

      if (!val.audience && !val.isPublic) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['status', 'audience'],
          message: 'Audience is required for active surveys',
          fatal: true,
        });

        return z.NEVER;
      }
    }

    return z.NEVER;
  });

export type CreateSurveyPayload = z.infer<typeof createSurveySchema>;
export type CreateQuestionPayload = z.infer<typeof createQuestionSchema>;
export type SurveyAudiencePayload = z.infer<typeof surveyAudienceSchema>;
export type QuestionOptionPayload = z.infer<typeof questionOptionSchema>;
