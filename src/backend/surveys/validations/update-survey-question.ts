import { MessagesApi } from '@/backend/shared/common/messages';
import { Locale } from '@/backend/surveys/entities/Survey';
import { surveyQuestionType, ZOD_VALUE } from '@/utils/zod-value';
import { z } from 'zod';
import { SurveyQuestionType } from '../entities/SurveyQuestion';

export const updateSurveyQuestionSchema = z
  .object({
    id: z.number(),
    order: z.coerce.number().optional(),
    questionType: z.enum(surveyQuestionType).optional(),
    locale: z.enum([Locale.EN, Locale.FR]).optional(),
    title: z.string().min(1).optional(),
    subtitle: z.string().optional(),
    translation: ZOD_VALUE.TRANSLATION.merge(z.object({ id: z.number().optional() })).optional(),
    minValue: z.coerce.number().optional(),
    maxValue: z.coerce.number().optional(),
    options: z
      .array(
        z.object({
          id: z.number().optional(),
          title: z.coerce.string(),
          isEligible: z.boolean().optional(),
          translation: z
            .object({
              id: z.number().optional(),
              locale: z.enum([Locale.FR]),
              title: z.coerce.string(),
            })
            .optional(),
        }),
      )
      .optional(),
    surveyId: z.number(),
    hasOtherOption: z.boolean().optional(),
    isMultiSelectionEnabled: z.boolean().optional(),
  })
  .superRefine((val, ctx) => {
    if (val.questionType) {
      if (val.questionType === SurveyQuestionType.Slider) {
        if (!val.minValue || !val.maxValue) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['questionType', 'minValue', 'maxValue'],
            message: MessagesApi.INVALID_BODY,
          });
        }
      }

      if (val.questionType !== SurveyQuestionType.Slider) {
        if (val.minValue || val.maxValue) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['questionType', 'minValue', 'maxValue'],
            message: MessagesApi.INVALID_BODY,
          });
        }
      }
    }

    return z.NEVER;
  });

export const updateQuestionOrderSchema = z.object({
  questions: z
    .array(
      z.object({
        id: z.number().min(1),
        order: z.number().min(1),
      }),
    )
    .nonempty(),
});

export type UpdateSurveyQuestionPayload = z.infer<typeof updateSurveyQuestionSchema>;
export type UpdateQuestionOrderPayload = z.infer<typeof updateQuestionOrderSchema>;
