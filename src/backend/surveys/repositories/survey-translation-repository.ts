import { DataSource, QueryRunner, Repository } from 'typeorm';
import { SurveyTranslation } from '../entities/SurveyTranslation';
import { TranslationPayload } from '../types/survey';
import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { MessagesApi } from '@/backend/shared/common/messages';

export class SurveyTranslationRepository extends Repository<SurveyTranslation> {
  private surveyTranslationRepository: Repository<SurveyTranslation>;
  constructor(private dataSource: DataSource) {
    super(SurveyTranslation, dataSource.createEntityManager());
    this.surveyTranslationRepository = dataSource.getRepository(SurveyTranslation);
  }

  public async createSurveyTranslation(data: TranslationPayload, queryRunner: QueryRunner): Promise<void> {
    const newSurveyTransl = this.surveyTranslationRepository.create(data);
    await queryRunner.manager.save(newSurveyTransl, { transaction: true });
    return;
  }

  public async updateSurveyTranslation(data: TranslationPayload): Promise<void> {
    await this.surveyTranslationRepository.update({ surveyId: data.surveyId }, { ...data });
  }

  public async findBySurveyId(surveyId: number): Promise<SurveyTranslation> {
    const translation = await this.surveyTranslationRepository.findOneBy({ surveyId });
    if (!translation) {
      throw customHttpError(400, MessagesApi.SURVEY_VALID, ErrorCode.BadRequest);
    }
    return translation;
  }
}
