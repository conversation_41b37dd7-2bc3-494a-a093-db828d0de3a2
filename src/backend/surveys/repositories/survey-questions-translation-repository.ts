import { DataSource, QueryRunner, Repository } from 'typeorm';
import { SurveyQuestionTranslation } from '../entities/SurveyQuestionTranslation';
import { TranslationPayload } from '../types/survey';

export class SurveyQuestionTranslationRepository extends Repository<SurveyQuestionTranslation> {
  private repository: Repository<SurveyQuestionTranslation>;
  constructor(private dataSource: DataSource) {
    super(SurveyQuestionTranslation, dataSource.createEntityManager());
    this.repository = dataSource.getRepository(SurveyQuestionTranslation);
  }

  public async createQuestionTranslation(
    surveyQuestionTranslation: TranslationPayload,
    questionId: number,
    surveyId: number,
    queryRunner: QueryRunner,
  ): Promise<void> {
    const newQuestion = this.repository.create({ questionId, surveyId, ...surveyQuestionTranslation });
    await queryRunner.manager.save(newQuestion, { transaction: true });
  }

  public async updateQuestionTranslation(
    surveyQuestionTranslation: TranslationPayload,
    queryRunner: QueryRunner,
  ): Promise<void> {
    await queryRunner.manager.update(
      SurveyQuestionTranslation,
      surveyQuestionTranslation.id,
      surveyQuestionTranslation,
    );
  }

  public async deleteAllTranslationOfQuestion(questionId: number, queryRunner: QueryRunner): Promise<void> {
    await queryRunner.manager.delete(SurveyQuestionTranslation, { questionId });
  }
}
