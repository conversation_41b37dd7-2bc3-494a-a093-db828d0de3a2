import { DataSource, QueryRunner, Repository } from 'typeorm';
import { SurveyQuestionOptionTranslation } from '../entities/SurveyQuestionOptionTranslation';
import { TranslationPayload } from '../types/survey';

export class SurveyQuestionOptionTranslationRepository extends Repository<SurveyQuestionOptionTranslation> {
  private repository: Repository<SurveyQuestionOptionTranslation>;

  constructor(private dataSource: DataSource) {
    super(SurveyQuestionOptionTranslation, dataSource.createEntityManager());
    this.repository = dataSource.getRepository(SurveyQuestionOptionTranslation);
  }

  public async updateQuestionOptionTranslations(data: TranslationPayload, queryRunner: QueryRunner): Promise<void> {
    await queryRunner.manager.update(SurveyQuestionOptionTranslation, { id: data.id }, { ...data });
  }

  public async createNewQuestionOptionTranslation(
    data: Partial<SurveyQuestionOptionTranslation>,
    queryRunner: QueryRunner,
  ): Promise<void> {
    await queryRunner.manager.insert(SurveyQuestionOptionTranslation, { ...data });
  }

  public async deleteTranslationForQuestionOptions(optionIds: number[], queryRunner: QueryRunner): Promise<void> {
    if (optionIds.length === 0) return;
    await queryRunner.manager.delete(SurveyQuestionOptionTranslation, optionIds);
  }
}
