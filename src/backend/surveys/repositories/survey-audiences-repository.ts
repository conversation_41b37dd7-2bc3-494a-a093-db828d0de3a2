import { DataSource, InsertResult, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { SurveyAudience } from '../entities/SurveyAudience';

export class SurveyAudienceRepository extends Repository<SurveyAudience> {
  constructor(private dataSource: DataSource) {
    super(SurveyAudience, dataSource.createEntityManager());
  }

  public async delByAudienceId(audienceId: number): Promise<void> {
    await this.delete({ audienceId });
  }

  public async insertWithQueryBuilder(
    values: QueryDeepPartialEntity<SurveyAudience> | QueryDeepPartialEntity<SurveyAudience>[],
  ): Promise<InsertResult> {
    const result = await this.createQueryBuilder().insert().values(values).execute();
    return result;
  }
}
