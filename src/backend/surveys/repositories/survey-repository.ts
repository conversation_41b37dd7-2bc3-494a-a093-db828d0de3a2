import { Company } from '@/backend/companies/entities/Company';
import { ErrorCode, badRequestError, customHttpError, notFoundError } from '@/backend/middlewares/capture-errors';
import { SURVEY_FIELDS, SURVEY_SORTABLE, SURVEY_TRANSL_FIELDS } from '@/backend/shared/common/keyof';
import { MessagesApi } from '@/backend/shared/common/messages';
import { CurrentUser } from '@/backend/shared/types/app';
import { AdminRole, UserAdmin } from '@/backend/users/entities/UserAdmin';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Survey, SurveyStatus } from '../entities/Survey';
import { SurveyAudience } from '../entities/SurveyAudience';
import { SurveyTranslation } from '../entities/SurveyTranslation';

export class SurveyRepository extends Repository<Survey> {
  constructor(private dataSource: DataSource) {
    super(Survey, dataSource.createEntityManager());
  }

  public async validSoftDelById(id: number): Promise<Survey> {
    const queryBuilder = this.queryInnerJoinAndMapOne(SURVEY_FIELDS);
    const survey = await queryBuilder.andWhere('survey.id = :id', { id }).getOne();
    if (!survey || survey.deletedAt) {
      throw customHttpError(400, 'Survey not available', ErrorCode.BadRequest);
    }
    return survey;
  }

  public async findCompany(companyId: number): Promise<void> {
    const survey = await this.findOneBy({ companyId });
    if (survey) {
      throw customHttpError(400, MessagesApi.DELL_COMPANY_VALID, ErrorCode.BadRequest);
    }
  }

  public async listSurveys(
    data: PaginationPayload<Survey>,
    user: CurrentUser,
  ): Promise<PaginationResponseData<Survey>> {
    const { page = 1, pageSize = 10, sortBy, sortOrder, search } = data;
    const queryBuilder = this.queryInnerJoinAndMapOne([...SURVEY_SORTABLE, 'isPublic', 'publicQRCode']);

    if (search) {
      queryBuilder.andWhere(
        '(CAST(survey.id as text) ILIKE :search OR survey.title ILIKE :search OR survey.description ILIKE :search OR company.name ILIKE :search OR survey.status::text ILIKE :search)',
        {
          search: `%${search}%`,
        },
      );
    }

    if (user.role === AdminRole.AccountManager) {
      queryBuilder.andWhere('survey.createdBy = :userId', { userId: user.id });
      queryBuilder.andWhere('survey.isPublic = true');
    }

    queryBuilder.orderBy('survey.successfulCompletions', 'DESC');

    if (sortBy && SURVEY_SORTABLE.includes(sortBy as keyof Survey)) {
      if (sortBy !== 'status') {
        queryBuilder.orderBy('survey.status', 'ASC');
      }

      if (sortBy === 'companyId') {
        queryBuilder.addOrderBy('company.name', sortOrder);
      } else {
        queryBuilder.addOrderBy(`survey.${sortBy}`, sortOrder);
      }
    } else {
      queryBuilder.addOrderBy('survey.id', 'DESC');
    }

    return await withPagination(queryBuilder, page, pageSize);
  }

  public async surveysByAudienceId(audienceId: number): Promise<Survey[]> {
    const queryBuilder = this.queryInnerJoinAndMapOne(SURVEY_FIELDS);
    queryBuilder.leftJoinAndSelect(SurveyAudience, 'survey_audience', 'survey_audience.surveyId = survey.id');
    queryBuilder.where('survey_audience.audienceId = :audienceId', { audienceId });
    return await queryBuilder.getMany();
  }

  public async surveysByCompanyId(companyId: number): Promise<Survey[]> {
    const queryBuilder = this.queryInnerJoinAndMapOne(SURVEY_FIELDS);
    queryBuilder.where('survey.companyId = :companyId', { companyId });
    return await queryBuilder.getMany();
  }

  public async getPublishedSurveys(): Promise<Survey[]> {
    const queryBuilder = this.queryInnerJoinAndMapOne(SURVEY_FIELDS);
    queryBuilder.where('status != :status', { status: SurveyStatus.Draft });
    queryBuilder.orderBy('survey.title', 'ASC');
    return await queryBuilder.getMany();
  }

  private queryInnerJoinAndMapOne(selects: Array<keyof Survey>): SelectQueryBuilder<Survey> {
    const queryBuilder = this.createQueryBuilder('survey');
    queryBuilder.where('survey.deletedAt IS NULL');
    queryBuilder.innerJoinAndMapOne('survey.company', Company, 'company', 'company.id = survey.companyId');
    queryBuilder.leftJoinAndMapOne('survey.translation', SurveyTranslation, 'suTrans', 'suTrans.surveyId = survey.id');
    queryBuilder.innerJoinAndMapOne('survey.userAdmin', UserAdmin, 'admin', 'admin.id = survey.createdBy');
    queryBuilder.select(selects.map(key => `survey.${key}`));
    queryBuilder.addSelect('survey.isPinned');
    queryBuilder.addSelect(['company.name']);
    queryBuilder.addSelect(['admin.firstName', 'admin.lastName', 'admin.email']);
    queryBuilder.addSelect(SURVEY_TRANSL_FIELDS.map(key => `suTrans.${key}`));
    return queryBuilder;
  }

  public async findSurveyExpired(id: number): Promise<Survey> {
    const survey = await this.findOneBy({ id });
    if (!survey) {
      throw customHttpError(400, MessagesApi.SURVEY_VALID, ErrorCode.BadRequest);
    }
    if (survey.status === SurveyStatus.Expired) {
      throw customHttpError(400, MessagesApi.SURVEY_EXPIRED, ErrorCode.BadRequest);
    }
    return survey;
  }

  public validateAvailableSurvey(survey: Survey | null, forSubmission: boolean = false): Survey {
    if (!survey) {
      throw notFoundError('Survey not found');
    }

    if (survey.status === SurveyStatus.Expired) {
      throw badRequestError('Survey is expired');
    }

    if (new Date(survey.expiryDate) < new Date()) {
      throw badRequestError('This survey has passed its expiry date and is no longer available');
    }

    if (forSubmission) {
      if (survey.status !== SurveyStatus.Active) {
        throw badRequestError('Survey is not active');
      }

      if (
        survey.maxParticipants !== null &&
        !isNaN(survey.maxParticipants) &&
        survey.successfulCompletions >= survey.maxParticipants
      ) {
        throw badRequestError('Survey limit reached');
      }
    }

    return survey;
  }

  public async getAvailableSurveyForEditing(id: number): Promise<Survey> {
    const survey = await this.findOneBy({ id });
    const validatedSurvey = this.validateAvailableSurvey(survey);

    return validatedSurvey;
  }

  public async getAvailableSurveyForSubmission(id: number): Promise<Survey> {
    const survey = await this.findOneBy({ id });
    const forSubmission = true;
    const validatedSurvey = this.validateAvailableSurvey(survey, forSubmission);

    return validatedSurvey;
  }

  // Retrieve last published survey for audience
  public async getAudienceLastSurvey(audienceId: number, surveyId?: number): Promise<Survey | null> {
    const queryBuilder = this.createQueryBuilder('surveys')
      .innerJoin(SurveyAudience, 'survey_audiences', 'surveys.id = survey_audiences.surveyId')
      .where('survey_audiences.audienceId = :audienceId', { audienceId })
      .andWhere('surveys.startDate IS NOT NULL')
      .orderBy('surveys.startDate', 'DESC');

    if (surveyId) {
      queryBuilder.andWhere('surveys.id <> :surveyId', { surveyId });
    }

    return await queryBuilder.getOne();
  }
}
