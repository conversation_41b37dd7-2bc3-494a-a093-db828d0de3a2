import {
  SURVEY_QUESTION_KEYS,
  SURVEY_QUESTION_OPTION_KEYS,
  SURVEY_QUESTION_OPTION_TRANSLATION_KEYS,
  SURVEY_QUESTION_TRANSLATION_KEYS,
} from '@/backend/shared/common/keyof';
import { UserSurveyAnswer } from '@/backend/users/entities/UserSurveyAnswer';
import { createPayloadModel } from '@/lib/utils';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import { SurveyQuestion, SurveyQuestionType } from '../entities/SurveyQuestion';
import { SurveyQuestionOption } from '../entities/SurveyQuestionOption';
import { SurveyQuestionOptionTranslation } from '../entities/SurveyQuestionOptionTranslation';
import { SurveyQuestionTranslation } from '../entities/SurveyQuestionTranslation';
import { UpdateSurveyQuestionPayload } from '../validations/update-survey-question';

export class SurveyQuestionsRepository extends Repository<SurveyQuestion> {
  private repository: Repository<SurveyQuestion>;

  constructor(private dataSource: DataSource) {
    super(SurveyQuestion, dataSource.createEntityManager());
    this.repository = dataSource.getRepository(SurveyQuestion);
  }

  public async findAndCountQuestions(surveyId: number): Promise<[SurveyQuestion[], number]> {
    const [result, number] = await this.repository.findAndCountBy({ surveyId });
    return [result, number];
  }

  public async questionsBySurveyId(surveyId: number): Promise<SurveyQuestion[]> {
    // surveyQuestions: s1
    // surveyQuestionTranslation: s2
    // surveyQuestionOption: s3
    // surveyQuestionOptionTranslation: s4
    const queryBuilder = this.repository.createQueryBuilder('s1');
    queryBuilder.where('s1.surveyId = :surveyId', { surveyId });
    queryBuilder.leftJoinAndMapOne('s1.translation', SurveyQuestionTranslation, 's2', 's2.questionId = s1.id');
    queryBuilder.leftJoinAndMapMany('s1.options', SurveyQuestionOption, 's3', 's3.questionId = s1.id');
    queryBuilder.leftJoinAndMapOne(
      's3.translation',
      SurveyQuestionOptionTranslation,
      's4',
      's4.questionOptionId = s3.id',
    );
    queryBuilder.leftJoinAndMapMany(
      's1.answers',
      UserSurveyAnswer,
      'userSurveyAnswer',
      'userSurveyAnswer.questionId = s1.id',
    );
    queryBuilder.select(SURVEY_QUESTION_KEYS.map(key => `s1.${key}`));
    queryBuilder.addSelect(SURVEY_QUESTION_TRANSLATION_KEYS.map(key => `s2.${key}`));
    queryBuilder.addSelect(SURVEY_QUESTION_OPTION_KEYS.map(key => `s3.${key}`));
    queryBuilder.addSelect(SURVEY_QUESTION_OPTION_TRANSLATION_KEYS.map(key => `s4.${key}`));
    queryBuilder.addSelect(['userSurveyAnswer.id']);
    queryBuilder.addOrderBy('s1.order', 'ASC');
    queryBuilder.addOrderBy('s3.id', 'ASC');
    return await queryBuilder.getMany();
  }

  public async updateQuestion(
    data: Omit<UpdateSurveyQuestionPayload, 'surveyId'> & { surveyId?: number },
    queryRunner: QueryRunner,
  ): Promise<void> {
    let updatedData = {};
    if (data.questionType && data.questionType !== SurveyQuestionType.Slider) {
      updatedData = { ...data, minValue: null, maxValue: null };
    } else {
      updatedData = { ...data };
    }
    const payload = createPayloadModel(updatedData, SURVEY_QUESTION_KEYS);
    if (Object.keys(payload).length > 1) {
      await queryRunner.manager.update(SurveyQuestion, data.id, payload as Partial<SurveyQuestion>);
    }
  }
}
