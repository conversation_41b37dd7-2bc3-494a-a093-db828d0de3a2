import { DataSource, QueryRunner, Repository } from 'typeorm';
import { SurveyQuestionOption } from '../entities/SurveyQuestionOption';
import { SurveyQuestionOptionPayload } from '../types/survey';

export class SurveyQuestionOptionRepository extends Repository<SurveyQuestionOption> {
  private repository: Repository<SurveyQuestionOption>;

  constructor(private dataSource: DataSource) {
    super(SurveyQuestionOption, dataSource.createEntityManager());
    this.repository = dataSource.getRepository(SurveyQuestionOption);
  }

  public async updateQuestionOptions(data: SurveyQuestionOptionPayload, queryRunner: QueryRunner): Promise<void> {
    await queryRunner.manager.update(SurveyQuestionOption, { id: data.id }, { ...data });
  }

  public async createNewQuestionOption(
    data: Partial<SurveyQuestionOption>,
    questionId: number,
    queryRunner: QueryRunner,
  ): Promise<SurveyQuestionOption | null> {
    const result = await queryRunner.manager.insert(SurveyQuestionOption, { ...data, questionId });
    if (result.identifiers.length === 0) return null;
    const insertedOptionId = result.identifiers[0].id;
    return queryRunner.manager.findOne(SurveyQuestionOption, { where: { id: insertedOptionId } });
  }

  public async deleteQuestionOptions(optionIds: number[], queryRunner: QueryRunner): Promise<void> {
    if (optionIds.length === 0) return;
    await queryRunner.manager.delete(SurveyQuestionOption, optionIds);
  }
}
