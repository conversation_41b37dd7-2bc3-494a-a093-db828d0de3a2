import { z } from 'zod';
import { PublicReferralCodeStatus } from '@/backend/referral-codes/entities/PublicReferralCode';
import { nameShape } from '@/backend/shared/validations/name';

const codeShape = z
  .string({ required_error: 'Code is required' })
  .regex(/^[a-zA-Z0-9]+$/, 'Code cannot contain spaces or include special characters')
  .min(1, 'Code must contain 1 character at least')
  .max(10, 'Code must contain at most 10 characters')
  .transform(val => val.toUpperCase());

export const createReferralCodeSchema = z.object({
  code: codeShape,
  value: z.number({ required_error: 'Value is required' }),
  expiryDate: z.coerce.date().nullable().optional(),
  name: nameShape,
  description: z.string().optional().nullable(),
  maxUses: z.number().min(1, { message: 'Max uses must be greater than or equal to 1' }).optional().nullable(),
});

export type CreateReferralCodePayload = z.infer<typeof createReferralCodeSchema>;

export const updateReferralCodeSchema = z.object({
  code: codeShape.optional(),
  value: z.number().optional(),
  expiryDate: z.coerce.date().optional().nullable(),
  name: nameShape.optional(),
  description: z.string().optional().nullable(),
  maxUses: z.number().min(1, { message: 'Max uses must be greater than or equal to 1' }).optional().nullable(),
  status: z.nativeEnum(PublicReferralCodeStatus).optional(),
});

export type UpdateReferralCodePayload = z.infer<typeof updateReferralCodeSchema>;
