import { DecimalColumnTransformer } from '@/backend/shared/utils/data-transform';
import { UserReferralReward } from '@/backend/users/entities/UserReferralReward';
import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

export enum PublicReferralCodeStatus {
  Enabled = 'Enabled',
  Disabled = 'Disabled',
}

@Entity('public_referral_codes')
export class PublicReferralCode {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'varchar', length: 50, unique: true })
  code: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: new DecimalColumnTransformer(),
  })
  value: number;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({
    type: 'enum',
    enum: PublicReferralCodeStatus,
    default: PublicReferralCodeStatus.Enabled,
  })
  status: PublicReferralCodeStatus;

  @Column({ type: 'int', nullable: true })
  maxUses: number | null;

  @Column({ type: 'int', default: 0 })
  usages: number;

  @Column({ type: 'date', nullable: true })
  expiryDate: Date | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @OneToMany(() => UserReferralReward, userReferralReward => userReferralReward.publicReferralCode)
  userReferralRewards: Relation<UserReferralReward[]>;
}
