import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { DataSource, Not, Repository } from 'typeorm';
import { badRequestError, notFoundError } from '@/backend/middlewares/capture-errors';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { MessagesApi } from '@/backend/shared/common/messages';

export class PublicReferralCodeRepository extends Repository<PublicReferralCode> {
  private userRepository: UserRepository;
  constructor(private dataSource: DataSource) {
    super(PublicReferralCode, dataSource.createEntityManager());
    this.userRepository = new UserRepository(dataSource);
  }

  public async findExistedById(id: number): Promise<PublicReferralCode> {
    const referralCode = await this.findOne({ where: { id } });
    if (!referralCode) {
      throw notFoundError(MessagesApi.REFERRAL_CODE_VALID);
    }
    return referralCode;
  }

  public async checkCode(code: string, id?: number): Promise<void> {
    const [publicReferralCode, user] = await Promise.all([
      this.findOne({ where: { code, id: id ? Not(id) : undefined } }),
      this.userRepository.findOne({ where: { referralCode: code } }),
    ]);
    if (publicReferralCode || user) throw badRequestError(MessagesApi.REFERRAL_CODE_EXISTS);
    return;
  }
}
