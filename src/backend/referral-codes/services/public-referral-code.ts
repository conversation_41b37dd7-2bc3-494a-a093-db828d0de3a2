import { dataSource } from '@/backend/database/data-source';
import { PublicReferralCodeRepository } from '../repositories/public-referral-code-repository';
import { PublicReferralCode } from '../entities/PublicReferralCode';
import { DeleteResult } from 'typeorm';
import { CreateReferralCodePayload, UpdateReferralCodePayload } from '../validations/referral-code';
import { badRequestError } from '@/backend/middlewares/capture-errors';
import { PaginationPayload, withPagination } from '@/types/pagination';
import { MessagesApi } from '@/backend/shared/common/messages';

export class PublicReferralCodeService {
  private repository = new PublicReferralCodeRepository(dataSource);

  constructor() {}

  public async create(payload: CreateReferralCodePayload): Promise<PublicReferralCode> {
    await this.repository.checkCode(payload.code);
    return this.repository.save(payload);
  }

  public async update(id: number, payload: UpdateReferralCodePayload): Promise<PublicReferralCode> {
    let publicReferralCode = await this.repository.findExistedById(id);
    if (payload.code) await this.repository.checkCode(payload.code, id);
    if (payload.maxUses && payload.maxUses < publicReferralCode.usages) {
      throw badRequestError(`${MessagesApi.REFERRAL_MAX_USES_TOO_LOW} (${publicReferralCode.usages}).`);
    }
    publicReferralCode = await this.repository.findExistedById(id);
    return this.repository.save({ ...publicReferralCode, ...payload });
  }

  public async delete(id: number): Promise<DeleteResult> {
    const publicReferralCode = await this.repository.findExistedById(id);
    if (publicReferralCode.usages > 0) {
      throw badRequestError(MessagesApi.REFERRAL_CAN_NOT_BE_DELETED);
    }
    return this.repository.delete(id);
  }

  public async get(id: number): Promise<PublicReferralCode> {
    return this.repository.findExistedById(id);
  }

  public async list(data: PaginationPayload<PublicReferralCode>) {
    const { page = 1, pageSize = 10, sortBy, sortOrder, search } = data;
    const queryBuilder = this.repository.createQueryBuilder('prc');

    if (search) {
      queryBuilder.where(
        '(prc.code ILIKE :search OR prc.name ILIKE :search OR prc.description ILIKE :search OR prc.status::text ILIKE :search)',
        {
          search: `%${search}%`,
        },
      );
    }

    const sortableFields: Array<keyof PublicReferralCode> = [
      'id',
      'code',
      'name',
      'description',
      'value',
      'maxUses',
      'expiryDate',
      'status',
      'createdAt',
      'usages',
    ];

    if (sortBy && sortableFields.includes(sortBy as keyof PublicReferralCode)) {
      queryBuilder.orderBy(`prc.${sortBy}`, sortOrder);
    } else {
      queryBuilder.orderBy('prc.createdAt', 'DESC');
    }
    queryBuilder.select(sortableFields.map(key => `prc.${key}`));

    return await withPagination(queryBuilder, page, pageSize);
  }
}
