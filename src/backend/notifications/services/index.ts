import { BranchIOService } from '@/backend/branch-io/services';
import { dataSource } from '@/backend/database/data-source';
import { HubspotService } from '@/backend/hubspot/services/hubspot-service';
import { HubspotContact } from '@/backend/hubspot/types/contact';
import { logger } from '@/backend/shared/utils/logger';
import { User } from '@/backend/users/entities/User';
import { UserDevice } from '@/backend/users/entities/UserDevice';
import { UserDeviceRepository } from '@/backend/users/repositories/user-device-repository';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import config from '@/config';
import { initFirebaseAdmin } from '@/lib/firebase/admin';
import { RequireIdOnly } from '@/types';
import { messaging } from 'firebase-admin';
import _ from 'lodash';
import { Logger } from 'pino';
import { In, IsNull } from 'typeorm';

export const enum NotificationType {
  NewSurvey = 'survey',
  VerifyUser = 'verify',
  UnverifyUser = 'unverify',
  ReferralBonus = 'referral',
  DeleteUser = 'delete',
}

const enum TitleLocKey {
  SurveyAvailableTitle = 'survey_available_title',
  UserVerificationTitle = 'user_verification_title',
  ReferralBonusTitle = 'referral_bonus_title',
}

const enum BodyLocKey {
  SurveyAvailableBody = 'survey_available_body',
  UserVerificationBody = 'user_verification_body',
  ReferralBonusBody = 'referral_bonus_body',
}

const MAX_TOKENS_PER_BATCH = 500;

export class NotificationService {
  private readonly userDeviceRepository = new UserDeviceRepository(dataSource);
  private readonly userRepository = new UserRepository(dataSource);
  private readonly hubspotService = new HubspotService();
  private readonly branchIOService = new BranchIOService();
  private readonly log: Logger;

  constructor() {
    initFirebaseAdmin();
    this.log = logger.child({ description: 'NotificationService' });
  }

  private async getUserDevices(userId: number, isForcePush = false): Promise<UserDevice[]> {
    const userDevices = await this.userDeviceRepository.find({
      where: {
        userId,
        enabled: isForcePush ? undefined : true,
      },
    });

    return userDevices;
  }

  public async notifySurveyAvailable(userIds: number[], surveyId: number): Promise<void> {
    const userDevices = await this.userDeviceRepository.find({
      where: {
        userId: In(userIds),
        enabled: true,
      },
    });
    const tokens = userDevices.map(device => device.token);
    const notificationUserIds = new Set(userDevices.map(device => device.userId));

    await this.sendFirebaseNotification(
      tokens,
      {
        titleLocKey: TitleLocKey.SurveyAvailableTitle,
        bodyLocKey: BodyLocKey.SurveyAvailableBody,
        bodyLocArgs: [surveyId.toString()],
      },
      {
        type: NotificationType.NewSurvey,
        surveyId: surveyId.toString(),
      },
    );

    // Handle to send email through Hubspot workflow if users have not downloaded the app
    const usersWithoutDevices = userIds.filter(userId => !notificationUserIds.has(userId));
    if (usersWithoutDevices.length) {
      const users = await this.userRepository.find({
        where: {
          id: In(usersWithoutDevices),
          deletedAt: IsNull(),
          notificationEnabled: true,
        },
      });

      if (!users.length) {
        return;
      }

      const deepLink = await this.branchIOService.createSurveyDeepLink({
        custom_data: {
          type: NotificationType.NewSurvey,
          surveyId: surveyId.toString(),
        },
      });
      const contacts: RequireIdOnly<HubspotContact>[] = users.map(user => ({
        id: user.hubspotContactId!,
        industrii_survey_link: deepLink,
      }));

      await this.hubspotService.updateContacts(contacts);
    }
  }

  public async notifyUserVerification(userId: number): Promise<void> {
    const userDevices = await this.getUserDevices(userId);
    const tokens = userDevices.map(device => device.token);

    await this.sendFirebaseNotification(
      tokens,
      {
        titleLocKey: TitleLocKey.UserVerificationTitle,
        bodyLocKey: BodyLocKey.UserVerificationBody,
        bodyLocArgs: [],
      },
      {
        type: NotificationType.VerifyUser,
      },
    );

    // Handle to send email through Hubspot workflow if users have not downloaded the app
    if (!tokens.length) {
      const user = await this.userRepository.findOne({
        where: {
          id: userId,
          deletedAt: IsNull(),
          notificationEnabled: true,
        },
      });

      if (!user) {
        return;
      }

      await this.hubspotService.updateContact(user.hubspotContactId!, {
        industrii_user_verification_link: config.INDUSTRII_USER_VERIFICATION_LINK,
      });
    }
  }

  public async notifyUserUnverification(userId: number): Promise<void> {
    const userDevices = await this.getUserDevices(userId);
    const tokens = userDevices.map(device => device.token);

    await this.sendFirebaseNotification(tokens, undefined, {
      type: NotificationType.UnverifyUser,
    });
  }

  public async notifyDeletedUser(userId: number): Promise<void> {
    const userDevices = await this.getUserDevices(userId);
    const tokens = userDevices.map(device => device.token);

    await this.sendFirebaseNotification(tokens, undefined, {
      type: NotificationType.DeleteUser,
    });
  }

  public async notifyReferralBonus(referralUserId: number, referredUser: User, amount: number): Promise<void> {
    const userDevices = await this.getUserDevices(referralUserId);
    const tokens = userDevices.map(device => device.token);

    await this.sendFirebaseNotification(
      tokens,
      {
        titleLocKey: TitleLocKey.ReferralBonusTitle,
        bodyLocKey: BodyLocKey.ReferralBonusBody,
        bodyLocArgs: [referredUser.firstName!, amount.toString()],
      },
      {
        type: NotificationType.ReferralBonus,
        firstName: referredUser.firstName!,
        referredUserId: referredUser.id.toString(),
        amount: amount.toString(),
      },
    );

    // Handle to send email through Hubspot workflow if users have not downloaded the app
    if (!tokens.length) {
      const user = await this.userRepository.findOne({
        where: {
          id: referralUserId,
          deletedAt: IsNull(),
          notificationEnabled: true,
        },
      });

      if (!user) {
        return;
      }

      const deepLink = await this.branchIOService.createReferralBonusDeepLink({
        custom_data: {
          type: NotificationType.ReferralBonus,
          first_name: user.firstName as string,
          referredUserId: referredUser.id.toString(),
          amount: amount.toString(),
        },
      });
      const data: Partial<HubspotContact> = { industrii_referral_bonus_link: deepLink };
      await this.hubspotService.updateContact(user.hubspotContactId!, data);
    }
  }

  public async sendFirebaseNotification(
    tokens: string[],
    loc?: {
      titleLocKey: TitleLocKey;
      bodyLocKey: BodyLocKey;
      bodyLocArgs: string[];
    },
    metadata?: {
      [key: string]: string;
    },
  ): Promise<void> {
    if (!tokens.length) {
      return;
    }

    const batches = _.chunk(tokens, MAX_TOKENS_PER_BATCH);
    const promises = batches.map(async batch => {
      try {
        await messaging().sendEachForMulticast({
          tokens: batch,
          data: metadata,
          android: {
            notification: loc
              ? { titleLocKey: loc.titleLocKey, bodyLocKey: loc.bodyLocKey, bodyLocArgs: loc.bodyLocArgs }
              : undefined,
          },
          apns: {
            payload: {
              aps: {
                alert: loc
                  ? { titleLocKey: loc.titleLocKey, locKey: loc.bodyLocKey, locArgs: loc.bodyLocArgs }
                  : undefined,
              },
            },
          },
        });
      } catch (error) {
        this.log.warn(error, 'Failed to send notification');
      }
    });

    await Promise.all(promises);
  }
}
