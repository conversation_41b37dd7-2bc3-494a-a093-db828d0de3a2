import { dataSource } from '@/backend/database/data-source';
import { HubspotService } from '@/backend/hubspot/services/hubspot-service';
import { ErrorCode, badRequestError, notFoundError } from '@/backend/middlewares/capture-errors';
import { PaymentTransactionInfo } from '@/backend/payments/types/payment-info';
import {
  AccountBalanceResponse,
  VopayResponse,
  VopayTransactionEvent,
  VopayTransactionStatus,
} from '@/backend/payments/types/vopay-response';
import { EtransferPayload } from '@/backend/payments/validations/e-transfer';
import { ExchangePublicTokenPayload } from '@/backend/payments/validations/exchange-public-token';
import { WithdrawPayload } from '@/backend/payments/validations/withdraw';
import { logger } from '@/backend/shared/utils/logger';
import { User, VerificationStatus } from '@/backend/users/entities/User';
import { PaymentMethodType, UserPaymentMethod } from '@/backend/users/entities/UserPaymentMethods';
import { TransactionType, UserTransaction } from '@/backend/users/entities/UserTransaction';
import { UserPaymentMethodRepository } from '@/backend/users/repositories/user-payment-method-repository';
import { UserTransactionRepository } from '@/backend/users/repositories/user-transaction-repository';
import { UserService } from '@/backend/users/services/user-service';
import config, { bankLogos } from '@/config';
import axios, { AxiosRequestConfig, Method } from 'axios';
import { createHash } from 'crypto';
import { Logger } from 'pino';
import {
  AccountsGetResponse,
  Configuration,
  CountryCode,
  Institution,
  PlaidApi,
  PlaidEnvironments,
  ProcessorTokenCreateRequestProcessorEnum,
  Products,
} from 'plaid';
import { In, IsNull, Not } from 'typeorm';

export class PaymentService {
  private readonly plaidClient: PlaidApi;
  private readonly userPaymentMethodRepository: UserPaymentMethodRepository;
  private readonly userTransactionRepository: UserTransactionRepository;
  private readonly hubspotService: HubspotService;
  private readonly userService: UserService;
  private readonly log: Logger;

  constructor() {
    const configuration = new Configuration({
      basePath: PlaidEnvironments[config.PLAID_ENV],
      baseOptions: {
        headers: {
          'PLAID-CLIENT-ID': config.PLAID_CLIENT_ID,
          'PLAID-SECRET': config.PLAID_SECRET,
        },
      },
    });

    this.plaidClient = new PlaidApi(configuration);
    this.userPaymentMethodRepository = new UserPaymentMethodRepository(dataSource);
    this.userTransactionRepository = new UserTransactionRepository(dataSource);
    this.hubspotService = new HubspotService();
    this.userService = new UserService();
    this.log = logger.child({ description: 'PaymentService' });
  }

  public async createLinkToken(userId: number): Promise<string> {
    try {
      const response = await this.plaidClient.linkTokenCreate({
        user: {
          client_user_id: userId.toString(),
        },
        client_name: 'Industrii',
        products: [Products.Auth],
        country_codes: [CountryCode.Ca],
        language: 'en',
      });

      return response.data.link_token;
    } catch (error) {
      const msg = 'Failed to create Plaid link token';
      this.log.warn(error, msg);
      throw badRequestError(msg, ErrorCode.PlaidError);
    }
  }

  public async exchangePublicToken(userId: number, data: ExchangePublicTokenPayload): Promise<void> {
    let accessToken = '';
    let processorToken = '';

    try {
      const tokenResponse = await this.plaidClient.itemPublicTokenExchange({
        public_token: data.publicToken,
      });

      const accountId = data.accounts[0].id;
      accessToken = tokenResponse.data.access_token;

      const processorTokenResponse = await this.plaidClient.processorTokenCreate({
        access_token: accessToken,
        account_id: accountId,
        processor: ProcessorTokenCreateRequestProcessorEnum.Vopay,
      });

      processorToken = processorTokenResponse.data.processor_token;
    } catch (error) {
      const msg = 'Failed to exchange public token';
      this.log.warn(error, msg);
      throw badRequestError(msg, ErrorCode.PlaidError);
    }

    const account = await this.getAccount(accessToken);
    const bank = await this.getBank(account.item.institution_id!);

    await this.userPaymentMethodRepository.update(
      {
        userId,
        type: PaymentMethodType.Plaid,
        deletedAt: IsNull(),
      },
      {
        deletedAt: new Date(),
      },
    );

    await this.userPaymentMethodRepository.save({
      userId,
      type: PaymentMethodType.Plaid,
      accessToken,
      processorToken,
      mask: data.accounts[0].mask,
      bankName: bank.name,
      institutionId: bank.institution_id,
    });
  }

  public async getPaymentMethods(userId: number): Promise<UserPaymentMethod[]> {
    const paymentMethods = await this.userPaymentMethodRepository.find({
      where: {
        userId,
        deletedAt: IsNull(),
      },
      select: ['id', 'userId', 'type', 'email', 'mask', 'institutionId', 'bankName', 'createdAt', 'updatedAt'],
    });

    return paymentMethods.map(paymentMethod => {
      if (paymentMethod.type === PaymentMethodType.Plaid) {
        paymentMethod.logo = bankLogos[paymentMethod.institutionId!] || null;
      }

      if (paymentMethod.type === PaymentMethodType.Etransfer) {
        paymentMethod.logo = bankLogos.etransfer;
      }

      return paymentMethod;
    });
  }

  public async delete(id: number, userId: number): Promise<void> {
    const paymentMethod = await this.userPaymentMethodRepository.findOneBy({
      id,
      userId,
      type: PaymentMethodType.Plaid,
      deletedAt: IsNull(),
    });

    if (!paymentMethod) {
      throw notFoundError('Payment method not found');
    }

    await this.userPaymentMethodRepository.softDelete(id);
  }

  public async updateEtransfer(userId: number, data: EtransferPayload): Promise<void> {
    const paymentMethod = await this.userPaymentMethodRepository.findOneBy({
      userId,
      type: PaymentMethodType.Etransfer,
      deletedAt: IsNull(),
    });

    if (!paymentMethod) {
      throw notFoundError('Payment method not found');
    }

    await this.userPaymentMethodRepository.save({
      id: paymentMethod.id,
      userId,
      type: PaymentMethodType.Etransfer,
      email: data.email,
    });
  }

  public async withdraw(userId: number, data: WithdrawPayload): Promise<void> {
    let hubspotContactId;

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const [user, paymentMethod] = await Promise.all([
        queryRunner.manager
          .getRepository(User)
          .createQueryBuilder('users')
          .useTransaction(true)
          .setLock('pessimistic_write')
          .where('users.id = :userId', { userId })
          .getOne(),
        queryRunner.manager.findOneBy(UserPaymentMethod, {
          userId,
          type: data.type,
          deletedAt: IsNull(),
        }),
      ]);

      if (!user) {
        throw notFoundError('User not found');
      }

      if (!paymentMethod) {
        throw notFoundError('Payment method not found');
      }

      if (user.verificationStatus !== VerificationStatus.Verified) {
        throw badRequestError('User is not verified');
      }

      if (user.balance < data.amount) {
        throw badRequestError('Insufficient balance');
      }

      hubspotContactId = user.hubspotContactId!;
      let transactionId = '';

      switch (data.type) {
        case PaymentMethodType.Plaid:
          transactionId = await this.withdrawEft(user, paymentMethod.processorToken!, data.amount);
          break;
        case PaymentMethodType.Etransfer:
          transactionId = await this.withdrawEtransfer(user, paymentMethod.email!, data.amount);
          break;
      }

      const { title, paidTo } = this.getPaymentTransactionInfo(data.type, paymentMethod);

      await queryRunner.manager
        .getRepository(UserTransaction)
        .createQueryBuilder()
        .insert()
        .values({
          userId,
          type: TransactionType.Withdrawal,
          amount: -data.amount,
          title,
          refId: transactionId,
          paidTo,
          vopayTransactionStatus: VopayTransactionStatus.Pending,
        })
        .execute();

      await queryRunner.manager.update(
        User,
        {
          id: userId,
        },
        {
          balance: () => `balance - ${data.amount}`,
        },
      );

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();

      const msg = 'Failed to withdraw';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    } finally {
      await queryRunner.release();
    }
    await this.hubspotService.updateOnFirstWithdrawal(hubspotContactId).catch(_error => {});
  }

  private getPaymentTransactionInfo(type: PaymentMethodType, paymentMethod: UserPaymentMethod): PaymentTransactionInfo {
    switch (type) {
      case PaymentMethodType.Plaid:
        return {
          title: 'Bank Withdrawal',
          paidTo: `${paymentMethod.bankName} - ${paymentMethod.mask}`,
        };
      case PaymentMethodType.Etransfer:
        return {
          title: 'Interac Withdrawal',
          paidTo: paymentMethod.email!,
        };
    }
  }

  // https://docs.vopay.com/reference/interacbulkpayoutpost
  private async withdrawEtransfer(user: User, email: string, amount: number): Promise<string> {
    const transactionId = await this.sendVopayRequest('POST', 'interac/bulk-payout', {
      Amount: amount.toString(),
      EmailAddress: email,
      RecipientName: `${user.firstName} ${user.lastName}`,
      Question: 'Security Answer in the Industrii App?',
      Answer: user.referralCode!,
    });

    return transactionId;
  }

  // https://docs.vopay.com/reference/eftwithdrawpost
  private async withdrawEft(user: User, token: string, amount: number): Promise<string> {
    const transactionId = await this.sendVopayRequest('POST', 'eft/withdraw', {
      Amount: amount.toString(),
      FirstName: user.firstName!,
      LastName: user.lastName!,
      PlaidProcessorToken: token,
    });

    return transactionId;
  }

  private generateVopaySignature(): string {
    const shasum = createHash('sha1');
    const date = new Date().toISOString().split('T')[0]; // Convert to yyyy-mm-dd

    shasum.update(config.VOPAY_API_KEY + config.VOPAY_SECRET + date);
    const signature = shasum.digest('hex');

    return signature;
  }

  private async sendVopayRequest(method: Method, path: string, data?: Record<string, string>): Promise<string> {
    const signature = this.generateVopaySignature();
    const requestData = {
      AccountID: config.VOPAY_ACCOUNT_ID,
      Key: config.VOPAY_API_KEY,
      Signature: signature,
      Currency: 'CAD',
      ...data,
    };

    const requestOptions: AxiosRequestConfig = {
      method,
      url: `${config.VOPAY_API_URL}/${path}`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/x-www-form-urlencoded',
      },
      data: requestData,
    };

    const response = await axios.request(requestOptions);
    const responseData = response.data as VopayResponse;

    if (!responseData.Success) {
      throw badRequestError(responseData.ErrorMessage || 'Vopay request failed', ErrorCode.VopayError);
    }

    return responseData.TransactionID;
  }

  private async getAccount(token: string): Promise<AccountsGetResponse> {
    try {
      const response = await this.plaidClient.accountsGet({
        access_token: token,
      });

      return response.data;
    } catch (error) {
      const msg = 'Failed to get account';
      this.log.warn(error, msg);
      throw badRequestError(msg, ErrorCode.PlaidError);
    }
  }

  public async getBank(institutionId: string): Promise<Institution> {
    try {
      const response = await this.plaidClient.institutionsGetById({
        institution_id: institutionId,
        country_codes: [CountryCode.Ca],
      });

      return response.data.institution;
    } catch (error) {
      const msg = 'Failed to get bank';
      this.log.warn(error, msg);
      throw badRequestError(msg, ErrorCode.PlaidError);
    }
  }

  // https://docs.vopay.com/docs/signature-verification
  public async verifyVopaySignature(id: unknown, validationKey: unknown): Promise<void> {
    const calculatedKey = createHash('sha1')
      .update(config.VOPAY_SECRET + id)
      .digest('hex');

    if (calculatedKey !== validationKey) {
      throw badRequestError('Invalid Vopay signature', ErrorCode.VopayError);
    }
  }

  public async processVopayWebhook(data: Record<string, unknown>): Promise<void> {
    if (data && data.TransactionID && data.ValidationKey) {
      this.verifyVopaySignature(data.TransactionID, data.ValidationKey);
      await this.handleVopayTransactionEvent(data as unknown as VopayTransactionEvent);
    }

    return;
  }

  public async handleVopayTransactionEvent(data: VopayTransactionEvent): Promise<void> {
    const statusConditions = {
      [VopayTransactionStatus.Requested]: {
        where: { refId: data.TransactionID, vopayTransactionStatus: IsNull() },
      },
      [VopayTransactionStatus.Pending]: {
        where: {
          refId: data.TransactionID,
          vopayTransactionStatus: Not(
            In([
              VopayTransactionStatus.InProgress,
              VopayTransactionStatus.Successful,
              VopayTransactionStatus.Failed,
              VopayTransactionStatus.Cancelled,
            ]),
          ),
        },
      },
      [VopayTransactionStatus.InProgress]: {
        where: {
          refId: data.TransactionID,
          vopayTransactionStatus: Not(
            In([VopayTransactionStatus.Successful, VopayTransactionStatus.Failed, VopayTransactionStatus.Cancelled]),
          ),
        },
      },
      [VopayTransactionStatus.Successful]: { where: { refId: data.TransactionID } },
      [VopayTransactionStatus.Failed]: { where: { refId: data.TransactionID } },
      [VopayTransactionStatus.Cancelled]: { where: { refId: data.TransactionID } },
    };

    const condition = statusConditions[data.Status as VopayTransactionStatus];

    if (condition) {
      await this.userTransactionRepository.update(
        { ...condition.where, type: TransactionType.Withdrawal },
        {
          vopayTransactionStatus: data.Status as VopayTransactionStatus,
        },
      );

      if (data.Status === VopayTransactionStatus.Failed) {
        await this.handleFailedTransaction(data.TransactionID);
      }
    }
  }

  private async handleFailedTransaction(refId: string): Promise<void> {
    const withdrawalTransaction = await this.userTransactionRepository.findOneBy({
      refId,
      type: TransactionType.Withdrawal,
    });

    if (!withdrawalTransaction) {
      return;
    }

    const transaction = await this.userTransactionRepository.findOneBy({
      refId,
      type: TransactionType.Credit,
    });

    if (!transaction) {
      await this.userService.setUserBalance(
        withdrawalTransaction.userId,
        {
          amount: -withdrawalTransaction.amount,
          type: TransactionType.Credit,
        },
        refId,
      );
    }
  }

  public async getAccountBalance(): Promise<AccountBalanceResponse> {
    const signature = this.generateVopaySignature();

    const requestOptions: AxiosRequestConfig = {
      method: 'GET',
      url: `${config.VOPAY_API_URL}/account/balance?AccountID=${config.VOPAY_ACCOUNT_ID}&Key=${config.VOPAY_API_KEY}&Signature=${signature}`,
      headers: {
        accept: 'application/json',
      },
    };

    const response = await axios.request(requestOptions);
    const responseData = response.data as AccountBalanceResponse;

    if (!responseData.Success) {
      throw badRequestError(responseData.ErrorMessage || 'Vopay request failed', ErrorCode.VopayError);
    }

    return responseData;
  }
}
