export interface VopayResponse {
  Success: boolean;
  ErrorMessage: string;
  TransactionID: string;
  TransactionStatus: VopayTransactionStatus;
  Flagged: string;
}

export interface AccountBalanceResponse {
  Success: string;
  ErrorMessage: string;
  AccountBalance: string;
  PendingFunds: string;
  Reserve: string;
  RollingReserveNSF: string;
  RollingReserveReturns: string;
  AvailableImmediately: string;
  AvailableFunds: string;
  Currency: string;
  AsOfDate: string;
}

export interface VopayTransactionEvent {
  Success: boolean;
  Status: string;
  FailureReason: string;
  ID: string;
  TransactionAmount: string;
  ClientReferenceNumber: string;
  TransactionType: string;
  TransactionID: string;
  AccountID: string;
  UpdatedAt: string;
  ValidationKey: string;
  Environment: string;
}

export enum VopayTransactionStatus {
  Requested = 'requested',
  Pending = 'pending',
  InProgress = 'in progress',
  Successful = 'successful',
  Failed = 'failed',
  Cancelled = 'cancelled',
}
