import { PaymentMethodType } from '@/backend/users/entities/UserPaymentMethods';
import { z } from 'zod';

export const withdrawSchema = z.object({
  type: z.nativeEnum(PaymentMethodType, {
    required_error: 'Payment method type is required',
    invalid_type_error: 'Invalid payment method type',
  }),
  amount: z.number({ required_error: 'Amount is required' }).int().positive({ message: 'Amount must be positive' }),
});

export type WithdrawPayload = z.infer<typeof withdrawSchema>;
