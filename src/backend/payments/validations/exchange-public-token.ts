import { AccountType, DepositoryAccountSubtype, LinkDeliveryVerificationStatus } from 'plaid';
import { z } from 'zod';

export const exchangePublicTokenSchema = z.object({
  publicToken: z.string({ required_error: 'Public token is required' }).min(1),
  accounts: z
    .array(
      z.object({
        id: z.string({ required_error: 'Account ID is required' }),
        name: z.string({ required_error: 'Account name is required' }),
        mask: z.string().max(4),
        type: z.enum([AccountType.Depository], {
          required_error: 'Account type is required',
          invalid_type_error: 'Invalid account type',
        }),
        subtype: z.enum([DepositoryAccountSubtype.Checking, DepositoryAccountSubtype.Savings], {
          required_error: 'Account subtype is required',
          invalid_type_error: 'Invalid account subtype',
        }),
        verificationStatus: z
          .nativeEnum(LinkDeliveryVerificationStatus, {
            invalid_type_error: 'Invalid verification status',
          })
          .nullable()
          .optional(),
        classType: z.string().nullable().optional(),
      }),
    )
    .nonempty(),
});

export type ExchangePublicTokenPayload = z.infer<typeof exchangePublicTokenSchema>;
