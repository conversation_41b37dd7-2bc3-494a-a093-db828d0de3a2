import { CompanyRepository } from '@/backend/companies/repositories/company-repository';
import { dataSource } from '@/backend/database/data-source';
import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import { SurveyRepository } from '@/backend/surveys/repositories/survey-repository';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { UserSurveyRepository } from '@/backend/users/repositories/user-survey-repository';
import { UserService } from '@/backend/users/services/user-service';
import { In, IsNull, LessThanOrEqual, Not } from 'typeorm';

export class CronService {
  private readonly userService = new UserService();
  private readonly surveyService = new SurveyService();

  private readonly surveyRepository = new SurveyRepository(dataSource);
  private readonly userSurveyRepository = new UserSurveyRepository(dataSource);
  private readonly companyRepository = new CompanyRepository(dataSource);

  constructor() {}

  // Scheduled to execute every day at 12:05 AM
  public async syncSurveysStatus(): Promise<void> {
    const expiredSurveys = await this.surveyRepository.find({
      where: {
        expiryDate: LessThanOrEqual(new Date()),
        status: Not(SurveyStatus.Expired),
      },
    });

    if (expiredSurveys.length) {
      const surveyIds = expiredSurveys.map(survey => survey.id);

      await Promise.all([
        this.surveyRepository.update({ id: In(surveyIds) }, { status: SurveyStatus.Expired }),
        this.userSurveyRepository.delete({ surveyId: In(surveyIds), isComplete: false, totalSubmissions: 0 }),
      ]);
    }

    const companies = await this.companyRepository.find({
      where: {
        deletedAt: IsNull(),
      },
    });

    if (companies.length) {
      await Promise.all(
        companies.map(async company => {
          const surveys = await this.surveyRepository.find({
            where: {
              companyId: company.id,
              status: In([SurveyStatus.Active, SurveyStatus.Expired]),
            },
            select: {
              id: true,
              status: true,
            },
          });

          const lastSurvey = await this.surveyService.getCompanyLastSurvey(company.id);
          const surveysCompleted = surveys.filter(survey => survey.status === SurveyStatus.Expired).length;
          const surveysInProgress = surveys.filter(survey => survey.status === SurveyStatus.Active).length;

          await this.companyRepository.update(
            { id: company.id },
            {
              surveysCompleted,
              surveysInProgress,
              lastSurveyDate: lastSurvey?.startDate ?? null,
            },
          );
        }),
      );
    }
  }

  // Scheduled to execute every minute
  public async importUsers(): Promise<void> {
    await this.userService.processImportTask();
  }

  // Scheduled to execute every minute
  public async processPublicSurveys(): Promise<void> {
    await this.surveyService.handlePublicSurveyAnswer();
  }
}
