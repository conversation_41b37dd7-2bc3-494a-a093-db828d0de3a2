import { dataSource } from '@/backend/database/data-source';
import { AssociationPayload } from '@/backend/hubspot/types/association';
import {
  EmailOptIn,
  HCPVerificationStatus,
  HubspotContact,
  HubspotContactStatus,
  HubspotContactWithSubscriptions,
  PreVerifiedContact,
} from '@/backend/hubspot/types/contact';
import { CTCProduct, ProductValidationStatus } from '@/backend/hubspot/types/ctcproduct';
import { HubspotError } from '@/backend/hubspot/types/error';
import {
  ContactManageCommunicationPreferences,
  HubspotEventType,
  TicketWorkflowStatusChanged,
} from '@/backend/hubspot/types/event';
import { HubspotObject, HubspotObjectType } from '@/backend/hubspot/types/object';
import { Program } from '@/backend/hubspot/types/program';
import { SyncUserRegistrationResponse } from '@/backend/hubspot/types/response';
import { getHubspotOptoutField, HubspotSubscriptionType, StatusState } from '@/backend/hubspot/types/subscription';
import { Ticket, TicketStatus } from '@/backend/hubspot/types/ticket';
import { customHttpError, ErrorCode } from '@/backend/middlewares/capture-errors';
import { logger } from '@/backend/shared/utils/logger';
import { SupportRequestStatus } from '@/backend/support/entities/SupportRequest';
import { SupportRequestRepository } from '@/backend/support/repositories/support-request-repository';
import { ContactStatus, VerificationStatus } from '@/backend/users/entities/User';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import config from '@/config';
import { RequireIdOnly } from '@/types';
import { Client, Signature } from '@hubspot/api-client';
import { PublicUpdateSubscriptionStatusRequestLegalBasisEnum } from '@hubspot/api-client/lib/codegen/communication_preferences';
import {
  AssociationSpecWithLabel,
  AssociationSpecWithLabelCategoryEnum,
  MultiAssociatedObjectWithLabel,
} from '@hubspot/api-client/lib/codegen/crm/associations/v4';
import { AssociationSpecAssociationCategoryEnum } from '@hubspot/api-client/lib/codegen/crm/contacts';
import { OptionInput, Property } from '@hubspot/api-client/lib/codegen/crm/properties';
import { getTime, startOfDay } from 'date-fns';
import { Logger } from 'pino';

const CTC_PRODUCT_NAME = 'Industrii';

export class HubspotService {
  private readonly hubspotClient: Client;
  private readonly supportRequestRepository: SupportRequestRepository;
  private readonly userRepository: UserRepository;
  private readonly log: Logger;

  constructor() {
    this.hubspotClient = new Client({ accessToken: config.HUBSPOT_ACCESS_TOKEN });
    this.supportRequestRepository = new SupportRequestRepository(dataSource);
    this.userRepository = new UserRepository(dataSource);
    this.log = logger.child({ description: 'HubspotService' });
  }

  public async getContact(email: string): Promise<HubspotContactWithSubscriptions | null> {
    try {
      const requestProperties = [
        'email',
        'phone',
        'firstname',
        'lastname',
        'hs_language',
        'verification_status',
        'industrii_email_opt_in',
        getHubspotOptoutField(HubspotSubscriptionType.CTC_PRODUCTS_SERVICES),
        getHubspotOptoutField(HubspotSubscriptionType.MARKETING_INFORMATION),
        getHubspotOptoutField(HubspotSubscriptionType.EDUCATIONAL_PROGRAM),
        getHubspotOptoutField(HubspotSubscriptionType.PROGRAM_NOTIFICATIONS),
        getHubspotOptoutField(HubspotSubscriptionType.CUSTOMER_SERVICE),
        getHubspotOptoutField(HubspotSubscriptionType.ONE_TO_ONE),
      ];

      const response = await this.hubspotClient.crm.contacts.basicApi.getById(
        email,
        requestProperties,
        undefined,
        undefined,
        false,
        'email',
      );

      const { properties, id } = response;
      return { id, ...properties } as HubspotContactWithSubscriptions;
    } catch (error) {
      const err = error as HubspotError;
      if (err.code === 404) {
        return null;
      }

      const msg = err.body.message ? err.body.message : 'Error while getting Hubspot contact';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async createContact(data: Partial<HubspotContact>): Promise<string> {
    try {
      if (data.hcp_specialty) {
        data.hcp_specialty = await this.handleSpecialty(data.hcp_specialty);
      }

      const newContact = await this.hubspotClient.crm.contacts.basicApi.create({
        properties: { ...data },
        associations: [],
      });

      return newContact.id;
    } catch (error) {
      const err = error as HubspotError;
      const msg = 'Error while creating Hubspot contact';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async handleSpecialty(specialty: string): Promise<string> {
    const property = await this.getObjectProperty(HubspotObject.Contact, 'hcp_specialty');

    // Check if specialty already exists by value or label
    const option = property.options.find(
      x => x.value.toLowerCase() === specialty.toLowerCase() || x.label.toLowerCase() === specialty.toLowerCase(),
    );
    if (!option) {
      const value = specialty.toLowerCase();
      await this.addOptionsToProperty(HubspotObject.Contact, 'hcp_specialty', [
        ...property.options,
        {
          label: specialty,
          value,
          hidden: false,
        },
      ]);
      return value;
    }

    return option.value;
  }

  public async updateContact(id: string, data: Partial<HubspotContact>): Promise<void> {
    try {
      if (data.hcp_specialty) {
        data.hcp_specialty = await this.handleSpecialty(data.hcp_specialty);
      }

      await this.hubspotClient.crm.contacts.basicApi.update(id, {
        properties: { ...data },
      });
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while updating Hubspot contact';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public async updateContacts(data: RequireIdOnly<HubspotContact>[]): Promise<void> {
    try {
      const contacts = await Promise.all(
        data.map(async item => {
          if (item.hcp_specialty) {
            item.hcp_specialty = await this.handleSpecialty(item.hcp_specialty);
          }
          return item;
        }),
      );

      await this.hubspotClient.crm.contacts.batchApi.update({
        inputs: contacts.map(contact => {
          const { id, ...otherFields } = contact;
          return { id, properties: { ...otherFields } };
        }),
      });
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while updating Hubspot contacts';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async createCTCProduct(): Promise<string> {
    try {
      const date = this.convertToHubspotDate(new Date());

      const newProduct = await this.hubspotClient.crm.objects.basicApi.create(HubspotObject.CTCProduct, {
        properties: {
          ctcproduct_name: CTC_PRODUCT_NAME,
          registration_date: date,
          validated: ProductValidationStatus.False,
        },
        associations: [],
      });

      return newProduct.id;
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while creating Hubspot CTC product';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async updateCTCProduct(id: string, data: Partial<CTCProduct>): Promise<void> {
    try {
      await this.hubspotClient.crm.objects.basicApi.update(HubspotObject.CTCProduct, id, {
        properties: { ...data },
      });
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while updating Hubspot CTC product';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async createProgram(data: Omit<Program, 'id'>): Promise<string> {
    try {
      const newProgram = await this.hubspotClient.crm.objects.basicApi.create(HubspotObject.Program, {
        properties: {
          ...data,
        },
        associations: [],
      });

      return newProgram.id;
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while creating Hubspot program';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public async getProgram(id: string): Promise<Program | null> {
    try {
      const requestProperties = [
        'program_name',
        'ctcproduct_name',
        'program_progress',
        'amount_earned',
        'submission_count',
      ];
      const response = await this.hubspotClient.crm.objects.basicApi.getById(
        HubspotObject.Program,
        id,
        requestProperties,
      );

      return { id: response.id, ...response.properties } as unknown as Program;
    } catch (error) {
      const err = error as HubspotError;
      if (err.code === 404) {
        return null;
      }

      const msg = err.body.message ? err.body.message : 'Error while getting Hubspot program';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public async updateProgram(id: string, data: Partial<Program>): Promise<void> {
    try {
      await this.hubspotClient.crm.objects.basicApi.update(HubspotObject.Program, id, {
        properties: { ...data },
      });
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while updating Hubspot program';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public async archiveProgram(id: string): Promise<void> {
    try {
      await this.hubspotClient.crm.objects.basicApi.archive(HubspotObject.Program, id);
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while archiving Hubspot program';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async createTicket(data: Partial<Ticket>): Promise<string> {
    try {
      const newTicket = await this.hubspotClient.crm.tickets.basicApi.create({
        properties: {
          ...data,
          createdate: getTime(new Date()).toString(),
          hs_pipeline_stage: TicketStatus.New,
        },
        associations: [],
      });

      return newTicket.id;
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while creating Hubspot ticket';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public async syncUserRegistration(data: Partial<HubspotContact>): Promise<SyncUserRegistrationResponse> {
    const contact = await this.getContact(data.email!);
    let contactId = contact ? contact.id : '';

    if (!contact) {
      contactId = await this.createContact(data);
    } else {
      await this.updateContact(contact.id, {
        firstname: data.firstname,
        lastname: data.lastname,
        phone: data.phone,
        hs_language: data.hs_language,
        verification_status: data.verification_status ? data.verification_status : contact.verification_status,
        industrii_email_opt_in: data.industrii_email_opt_in,
        industrii_contact_status: data.industrii_contact_status,
        industrii_complete_ws: data.industrii_complete_ws,
      });
    }

    const ctcProductId = await this.associateContactWithCTCProduct(contactId);
    await this.updateCTCProduct(ctcProductId, {
      last_login: this.convertToHubspotDate(new Date()),
    });

    return {
      contactId,
      ctcProductId,
    };
  }

  public async syncPreVerifiedUser(
    data: Partial<PreVerifiedContact>,
    signUpLink: string,
  ): Promise<SyncUserRegistrationResponse> {
    const {
      email,
      firstName,
      lastName,
      verification_status,
      practice_setting,
      employment_status,
      hcp_specialty,
      industrii_contact_status,
      industrii_complete_ws,
    } = data;

    const contact = await this.getContact(email!);
    let contactId = contact ? contact.id : '';

    if (!contact) {
      contactId = await this.createContact({
        email,
        firstname: firstName,
        lastname: lastName,
        industrii_branch_link: signUpLink,
        practice_setting,
        employment_status,
        hcp_specialty,
        verification_status,
        industrii_contact_status,
        industrii_complete_ws,
      });
    } else {
      await this.updateContact(contact.id, {
        firstname: firstName,
        lastname: lastName,
        industrii_branch_link: signUpLink,
        practice_setting,
        employment_status,
        hcp_specialty,
        verification_status,
        industrii_contact_status,
        industrii_complete_ws,
      });
    }

    const ctcProductId = await this.associateContactWithCTCProduct(contactId);

    if (verification_status === HCPVerificationStatus.Verified) {
      await this.updateCTCProduct(ctcProductId, {
        validated: ProductValidationStatus.True,
      });
    }

    return {
      contactId,
      ctcProductId,
    };
  }

  // https://developers.hubspot.com/docs/api/crm/associations#associate-records-with-a-label
  private async getAssociationWithLabel(
    objectType: string,
    toObjectType: string,
    category: AssociationSpecWithLabelCategoryEnum,
    isRequiredLabel = false,
  ): Promise<AssociationSpecWithLabel | null> {
    try {
      const response = await this.hubspotClient.crm.associations.v4.schema.definitionsApi.getAll(
        objectType,
        toObjectType,
      );

      const data = response.results;

      return isRequiredLabel
        ? data.find(item => item.category === category && item.label !== null) || null
        : data.find(item => item.category === category) || null;
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while getting Hubspot association with label';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async associateObjects(data: AssociationPayload): Promise<void> {
    const { objectType, objectId, toObjectType, toObjectId, associationSpec } = data;

    try {
      await this.hubspotClient.crm.associations.v4.basicApi.create(objectType, objectId, toObjectType, toObjectId, [
        associationSpec,
      ]);
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while associating Hubspot objects';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public convertToHubspotDate(date: Date): string {
    return getTime(startOfDay(date)).toString();
  }

  public async syncVerificationRequest(
    contactId: string,
    ctcProductId: string,
    data: Partial<HubspotContact>,
    updateLoginDate = true,
  ): Promise<void> {
    const date = this.convertToHubspotDate(new Date());

    await Promise.all([
      this.updateContact(contactId, {
        ...data,
        hcp_specialty: data.hcp_specialty!,
      }),
      updateLoginDate
        ? this.updateCTCProduct(ctcProductId, {
            last_login: date,
          })
        : null,
    ]);
  }

  private async getObjectProperty(objectType: HubspotObjectType, property: string): Promise<Property> {
    try {
      const data = await this.hubspotClient.crm.properties.coreApi.getByName(objectType, property);

      return data;
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while getting Hubspot object property';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  private async addOptionsToProperty(
    objectType: HubspotObjectType,
    property: string,
    options: OptionInput[],
  ): Promise<void> {
    try {
      await this.hubspotClient.crm.properties.coreApi.update(objectType, property, {
        options,
      });
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while adding options to Hubspot object property';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public async syncVerificationComplete(
    contactId: string,
    ctcProductId: string,
    verificationStatus: VerificationStatus,
    contactStatus: ContactStatus,
    isCompleteWS: boolean,
    note?: string | null,
  ): Promise<void> {
    await Promise.all([
      this.updateContact(contactId, {
        verification_status: this.mapVerificationStatusToHubspot(verificationStatus, isCompleteWS),
        industrii_note: verificationStatus === VerificationStatus.Denied ? (note ? note : '') : '',
        industrii_contact_status: this.mapContactStatusToHubspot(contactStatus),
      }),
      this.updateCTCProduct(ctcProductId, {
        validated:
          verificationStatus === VerificationStatus.Verified
            ? ProductValidationStatus.True
            : ProductValidationStatus.False,
      }),
    ]);
  }

  public async syncExistingSurveyCompletion(programId: string, data: Omit<Program, 'id'>): Promise<void> {
    const program = await this.getProgram(programId);

    if (!program) {
      throw customHttpError(400, 'Cannot find program', ErrorCode.HubspotError);
    }

    await this.updateProgram(programId, {
      ...data,
      amount_earned: (parseFloat(program.amount_earned) + parseFloat(data.amount_earned)).toString(),
    });
  }

  public async syncNewSurveyCompletion(
    contactId: string,
    ctcProductId: string,
    data: Omit<Program, 'id'>,
  ): Promise<string> {
    const programId = await this.createProgram(data);

    const [contactProgramAssociation, productProgramAssociation] = await Promise.all([
      this.getAssociationWithLabel(
        HubspotObject.Contact,
        HubspotObject.Program,
        AssociationSpecWithLabelCategoryEnum.UserDefined,
        true,
      ),
      this.getAssociationWithLabel(
        HubspotObject.CTCProduct,
        HubspotObject.Program,
        AssociationSpecWithLabelCategoryEnum.UserDefined,
      ),
    ]);

    if (!contactProgramAssociation || !productProgramAssociation) {
      throw customHttpError(400, 'Cannot find association with label', ErrorCode.HubspotError);
    }

    await Promise.all([
      this.associateObjects({
        objectType: HubspotObject.Contact,
        objectId: contactId,
        toObjectType: HubspotObject.Program,
        toObjectId: programId,
        associationSpec: {
          associationCategory: contactProgramAssociation.category as unknown as AssociationSpecAssociationCategoryEnum,
          associationTypeId: contactProgramAssociation.typeId,
        },
      }),
      this.associateObjects({
        objectType: HubspotObject.CTCProduct,
        objectId: ctcProductId,
        toObjectType: HubspotObject.Program,
        toObjectId: programId,
        associationSpec: {
          associationCategory: productProgramAssociation.category as unknown as AssociationSpecAssociationCategoryEnum,
          associationTypeId: productProgramAssociation.typeId,
        },
      }),
    ]);

    const date = this.convertToHubspotDate(new Date());
    await this.updateCTCProduct(ctcProductId, {
      last_login: date,
    });

    return programId;
  }

  public async syncSupportRequest(contactId: string, ctcProductId: string, data: Partial<Ticket>): Promise<string> {
    const ticketId = await this.createTicket(data);

    const association = await this.getAssociationWithLabel(
      HubspotObject.Contact,
      HubspotObject.Ticket,
      AssociationSpecWithLabelCategoryEnum.HubspotDefined,
    );

    if (!association) {
      throw customHttpError(400, 'Cannot find association with label', ErrorCode.HubspotError);
    }

    await this.associateObjects({
      objectType: HubspotObject.Contact,
      objectId: contactId,
      toObjectType: HubspotObject.Ticket,
      toObjectId: ticketId,
      associationSpec: {
        associationCategory: association.category as unknown as AssociationSpecAssociationCategoryEnum,
        associationTypeId: association.typeId,
      },
    });

    const date = this.convertToHubspotDate(new Date());
    await this.updateCTCProduct(ctcProductId, {
      last_login: date,
    });

    return ticketId;
  }

  public verifySignature(requestBody: string, signature: string, signatureVersion: string): void {
    const isValid = Signature.isValid({
      signature,
      clientSecret: config.HUBSPOT_CLIENT_SECRET,
      requestBody,
      signatureVersion,
      url: config.HUBSPOT_WEBHOOK_URL,
      method: 'POST',
    });

    if (!isValid) {
      throw customHttpError(400, 'Invalid signature', ErrorCode.BadRequest);
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public async processWebhook(data: any): Promise<void> {
    const eventType = data.subscriptionType;

    switch (eventType) {
      case HubspotEventType.TicketWorkflowStatusChanged: {
        await this.syncTicketClosed(data as TicketWorkflowStatusChanged);
        break;
      }

      case HubspotEventType.ContactUnsubscribeAllEmail: {
        await this.syncEmailOptIn(data as ContactManageCommunicationPreferences);
        break;
      }

      case HubspotEventType.ContactSubscribeAnEmail: {
        await this.syncEmailOptIn(data as ContactManageCommunicationPreferences);
        break;
      }

      default:
        break;
    }
  }

  private async syncTicketClosed(data: TicketWorkflowStatusChanged): Promise<void> {
    await this.supportRequestRepository.update(
      {
        hubspotTicketId: data.hs_object_id.toString(),
      },
      {
        status: SupportRequestStatus.Closed,
      },
    );
  }

  private async syncEmailOptIn(data: ContactManageCommunicationPreferences): Promise<void> {
    const { email, hs_object_id, industrii_email_opt_in } = data;

    if (industrii_email_opt_in === EmailOptIn.False) {
      const [user, contact] = await Promise.all([
        this.userRepository.findOneBy({
          email,
          hubspotContactId: hs_object_id.toString(),
        }),
        this.getContact(email),
      ]);

      if (user && contact) {
        if (user.isEmailOptIn && contact.industrii_email_opt_in === EmailOptIn.False) {
          await this.userRepository.update(
            {
              email,
              hubspotContactId: hs_object_id.toString(),
            },
            {
              isEmailOptIn: false,
            },
          );
        }
      }
    } else {
      await this.userRepository.update(
        {
          email,
          hubspotContactId: hs_object_id.toString(),
        },
        {
          isEmailOptIn: true,
        },
      );
    }
  }

  public getTicketUrl(ticketId: string): string {
    return `https://app.hubspot.com/contacts/${config.HUBSPOT_PORTAL_ID}/record/0-5/${ticketId}`;
  }

  public async associateContactWithCTCProduct(contactId: string): Promise<string> {
    const [response, association] = await Promise.all([
      this.hubspotClient.crm.associations.v4.basicApi.getPage(
        HubspotObject.Contact,
        contactId,
        HubspotObject.CTCProduct,
      ),
      this.getAssociationWithLabel(
        HubspotObject.Contact,
        HubspotObject.CTCProduct,
        AssociationSpecWithLabelCategoryEnum.UserDefined,
        true,
      ),
    ]);

    if (!association) {
      throw customHttpError(400, 'Cannot find association with label', ErrorCode.HubspotError);
    }

    if (response.results.length > 0) {
      // Get existing CTC product
      const ctcProduct = this.getAssociatedObjectByTypeId(association.typeId, response.results);

      if (ctcProduct) {
        const productId = ctcProduct.toObjectId;
        return productId;
      }
    }

    const ctcProductId = await this.createCTCProduct();
    await this.associateObjects({
      objectType: HubspotObject.Contact,
      objectId: contactId,
      toObjectType: HubspotObject.CTCProduct,
      toObjectId: ctcProductId,
      associationSpec: {
        associationCategory: association.category as unknown as AssociationSpecAssociationCategoryEnum,
        associationTypeId: association.typeId,
      },
    });

    return ctcProductId;
  }

  private getAssociatedObjectByTypeId(
    typeId: number,
    associations: MultiAssociatedObjectWithLabel[],
  ): MultiAssociatedObjectWithLabel | undefined {
    const record = associations.find(item => {
      return item.associationTypes.some(associationType => {
        return associationType.typeId === typeId;
      });
    });

    return record;
  }

  public async updateOnFirstWithdrawal(contactId: string): Promise<void> {
    try {
      const contact = await this.hubspotClient.crm.contacts.basicApi.getById(contactId, ['industrii_withdraw']);

      if (contact && !contact.properties.industrii_withdraw) {
        await this.updateContact(contactId, {
          industrii_withdraw: 'Yes',
        });
      }
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while updating Hubspot contact on first withdrawal';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }

  public async associateProgramWithCTCProduct(programId: string, ctcProductId: string): Promise<void> {
    const [response, association] = await Promise.all([
      this.hubspotClient.crm.associations.v4.basicApi.getPage(
        HubspotObject.Program,
        programId,
        HubspotObject.CTCProduct,
      ),
      this.getAssociationWithLabel(
        HubspotObject.Program,
        HubspotObject.CTCProduct,
        AssociationSpecWithLabelCategoryEnum.UserDefined,
      ),
    ]);

    if (!association) {
      throw customHttpError(400, 'Cannot find association with label', ErrorCode.HubspotError);
    }

    if (response.results.length > 0) {
      // Get existing CTC product
      const ctcProduct = this.getAssociatedObjectByTypeId(association.typeId, response.results);
      if (ctcProduct) {
        return;
      }
    }

    await this.associateObjects({
      objectType: HubspotObject.Program,
      objectId: programId,
      toObjectType: HubspotObject.CTCProduct,
      toObjectId: ctcProductId,
      associationSpec: {
        associationCategory: association.category as unknown as AssociationSpecAssociationCategoryEnum,
        associationTypeId: association.typeId,
      },
    });

    await this.updateCTCProduct(ctcProductId, {
      last_login: this.convertToHubspotDate(new Date()),
    });
  }

  public async updateLastLoginDate(ctcProductId: string, date?: Date): Promise<void> {
    await this.updateCTCProduct(ctcProductId, {
      last_login: date ? this.convertToHubspotDate(date) : this.convertToHubspotDate(new Date()),
    });
  }

  public mapContactStatusToHubspot(contactStatus: ContactStatus): HubspotContactStatus {
    switch (contactStatus) {
      case ContactStatus.Complete:
        return HubspotContactStatus.Complete;
      case ContactStatus.ReviewInfo:
        return HubspotContactStatus.ReviewInfo;
      case ContactStatus.WaitForInfo:
        return HubspotContactStatus.WaitForInfo;
      default:
        return HubspotContactStatus.WSPending;
    }
  }

  public mapVerificationStatusToHubspot(
    verificationStatus: VerificationStatus,
    isCompleteWS = true,
  ): HCPVerificationStatus {
    switch (verificationStatus) {
      case VerificationStatus.Verified:
        return HCPVerificationStatus.Verified;
      case VerificationStatus.Denied:
        return HCPVerificationStatus.Rejected;
      default:
        return isCompleteWS ? HCPVerificationStatus.InReview : HCPVerificationStatus.NotStarted;
    }
  }

  public async syncPublicSurveyUser(data: Partial<HubspotContact>): Promise<SyncUserRegistrationResponse> {
    const contact = await this.getContact(data.email!);
    let contactId = contact ? contact.id : '';

    if (!contact) {
      contactId = await this.createContact(data);
    }

    const ctcProductId = await this.associateContactWithCTCProduct(contactId);

    return {
      contactId,
      ctcProductId,
    };
  }

  public async updateEmailSubscription(email: string, statusState: StatusState): Promise<void> {
    try {
      const channel = 'EMAIL';
      const legalBasisExplanation = 'N/A';

      const inputs = Object.entries(HubspotSubscriptionType).map(([_key, value]) => ({
        subscriptionId: value,
        channel,
        statusState,
        subscriberIdString: email,
        legalBasis: PublicUpdateSubscriptionStatusRequestLegalBasisEnum.LegitimateInterestOther,
        legalBasisExplanation,
      }));

      await this.hubspotClient.apiRequest({
        method: 'POST',
        path: '/communication-preferences/v4/statuses/batch/write',
        body: {
          inputs,
        },
      });
    } catch (error) {
      const err = error as HubspotError;
      const msg = err.body.message ? err.body.message : 'Error while updating Hubspot email subscription status';
      this.log.warn(err.body, msg);
      throw customHttpError(400, msg, ErrorCode.HubspotError);
    }
  }
}
