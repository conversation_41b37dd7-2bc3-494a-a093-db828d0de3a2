import { EmailOptIn } from '@/backend/hubspot/types/contact';

export enum HubspotEventType {
  TicketWorkflowStatusChanged = 'ticket.workflow.statusChanged',
  ContactUnsubscribeAllEmail = 'contact.workflow.unsubscribeAllEmail',
  ContactSubscribeAnEmail = 'contact.workflow.subscribeAnEmail',
}

export interface TicketWorkflowStatusChanged {
  subscriptionType: HubspotEventType;
  hs_object_id: number;
  hs_pipeline_stage: number;
}

export interface ContactManageCommunicationPreferences {
  subscriptionType: HubspotEventType;
  email: string;
  hs_object_id: number;
  industrii_email_opt_in: EmailOptIn;
}
