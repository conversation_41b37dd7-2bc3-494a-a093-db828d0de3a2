import config from '@/config';

/**
 * HubSpot subscription type mapping
 * Maps subscription type names to their corresponding HubSpot opt-out field IDs
 */
export const HubspotSubscriptionType = {
  CTC_PRODUCTS_SERVICES: config.HUBSPOT_CTC_PRODUCTS_SERVICES_SUBSCRIPTION_ID,
  MARKETING_INFORMATION: config.HUBSPOT_MARKETING_INFORMATION_SUBSCRIPTION_ID,
  EDUCATIONAL_PROGRAM: config.HUBSPOT_EDUCATIONAL_PROGRAM_SUBSCRIPTION_ID,
  PROGRAM_NOTIFICATIONS: config.HUBSPOT_PROGRAM_NOTIFICATIONS_SUBSCRIPTION_ID,
  CUSTOMER_SERVICE: config.HUBSPOT_CUSTOMER_SERVICE_SUBSCRIPTION_ID,
  ONE_TO_ONE: config.HUBSPOT_ONE_TO_ONE_SUBSCRIPTION_ID,
} as const;

/**
 * Type for HubSpot subscription field names
 */
export type HubspotSubscriptionField = `hs_email_optout_${string}`;

/**
 * Utility function to generate HubSpot opt-out field name
 * @param subscriptionId - The subscription type ID
 * @returns The formatted HubSpot field name
 */
export const getHubspotOptoutField = (subscriptionId: string): HubspotSubscriptionField => {
  return `hs_email_optout_${subscriptionId}`;
};

export enum StatusState {
  Subscribed = 'SUBSCRIBED',
  Unsubscribed = 'UNSUBSCRIBED',
}
