import { HubspotSubscriptionField } from '@/backend/hubspot/types/subscription';

export enum HCPVerificationStatus {
  NotStarted = 'Not started',
  InReview = 'In review',
  Rejected = 'Rejected',
  Verified = 'Verified',
}

export enum PreferredLanguage {
  EnglishCanada = 'en-ca',
  FrenchCanada = 'fr-ca',
}

export enum EmailOptIn {
  True = 'True',
  False = 'False',
}

export enum HubspotContactStatus {
  WSPending = 'WS Pending',
  WaitForInfo = 'Wait For Info',
  ReviewInfo = 'Review Info',
  Complete = 'Complete',
}

export enum CompleteWS {
  True = 'True',
  False = 'False',
}

export interface HubspotContact {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  practice_setting: string;
  employment_status: string;
  hcp_registration_number: string;
  hcp_registration_province: string;
  hs_language: PreferredLanguage;
  hcp_specialty: string;
  verification_status: HCPVerificationStatus;
  industrii_branch_link: string;
  industrii_withdraw: string;
  industrii_survey_link: string; // New survey available
  industrii_referral_bonus_link: string; // Referral bonus when referred user is verified
  industrii_user_verification_link: string; // User is verified
  industrii_email_opt_in: EmailOptIn;
  industrii_contact_status: HubspotContactStatus;
  industrii_complete_ws: CompleteWS;
  industrii_note: string;
  industrii_last_sign_up: string;
}

export type HubspotContactWithSubscriptions = HubspotContact & { [K in HubspotSubscriptionField]?: string | null };

export interface PreVerifiedContact {
  email: string;
  firstName: string;
  lastName: string;
  verification_status: HCPVerificationStatus;
  practice_setting: string;
  employment_status: string;
  hcp_specialty: string;
  industrii_contact_status: HubspotContactStatus;
  industrii_complete_ws: CompleteWS;
}
