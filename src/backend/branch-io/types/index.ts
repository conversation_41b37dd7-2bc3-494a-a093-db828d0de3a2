import { NotificationType } from '@/backend/notifications/services';

// Don't know why I used both camelCase and snake_case in the same file. But from now on, I will use camelCase.
export interface DeepLinkData {
  custom_data: {
    type?: NotificationType;
    surveyId?: string;
    amount?: string;
    referredUserId?: string;
    isPreVerified?: boolean;
    email?: string;
    first_name?: string;
    last_name?: string;
    referral_code?: string;
  };
}
