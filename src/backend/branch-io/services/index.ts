import { DeepLinkData } from '@/backend/branch-io/types';
import { badRequestError } from '@/backend/middlewares/capture-errors';
import { logger } from '@/backend/shared/utils/logger';
import config from '@/config';
import axios from 'axios';
import { Logger } from 'pino';

export class BranchIOService {
  private readonly apiUrl: string;
  private readonly branchKey: string;
  private readonly log: Logger;

  constructor() {
    this.apiUrl = config.BRANCH_API_URL;
    this.branchKey = config.BRANCH_KEY;
    this.log = logger.child({ description: 'BranchIOService' });
  }

  public async createReferralDeepLink(data: DeepLinkData): Promise<string> {
    try {
      const alias = data.custom_data.referral_code ?? undefined;
      return await this.createDeepLink({ ...data, $marketing_title: 'Link Referral' }, alias);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const originalMsg = error.response?.data?.error?.message as string;

      if (originalMsg && originalMsg.includes('Link alias conflict')) {
        return `${config.BRANCH_DOMAIN}/${data.custom_data.referral_code}`;
      }

      const msg = originalMsg ? originalMsg : 'Failed to generate referral deep link';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }
  }

  public async createSignUpDeepLink(data: DeepLinkData): Promise<string> {
    try {
      return await this.createDeepLink({ ...data, $marketing_title: 'Pre-verified User' });
    } catch (error) {
      const msg = 'Failed to generate sign up deep link';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }
  }

  public async createSurveyDeepLink(data: DeepLinkData): Promise<string> {
    try {
      const link = `${await this.createDeepLink({ ...data, $marketing_title: 'Survey' })}?screen=surveys&id=${data.custom_data.surveyId}`;
      return link;
    } catch (error) {
      const msg = 'Failed to generate survey deep link';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }
  }

  public async createVerificationDeepLink(data: DeepLinkData): Promise<string> {
    try {
      const link = `${await this.createDeepLink({ ...data, $marketing_title: 'Verification' })}?screen=surveys`;
      return link;
    } catch (error) {
      const msg = 'Failed to generate verification deep link';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }
  }

  public async createReferralBonusDeepLink(data: DeepLinkData): Promise<string> {
    try {
      const link = `${await this.createDeepLink({ ...data, $marketing_title: 'Referral Bonus' })}?screen=wallet`;
      return link;
    } catch (error) {
      const msg = 'Failed to generate referral bonus deep link';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }
  }

  private async createDeepLink(data: object, alias?: string): Promise<string> {
    const options = {
      method: 'POST',
      url: `${this.apiUrl}/url`,
      headers: { accept: 'application/json', 'content-type': 'application/json' },
      data: {
        branch_key: this.branchKey,
        type: 0,
        alias,
        data: {
          ...data,
        },
      },
    };

    const response = await axios.request(options);
    return response.data.url;
  }
}
