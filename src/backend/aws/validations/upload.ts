import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { MessagesApi } from '@/backend/shared/common/messages';
import { ZOD_VALUE } from '@/utils/zod-value';
import { NextRequest } from 'next/server';
import { z } from 'zod';

export const validateImgFormData = async (req: NextRequest): Promise<[Blob, string]> => {
  const ALLOWED_FILE_TYPES = ['image/png', 'image/jpeg'];
  const ALLOWED_UPLOAD_TYPES = ['surveys'];
  const MAX_FILE_SIZE = 3500000; // 3.5 MB in bytes

  const formData = await req.formData();
  const file = formData.get('file');
  const type = String(formData.get('type'));

  if (!file || !(file instanceof Blob)) {
    throw customHttpError(400, MessagesApi.IMG_REQUIRED, ErrorCode.BadRequest);
  }

  if (!type || !ALLOWED_UPLOAD_TYPES.includes(type)) {
    throw customHttpError(400, MessagesApi.TYPE_REQUIRED, ErrorCode.BadRequest);
  }

  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    throw customHttpError(400, MessagesApi.TYPE_FILE_REQUIRED, ErrorCode.BadRequest);
  }

  if (file.size >= MAX_FILE_SIZE) {
    throw customHttpError(400, MessagesApi.MAX_FILE_SIZE, ErrorCode.BadRequest);
  }

  return [file, type];
};

export const deleteImgSchema = z.object({
  url: ZOD_VALUE.URL,
});

export type DeleteImgPayload = z.infer<typeof deleteImgSchema>;
