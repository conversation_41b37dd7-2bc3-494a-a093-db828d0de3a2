import config from '@/config';
import {
  BaseAddressResponse,
  LocationResult,
  RetrievePayload,
  SearchPayload,
  SearchResult,
} from '@/backend/canadapost/validations/canadapost';

export class CanadaPostService {
  private readonly url;
  private readonly apiKey = config.CANADAPOST_API_KEY;
  private readonly baseParams: Record<string, unknown>;

  constructor(retrieve?: boolean) {
    this.baseParams = {
      Key: this.apiKey,
      Country: 'CAN',
      LanguagePreference: 'en',
      SearchFor: 'Everything',
      OrderBy: 'UserLocation',
      $cache: true,
      $block: true,
    };
    this.url = 'https://ws1.postescanada-canadapost.ca/AddressComplete/Interactive/Find/v2.10/json3ex.ws';
    if (retrieve) {
      this.url =
        'https://ws1.postescanada-canadapost.ca/AddressComplete/Interactive/RetrieveFormatted/v2.10/json3ex.ws';
    }
  }

  private generateQueryParams(params: Record<string, unknown>) {
    if (Object.keys(params).length === 0) {
      return '';
    }
    const queryParams = new URLSearchParams();
    for (const key in params) {
      const value = params[key];
      if (Array.isArray(value)) {
        for (const item of value) {
          queryParams.append(key, item);
        }
      } else {
        queryParams.append(key, value as string);
      }
    }

    return queryParams.toString();
  }

  async searchAddress(searchPayload: SearchPayload): Promise<BaseAddressResponse<SearchResult>> {
    const url = `${this.url}?${this.generateQueryParams({ ...this.baseParams, ...searchPayload })}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.json();
  }

  async retrieveAddress(addressPayload: RetrievePayload): Promise<BaseAddressResponse<LocationResult>> {
    const url = `${this.url}?${this.generateQueryParams({ ...this.baseParams, ...addressPayload })}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.json();
  }
}
