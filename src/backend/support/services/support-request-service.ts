import { dataSource } from '@/backend/database/data-source';
import { HubspotService } from '@/backend/hubspot/services/hubspot-service';
import { SupportRequest, SupportRequestStatus } from '@/backend/support/entities/SupportRequest';
import { SupportRequestRepository } from '@/backend/support/repositories/support-request-repository';
import { SubmitSupportPayload } from '@/backend/support/validations/submit-support';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';

export class SupportRequestService {
  private userRepository: UserRepository;
  private supportRequestRepository: SupportRequestRepository;
  private hubspotService: HubspotService;

  constructor() {
    this.userRepository = new UserRepository(dataSource);
    this.supportRequestRepository = new SupportRequestRepository(dataSource);
    this.hubspotService = new HubspotService();
  }

  public async listSupports(data: PaginationPayload<SupportRequest>): Promise<PaginationResponseData<SupportRequest>> {
    const { page = 1, pageSize = 10, sortBy, sortOrder, search } = data;
    const queryBuilder = this.supportRequestRepository.createQueryBuilder('support');

    const sortableFields: Array<keyof SupportRequest> = ['id', 'email', 'description', 'createdAt', 'status'];
    queryBuilder.select([...sortableFields.map(field => `support.${field}`), 'support.hubspotTicketUrl']);

    if (search) {
      queryBuilder.where(
        'support.title ILIKE :search OR support.description ILIKE :search OR support.email ILIKE :search',
        {
          search: `%${search.toLowerCase()}%`,
        },
      );
    }

    if (sortBy && sortableFields.includes(sortBy as keyof SupportRequest)) {
      if (sortBy !== 'status') {
        queryBuilder.orderBy('support.status', 'ASC');
      }
      queryBuilder.addOrderBy(`support.${sortBy}`, sortOrder);
    } else {
      queryBuilder.orderBy('support.status', 'DESC');
    }

    return await withPagination(queryBuilder, page, pageSize);
  }

  public async submit(userId: number, data: SubmitSupportPayload): Promise<void> {
    const title = 'Industrii Support Request';
    const user = await this.userRepository.findOneById(userId);

    const hubspotTicketId = await this.hubspotService.syncSupportRequest(
      user.hubspotContactId!,
      user.hubspotProductId!,
      {
        subject: title,
        content: data.message,
      },
    );

    const hubspotTicketUrl = this.hubspotService.getTicketUrl(hubspotTicketId);

    await this.supportRequestRepository.save({
      userId,
      email: user.email,
      title,
      description: data.message,
      status: SupportRequestStatus.Open,
      hubspotTicketId,
      hubspotTicketUrl,
    });
  }

  public async requestAccountDelete(userId: number): Promise<void> {
    const title = 'Industrii Delete Account Request';
    const description = 'User requested to delete account';
    const user = await this.userRepository.findOneById(userId);

    const hubspotTicketId = await this.hubspotService.syncSupportRequest(
      user.hubspotContactId!,
      user.hubspotProductId!,
      {
        subject: title,
        content: description,
      },
    );

    const hubspotTicketUrl = this.hubspotService.getTicketUrl(hubspotTicketId);

    await this.supportRequestRepository.save({
      userId,
      email: user.email,
      title,
      description,
      status: SupportRequestStatus.Open,
      hubspotTicketId,
      hubspotTicketUrl,
    });
  }
}
