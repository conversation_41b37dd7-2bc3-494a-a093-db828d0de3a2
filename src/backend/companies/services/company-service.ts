import { dataSource } from '@/backend/database/data-source';
import { SurveyStatus } from '@/backend/surveys/entities/Survey';
import { SurveyRepository } from '@/backend/surveys/repositories/survey-repository';
import { PaginationPayload, PaginationResponseData } from '@/types/pagination';
import { Company } from '../entities/Company';
import { CompanyRepository } from '../repositories/company-repository';

export class CompanyService {
  private readonly companyRepository = new CompanyRepository(dataSource);
  private readonly surveyRepository = new SurveyRepository(dataSource);

  constructor() {}

  public async create(name: string): Promise<void> {
    return this.companyRepository.createCompany(name);
  }

  public async update(id: number, name: string): Promise<Company> {
    return this.companyRepository.updateCompany(id, name);
  }

  public async delete(id: number): Promise<void> {
    return this.companyRepository.deleteCompany(id);
  }

  public async checkNameExist(name: string): Promise<boolean> {
    return this.companyRepository.checkNameExist(name);
  }

  public async listCompanies(data: PaginationPayload<Company>): Promise<PaginationResponseData<Company>> {
    return this.companyRepository.listCompanies(data);
  }

  public async get(id: number): Promise<Company> {
    return this.companyRepository.getCompanyById(id);
  }

  public async syncStats(companyId: number | null): Promise<void> {
    if (!companyId) {
      return;
    }

    const [surveysInProgress, surveysCompleted] = await Promise.all([
      this.surveyRepository.count({
        where: {
          companyId,
          status: SurveyStatus.Active,
        },
      }),
      this.surveyRepository.count({
        where: {
          companyId,
          status: SurveyStatus.Expired,
        },
      }),
    ]);

    await this.companyRepository.update(
      {
        id: companyId,
      },
      {
        surveysInProgress,
        surveysCompleted,
      },
    );
  }
}
