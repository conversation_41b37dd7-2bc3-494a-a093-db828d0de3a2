import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { COMPANY_SORTABLE, NOT_DELETED } from '@/backend/shared/common/keyof';
import { MessagesApi } from '@/backend/shared/common/messages';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';
import { DataSource, Not, Repository } from 'typeorm';
import { Company } from '../entities/Company';

export class CompanyRepository extends Repository<Company> {
  constructor(private dataSource: DataSource) {
    super(Company, dataSource.createEntityManager());
  }

  public async createCompany(name: string): Promise<void> {
    const companies = await this.findOne({ where: { name, ...NOT_DELETED } });
    if (companies) {
      throw customHttpError(400, MessagesApi.COMPANY_EXISTS, ErrorCode.BadRequest);
    }
    const newCompany = this.create({ name });
    await this.save(newCompany);
    return;
  }

  public async getCompanyById(id: number): Promise<Company> {
    const company = await this.findOneBy({ id });
    if (!company || company.deletedAt) {
      throw customHttpError(400, MessagesApi.COMPANY_VALID, ErrorCode.BadRequest);
    }
    return company;
  }

  public async updateCompany(id: number, name: string): Promise<Company> {
    const company = await this.getCompanyById(id);
    const companySameName = await this.findOne({ where: { name, ...NOT_DELETED, id: Not(id) } });
    if (companySameName) {
      throw customHttpError(400, MessagesApi.COMPANY_EXISTS, ErrorCode.BadRequest);
    }
    company.name = name;
    return this.save(company);
  }

  public async deleteCompany(id: number): Promise<void> {
    const company = await this.getCompanyById(id);
    company.deletedAt = new Date();
    await this.save(company);
  }

  public async listCompanies(data: PaginationPayload<Company>): Promise<PaginationResponseData<Company>> {
    const { page = 1, pageSize = 10, sortBy, sortOrder } = data;
    const queryBuilder = this.createQueryBuilder('company');

    queryBuilder.where('company.deletedAt IS NULL');

    queryBuilder.select(COMPANY_SORTABLE.map(key => `company.${key}`));

    if (sortBy && COMPANY_SORTABLE.includes(sortBy as keyof Company)) {
      queryBuilder.orderBy(`company.${sortBy}`, sortOrder);
    }

    return await withPagination(queryBuilder, page, pageSize);
  }

  public async checkNameExist(name: string): Promise<boolean> {
    const companies = await this.findOneBy({ name, ...NOT_DELETED });
    if (companies) return true;
    return false;
  }
}
