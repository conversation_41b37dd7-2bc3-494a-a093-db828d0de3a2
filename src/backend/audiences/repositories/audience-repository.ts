import { Audience } from '@/backend/audiences/entities/Audience';
import { ErrorCode, badRequestError, customHttpError, notFoundError } from '@/backend/middlewares/capture-errors';
import { AUDIENCE_FIELDS, AUDIENCE_SORTABLE, NOT_DELETED } from '@/backend/shared/common/keyof';
import { MessagesApi } from '@/backend/shared/common/messages';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { Survey } from '@/backend/surveys/entities/Survey';
import { SurveyAudience } from '@/backend/surveys/entities/SurveyAudience';
import { User, VerificationStatus } from '@/backend/users/entities/User';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';
import { Brackets, DataSource, InsertResult, IsNull, Not, Repository, SelectQueryBuilder } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { CreateAudiencePayload, UpdateAudiencePayload, UsersIncludedPayload } from '../validations/add-update-audience';

export class AudienceRepository extends Repository<Audience> {
  private audienceRepository: Repository<Audience>;
  private userRepository: Repository<User>;
  private surveyAudienceRepository: Repository<SurveyAudience>;

  constructor(private dataSource: DataSource) {
    super(Audience, dataSource.createEntityManager());
    this.audienceRepository = dataSource.getRepository(Audience);
    this.userRepository = dataSource.getRepository(User);
    this.surveyAudienceRepository = dataSource.getRepository(SurveyAudience);
  }

  public async createAudience(data: CreateAudiencePayload): Promise<Audience> {
    const audience = await this.audienceRepository.findOne({ where: { name: data.name, ...NOT_DELETED } });
    if (audience) {
      throw customHttpError(400, MessagesApi.AUDIENCE_EXISTS, ErrorCode.BadRequest);
    }
    const newAudience = new Audience();
    Object.assign(newAudience, { ...data, numberOfUsers: data.numberOfUsers ?? 0 });
    await this.audienceRepository.createQueryBuilder().insert().values(newAudience).execute();
    return newAudience;
  }

  public async listAudiences(data: PaginationPayload<Audience>): Promise<PaginationResponseData<Audience>> {
    const { page = 1, pageSize = 10, sortBy, sortOrder, search } = data;
    const queryBuilder = this.audienceRepository.createQueryBuilder('audiences');
    queryBuilder.where('audiences.deletedAt IS NULL');
    queryBuilder.andWhere('audiences.isVisible = TRUE');
    queryBuilder.leftJoinAndMapMany(
      'audiences.specialties',
      Specialty,
      'specialty',
      'specialty.id = ANY(audiences.specialtyIds)',
    );

    queryBuilder.leftJoinAndMapMany(
      'audiences.surveys',
      Survey,
      'survey',
      'survey.id = ANY(audiences.completedSurveys)',
    );

    if (sortBy && AUDIENCE_SORTABLE.includes(sortBy as keyof Audience)) {
      queryBuilder.orderBy(`audiences.${sortBy}`, sortOrder);
    }

    if (search) {
      queryBuilder.andWhere('audiences.name ILIKE :search', { search: `%${search}%` });
    }

    return await withPagination(queryBuilder, page, pageSize);
  }

  public async updateAudience(id: number, data: UpdateAudiencePayload): Promise<Audience> {
    const audience = await this.validSoftDelById(id);
    if (!Object.keys(data).length) {
      throw customHttpError(400, MessagesApi.INVALID_BODY, ErrorCode.BadRequest);
    }
    const { name } = data;

    if (name) {
      const audienceSameName = await this.audienceRepository.findOne({
        where: { name, id: Not(id), ...NOT_DELETED },
      });
      if (audienceSameName) {
        throw customHttpError(400, MessagesApi.AUDIENCE_EXISTS, ErrorCode.BadRequest);
      }
    }

    return this.audienceRepository.save({ ...audience, ...data, numberOfUsers: data.numberOfUsers ?? 0 });
  }

  public async deleteAudience(id: number): Promise<void> {
    const [audience, surveyAudience] = await Promise.all([
      this.validSoftDelById(id),
      this.surveyAudienceRepository.findOne({ where: { audienceId: id } }),
    ]);

    if (!audience.isVisible || surveyAudience) {
      throw badRequestError('Cannot delete audience that is in use', ErrorCode.BadRequest);
    }
    audience.deletedAt = new Date();
    await this.audienceRepository.save(audience);
  }

  public async checkNameExist(name: string): Promise<boolean> {
    const audiences = await this.audienceRepository.findOneBy({ name, ...NOT_DELETED });
    return !!audiences;
  }

  public async validSoftDelById(id: number): Promise<Audience> {
    const audience = await this.audienceRepository.findOneBy({ id });
    if (!audience || audience.deletedAt) {
      throw customHttpError(400, MessagesApi.AUDIENCE_VALID, ErrorCode.BadRequest);
    }
    return audience;
  }

  public async audienceMatchUsers(data: UsersIncludedPayload, isExporting?: boolean): Promise<[User[], number]> {
    const queryBuilder = this.addWhereForAudienceMatchUsers(data);
    if (isExporting) {
      const keys = [
        'id',
        'firstName',
        'lastName',
        'birthday',
        'email',
        'phone',
        'licenseNumber',
        'address',
        'city',
        'province',
        'country',
        'postalCode',
        'practiceSetting',
        'employmentStatus',
      ];
      queryBuilder.select(keys.map(key => `user.${key}`));
    } else {
      queryBuilder.select(`user.id`);
    }
    return await queryBuilder.getManyAndCount();
  }

  private addWhereForAudienceMatchUsers(
    audience: UsersIncludedPayload,
    query?: PaginationPayload<User>,
  ): SelectQueryBuilder<User> {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .where('user.deletedAt IS NULL')
      .andWhere('user.verificationStatus = :verificationStatus', {
        verificationStatus: VerificationStatus.Verified,
      });

    if (query) {
      const { search } = query;
      if (search) {
        queryBuilder.andWhere(
          "(CONCAT(NULLIF(user.firstName, ''), ' ', NULLIF(user.lastName, '')) ILIKE :search OR " +
            'user.email ILIKE :search OR user.phone ILIKE :search OR user.province ILIKE :search)',
          { search: `%${search.toLowerCase()}%` },
        );
      }
    }

    if (audience.cities && audience.cities.length) {
      queryBuilder.andWhere('user.city IN (:...cities)', { cities: audience.cities });
    }

    if (audience.employmentStatuses && audience.employmentStatuses.length) {
      queryBuilder.andWhere('user.employmentStatus IN (:...employmentStatuses)', {
        employmentStatuses: audience.employmentStatuses,
      });
    }

    if (audience.practiceSettings && audience.practiceSettings.length) {
      queryBuilder.andWhere('user.practiceSetting IN (:...practiceSettings)', {
        practiceSettings: audience.practiceSettings,
      });
    }

    if (audience.provinces && audience.provinces.length) {
      queryBuilder.andWhere('user.province IN (:...provinces)', { provinces: audience.provinces });
    }

    if (audience.specialtyIds && audience.specialtyIds.length) {
      queryBuilder.andWhere('user.specialtyId IN (:...specialtyIds)', { specialtyIds: audience.specialtyIds });
    }

    if (audience.userIds && audience.userIds.length) {
      queryBuilder.andWhere('user.id IN (:...userIds)', { userIds: audience.userIds });
    }

    if (audience.completedSurveys && audience.completedSurveys.length) {
      queryBuilder
        .leftJoin(UserSurvey, 'user_surveys', 'user_surveys.userId = user.id')
        .andWhere(
          new Brackets(qb => {
            qb.where('user_surveys.isComplete = true').orWhere('user_surveys.totalSubmissions > 0');
          }),
        )
        .andWhere('user_surveys.surveyId IN (:...completedSurveys)', { completedSurveys: audience.completedSurveys });
    }

    return queryBuilder;
  }

  public async audiencesBySurveyId(surveyId: number): Promise<Audience[]> {
    const queryBuilder = this.audienceRepository.createQueryBuilder('audiences');
    queryBuilder.where('audiences.deletedAt IS NULL');
    queryBuilder.leftJoinAndMapMany('audiences.surveyAudiences', SurveyAudience, 'sa', 'sa.surveyId = :surveyId', {
      surveyId,
    });
    queryBuilder.andWhere('sa.audienceId = audiences.id');
    queryBuilder.select(AUDIENCE_FIELDS.map(key => `audiences.${key}`));
    return await queryBuilder.getMany();
  }

  public async insertWithQueryBuilder(
    values: QueryDeepPartialEntity<Audience> | QueryDeepPartialEntity<Audience>[],
  ): Promise<InsertResult> {
    const result = await this.createQueryBuilder().insert().values(values).execute();
    return result;
  }

  public async getPublicAudience(audienceId: number): Promise<Audience> {
    const publicAudience = await this.audienceRepository.findOneBy({
      id: audienceId,
      isVisible: true,
      deletedAt: IsNull(),
    });

    if (!publicAudience) {
      throw notFoundError('Audience not found');
    }

    return publicAudience;
  }

  public async getUsersInAudience(
    audienceId: number,
    data: PaginationPayload<User>,
  ): Promise<PaginationResponseData<User>> {
    const { page = 1, pageSize = 10 } = data;

    const audience = await this.getPublicAudience(audienceId);
    const queryBuilder = this.addWhereForAudienceMatchUsers(audience, data);

    return await withPagination(queryBuilder, page, pageSize);
  }
}
