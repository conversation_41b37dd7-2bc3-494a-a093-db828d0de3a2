import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('audiences')
export class Audience {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'varchar', length: 255, nullable: true })
  name: string;

  @Column({ type: 'boolean', default: true })
  isVisible: boolean;

  @Column({ type: 'int', default: 0 })
  numberOfUsers: number;

  @Column({ type: 'date', nullable: true })
  lastSurveyDate: Date | null;

  @Column('text', { array: true, nullable: true })
  cities: string[] | null;

  @Column('int', { array: true, nullable: true })
  completedSurveys: number[] | null;

  @Column('text', { array: true, nullable: true })
  employmentStatuses: string[] | null;

  @Column('text', { array: true, nullable: true })
  practiceSettings: string[] | null;

  @Column('text', { array: true, nullable: true })
  provinces: string[] | null;

  @Column('int', { array: true, nullable: true })
  specialtyIds: number[] | null;

  @Column('int', { array: true, nullable: true })
  userIds: number[] | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date | null;
}
