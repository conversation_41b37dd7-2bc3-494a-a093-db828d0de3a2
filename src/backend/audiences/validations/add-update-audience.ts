import { nameShape } from '@/backend/shared/validations/name';
import { z } from 'zod';

export const createAudienceSchema = z.object({
  name: nameShape,
  cities: z.string().array().optional().nullable(),
  completedSurveys: z.number().array().optional().nullable(),
  employmentStatuses: z.string().array().optional().nullable(),
  practiceSettings: z.string().array().optional().nullable(),
  provinces: z.string().array().optional().nullable(),
  specialtyIds: z.number().array().optional().nullable(),
  numberOfUsers: z.number().optional().nullable(),
  userIds: z.number().array().optional().nullable(),
});
export type CreateAudiencePayload = z.infer<typeof createAudienceSchema>;

export const updateAudienceSchema = createAudienceSchema.omit({ name: true }).extend({
  name: nameShape.optional(),
});
export type UpdateAudiencePayload = z.infer<typeof updateAudienceSchema>;

export const usersIncludedSchema = createAudienceSchema.omit({ name: true });
export type UsersIncludedPayload = z.infer<typeof usersIncludedSchema>;
