import { dataSource } from '@/backend/database/data-source';
import { exportCsv, stringToBlob } from '@/backend/shared/utils/export-csv';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { Survey, SurveyStatus } from '@/backend/surveys/entities/Survey';
import { SurveyAudienceRepository } from '@/backend/surveys/repositories/survey-audiences-repository';
import { SurveyRepository } from '@/backend/surveys/repositories/survey-repository';
import { User, VerificationStatus } from '@/backend/users/entities/User';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { UserSurveyRepository } from '@/backend/users/repositories/user-survey-repository';
import { PaginationPayload, PaginationResponseData } from '@/types/pagination';
import { DATE_FORMATS, formatDate } from '@/utils/date-format';
import _ from 'lodash';
import { Brackets, ILike, In, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Not } from 'typeorm';
import { Audience } from '../entities/Audience';
import { AudienceRepository } from '../repositories/audience-repository';
import { CreateAudiencePayload, UpdateAudiencePayload } from '../validations/add-update-audience';

export class AudienceService {
  private readonly audienceRepository = new AudienceRepository(dataSource);
  private readonly surveyAudienceRepository = new SurveyAudienceRepository(dataSource);
  private readonly userSurveyRepository = new UserSurveyRepository(dataSource);
  private readonly surveyRepository = new SurveyRepository(dataSource);
  private readonly userRepository = new UserRepository(dataSource);

  constructor() {}

  public async create(data: CreateAudiencePayload): Promise<Audience> {
    const [_, numberOfUsers] = await this.usersIncluded(data);
    const audience = await this.audienceRepository.createAudience({ ...data, numberOfUsers });
    await this.updateLastSurveyDate(audience.id);

    return audience;
  }

  public async listAudiences(data: PaginationPayload<Audience>): Promise<PaginationResponseData<Audience>> {
    return await this.audienceRepository.listAudiences(data);
  }

  public async regenerateUserSurveys(surveyId: number, users: User[]): Promise<number[]> {
    const survey = await this.surveyRepository.findOneBy({ id: surveyId });
    if (!survey) {
      return [];
    }

    const userSurveys = await this.userSurveyRepository.find({
      where: { surveyId },
      relations: {
        userSubmissions: true,
      },
      select: {
        id: true,
        userId: true,
        isComplete: true,
        userSubmissions: {
          id: true,
        },
      },
    });

    const userSurveyIds = userSurveys.map(userSurvey => userSurvey.userId);
    const userFromAudienceIds = users.map(user => user.id);

    // Find duplicate user ids to prevent sending duplicate push notifications
    const duplicateIds = _.intersection(userSurveyIds, userFromAudienceIds);
    const notificationIds = _.xor(duplicateIds, userFromAudienceIds);

    if (
      duplicateIds.length ||
      (userSurveyIds.length && !notificationIds.length) ||
      (userSurveyIds.length && notificationIds.length && !duplicateIds.length)
    ) {
      await Promise.all([
        this.userSurveyRepository.delete({
          surveyId,
          userId: duplicateIds.length ? Not(In(duplicateIds)) : In(userSurveyIds),
          totalSubmissions: 0,
          isComplete: false,
          isPassScreening: true,
        }),
        this.userSurveyRepository.update(
          {
            surveyId,
            userId: duplicateIds.length ? Not(In(duplicateIds)) : In(userSurveyIds),
            totalSubmissions: MoreThan(0),
            isComplete: false,
          },
          {
            isComplete: true,
          },
        ),
      ]);
    }

    // Regenerate user surveys if responses per user has changed
    const surveysToReopen = userSurveys
      .filter(
        us =>
          us.isComplete &&
          us.userSubmissions.length < survey.responsesPerUser &&
          userFromAudienceIds.includes(us.userId),
      )
      .map(us => us.id);

    const surveysToClose = userSurveys
      .filter(us => !us.isComplete && us.userSubmissions.length >= survey.responsesPerUser)
      .map(us => us.id);

    await Promise.all([
      surveysToReopen.length
        ? this.userSurveyRepository.update({ id: In(surveysToReopen) }, { isComplete: false })
        : null,
      surveysToClose.length ? this.userSurveyRepository.update({ id: In(surveysToClose) }, { isComplete: true }) : null,
    ]);

    if (notificationIds.length) {
      const newUserSurveys = notificationIds.map(id => {
        return {
          userId: id,
          surveyId,
          isComplete: false,
        };
      });

      await this.userSurveyRepository.insertWithQueryBuilder(newUserSurveys);
      return notificationIds;
    }

    return [];
  }

  public async update(id: number, data: UpdateAudiencePayload): Promise<Audience> {
    const [users, numberOfUsers] = await this.usersIncluded(data);
    const updatedAudience = await this.audienceRepository.updateAudience(id, { ...data, numberOfUsers });
    const surveyAudiences = await this.surveyAudienceRepository
      .createQueryBuilder('survey_audiences')
      .innerJoin(Survey, 'surveys', 'surveys.id = survey_audiences.surveyId')
      .where('surveys.status = :status', { status: SurveyStatus.Active })
      .andWhere('survey_audiences.audienceId = :audienceId', { audienceId: id })
      .getMany();

    const promises = surveyAudiences.map(async surveyAudience => {
      await this.regenerateUserSurveys(surveyAudience.surveyId, users);
    });
    await Promise.all(promises);
    await this.updateLastSurveyDate(id);

    return updatedAudience;
  }

  public async delete(id: number): Promise<void> {
    return await this.audienceRepository.deleteAudience(id);
  }

  public async checkNameExist(name: string): Promise<boolean> {
    return await this.audienceRepository.checkNameExist(name);
  }

  public async findAndValidate(id: number): Promise<Audience> {
    return await this.audienceRepository.validSoftDelById(id);
  }

  public async duplicate(id: number): Promise<Audience> {
    const audience = await this.audienceRepository.validSoftDelById(id);
    const similarAudiences = await this.audienceRepository.find({
      where: {
        name: ILike(`${audience.name} (%)`),
      },
    });

    // Ex: last duplicated audience is name (1), then the next duplicated audience is name (2)
    const numericSuffixes = similarAudiences.map(audience => {
      const match = /\((\d+)\)$/.exec(audience.name);
      return match ? parseInt(match[1]) : 0;
    });
    const maxSuffix = Math.max(0, ...numericSuffixes);

    const payload = new Audience();
    Object.assign(payload, {
      ...audience,
      name: `${audience.name} (${maxSuffix + 1})`,
      id: undefined,
      createdAt: undefined,
      deletedAt: undefined,
      updatedAt: undefined,
    });
    return this.create(payload);
  }

  public async audienceSelfUpdate(audiences?: Audience[]): Promise<void> {
    if (!audiences) {
      audiences = await this.audienceRepository.find({
        where: {
          deletedAt: IsNull(),
        },
      });
    }

    await Promise.all(
      audiences.map(async audience => {
        return this.update(audience.id, audience);
      }),
    );
  }

  public async exportAudience(id: number): Promise<Blob> {
    const audience = await this.findAndValidate(id);
    const [users] = await this.usersIncluded(audience, true);
    const csv = await exportCsv(users);
    return stringToBlob(csv as string);
  }

  public async exportAudienceSurveys(surveys: Survey[]): Promise<Blob> {
    const surveyData = surveys.map(survey => {
      return {
        ...survey,
        company: survey.company?.name,
        createdAt: formatDate(survey.createdAt, DATE_FORMATS.ISO_DATE),
        expiryDate: formatDate(survey.expiryDate, DATE_FORMATS.ISO_DATE),
      };
    });
    const csv = await exportCsv(surveyData);
    return stringToBlob(csv as string);
  }

  public async usersIncluded(data: UpdateAudiencePayload, isExporting?: boolean) {
    return await this.audienceRepository.audienceMatchUsers(data, isExporting);
  }

  public async audiencesBySurveyId(surveyId: number): Promise<Audience[]> {
    return await this.audienceRepository.audiencesBySurveyId(surveyId);
  }

  public async getAudiencesByUser(
    user: User,
    { isGetFilters = false, isVisibleOnly = false } = {},
  ): Promise<Audience[]> {
    if (user.verificationStatus !== VerificationStatus.Verified) {
      return [];
    }

    const userCompletedSurveys = await this.userSurveyRepository
      .createQueryBuilder('us')
      .where('us.userId = :userId', { userId: user.id })
      .andWhere(
        new Brackets(qb => {
          qb.where('us.isComplete = true').orWhere('us.totalSubmissions > 0');
        }),
      )
      .getMany();

    const surveyIds = userCompletedSurveys.map(userSurvey => userSurvey.surveyId);

    const queryBuilder = this.audienceRepository
      .createQueryBuilder('audience')
      .where('audience.deletedAt IS NULL')
      .andWhere(this.buildArrayCondition('audience.specialtyIds', user.specialtyId))
      .andWhere(this.buildArrayCondition('audience.cities', user.city))
      .andWhere(this.buildArrayCondition('audience.provinces', user.province))
      .andWhere(this.buildArrayCondition('audience.employmentStatuses', user.employmentStatus))
      .andWhere(this.buildArrayCondition('audience.practiceSettings', user.practiceSetting))
      .andWhere(this.buildArrayCondition('audience.userIds', user.id))
      .andWhere(
        new Brackets(qb => {
          qb.where('array_length(audience.completedSurveys, 1) IS NULL')
            .orWhere('audience.completedSurveys IS NULL')
            .orWhere('audience.completedSurveys && ARRAY[:...surveyIds]::integer[]', { surveyIds });
        }),
      );

    if (isVisibleOnly) {
      queryBuilder.andWhere('audience.isVisible = true');
    }

    if (isGetFilters) {
      queryBuilder
        .leftJoinAndMapMany('audience.specialties', Specialty, 'specialty', 'specialty.id = ANY(audience.specialtyIds)')
        .leftJoinAndMapMany('audience.surveys', Survey, 'survey', 'survey.id = ANY(audience.completedSurveys)');
    }

    const audiences = await queryBuilder.getMany();
    return audiences;
  }

  private buildArrayCondition(columnName: string, value: unknown): Brackets {
    return new Brackets(qb => {
      qb.where(`array_length(${columnName}, 1) IS NULL`)
        .orWhere(`${columnName} IS NULL`)
        .orWhere(`:${columnName}${typeof value === 'string' ? '::text' : ''} = ANY(${columnName})`, {
          [columnName]: value,
        });
    });
  }

  private async updateLastSurveyDate(audienceId: number): Promise<void> {
    const lastSurvey = await this.surveyRepository.getAudienceLastSurvey(audienceId);

    if (lastSurvey) {
      await this.audienceRepository.update({ id: audienceId }, { lastSurveyDate: lastSurvey.startDate });
    }
  }

  public async getUsersInAudience(
    audienceId: number,
    data: PaginationPayload<User>,
  ): Promise<PaginationResponseData<User>> {
    return await this.audienceRepository.getUsersInAudience(audienceId, data);
  }

  public async addUserToAudiences(userId: number, audienceIds: number[]): Promise<void> {
    const queryBuilder = this.audienceRepository
      .createQueryBuilder()
      .update(Audience)
      .set({
        userIds: () => `ARRAY_APPEND(COALESCE(userIds, ARRAY[]::int[]), ${userId})`,
      })
      .where('deletedAt IS NULL')
      .andWhere('isVisible = true');

    if (audienceIds.length > 0) {
      queryBuilder.andWhere('id IN (:...audienceIds)', { audienceIds });
    }

    await queryBuilder.execute();
  }

  public async removeUserFromAudiences(userId: number, audienceIds: number[]): Promise<void> {
    const queryBuilder = this.audienceRepository
      .createQueryBuilder()
      .update(Audience)
      .set({
        userIds: () => `
          CASE 
            WHEN array_length("userIds", 1) = 1 AND "userIds"[1] = ${userId} THEN NULL
            ELSE ARRAY_REMOVE("userIds", ${userId}) 
          END
        `,
      })
      .where('deletedAt IS NULL')
      .andWhere('isVisible = true')
      .andWhere('userIds IS NOT NULL');

    if (audienceIds.length > 0) {
      queryBuilder.andWhere('id IN (:...audienceIds)', { audienceIds });
    }

    await queryBuilder.execute();
  }

  public async removeUsersFromAudience(audienceId: number, userIds: number[]): Promise<void> {
    const audience = await this.audienceRepository.getPublicAudience(audienceId);

    if (!audience.userIds) {
      return;
    }

    let updatedUserIdsQuery: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const queryParams: any = { audienceId, userIds };

    if (audience.userIds.length === 0) {
      const allUsers = await this.userRepository
        .createQueryBuilder('user')
        .select('user.id')
        .where('user.deletedAt IS NULL')
        .andWhere('user.verificationStatus = :verificationStatus', { verificationStatus: VerificationStatus.Verified })
        .getMany();

      const allUserIds = allUsers.map(user => user.id);
      const filteredUserIds = allUserIds.filter(id => !userIds.includes(id));

      // If filtering results in an empty array, store NULL instead of []
      updatedUserIdsQuery = filteredUserIds.length > 0 ? ':filteredUserIds' : 'NULL';
      queryParams.filteredUserIds = filteredUserIds.length > 0 ? `{${filteredUserIds.join(',')}}` : null;
    } else {
      // If userIds to remove is empty, set audience.userIds to NULL
      if (userIds.length === 0) {
        updatedUserIdsQuery = 'NULL';
      } else {
        // Remove userIds from existing list
        updatedUserIdsQuery = `
        CASE 
          WHEN array_length("userIds", 1) = 1 AND "userIds"[1] = ANY(:userIds) THEN NULL
          ELSE ARRAY(SELECT UNNEST("userIds") EXCEPT SELECT UNNEST(:userIds))
        END
      `;
      }
    }

    // Update audience userIds (set NULL when array is empty)
    await this.audienceRepository
      .createQueryBuilder()
      .update(Audience)
      .set({ userIds: () => updatedUserIdsQuery })
      .where('id = :audienceId', { audienceId })
      .andWhere('deletedAt IS NULL')
      .andWhere('isVisible = TRUE')
      .setParameters(queryParams)
      .execute();
  }
}
