import { z } from 'zod';
import { nameShape } from '@/backend/shared/validations/name';

export const createSpecialtySchema = z.object({
  name: nameShape,
  referralValue: z.coerce.number({ required_error: 'Value is required' }).nonnegative('Value must be non-negative'),
  frenchName: z
    .string({ required_error: 'French name is required', invalid_type_error: 'French name must be a string' })
    .trim()
    .min(1, { message: 'French name cannot be empty' })
    .max(50, { message: 'French name cannot exceed 50 characters' }),
});

export type CreateSpecialtyPayload = z.infer<typeof createSpecialtySchema>;
