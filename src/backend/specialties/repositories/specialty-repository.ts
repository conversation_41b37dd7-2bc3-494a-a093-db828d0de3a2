import { Specialty } from '@/backend/specialties/entities/Specialty';
import { User, VerificationStatus } from '@/backend/users/entities/User';
import { DataSource, Repository } from 'typeorm';

export class SpecialtyRepository extends Repository<Specialty> {
  constructor(private dataSource: DataSource) {
    super(Specialty, dataSource.createEntityManager());
  }

  public async specialtyUsers(id: number): Promise<[User[], number]> {
    const queryBuilder = this.dataSource
      .getRepository(User)
      .createQueryBuilder('user')
      .where('user.specialtyId = :id', { id })
      .andWhere('user.deletedAt IS NULL')
      .andWhere('user.verificationStatus = :verificationStatus', {
        verificationStatus: VerificationStatus.Verified,
      });

    return queryBuilder.getManyAndCount();
  }
}
