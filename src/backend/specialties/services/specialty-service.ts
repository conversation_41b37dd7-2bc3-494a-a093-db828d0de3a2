import { AudienceRepository } from '@/backend/audiences/repositories/audience-repository';
import { Company } from '@/backend/companies/entities/Company';
import { dataSource } from '@/backend/database/data-source';
import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { MessagesApi } from '@/backend/shared/common/messages';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { SpecialtyRepository } from '@/backend/specialties/repositories/specialty-repository';
import { SpecialtyTranslationRepository } from '@/backend/specialties/repositories/specialty-translation-repository';
import { CreateSpecialtyPayload } from '@/backend/specialties/validations/create-specialty';
import { UpdateSpecialtyPayload } from '@/backend/specialties/validations/update-specialty';
import { Locale, Survey } from '@/backend/surveys/entities/Survey';
import { SurveyAudienceRepository } from '@/backend/surveys/repositories/survey-audiences-repository';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';
import _ from 'lodash';
import 'reflect-metadata';
import { ArrayContains, IsNull, Not } from 'typeorm';

export class SpecialtyService {
  private SORTABLE_FIELDS: Array<keyof Specialty> = [
    'id',
    'name',
    'numberOfUsers',
    'numberOfSurveys',
    'lastSurveyDate',
    'referralValue',
  ];

  private specialtyRepository = new SpecialtyRepository(dataSource);
  private userRepository = new UserRepository(dataSource);
  private audienceRepository = new AudienceRepository(dataSource);
  private surveyAudienceRepository = new SurveyAudienceRepository(dataSource);
  private specialtyTranslationRepository = new SpecialtyTranslationRepository(dataSource);

  constructor() {}

  public async getSpecialties(data: PaginationPayload<Specialty>): Promise<PaginationResponseData<Specialty>> {
    const { page, pageSize, sortBy, sortOrder } = data;
    const queryBuilder = this.specialtyRepository.createQueryBuilder('specialties');
    queryBuilder.where('specialties.deletedAt IS NULL');
    queryBuilder.select(this.SORTABLE_FIELDS.map(key => `specialties.${key}`));

    if (sortBy && this.SORTABLE_FIELDS.includes(sortBy as keyof Specialty)) {
      queryBuilder.orderBy(`specialties.${sortBy}`, sortOrder);
    }
    return withPagination(queryBuilder, page, pageSize);
  }

  public async getAllSpecialties(): Promise<Specialty[]> {
    return this.specialtyRepository.find({
      where: { deletedAt: IsNull() },
      order: { name: 'ASC' },
    });
  }

  public async create(data: CreateSpecialtyPayload): Promise<Specialty> {
    const specialty = await this.specialtyRepository.findOneBy({
      name: data.name,
      deletedAt: IsNull(),
    });

    if (specialty) {
      throw customHttpError(400, 'Specialty name already exists', ErrorCode.AlreadyExists);
    }

    let newSpecialty = this.specialtyRepository.create(data);
    newSpecialty = await this.specialtyRepository.save(newSpecialty);

    if (data.frenchName) {
      await this.specialtyTranslationRepository.insert({
        specialtyId: newSpecialty.id,
        locale: Locale.FR,
        name: data.frenchName,
      });
    }

    const [surveys, [_count, numberOfUsers]] = await Promise.all([
      this.getSurveysBySpecialtyId(newSpecialty.id),
      this.specialtyRepository.specialtyUsers(newSpecialty.id),
    ]);

    const sortedSurveys = _.orderBy(surveys, ['startDate'], ['desc']);
    const lastSurvey = sortedSurveys.find(x => x.startDate !== null);

    return this.specialtyRepository.save({
      ...newSpecialty,
      numberOfUsers,
      numberOfSurveys: surveys.length,
      lastSurveyDate: lastSurvey?.startDate ?? null,
    });
  }

  public async checkNameExist(name: string): Promise<boolean> {
    const specialty = await this.specialtyRepository.findOneBy({
      name,
      deletedAt: IsNull(),
    });
    return !!specialty;
  }

  public async update(id: number, data: UpdateSpecialtyPayload): Promise<Specialty> {
    const specialty = await this.validSoftDelById(id);
    if (data.name && data.name !== specialty.name) {
      const existingSpecialty = await this.specialtyRepository.findOneBy({
        id: Not(id),
        name: data.name,
        deletedAt: IsNull(),
      });

      if (existingSpecialty) {
        throw customHttpError(400, 'Specialty name already exists', ErrorCode.AlreadyExists);
      }
    }

    if (data.frenchName) {
      const translation = await this.specialtyTranslationRepository.findOneBy({
        specialtyId: id,
        locale: Locale.FR,
      });

      if (translation) {
        translation.name = data.frenchName;
        await this.specialtyTranslationRepository.save(translation);
      } else {
        this.specialtyTranslationRepository.insert({
          specialtyId: id,
          name: data.frenchName,
          locale: Locale.FR,
        });
      }
    }

    const [_, numberOfUsers] = await this.specialtyRepository.specialtyUsers(id);
    return await this.specialtyRepository.save({ ...specialty, ...data, numberOfUsers });
  }

  public async delete(id: number): Promise<void> {
    const specialty = await this.validSoftDelById(id);

    const [audience, user] = await Promise.all([
      this.audienceRepository.findOneBy({ specialtyIds: ArrayContains([id]), deletedAt: IsNull() }),
      this.userRepository.findOneBy({ specialtyId: id, deletedAt: IsNull() }),
    ]);

    if (audience || user) {
      throw customHttpError(400, 'Specialty is in use', ErrorCode.BadRequest);
    }

    specialty.deletedAt = new Date();
    await this.specialtyRepository.save(specialty);
  }

  public async validSoftDelById(id: number): Promise<Specialty> {
    const specialty = await this.specialtyRepository.findOneBy({ id });
    if (!specialty || specialty.deletedAt) {
      throw customHttpError(400, MessagesApi.SPECIALTY_VALID, ErrorCode.BadRequest);
    }
    return specialty;
  }

  public async getById(id: number): Promise<Specialty> {
    const specialty = await this.specialtyRepository.findOne({
      where: { id, deletedAt: IsNull() },
      relations: {
        translations: true,
      },
    });

    if (!specialty) {
      throw customHttpError(404, 'Specialty not found', ErrorCode.NotFound);
    }
    return specialty;
  }

  public async specialtySelfUpdate(specialties?: Specialty[]): Promise<void> {
    if (!specialties) {
      specialties = await this.specialtyRepository.find({});
    }

    await Promise.all(
      specialties.map(async specialty => {
        const [_, numberOfUsers] = await this.specialtyRepository.specialtyUsers(specialty.id);
        return await this.specialtyRepository.update(specialty.id, { numberOfUsers });
      }),
    );
  }

  public async getSurveysBySpecialtyId(id: number): Promise<Survey[]> {
    const surveyAudiences = await this.surveyAudienceRepository
      .createQueryBuilder('sa')
      .leftJoinAndMapOne('sa.survey', Survey, 'survey', 'sa.surveyId = survey.id')
      .leftJoinAndMapOne('survey.company', Company, 'company', 'survey.companyId = company.id')
      .leftJoinAndSelect('sa.audience', 'audience', 'sa.audienceId = audience.id')
      .where(':id = ANY(audience.specialtyIds)', { id })
      .orWhere('array_length(audience.specialtyIds, 1) IS NULL')
      .orWhere('audience.specialtyIds IS NULL')
      .getMany();

    return surveyAudiences.map(sa => sa.survey);
  }
}
