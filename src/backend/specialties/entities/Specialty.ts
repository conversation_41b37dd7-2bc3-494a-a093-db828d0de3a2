import { DecimalColumnTransformer } from '@/backend/shared/utils/data-transform';
import { Column, CreateDateColumn, Entity, Index, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { SpecialtyTranslation } from './SpecialtyTranslation';

@Entity('specialties')
export class Specialty {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'boolean', default: false })
  isEnabled: boolean;

  @Column({ type: 'int', default: 0 })
  numberOfUsers: number;

  @Column({ type: 'int', default: 0 })
  numberOfSurveys: number;

  @Column({ type: 'date', nullable: true })
  lastSurveyDate: Date | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: new DecimalColumnTransformer(),
  })
  referralValue: number;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @OneToMany(() => SpecialtyTranslation, translation => translation.specialty)
  translations: SpecialtyTranslation[];
}
