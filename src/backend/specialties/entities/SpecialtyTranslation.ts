import { Specialty } from '@/backend/specialties/entities/Specialty';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('specialty_translations')
export class SpecialtyTranslation {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  specialtyId: number;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', length: 10 })
  locale: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => Specialty)
  @JoinColumn({ name: 'specialtyId' })
  specialty: Relation<Specialty>;
}
