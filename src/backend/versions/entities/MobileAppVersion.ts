import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('mobile_app_versions')
export class MobileAppVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 10 })
  os: string;

  // The minimum version required to use the app
  @Column({ type: 'varchar', length: 10 })
  minVersion: string;

  @Column({ type: 'varchar', length: 10 })
  currentVersion: string;
}
