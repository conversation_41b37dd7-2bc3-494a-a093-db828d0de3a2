import { TokenService } from '@/backend/auth/services/token-service';
import { FirebaseErrorCode, IndustriiCookies, IndustriiTokens } from '@/backend/auth/types/auth';
import { AdminChangePasswordPayload } from '@/backend/auth/validations/admin-change-password';
import { AdminResetPasswordPayload } from '@/backend/auth/validations/admin-reset-password';
import { LoginPayload } from '@/backend/auth/validations/login';
import { dataSource } from '@/backend/database/data-source';
import { HubspotService } from '@/backend/hubspot/services/hubspot-service';
import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { logger } from '@/backend/shared/utils/logger';
import { User } from '@/backend/users/entities/User';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserAdminRepository } from '@/backend/users/repositories/user-admin-repository';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { UserRole } from '@/backend/users/types/user';
import config from '@/config';
import { firebaseClient } from '@/lib/firebase/client';
import { waitUntil } from '@vercel/functions';
import { auth } from 'firebase-admin';
import { DecodedIdToken } from 'firebase-admin/auth';
import { FirebaseError } from 'firebase/app';
import {
  confirmPasswordReset,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  verifyPasswordResetCode,
} from 'firebase/auth';
import { Logger } from 'pino';
import { IsNull } from 'typeorm';

export class AuthService {
  private readonly userRepository = new UserRepository(dataSource);
  private readonly userAdminRepository = new UserAdminRepository(dataSource);
  private readonly tokenService = new TokenService();
  private readonly hubspotService = new HubspotService();
  private readonly log: Logger = logger.child({ description: 'AuthService' });

  constructor() {}

  public async login(data: LoginPayload): Promise<IndustriiTokens> {
    let firebaseUID = '';

    try {
      const userCredential = await signInWithEmailAndPassword(firebaseClient, data.email, data.password);
      firebaseUID = userCredential.user.uid;
    } catch (error) {
      this.log.warn(error, 'Failed to login user');
      if (error instanceof FirebaseError && error.code === FirebaseErrorCode.InvalidCredential) {
        throw customHttpError(400, 'Invalid email or password', ErrorCode.InvalidCredential);
      }

      throw customHttpError(400, 'An error occurred during login', ErrorCode.BadRequest);
    }

    const user = await this.userAdminRepository.findOneBy({
      firebaseUserId: firebaseUID,
    });

    if (!user) {
      throw customHttpError(404, 'User not found', ErrorCode.NotFound);
    }

    return this.tokenService.generateIndustriiTokens({
      userId: user.id,
      email: user.email,
      phone: user.phone,
      role: user.role,
    });
  }

  public createCookieOptions(tokens: IndustriiTokens): IndustriiCookies {
    return {
      accessCookie: {
        name: 'accessToken',
        value: tokens.accessToken,
        maxAge: config.ACCESS_TOKEN_EXPIRE_TIME,
        httpOnly: true,
        secure: true,
      },
      refreshCookie: {
        name: 'refreshToken',
        value: tokens.refreshToken,
        maxAge: config.REFRESH_TOKEN_EXPIRE_TIME,
        httpOnly: true,
        secure: true,
      },
    };
  }

  public async verifyCredential(idToken: string): Promise<IndustriiTokens | null> {
    let decoded: DecodedIdToken;

    try {
      decoded = await auth().verifyIdToken(idToken);
    } catch (error) {
      this.log.warn(error, 'Failed to verify id token');
      return null;
    }

    const user = await this.userRepository.findOneBy({
      phone: decoded.phone_number,
      deletedAt: IsNull(),
    });

    if (!user) {
      throw customHttpError(404, 'User not found', ErrorCode.NotFound);
    }

    if (user.firebaseUserId !== decoded.uid) {
      await this.userRepository.update(user.id, {
        firebaseUserId: decoded.uid,
      });
    }

    waitUntil(
      (async (): Promise<void> => {
        await this.userRepository.update(user.id, {
          lastLogin: new Date(),
        });

        await this.hubspotService.updateLastLoginDate(user.hubspotProductId!);
      })(),
    );

    const { accessToken, refreshToken } = this.tokenService.generateIndustriiTokens({
      userId: user.id,
      email: user.email,
      phone: user.phone,
      role: UserRole.User,
    });

    return {
      accessToken,
      refreshToken,
    };
  }

  public async forgotAdminPassword(email: string): Promise<void> {
    const user = await this.userAdminRepository.findOneBy({ email });
    if (!user) {
      throw customHttpError(404, 'Email not found', ErrorCode.NotFound);
    }

    try {
      await sendPasswordResetEmail(firebaseClient, email);
    } catch (error) {
      const msg = 'Failed to send password reset email';
      this.log.warn(error, msg);
      throw customHttpError(400, msg, ErrorCode.BadRequest);
    }
  }

  public async verifyAdminPasswordResetCode(code: string): Promise<string> {
    let email: string | undefined;
    try {
      email = await verifyPasswordResetCode(firebaseClient, code);
    } catch (error) {
      const msg = 'Invalid password reset code';
      this.log.warn(error, msg);
      throw customHttpError(400, msg, ErrorCode.InvalidCredential);
    }
    return email;
  }

  public async adminChangePassword(id: number, data: AdminChangePasswordPayload) {
    const user = await this.userAdminRepository.findOneBy({ id });
    if (!user) {
      throw customHttpError(404, 'User not found', ErrorCode.NotFound);
    }

    const { currentPassword, newPassword } = data;
    try {
      await signInWithEmailAndPassword(firebaseClient, user.email ?? '', currentPassword);
    } catch (error) {
      const msg = 'Failed to verify current password';
      this.log.warn(error, msg);
      if (error instanceof FirebaseError && error.code === FirebaseErrorCode.InvalidCredential) {
        throw customHttpError(400, 'Current password is incorrect', ErrorCode.InvalidCredential);
      }
      throw customHttpError(400, 'An error occurred during verifying current password', ErrorCode.BadRequest);
    }
    await auth().updateUser(user.firebaseUserId, {
      password: newPassword,
    });
    return;
  }

  public async resetAdminPassword(data: AdminResetPasswordPayload): Promise<void> {
    const { code, password } = data;
    const email = await this.verifyAdminPasswordResetCode(code);
    let isSetPassword = true;

    try {
      isSetPassword = await this.adminHasPassword(email);
      await confirmPasswordReset(firebaseClient, code, password);
    } catch (error) {
      const errorMessage = isSetPassword ? 'Failed to set password' : 'Failed to reset password';
      this.log.warn(error, errorMessage);
      throw customHttpError(400, errorMessage, ErrorCode.BadRequest);
    }
  }

  public async getCurrentUser(id: number): Promise<User> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw customHttpError(404, 'User not found', ErrorCode.NotFound);
    }
    return user;
  }

  public async getCurrentUserAdmin(id: number): Promise<UserAdmin> {
    const user = await this.userAdminRepository.findOneBy({ id });

    if (!user) {
      throw customHttpError(404, 'User not found', ErrorCode.NotFound);
    }
    return user;
  }

  public async adminHasPassword(email: string): Promise<boolean> {
    try {
      const firebaseUser = await auth().getUserByEmail(email);
      const providers = firebaseUser.providerData.map(provider => provider.providerId);
      return providers.includes('password');
    } catch (error) {
      this.log.warn(error, 'Failed to get user by email');
      return false;
    }
  }
}
