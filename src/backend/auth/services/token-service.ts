import { AuthInfo, IndustriiTokens } from '@/backend/auth/types/auth';
import { dataSource } from '@/backend/database/data-source';
import { ErrorCode, customHttpError } from '@/backend/middlewares/capture-errors';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import config from '@/config';
import jwt from 'jsonwebtoken';

export class TokenService {
  private readonly userRepository = new UserRepository(dataSource);

  constructor() {}

  public generateAccessToken(payload: AuthInfo): string {
    return jwt.sign(payload, config.ACCESS_TOKEN_SECRET, { expiresIn: config.ACCESS_TOKEN_EXPIRE_TIME });
  }

  public generateRefreshToken(payload: AuthInfo): string {
    return jwt.sign(payload, config.REFRESH_TOKEN_SECRET, { expiresIn: config.REFRESH_TOKEN_EXPIRE_TIME });
  }

  public generateIndustriiTokens(payload: AuthInfo): IndustriiTokens {
    return {
      accessToken: this.generateAccessToken(payload),
      refreshToken: this.generateRefreshToken(payload),
    };
  }

  public async renewAccessToken(refreshToken: string): Promise<IndustriiTokens> {
    let decoded: AuthInfo;
    try {
      decoded = jwt.verify(refreshToken as string, config.REFRESH_TOKEN_SECRET) as AuthInfo;
    } catch (error) {
      throw customHttpError(401, 'Invalid refresh token', ErrorCode.Unauthorized);
    }

    await this.userRepository.findOneById(decoded.userId);
    const accessToken = this.generateAccessToken({
      userId: decoded.userId,
      email: decoded.email,
      phone: decoded.phone,
      role: decoded.role,
    });

    return {
      accessToken,
      refreshToken,
    };
  }
}
