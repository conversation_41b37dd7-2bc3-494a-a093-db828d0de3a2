import { PreferredLanguage } from '@/backend/hubspot/types/contact';
import { z } from 'zod';

export const userRegistrationSchema = z.object({
  firstName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  lastName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  phone: z
    .string({
      required_error: 'Phone number is required',
    })
    .regex(new RegExp('^\\+1\\d{10}$')),
  email: z
    .string({
      required_error: 'Email is required',
    })
    .toLowerCase()
    .email('Invalid email address'),
  referralCode: z.string().toUpperCase().optional(),
  preferredLanguage: z.nativeEnum(PreferredLanguage, {
    required_error: 'Preferred language is required',
    invalid_type_error: 'Invalid preferred language',
  }),
  idToken: z.string({
    required_error: 'ID token is required',
  }),
  isEmailOptIn: z.boolean().default(false),
  notificationEnabled: z.boolean().default(false),
});

export const userRegistrationValidationSchema = z.object({
  phone: z
    .string({
      required_error: 'Phone number is required',
    })
    .regex(new RegExp('^\\+1\\d{10}$')),
  email: z
    .string({
      required_error: 'Email is required',
    })
    .toLowerCase()
    .email('Invalid email address'),
  referralCode: z.string().toUpperCase().optional(),
});

export type UserRegistrationPayload = z.infer<typeof userRegistrationSchema>;
export type UserRegistrationValidationPayload = z.infer<typeof userRegistrationValidationSchema>;
