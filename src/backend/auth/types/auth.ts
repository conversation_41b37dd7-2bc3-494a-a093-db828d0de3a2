import { Role } from '@/backend/users/types/user';
import { ResponseCookie } from 'next/dist/compiled/@edge-runtime/cookies';

export interface IndustriiTokens {
  accessToken: string;
  refreshToken: string;
}

export interface IndustriiCookies {
  accessCookie: ResponseCookie;
  refreshCookie: ResponseCookie;
}

export interface AuthInfo {
  userId: number;
  email: string | null;
  phone: string | null;
  role: Role;
}

export enum FirebaseErrorCode {
  InvalidCredential = 'auth/invalid-credential',
  UserNotFound = 'auth/user-not-found',
}
