import { AdminRole } from '@/backend/users/entities/UserAdmin';
import { z } from 'zod';

export const addAdminSchema = z.object({
  firstName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  lastName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  email: z.string().email(),
  role: z.nativeEnum(AdminRole),
});

export type AddAdminPayload = z.infer<typeof addAdminSchema>;

export const updateAdminSchema = z.object({
  firstName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')).optional(),
  lastName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')).optional(),
  role: z.nativeEnum(AdminRole).optional(),
});

export type UpdateAdminPayload = z.infer<typeof updateAdminSchema>;
