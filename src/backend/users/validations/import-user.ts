import { z } from 'zod';
import { UserType } from '../entities/User';

export const importUsersSchema = z.object({
  users: z.array(
    z.object({
      firstName: z
        .string()
        .min(1, { message: 'First name cannot be empty' })
        .max(50, { message: 'First name must be 50 characters or less' })
        .regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i'), {
          message: 'First name can only contain letters, spaces, hyphens, and apostrophes',
        }),
      lastName: z
        .string()
        .min(1, { message: 'Last name cannot be empty' })
        .max(50, { message: 'Last name must be 50 characters or less' })
        .regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i'), {
          message: 'Last name can only contain letters, spaces, hyphens, and apostrophes',
        }),
      email: z.string({ required_error: 'Email is required' }).email('Invalid email address').toLowerCase(),
      specialtyId: z.number({ required_error: 'Specialty is required' }).int().positive(),
      birthday: z.coerce.date({ required_error: 'Birthday is required' }).optional().nullable(),
      province: z
        .string()
        .optional()
        .nullable()
        .transform(val => (val === '' ? null : val)),
      practiceSetting: z
        .string()
        .optional()
        .nullable()
        .transform(val => (val === '' ? null : val)),
      employmentStatus: z
        .string()
        .optional()
        .nullable()
        .transform(val => (val === '' ? null : val)),
      userType: z.nativeEnum(UserType).optional().nullable(),
    }),
  ),
});

export type ImportUsersPayload = z.infer<typeof importUsersSchema>;
