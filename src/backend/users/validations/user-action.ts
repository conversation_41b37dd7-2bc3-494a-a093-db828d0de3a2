import { z } from 'zod';
import { UserAction } from '@/backend/users/entities/UserAction';

export const getUserActionsSchema = z.object({
  from: z.coerce.date().optional().nullable(),
  to: z.coerce.date().optional().nullable(),
});

export type GetUserActionsPayload = z.infer<typeof getUserActionsSchema>;

export type UserActionsResponse = {
  actions: UserAction[];
  from?: Date | null;
  to?: Date | null;
};
