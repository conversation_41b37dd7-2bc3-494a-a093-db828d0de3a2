import { Gender } from '@/backend/users/entities/User';
import { z } from 'zod';

export const updateOnboardingInfoSchema = z
  .object({
    gender: z
      .nativeEnum(Gender, { required_error: 'Gender is required', invalid_type_error: 'Invalid gender' })
      .optional(),
    birthday: z.coerce.date({ required_error: 'Birthday is required' }).optional(),
    address: z
      .string({ required_error: 'Address is required' })
      .min(1, { message: 'Must be 1 or more characters long' })
      .max(255, { message: 'Must be 255 or fewer characters long' })
      .optional(),
    city: z
      .string({ required_error: 'City is required' })
      .min(1, { message: 'Must be 1 or more characters long' })
      .max(50, { message: 'Must be 50 or fewer characters long' })
      .optional(),
    province: z
      .string({ required_error: 'Province is required' })
      .min(1, { message: 'Must be 1 or more characters long' })
      .max(50, { message: 'Must be 50 or fewer characters long' })
      .optional(),
    country: z
      .string({ required_error: 'Country is required' })
      .min(1, { message: 'Must be 1 or more characters long' })
      .max(50, { message: 'Must be 50 or fewer characters long' })
      .optional(),
    postalCode: z
      .string({ required_error: 'Postal code is required' })
      .min(1, { message: 'Must be 1 or more characters long' })
      .max(50, { message: 'Must be 50 or fewer characters long' })
      .optional(),
    licenseNumber: z
      .string()
      .regex(new RegExp('^[0-9]*$'))
      .min(1, { message: 'Must be 1 or more characters long' })
      .max(100, { message: 'Must be 100 or fewer characters long' })
      .optional(),
    specialtyId: z.number({ required_error: 'Specialty is required' }).int().positive().optional(),
    practiceSettingId: z.number({ required_error: 'Practice setting is required' }).int().positive().optional(),
    employmentStatusId: z.number({ required_error: 'Employment status is required' }).int().positive().optional(),
    canadaPostId: z.string().optional(),
  })
  .superRefine((value, context) => {
    // Include canadaPostId to save Country, Province, City to database
    if ((value.country || value.province || value.city) && !value.canadaPostId) {
      return context.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Canada Post ID is required',
        path: ['canadaPostId'],
      });
    }

    return z.NEVER;
  });

export type UpdateOnboardingInfoPayload = z.infer<typeof updateOnboardingInfoSchema>;
