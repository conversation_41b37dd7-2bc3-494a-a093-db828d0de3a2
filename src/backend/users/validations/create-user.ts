import { z } from 'zod';
import { Gender, UserType } from '../entities/User';

export const createUserSchema = z.object({
  email: z
    .string({ required_error: 'Email is required' })
    .toLowerCase()
    .min(1, { message: 'Must be 1 or more characters long' })
    .email('Invalid email address'),
  phone: z.preprocess(
    phone => (!phone ? undefined : phone),
    z
      .string()
      .regex(new RegExp(/^([+]?[\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/), 'Invalid phone number')
      .optional(),
  ),
  firstName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  lastName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  country: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  province: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  city: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  address: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  postalCode: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  birthday: z.coerce.date().optional(),
  specialtyId: z.coerce.number({ required_error: 'Specialty is required', invalid_type_error: 'Invalid specialty' }),
  gender: z.nativeEnum(Gender).optional().nullable(),
  licenseNumber: z
    .string()
    .regex(new RegExp('^[0-9]*$'))
    .min(1, { message: 'Must be 1 or more characters long' })
    .max(100, { message: 'Must be 100 or fewer characters long' })
    .optional(),
  practiceSetting: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  employmentStatus: z
    .string()
    .optional()
    .transform(val => (val === '' ? null : val)),
  canadaPostId: z.string().optional(),
  userType: z.nativeEnum(UserType).optional().nullable(),
});

export type CreateUserPayload = z.infer<typeof createUserSchema>;
