import { z } from 'zod';

export const updateUserProfileSchema = z.object({
  firstName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')).optional(),
  lastName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')).optional(),
  // phone: z.string().regex(new RegExp('^\\+1\\d{10}$')).optional(),
  phone: z.string().optional(),
  email: z.string().toLowerCase().email('Invalid email address').optional(),
  idToken: z.string().optional(),
  notificationEnabled: z.boolean().optional(),
  isEmailOptIn: z.boolean().optional(),
});

export const validateUserProfileSchema = z.object({
  // phone: z.string().regex(new RegExp('^\\+1\\d{10}$')).optional(),
  phone: z.string().optional(),
  email: z.string().toLowerCase().email('Invalid email address').optional(),
});

export type UpdateUserProfilePayload = z.infer<typeof updateUserProfileSchema>;
export type ValidateUserProfilePayload = z.infer<typeof validateUserProfileSchema>;
