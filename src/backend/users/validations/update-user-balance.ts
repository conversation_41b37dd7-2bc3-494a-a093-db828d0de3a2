import { TransactionType } from '@/backend/users/entities/UserTransaction';
import { z } from 'zod';

export const updateUserBalanceSchema = z.object({
  type: z.enum([TransactionType.Credit, TransactionType.Adjustment], {
    required_error: 'Transaction type is required',
    invalid_type_error: 'Invalid transaction type',
  }),
  amount: z.number({ required_error: 'Amount is required' }).int().positive({ message: 'Amount must be positive' }),
});

export type UpdateUserBalancePayload = z.infer<typeof updateUserBalanceSchema>;
