import { z } from 'zod';
import { Gender, UserType } from '../entities/User';

export const updateUserSchema = z.object({
  firstName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  lastName: z.string().min(1).max(50).regex(new RegExp("^[a-zàâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ '-]+$", 'i')),
  email: z
    .string({ required_error: 'Email is required' })
    .toLowerCase()
    .regex(new RegExp(/^[\w-.+]+@([\w-]+\.)+[\w-]{2,3}$/), 'Please enter a valid email'),
  phone: z
    .string()
    .optional()
    .nullable()
    .transform(val => {
      if (!val) return val;
      return val.replaceAll(/[()\- ]/g, '');
    }),
  birthday: z.coerce.date({ required_error: 'Birthday is required' }).optional().nullable(),
  address: z
    .string()
    .max(255, { message: 'Must be 255 or fewer characters long' })
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  city: z
    .string()
    .max(50, { message: 'Must be 50 or fewer characters long' })
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  province: z
    .string()
    .max(50, { message: 'Must be 50 or fewer characters long' })
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  country: z
    .string()
    .max(50, { message: 'Must be 50 or fewer characters long' })
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  postalCode: z
    .string()
    .max(50, { message: 'Must be 50 or fewer characters long' })
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  licenseNumber: z
    .string()
    .regex(new RegExp('^[0-9]*$'))
    .max(100, { message: 'Must be 100 or fewer characters long' })
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  practiceSetting: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  employmentStatus: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
  specialtyId: z.coerce
    .number({ required_error: 'Specialty is required', invalid_type_error: 'Invalid specialty' })
    .int()
    .positive(),
  gender: z.nativeEnum(Gender).optional().nullable(),
  userType: z.nativeEnum(UserType).optional().nullable(),
});

export type UpdateUserPayload = z.infer<typeof updateUserSchema>;
