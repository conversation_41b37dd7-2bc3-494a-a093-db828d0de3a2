import { UserActionRepository } from '@/backend/users/repositories/user-action-repository';
import { dataSource } from '@/backend/database/data-source';
import { LogUserActionPayload } from '@/backend/users/types/user-action';
import { GetUserActionsPayload, UserActionsResponse } from '@/backend/users/validations/user-action';
import { Between } from 'typeorm';
import { subDays } from 'date-fns';

export const EntitiesName = {
  Users: 'Users',
  Companies: 'Companies',
  Specialties: 'Specialties',
  Audiences: 'Audiences',
  ReferralCodes: 'Referral Codes',
  UserAdmins: 'Dashboard Users',
  Permissions: 'Permissions',
  Surveys: 'Surveys',
};

export class UserActionService {
  private repository = new UserActionRepository(dataSource);
  constructor() {}

  public async logAction(data: LogUserActionPayload): Promise<void> {
    await this.repository.createQueryBuilder().insert().values(data).execute();
  }

  public async getLogActions({ from, to }: GetUserActionsPayload): Promise<UserActionsResponse> {
    let actions = [];
    if (!from || !to) {
      const [latest] = await this.repository.find({
        order: {
          createdAt: 'DESC',
        },
        take: 1,
      });
      if (!latest) {
        return {
          actions: [],
          from: subDays(new Date(), 2),
          to: new Date(),
        };
      }
      const customTo = latest.createdAt;
      customTo.setHours(23, 59, 59, 999);
      const customFrom = subDays(customTo, 2);
      customFrom.setHours(0, 0, 0, 0);
      actions = await this.repository.find({
        where: {
          createdAt: Between(customFrom, customTo),
        },
        relations: {
          user: true,
        },
        order: {
          createdAt: 'DESC',
        },
      });
      return {
        actions,
        from: customFrom,
        to: customTo,
      };
    }
    actions = await this.repository.find({
      where: {
        createdAt: Between(from, to),
      },
      relations: {
        user: true,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    return {
      actions,
    };
  }
}
