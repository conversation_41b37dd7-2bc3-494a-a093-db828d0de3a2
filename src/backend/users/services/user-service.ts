import { Audience } from '@/backend/audiences/entities/Audience';
import { AudienceService } from '@/backend/audiences/services/audience-service';
import { TokenService } from '@/backend/auth/services/token-service';
import { FirebaseErrorCode } from '@/backend/auth/types/auth';
import { UserRegistrationPayload } from '@/backend/auth/validations/app-user-registration';
import { BranchIOService } from '@/backend/branch-io/services';
import { dataSource } from '@/backend/database/data-source';
import { HubspotService } from '@/backend/hubspot/services/hubspot-service';
import {
  CompleteWS,
  EmailOptIn,
  HCPVerificationStatus,
  HubspotContact,
  HubspotContactStatus,
} from '@/backend/hubspot/types/contact';
import { StatusState } from '@/backend/hubspot/types/subscription';
import { ErrorCode, badRequestError, notFoundError } from '@/backend/middlewares/capture-errors';
import { NotificationService } from '@/backend/notifications/services';
import { PublicReferralCode, PublicReferralCodeStatus } from '@/backend/referral-codes/entities/PublicReferralCode';
import { PublicReferralCodeRepository } from '@/backend/referral-codes/repositories/public-referral-code-repository';
import { exportCsv, stringToBlob } from '@/backend/shared/utils/export-csv';
import { handleTransactionError } from '@/backend/shared/utils/handle-transaction-error';
import { logger } from '@/backend/shared/utils/logger';
import { delay } from '@/backend/shared/utils/promise';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { SpecialtyRepository } from '@/backend/specialties/repositories/specialty-repository';
import { SpecialtyService } from '@/backend/specialties/services/specialty-service';
import { SurveyService } from '@/backend/surveys/services/survey-service';
import { ContactStatus, User, UserType, VerificationStatus } from '@/backend/users/entities/User';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { ImportStatus, UserImportTask } from '@/backend/users/entities/UserImportTask';
import { PaymentMethodType } from '@/backend/users/entities/UserPaymentMethods';
import { UserReferralReward } from '@/backend/users/entities/UserReferralReward';
import { UserSubmission } from '@/backend/users/entities/UserSubmission';
import { TransactionType, UserTransaction } from '@/backend/users/entities/UserTransaction';
import { EmploymentStatusRepository } from '@/backend/users/repositories/employment-status-repository';
import { PracticeSettingRepository } from '@/backend/users/repositories/practice-setting-repository';
import { PublicSurveyAnswerRepository } from '@/backend/users/repositories/public-survey-answer-repository';
import { UserAdminRepository } from '@/backend/users/repositories/user-admin-repository';
import { UserDeviceRepository } from '@/backend/users/repositories/user-device-repository';
import { UserImportTaskRepository } from '@/backend/users/repositories/user-import-task-repository';
import { UserPaymentMethodRepository } from '@/backend/users/repositories/user-payment-method-repository';
import { UserReferralRewardRepository } from '@/backend/users/repositories/user-referral-reward-repository';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { UserSubmissionRepository } from '@/backend/users/repositories/user-submissions';
import { UserTransactionRepository } from '@/backend/users/repositories/user-transaction-repository';
import { RegionService } from '@/backend/users/services/region-service';
import { ImportUser, UserImportValidated } from '@/backend/users/types/import-user';
import { RewardUsersResponse } from '@/backend/users/types/reward-users';
import {
  IndustryInfo,
  UserRegistrationInfo,
  UserRegistrationResponse,
  UserRole,
  ValidateUserProfileResponse,
} from '@/backend/users/types/user';
import { CreateUserPayload } from '@/backend/users/validations/create-user';
import { EditUserInAudiencesPayload } from '@/backend/users/validations/edit-user-in-audiences';
import { EditUsersInAudiencePayload } from '@/backend/users/validations/edit-users-in-audience';
import { ExportUsersPayload } from '@/backend/users/validations/export-users';
import { ImportUsersPayload } from '@/backend/users/validations/import-user';
import { RequestUserInfoPayload } from '@/backend/users/validations/request-user-info';
import { UpdateNotePayload } from '@/backend/users/validations/update-note';
import { UpdateOnboardingInfoPayload } from '@/backend/users/validations/update-onboarding-info';
import { UpdateUserPayload } from '@/backend/users/validations/update-user';
import { UpdateUserBalancePayload } from '@/backend/users/validations/update-user-balance';
import { UpdateUserProfilePayload, ValidateUserProfilePayload } from '@/backend/users/validations/update-user-profile';
import { UpdateUserTypePayload } from '@/backend/users/validations/update-user-type';
import { VerifyUserPayload } from '@/backend/users/validations/verify-user';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';
import { waitUntil } from '@vercel/functions';
import { randomUUID } from 'crypto';
import { parse } from 'csv-parse/sync';
import { format } from 'date-fns';
import { auth } from 'firebase-admin';
import { DecodedIdToken } from 'firebase-admin/auth';
import _ from 'lodash';
import * as referralCodes from 'referral-codes';
import { Brackets, In, IsNull, LessThan, Not, QueryRunner } from 'typeorm';

export class UserService {
  private readonly tokenService = new TokenService();
  private readonly specialtyService = new SpecialtyService();
  private readonly hubspotService = new HubspotService();
  private readonly audienceService = new AudienceService();
  private readonly regionService = new RegionService();
  private readonly notificationService = new NotificationService();
  private readonly branchIOService = new BranchIOService();
  private readonly surveyService = new SurveyService();

  private readonly userRepository = new UserRepository(dataSource);
  private readonly userAdminRepository = new UserAdminRepository(dataSource);
  private readonly userReferralRewardRepository = new UserReferralRewardRepository(dataSource);
  private readonly userTransactionRepository = new UserTransactionRepository(dataSource);
  private readonly publicReferralCodeRepository = new PublicReferralCodeRepository(dataSource);
  private readonly employmentStatusRepository = new EmploymentStatusRepository(dataSource);
  private readonly practiceSettingRepository = new PracticeSettingRepository(dataSource);
  private readonly specialtyRepository = new SpecialtyRepository(dataSource);
  private readonly userDeviceRepository = new UserDeviceRepository(dataSource);
  private readonly userPaymentMethodRepository = new UserPaymentMethodRepository(dataSource);
  private readonly userImportTaskRepository = new UserImportTaskRepository(dataSource);
  private readonly publicSurveyAnswerRepository = new PublicSurveyAnswerRepository(dataSource);
  private readonly userSubmissionRepository = new UserSubmissionRepository(dataSource);

  private readonly log = logger.child({ description: 'UserService' });

  private SORTABLE_FIELDS: Array<keyof User> = [
    'id',
    'firstName',
    'email',
    'province',
    'createdAt',
    'updatedAt',
    'lastLogin',
    'specialty',
    'contactStatus',
    'verificationStatus',
    'isCompleteWS',
  ];

  constructor() {}

  public async getUsers(
    data: PaginationPayload<User>,
    prioritizedUserIds: number[] = [],
  ): Promise<PaginationResponseData<User>> {
    const { page, pageSize, search, sortBy, sortOrder, filters } = data;

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .where('user.deletedAt IS NULL')
      .leftJoinAndMapOne('user.specialty', Specialty, 'specialty', 'user.specialtyId = specialty.id');

    if (search) {
      queryBuilder.andWhere(
        "(CONCAT(NULLIF(user.firstName, ''), ' ', NULLIF(user.lastName, '')) ILIKE :search OR " +
          'user.email ILIKE :search OR user.phone ILIKE :search OR user.province ILIKE :search)',
        { search: `%${search.toLowerCase()}%` },
      );
    }

    if (prioritizedUserIds.length > 0) {
      queryBuilder
        .addSelect(`CASE WHEN user.id IN (:...prioritizedUserIds) THEN 1 ELSE 2 END`, 'priority_order')
        .setParameter('prioritizedUserIds', prioritizedUserIds)
        .addOrderBy('priority_order', 'ASC');
    }

    if (filters && Array.isArray(filters)) {
      const ALLOWED_FIELDS = ['verificationStatus', 'contactStatus', 'isCompleteWS', 'userType'];
      type ParsedFilter = {
        field: 'verificationStatus' | 'contactStatus' | 'isCompleteWS' | 'userType';
        operator: 'is' | 'isNot';
        values: (string | null)[];
      };

      const seenFields = new Set();

      const parsedFilters: ParsedFilter[] = Array.isArray(filters)
        ? filters
            .filter(filter => typeof filter === 'object' && filter !== null)
            .reduce((acc, filter) => {
              const { field, operator, value } = filter as {
                field: string;
                operator: string;
                value: string;
              };

              if (!field || !operator || !value) {
                return acc;
              }

              if (!ALLOWED_FIELDS.includes(field) || seenFields.has(field)) {
                return acc;
              }

              seenFields.add(field);
              const sanitizedValue = value ?? '';

              if (field === 'verificationStatus') {
                const validValues = sanitizedValue.split(',').map(v => v.trim());

                if (validValues.every(v => Object.values(VerificationStatus).includes(v as VerificationStatus))) {
                  acc.push({ field, operator, values: validValues });
                }
              } else if (field === 'contactStatus') {
                const validValues = sanitizedValue.split(',').map(v => v.trim());

                if (validValues.every(v => Object.values(ContactStatus).includes(v as ContactStatus))) {
                  acc.push({ field, operator, values: validValues });
                }
              } else if (field === 'isCompleteWS') {
                const boolValues = sanitizedValue.split(',').map(v => v.trim().toLowerCase() === 'true');

                acc.push({ field, operator, values: boolValues });
              } else if (field === 'userType') {
                const validValues = sanitizedValue.split(',').map(v => v.trim());

                if (validValues.every(v => Object.values(UserType).includes(v as UserType) || v === 'null')) {
                  const processedValues = validValues.map(v => (v === 'null' ? null : v));
                  acc.push({ field, operator, values: processedValues });
                }
              }

              return acc;
            }, [] as ParsedFilter[])
        : [];

      if (parsedFilters.length > 0) {
        parsedFilters.forEach(({ field, operator, values }) => {
          if (operator === 'is') {
            const nullCheck = values.includes(null);

            if (nullCheck) {
              queryBuilder.andWhere(
                new Brackets(qb => {
                  qb.where(`user.${field} IS NULL`);

                  if (values.length > 1) {
                    qb.orWhere(`user.${field} IN (:...${field})`, { [field]: values.filter(v => v !== null) });
                  }
                }),
              );
            } else {
              queryBuilder.andWhere(`user.${field} IN (:...${field})`, { [field]: values });
            }
          } else if (operator === 'isNot') {
            const nullCheck = values.includes(null);

            if (nullCheck) {
              queryBuilder.andWhere(
                new Brackets(qb => {
                  qb.where(`user.${field} IS NOT NULL`);

                  if (values.length > 1) {
                    qb.orWhere(`user.${field} NOT IN (:...${field})`, { [field]: values.filter(v => v !== null) });
                  }
                }),
              );
            } else {
              queryBuilder.andWhere(
                new Brackets(qb => {
                  qb.where(`user.${field} IS NULL`);
                  qb.orWhere(`user.${field} NOT IN (:...${field})`, { [field]: values });
                }),
              );
            }
          }
        });
      }
    }

    if (sortBy && this.SORTABLE_FIELDS.includes(sortBy)) {
      if (sortBy === 'specialty') {
        queryBuilder.addOrderBy('specialty.name', sortOrder);
      } else if (sortBy === 'lastLogin') {
        queryBuilder.addOrderBy('user.lastLogin', sortOrder, 'NULLS LAST');
      } else {
        queryBuilder.addOrderBy(`user.${sortBy}`, sortOrder);
      }
    } else {
      queryBuilder.addOrderBy('user.id', 'ASC');
    }

    return await withPagination(queryBuilder, page, pageSize);
  }

  async deleteUser(id: number): Promise<void> {
    const user = await this.userRepository.findOneById(id);

    try {
      if (user.firebaseUserId) {
        await auth()
          .deleteUser(user.firebaseUserId)
          .catch(error => {
            if (error.code !== FirebaseErrorCode.UserNotFound) {
              throw error;
            }
          });
      }

      const deletedAt = new Date();
      await this.userRepository.update(
        {
          id,
        },
        {
          email: `deleted-${deletedAt.getTime()}-${user.email}`,
          phone: `deleted-${deletedAt.getTime()}-${user.phone}`,
          deletedAt,
          firebaseUserId: user.firebaseUserId ? null : undefined,
        },
      );

      waitUntil(this.syncAudiencesAndSpecialties(id));
    } catch (error) {
      const msg = 'Failed to delete user';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }
  }

  async getUser(id: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id, deletedAt: IsNull() },
      relations: {
        userReferralReward: {
          user: true,
          referredByUser: true,
          publicReferralCode: true,
        },
      },
    });

    if (!user) {
      throw notFoundError('User not found');
    }

    return user;
  }

  async updateUser(id: number, data: UpdateUserPayload): Promise<User> {
    const user = await this.userRepository.findOneBy({ id, deletedAt: IsNull() });

    if (!user) {
      throw notFoundError('User not found');
    }

    if (!user.hubspotContactId || !user.hubspotContactId) {
      throw badRequestError('Please wait for the user to be created in Hubspot');
    }

    const response = await this.checkUserProfile(user, data.email, data.phone);
    if (response.isEmailExist) {
      throw badRequestError('Email already taken', ErrorCode.AlreadyExists);
    }

    if (response.isPhoneExist) {
      throw badRequestError('Phone number already taken', ErrorCode.AlreadyExists);
    }

    if (data.phone === null && user.firebaseUserId) {
      await auth()
        .deleteUser(user.firebaseUserId)
        .catch(error => {
          if (error.code !== FirebaseErrorCode.UserNotFound) {
            throw error;
          }
        });
    }

    const specialty = await this.specialtyService.getById(data.specialtyId);

    let contactStatus = user.contactStatus;
    let isCompleteWS = user.isCompleteWS;
    if (this.isSkipOnboardingSurvey({ ...user, ...data } as User)) {
      if (
        user.verificationStatus === VerificationStatus.Verified ||
        user.verificationStatus === VerificationStatus.Denied
      ) {
        contactStatus = ContactStatus.Complete;
      }

      if (user.verificationStatus === VerificationStatus.Unverified) {
        contactStatus = ContactStatus.ReviewInfo;
      }

      if (!user.isCompleteWS) {
        isCompleteWS = true;
      }
    } else {
      isCompleteWS = false;
      contactStatus = ContactStatus.WSPending;
    }

    await this.hubspotService.updateContact(user.hubspotContactId!, {
      email: data.email && user.email !== data.email ? data.email : undefined,
      phone: data.phone && user.phone !== data.phone ? data.phone : data.phone === null ? '' : undefined,
      firstname: data.firstName,
      lastname: data.lastName,
      practice_setting: this.sanitizeField(data.practiceSetting),
      employment_status: this.sanitizeField(data.employmentStatus),
      hcp_specialty: specialty.name,
      hcp_registration_number: this.sanitizeField(data.licenseNumber),
      hcp_registration_province: this.sanitizeField(data.province),
      industrii_complete_ws: isCompleteWS ? CompleteWS.True : CompleteWS.False,
      industrii_contact_status: this.hubspotService.mapContactStatusToHubspot(contactStatus!),
      verification_status: this.hubspotService.mapVerificationStatusToHubspot(user.verificationStatus!, isCompleteWS),
    });

    // Update EFT email when update email profile
    if (data.email && data.email !== user.email) {
      await this.userPaymentMethodRepository.update(
        {
          userId: user.id,
          type: PaymentMethodType.Etransfer,
        },
        {
          email: data.email,
        },
      );
    }

    const tempFirebaseUserId = `temp-${randomUUID()}`;

    const updatedUser = await this.userRepository.save({
      ...user,
      ...data,
      firebaseUserId:
        data.phone && data.phone !== user.phone ? tempFirebaseUserId : data.phone === null ? null : undefined,
      contactStatus,
      isCompleteWS,
    });

    waitUntil(
      (async (): Promise<void> => {
        if (data.city && data.city !== user.city) {
          const province = await this.regionService.getProvince(data.province || user.province);
          await this.regionService.addCity(data.city, province ? province.id : null);
        }

        if (updatedUser.isCompleteWS) {
          const referralReward = await this.userReferralRewardRepository.findOneBy({
            userId: user.id,
          });

          if (referralReward) {
            const flag = updatedUser.verificationStatus === VerificationStatus.Verified;
            const reward = await this.rewardUsers(updatedUser, referralReward, flag);

            if (referralReward.referredBy && reward.isReferralUserRewarded && reward.amount > 0) {
              await this.notificationService.notifyReferralBonus(referralReward.referredBy, user, reward.amount);
            }
          }
        }
      })(),
    );
    waitUntil(this.syncAudiencesAndSpecialties(null));

    return updatedUser;
  }

  async exportUsers(data: ExportUsersPayload): Promise<Blob> {
    const queryBuilder = this.userRepository
      .createQueryBuilder('users')
      .leftJoinAndMapOne('users.specialty', Specialty, 's', 'users.specialtyId = s.id')
      .leftJoinAndMapOne('users.transaction', UserTransaction, 'tx', 'users.id = tx.userId AND tx.type = :type', {
        type: TransactionType.ReferralReward,
      });

    if (data.ids && data.ids.length > 0) {
      queryBuilder.where('users.deletedAt IS NULL AND users.id IN (:...ids)', {
        ids: data.ids,
      });
    } else {
      queryBuilder.where('users.deletedAt IS NULL');
    }

    const users = await queryBuilder.getMany();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const exportData = users.map((user: any) => {
      return {
        id: user.id,
        first_name: user.firstName,
        last_name: user.lastName,
        email: user.email,
        phone: user.phone,
        license_number: user.licenseNumber,
        address: user.address,
        city: user.city,
        province: user.province,
        country: user.country,
        postal_code: user.postalCode,
        birthday: user.birthday,
        practice_setting: user.practiceSetting,
        employment_status: user.employmentStatus,
        specialty: user.specialty ? user.specialty.name : null,
        date_joined: format(user.createdAt, 'dd/MM/yyyy'),
        verify_status:
          user.verificationStatus === VerificationStatus.Verified
            ? 'Verified'
            : user.verificationStatus === VerificationStatus.Unverified
              ? 'Unverified'
              : 'Denied',
        contact_status: this.hubspotService.mapContactStatusToHubspot(user.contactStatus),
        last_login: user.lastLogin ? format(user.lastLogin, 'dd/MM/yyyy') : null,
        completed_welcome_survey: user.isCompleteWS ? 'Yes' : 'No',
        referral_code_used: user.transaction ? user.transaction.code : null,
        gender: user.gender ? user.gender : null,
        user_role: user.userType ? user.userType : null,
      };
    });

    const csv = await exportCsv(exportData);
    return stringToBlob(csv as string);
  }

  public async validateAppUserEmail(email: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { email, firebaseUserId: Not(IsNull()) },
    });

    return !!user;
  }

  public async validateAppUserInfo(email: string, phone: string, referralCode?: string): Promise<UserRegistrationInfo> {
    const userQuery = this.userRepository.findOne({
      where: [
        { phone, firebaseUserId: Not(IsNull()) },
        { email, firebaseUserId: Not(IsNull()) },
      ],
    });

    const adminQuery = this.userAdminRepository.findOne({
      where: [{ email }, { phone }],
    });

    const referralUserQuery = referralCode
      ? this.userRepository.findOne({
          where: { referralCode, specialtyId: Not(IsNull()), deletedAt: IsNull() },
          relations: ['specialty'],
        })
      : null;

    const publicReferralQuery = referralCode
      ? this.publicReferralCodeRepository.findOneBy({
          code: referralCode,
        })
      : null;

    const [user, admin, referralUser, publicReferralCode] = await Promise.all([
      userQuery,
      adminQuery,
      referralUserQuery,
      publicReferralQuery,
    ]);

    let isPublicRefCodeValid = true;
    if (publicReferralCode) {
      if (
        (publicReferralCode.expiryDate && publicReferralCode.expiryDate <= new Date()) ||
        publicReferralCode.status === PublicReferralCodeStatus.Disabled
      ) {
        isPublicRefCodeValid = false;
      }

      if (publicReferralCode.maxUses) {
        const count = await this.userReferralRewardRepository.count({
          where: {
            publicReferralCodeId: publicReferralCode.id,
          },
        });

        if (publicReferralCode.maxUses <= count) {
          isPublicRefCodeValid = false;
        }
      }
    }

    const isEmailExist = user && user.email === email ? true : admin && admin.email === email ? true : false;
    const isPhoneExist = user && user.phone === phone ? true : admin && admin.phone === phone ? true : false;

    return {
      isEmailExist,
      isPhoneExist,
      referralUser,
      publicReferralCode: publicReferralCode && isPublicRefCodeValid ? publicReferralCode : null,
    };
  }

  private async verifyIdToken(idToken: string, phone: string): Promise<DecodedIdToken> {
    let decoded: DecodedIdToken;

    try {
      decoded = await auth().verifyIdToken(idToken);
    } catch (error) {
      const msg = 'Invalid ID token';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }

    if (decoded.phone_number !== phone) {
      throw badRequestError('Phone and ID token does not match');
    }

    return decoded;
  }

  public async registerAppUser(data: UserRegistrationPayload): Promise<UserRegistrationResponse> {
    const { email, phone, referralCode, idToken } = data;
    const decoded = await this.verifyIdToken(idToken, phone);

    const { isEmailExist, isPhoneExist, referralUser, publicReferralCode } = await this.validateAppUserInfo(
      email,
      phone,
      referralCode,
    );

    if (isEmailExist) {
      throw badRequestError('Email already exists');
    }

    if (isPhoneExist) {
      throw badRequestError('Phone number already exists');
    }

    if (referralCode && !referralUser && !publicReferralCode) {
      throw badRequestError('Invalid referral code');
    }

    const firebaseUId = decoded.uid;

    const user = await this.createAppUser(data, firebaseUId, referralUser, publicReferralCode);
    const { accessToken, refreshToken } = this.tokenService.generateIndustriiTokens({
      userId: user.id,
      email,
      phone,
      role: UserRole.User,
    });

    waitUntil(
      (async (): Promise<void> => {
        // Generate default Interac e-Transfer payment method for user
        const userPaymentMethod = await this.userPaymentMethodRepository.findOneBy({
          userId: user.id,
          type: PaymentMethodType.Etransfer,
        });

        if (!userPaymentMethod) {
          await this.userPaymentMethodRepository.save({
            userId: user.id,
            type: PaymentMethodType.Etransfer,
            email: data.email,
          });
        }
      })(),
    );

    const specialty = user.specialtyId
      ? await this.specialtyRepository.findOneBy({
          id: user.specialtyId,
        })
      : null;

    const isReferral = !!referralUser || !!publicReferralCode;
    const refUserInfo = user.isCompleteWS
      ? {
          firstName: referralUser ? (referralUser.firstName ? referralUser.firstName : 'Unknown') : null,
          lastName: referralUser ? (referralUser.lastName ? referralUser.lastName : 'Unknown') : null,
          refValue: specialty ? specialty.referralValue : publicReferralCode ? publicReferralCode.value : null,
        }
      : null;

    if (user.isCompleteWS && isReferral) {
      await this.userReferralRewardRepository.update(
        {
          userId: user.id,
        },
        {
          isCongratulationDisplayed: true,
        },
      );
    }

    return {
      accessToken,
      refreshToken,
      isReferral,
      refUserInfo,
    };
  }

  private async createAppUser(
    data: UserRegistrationPayload,
    firebaseUId: string,
    referralUser: User | null,
    publicReferralCode: PublicReferralCode | null,
  ): Promise<User> {
    const oldUser = await this.userRepository.findOneBy({
      email: data.email,
    });

    let newUser: User;
    if (oldUser) {
      newUser = await this.updateOldAppUser(data, oldUser, firebaseUId, referralUser, publicReferralCode);
    } else {
      newUser = await this.createNewAppUser(data, firebaseUId, referralUser, publicReferralCode);
    }

    waitUntil(
      (async (): Promise<void> => {
        if (referralUser) {
          const user = await this.userRepository.findOneById(newUser.id);

          if (user && user.specialtyId) {
            const referralReward = await this.userReferralRewardRepository.findOneBy({
              userId: user.id,
            });

            if (referralReward) {
              const flag = user.verificationStatus === VerificationStatus.Verified;
              const reward = await this.rewardUsers(user, referralReward, flag);

              if (referralReward.referredBy && reward.isReferralUserRewarded && reward.amount > 0) {
                await this.notificationService.notifyReferralBonus(referralReward.referredBy, user, reward.amount);
              }
            }
          }
        }
      })(),
    );

    return newUser;
  }

  private async updateOldAppUser(
    data: UserRegistrationPayload,
    user: User,
    firebaseUId: string,
    referralUser: User | null,
    publicReferralCode: PublicReferralCode | null,
  ): Promise<User> {
    const { firstName, lastName, email, phone, preferredLanguage, isEmailOptIn, notificationEnabled } = data;
    const isSkipOnboardingSurvey = this.isSkipOnboardingSurvey(user);

    const { contactId, ctcProductId } = await this.hubspotService.syncUserRegistration({
      firstname: firstName,
      lastname: lastName,
      email,
      phone,
      hs_language: data.preferredLanguage,
      industrii_email_opt_in: data.isEmailOptIn ? EmailOptIn.True : EmailOptIn.False,
      verification_status:
        user.verificationStatus === VerificationStatus.Verified
          ? undefined
          : isSkipOnboardingSurvey
            ? undefined
            : HCPVerificationStatus.NotStarted,
      industrii_contact_status:
        user.verificationStatus === VerificationStatus.Verified
          ? undefined
          : isSkipOnboardingSurvey
            ? undefined
            : HubspotContactStatus.WSPending,
      industrii_complete_ws:
        user.verificationStatus === VerificationStatus.Verified
          ? undefined
          : isSkipOnboardingSurvey
            ? undefined
            : CompleteWS.False,
      industrii_last_sign_up: this.hubspotService.convertToHubspotDate(new Date()),
    });

    let referralCode = user.referralCode;
    let referralLink = user.referralLink;
    if (!user.referralCode) {
      referralCode = await this.generateReferralCode(firstName);
      referralLink = await this.branchIOService.createReferralDeepLink({
        custom_data: {
          referral_code: referralCode,
        },
      });
    }

    await this.userRepository.update(
      {
        id: user.id,
      },
      {
        phone,
        firstName,
        lastName,
        firebaseUserId: firebaseUId,
        preferredLanguage,
        hubspotContactId: contactId,
        hubspotProductId: ctcProductId,
        isEmailOptIn,
        notificationEnabled,
        lastLogin: new Date(),
        referralCode,
        referralLink,
      },
    );

    await this.userRepository.update(
      {
        id: Not(user.id),
        phone,
      },
      {
        phone: null,
      },
    );

    const isPreVerifiedUser = true;
    await this.createUserReward(user.id, isPreVerifiedUser, referralUser, publicReferralCode);

    return user;
  }

  private async createNewAppUser(
    data: UserRegistrationPayload,
    firebaseUId: string,
    referralUser: User | null,
    publicReferralCode: PublicReferralCode | null,
  ): Promise<User> {
    const { firstName, lastName, email, phone, preferredLanguage, isEmailOptIn, notificationEnabled } = data;

    const { contactId, ctcProductId } = await this.hubspotService.syncUserRegistration({
      firstname: firstName,
      lastname: lastName,
      email,
      phone,
      hs_language: data.preferredLanguage,
      industrii_email_opt_in: data.isEmailOptIn ? EmailOptIn.True : EmailOptIn.False,
      verification_status: HCPVerificationStatus.NotStarted,
      industrii_contact_status: HubspotContactStatus.WSPending,
      industrii_complete_ws: CompleteWS.False,
      industrii_last_sign_up: this.hubspotService.convertToHubspotDate(new Date()),
    });

    const referralCode = await this.generateReferralCode(firstName);
    const referralLink = await this.branchIOService.createReferralDeepLink({
      custom_data: {
        referral_code: referralCode,
      },
    });

    const newUser = await this.userRepository.save({
      email,
      phone,
      firstName,
      lastName,
      firebaseUserId: firebaseUId,
      referralCode,
      referralLink,
      preferredLanguage,
      isEmailOptIn,
      notificationEnabled,
      verificationStatus: VerificationStatus.Unverified,
      contactStatus: ContactStatus.WSPending,
      lastLogin: new Date(),
      isCompleteWS: false,
      hubspotContactId: contactId,
      hubspotProductId: ctcProductId,
    });

    await this.userRepository.update(
      {
        id: Not(newUser.id),
        phone,
      },
      {
        phone: null,
      },
    );

    const isPreVerifiedUser = false;
    await this.createUserReward(newUser.id, isPreVerifiedUser, referralUser, publicReferralCode);

    return newUser;
  }

  private async generateReferralCode(firstName: string): Promise<string> {
    // Referral code takes the first 4 letters of name + 4 random string
    const formattedName = firstName.replace(/[^a-zA-Z]/g, '');
    const firstFourChars = formattedName.length >= 4 ? formattedName.slice(0, 4) : formattedName;
    const length = 8 - firstFourChars.length;

    let exists = false;
    let refCode;

    do {
      refCode = referralCodes
        .generate({
          length,
          count: 1,
          prefix: firstFourChars,
        })[0]
        .toUpperCase();

      exists = !!(await this.userRepository.findOneBy({ referralCode: refCode }));
    } while (exists);

    return refCode;
  }

  private async createUserReward(
    userId: number,
    isPreVerifiedUser: boolean,
    referralUser: User | null,
    publicReferralCode: PublicReferralCode | null,
  ): Promise<void> {
    if (publicReferralCode) {
      const amount = publicReferralCode.value;
      await this.handlePublicCode(publicReferralCode.id, amount, userId, isPreVerifiedUser);
      return;
    }

    if (referralUser) {
      const referredBy = referralUser ? referralUser.id : null;
      if (referredBy) {
        await this.userReferralRewardRepository
          .createQueryBuilder()
          .insert()
          .values({
            userId,
            referredBy,
            isUserRewarded: false,
            isReferralUserRewarded: false,
          })
          .execute();
      }
      return;
    }
  }

  private async handlePublicCode(
    codeId: number,
    amount: number,
    userId: number,
    isPreVerifiedUser: boolean,
  ): Promise<void> {
    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await this.validatePublicRefCode(queryRunner, codeId);
      await queryRunner.manager.increment(PublicReferralCode, { id: codeId }, 'usages', 1);

      await queryRunner.manager
        .getRepository(UserReferralReward)
        .createQueryBuilder()
        .insert()
        .values({
          userId,
          publicReferralCodeId: codeId,
          isUserRewarded: false,
          amount,
        })
        .execute();

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (!isPreVerifiedUser) {
        await queryRunner.manager.delete(User, { id: userId });
      }

      handleTransactionError(error, 'Failed to reward user');
      throw error; // Throw error to satisfy function return type
    } finally {
      await queryRunner.release();
    }
  }

  private async validatePublicRefCode(queryRunner: QueryRunner, codeId: number): Promise<PublicReferralCode> {
    const refCode = await queryRunner.manager
      .getRepository(PublicReferralCode)
      .createQueryBuilder('public_referral_codes')
      .useTransaction(true)
      .setLock('pessimistic_write')
      .where('public_referral_codes.id = :codeId', { codeId })
      .getOne();

    if (!refCode) {
      throw badRequestError('Referral code not found');
    }

    if (refCode.expiryDate && refCode.expiryDate <= new Date()) {
      throw badRequestError('Referral code is expired');
    }

    if (refCode.status === PublicReferralCodeStatus.Disabled) {
      throw badRequestError('Referral code is disabled');
    }

    if (refCode.maxUses && refCode.usages >= refCode.maxUses) {
      throw badRequestError('Referral code is exhausted');
    }

    return refCode;
  }

  public async checkPhoneWhenLogin(phone: string): Promise<boolean> {
    const user = await this.userRepository.findOneBy({ phone, firebaseUserId: Not(IsNull()) });
    return !!user;
  }

  public async checkPhoneExist(phone: string): Promise<boolean> {
    const [user, adminUser] = await Promise.all([
      this.userRepository.findOneBy({ phone }),
      this.userAdminRepository.findOneBy({ phone }),
    ]);

    if (user || adminUser) {
      return true;
    }

    return false;
  }

  public async me(id: number, role: string): Promise<User | UserAdmin> {
    const user =
      role === UserRole.User
        ? await this.userRepository.findOne({
            where: {
              id,
            },
            relations: ['specialty'],
          })
        : await this.userAdminRepository.findOneBy({ id });

    if (!user) {
      throw notFoundError('User not found');
    }

    return user;
  }

  public async getIndustryInfo(): Promise<IndustryInfo> {
    const [employmentStatus, practiceSettings, specialties] = await Promise.all([
      this.employmentStatusRepository.find({
        order: {
          name: 'ASC',
        },
      }),
      this.practiceSettingRepository.find({
        order: {
          name: 'ASC',
        },
      }),
      this.specialtyRepository.find({
        where: {
          deletedAt: IsNull(),
          name: Not('DEMO'), // Hard code to exclude DEMO specialty in PROD
        },
        relations: {
          translations: true,
        },
        order: {
          name: 'ASC',
        },
      }),
    ]);

    // Sort specialties to move "Other" to the end
    const sortedSpecialties = [...specialties].sort((a, b) => {
      if (a.name.toLowerCase().trim() === 'other') return 1;
      if (b.name.toLowerCase().trim() === 'other') return -1;
      return a.name.localeCompare(b.name);
    });

    return { employmentStatus, practiceSettings, specialties: sortedSpecialties };
  }

  public async updateOnboardingInfo(id: number, data: UpdateOnboardingInfoPayload): Promise<Record<string, unknown>> {
    const user = await this.userRepository.findOneById(id);

    if (
      (!user.gender && !data.gender) ||
      (!user.birthday && !data.birthday) ||
      (!user.address && !data.address) ||
      (!user.city && !data.city) ||
      (!user.province && !data.province) ||
      (!user.country && !data.country) ||
      (!user.postalCode && !data.postalCode) ||
      (!user.licenseNumber && !data.licenseNumber) ||
      (!user.specialtyId && !data.specialtyId) ||
      (!user.practiceSetting && !data.practiceSettingId) ||
      (!user.employmentStatus && !data.employmentStatusId)
    ) {
      throw badRequestError('Missing user info');
    }

    const [practiceSetting, employmentStatus, specialty] = await Promise.all([
      data.practiceSettingId ? this.practiceSettingRepository.findOneBy({ id: data.practiceSettingId }) : null,
      data.employmentStatusId ? this.employmentStatusRepository.findOneBy({ id: data.employmentStatusId }) : null,
      data.specialtyId
        ? this.specialtyRepository.findOneBy({ id: data.specialtyId, deletedAt: IsNull() })
        : this.specialtyRepository.findOneBy({ id: user.specialtyId!, deletedAt: IsNull() }),
    ]);

    if (data.practiceSettingId && !practiceSetting) {
      throw notFoundError('Practice setting not found');
    }

    if (data.employmentStatusId && !employmentStatus) {
      throw notFoundError('Employment status not found');
    }

    if (!specialty) {
      throw notFoundError('Specialty not found');
    }

    const verification_status =
      user.verificationStatus === VerificationStatus.Verified
        ? HCPVerificationStatus.Verified
        : user.verificationStatus === VerificationStatus.Denied
          ? HCPVerificationStatus.Rejected
          : HCPVerificationStatus.InReview;

    const industrii_contact_status =
      user.verificationStatus === VerificationStatus.Verified || user.verificationStatus === VerificationStatus.Denied
        ? HubspotContactStatus.Complete
        : HubspotContactStatus.ReviewInfo;

    await this.hubspotService.syncVerificationRequest(user.hubspotContactId!, user.hubspotProductId!, {
      employment_status: employmentStatus ? employmentStatus.name : user.employmentStatus ?? undefined,
      practice_setting: practiceSetting ? practiceSetting.name : user.practiceSetting ?? undefined,
      hcp_specialty: specialty.name,
      hcp_registration_number: data.licenseNumber ? data.licenseNumber : user.licenseNumber ?? undefined,
      hcp_registration_province: data.province ? data.province : user.province ?? undefined,
      verification_status,
      industrii_contact_status,
      industrii_complete_ws: CompleteWS.True,
    });

    const contactStatus =
      user.verificationStatus === VerificationStatus.Verified || user.verificationStatus === VerificationStatus.Denied
        ? ContactStatus.Complete
        : ContactStatus.ReviewInfo;
    const updatedUser = await this.userRepository.save({
      ...user,
      ...data,
      employmentStatus: employmentStatus ? employmentStatus.name : user.employmentStatus,
      practiceSetting: practiceSetting ? practiceSetting.name : user.practiceSetting,
      specialtyId: specialty.id,
      contactStatus,
      isCompleteWS: true,
    });

    const referralReward = await this.userReferralRewardRepository.findOneBy({
      userId: user.id,
    });

    waitUntil(
      (async (): Promise<void> => {
        if (referralReward) {
          const flag = user.verificationStatus === VerificationStatus.Verified;
          const reward = await this.rewardUsers(updatedUser, referralReward, flag);

          if (referralReward.referredBy && reward.isReferralUserRewarded && reward.amount > 0) {
            await this.notificationService.notifyReferralBonus(referralReward.referredBy, user, reward.amount);
          }
        }

        if (updatedUser.verificationStatus === VerificationStatus.Verified) {
          await this.generateUserSurveys(updatedUser);
        }

        if (data.canadaPostId) {
          await this.regionService.addLocation(data.canadaPostId);
        }
      })(),
    );

    const isReferral = referralReward && !referralReward.isCongratulationDisplayed ? true : false;
    const referralUser =
      referralReward && referralReward.referredBy && !referralReward.isCongratulationDisplayed
        ? await this.userRepository.findOneBy({ id: referralReward.referredBy })
        : null;

    const refUserInfo =
      isReferral && referralReward
        ? {
            firstName: referralUser ? (referralUser.firstName ? referralUser.firstName : 'Unknown') : null,
            lastName: referralUser ? (referralUser.lastName ? referralUser.lastName : 'Unknown') : null,
            refValue: referralReward.amount ? referralReward.amount : specialty ? specialty.referralValue : null,
          }
        : null;

    await this.userReferralRewardRepository.update(
      {
        userId: id,
        isCongratulationDisplayed: false,
      },
      {
        isCongratulationDisplayed: true,
      },
    );

    waitUntil(this.syncAudiencesAndSpecialties(id));

    return {
      ...updatedUser,
      isReferral,
      refUserInfo,
    };
  }

  public async adminAddUser(data: CreateUserPayload): Promise<User> {
    const { email, firstName, lastName, phone } = data;

    const [user, userAdmin, specialty] = await Promise.all([
      this.userRepository.findOne({ where: [{ email }, { phone }] }),
      this.userAdminRepository.findOne({ where: [{ email }, { phone }] }),
      this.specialtyRepository.findOneBy({ id: data.specialtyId, deletedAt: IsNull() }),
    ]);

    if (user || userAdmin) {
      throw badRequestError('User already exists', ErrorCode.AlreadyExists);
    }

    if (!specialty) {
      throw notFoundError('Specialty not found');
    }

    const signUpLink = await this.branchIOService.createSignUpDeepLink({
      custom_data: {
        isPreVerified: true,
        email,
        first_name: firstName,
        last_name: lastName,
      },
    });

    const { contactId, ctcProductId } = await this.hubspotService.syncPreVerifiedUser(
      {
        email,
        firstName,
        lastName,
        practice_setting: this.sanitizeField(data.practiceSetting),
        employment_status: this.sanitizeField(data.employmentStatus),
        hcp_specialty: specialty.name,
        verification_status: HCPVerificationStatus.NotStarted,
        industrii_contact_status: HubspotContactStatus.WSPending,
        industrii_complete_ws: CompleteWS.False,
      },
      signUpLink,
    );

    const referralCode = await this.generateReferralCode(firstName);
    const referralLink = await this.branchIOService.createReferralDeepLink({
      custom_data: {
        referral_code: referralCode,
      },
    });

    const newUser = await this.userRepository.save({
      ...data,
      referralCode,
      referralLink,
      hubspotContactId: contactId,
      hubspotProductId: ctcProductId,
      verificationStatus: VerificationStatus.Unverified,
      contactStatus: ContactStatus.WSPending,
      isCompleteWS: false,
    });

    await this.userPaymentMethodRepository.save({
      userId: newUser.id,
      type: PaymentMethodType.Etransfer,
      email: data.email,
    });

    if (this.isSkipOnboardingSurvey(newUser)) {
      await this.userRepository.update(
        {
          id: newUser.id,
        },
        {
          contactStatus: ContactStatus.ReviewInfo,
          isCompleteWS: true,
        },
      );

      waitUntil(
        (async (): Promise<void> => {
          await this.hubspotService.syncVerificationRequest(
            contactId,
            ctcProductId,
            {
              practice_setting: newUser.practiceSetting ?? undefined,
              employment_status: newUser.employmentStatus ?? undefined,
              hcp_specialty: specialty.name,
              hcp_registration_number: newUser.licenseNumber!,
              hcp_registration_province: newUser.province!,
              verification_status: HCPVerificationStatus.InReview,
              industrii_contact_status: HubspotContactStatus.ReviewInfo,
              industrii_complete_ws: CompleteWS.True,
            },
            false,
          );

          if (data.canadaPostId) {
            await this.regionService.addLocation(data.canadaPostId);
          }
        })(),
      );
    }

    waitUntil(this.syncAudiencesAndSpecialties(newUser.id));
    return newUser;
  }

  public async validateCsvImportUser(csvContent: string): Promise<UserImportValidated[]> {
    const records: ImportUser[] = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      delimiter: ',',
    });

    const [specialties, practiceSettings, employmentStatuses, users, userAdmins] = await Promise.all([
      this.specialtyService.getAllSpecialties(),
      this.practiceSettingRepository.find(),
      this.employmentStatusRepository.find(),
      this.userRepository.find({
        select: ['email'],
      }),
      this.userAdminRepository.find({
        select: ['email'],
      }),
    ]);

    const specialtiesMap = new Map(specialties.map(specialty => [specialty.name, specialty.id]));
    const practiceSettingsMap = practiceSettings.map(p => p.name);
    const employmentStatusMap = employmentStatuses.map(es => es.name);
    const existingEmails = new Set([...users.map(user => user.email), ...userAdmins.map(admin => admin.email)]);

    const results: UserImportValidated[] = [];

    for (const record of records) {
      const {
        first_name,
        last_name,
        email,
        specialty,
        birthday,
        province,
        employment_status,
        practice_setting,
        user_role,
      } = record;
      const errors: string[] = [];

      if (!first_name) errors.push('first_name');
      if (!last_name) errors.push('last_name');

      const specialtyId = specialtiesMap.get(specialty);
      if (!specialtyId) errors.push('specialtyId');

      if (!Object.values(UserType).includes(user_role as UserType)) {
        errors.push('user_role');
      }

      const practiceSetting = practiceSettingsMap.find(p => p === practice_setting);
      const employmentStatus = employmentStatusMap.find(e => e === employment_status);

      const isDuplicated = email && existingEmails.has(email.toLowerCase());
      let existedUser: User | UserAdmin | null = null;
      if (isDuplicated) {
        const [user, userAdmin] = await Promise.all([
          this.userRepository.findOne({
            where: { email: email.toLowerCase() },
            select: [
              'firstName',
              'lastName',
              'email',
              'specialtyId',
              'birthday',
              'province',
              'practiceSetting',
              'employmentStatus',
              'userType',
            ],
          }),
          this.userAdminRepository.findOne({
            where: { email: email.toLowerCase() },
            select: ['firstName', 'lastName', 'email'],
          }),
        ]);
        existedUser = user || userAdmin;
      }

      results.push({
        user: {
          firstName: first_name,
          lastName: last_name,
          email,
          specialtyId,
          birthday: isNaN(new Date(birthday).getTime()) ? undefined : birthday,
          province,
          practiceSetting,
          employmentStatus,
          userType: user_role as UserType,
        },
        errors,
        existedUser,
      });
    }

    return results;
  }

  public async importUsers(data: ImportUsersPayload): Promise<void> {
    const emails = data.users.map(user => user.email);
    const uniqueEmails = new Set(emails);

    if (emails.length !== uniqueEmails.size) {
      throw badRequestError('No duplicates email is allowed');
    }

    const [users, admins] = await Promise.all([
      this.userRepository.find({
        where: {
          email: In(emails),
          deletedAt: IsNull(),
        },
        select: [
          'email',
          'firstName',
          'lastName',
          'gender',
          'birthday',
          'address',
          'city',
          'province',
          'country',
          'postalCode',
          'licenseNumber',
          'employmentStatus',
          'practiceSetting',
          'specialtyId',
          'userType',
        ],
      }),
      this.userAdminRepository.find({
        select: ['email'],
        where: {
          deletedAt: IsNull(),
        },
      }),
    ]);

    const isAdminExist = admins.find(admin => {
      return admin.email ? uniqueEmails.has(admin.email) : false;
    });

    if (isAdminExist) {
      throw badRequestError('Email is already existed as Admin');
    }

    const usersMap = new Map(users.map(user => [user.email, user]));
    const usersToInsert = data.users.map(user => {
      const existingUser = usersMap.get(user.email);
      if (existingUser) {
        const mergedUser = {
          ...existingUser,
          ...user,
        };

        const isCompleteWS = this.isSkipOnboardingSurvey(mergedUser);

        return {
          ...mergedUser,
          verificationStatus: VerificationStatus.Verified,
          isVerified: true,
          contactStatus: isCompleteWS
            ? ContactStatus.Complete
            : existingUser.contactStatus === ContactStatus.WaitForInfo ||
                existingUser.contactStatus === ContactStatus.ReviewInfo
              ? existingUser.contactStatus
              : ContactStatus.WSPending,
          isCompleteWS,
        };
      }

      return {
        ...user,
        verificationStatus: VerificationStatus.Verified,
        isVerified: true,
        contactStatus: ContactStatus.WSPending,
        isCompleteWS: false,
      };
    });

    const result = await this.userRepository.upsert(usersToInsert, ['email']);
    const insertedIds = result.identifiers.map(x => ({
      userId: x.id,
    }));
    await this.userImportTaskRepository.insert(insertedIds);

    waitUntil(this.syncAudiencesAndSpecialties(null));
  }

  public async processImportTask(): Promise<void> {
    const MAX_TASKS_PER_RUN = 30;
    const BATCH_SIZE = 10;
    const MAX_RETRIES = 5;

    const tasks = await this.userImportTaskRepository.find({
      where: {
        status: ImportStatus.Open,
        count: LessThan(MAX_RETRIES),
      },
      take: MAX_TASKS_PER_RUN,
    });

    if (tasks.length === 0) {
      return;
    }

    const taskIds = tasks.map(task => task.id);
    await this.userImportTaskRepository.update(
      { id: In(taskIds) },
      { status: ImportStatus.InProgress, count: () => 'count + 1' },
    );

    const batches = _.chunk(tasks, BATCH_SIZE);

    for (const batch of batches) {
      await Promise.all(batch.map(task => this.syncImportedUserToHubspot(task)));

      await delay(5000);
    }
  }

  private async syncImportedUserToHubspot(task: UserImportTask): Promise<void> {
    try {
      const user = await this.userRepository.findOneBy({ id: task.userId });

      if (!user || (user && user.deletedAt !== null)) {
        await this.userImportTaskRepository.update({ id: task.id }, { status: ImportStatus.Done });
        return;
      }

      const specialty = await this.specialtyService.getById(user.specialtyId!);

      const referralCode = user.referralCode ? undefined : await this.generateReferralCode(user.firstName!);
      const referralLink = referralCode
        ? await this.branchIOService.createReferralDeepLink({
            custom_data: {
              referral_code: referralCode,
            },
          })
        : undefined;

      const signUpLink = await this.branchIOService.createSignUpDeepLink({
        custom_data: {
          isPreVerified: true,
          email: user.email,
          first_name: user.firstName!,
          last_name: user.lastName!,
        },
      });

      const { contactId, ctcProductId } = await this.hubspotService.syncPreVerifiedUser(
        {
          email: user.email,
          firstName: user.firstName!,
          lastName: user.lastName!,
          practice_setting: user.practiceSetting!,
          employment_status: user.employmentStatus!,
          hcp_specialty: specialty.name,
          verification_status: HCPVerificationStatus.Verified,
          industrii_contact_status: HubspotContactStatus.WSPending,
          industrii_complete_ws: CompleteWS.False,
        },
        signUpLink,
      );

      await this.userRepository.update(
        {
          id: user.id,
        },
        {
          referralCode,
          referralLink,
          hubspotContactId: contactId,
          hubspotProductId: ctcProductId,
        },
      );

      const etransferPaymentMethod = await this.userPaymentMethodRepository.findOneBy({
        userId: user.id,
        type: PaymentMethodType.Etransfer,
      });

      if (!etransferPaymentMethod) {
        await this.userPaymentMethodRepository.insert({
          userId: user.id,
          type: PaymentMethodType.Etransfer,
          email: user.email,
        });
      }

      if (user.isCompleteWS) {
        const referralReward = await this.userReferralRewardRepository.findOneBy({
          userId: user.id,
        });

        if (referralReward) {
          const flag = user.verificationStatus === VerificationStatus.Verified;
          const reward = await this.rewardUsers(user, referralReward, flag);

          if (referralReward.referredBy && reward.isReferralUserRewarded && reward.amount > 0) {
            await this.notificationService.notifyReferralBonus(referralReward.referredBy, user, reward.amount);
          }
        }
      }

      await this.userImportTaskRepository.update({ id: task.id }, { status: ImportStatus.Done });
    } catch (error) {
      await this.userImportTaskRepository.update(
        { id: task.id },
        { status: ImportStatus.Open, error: JSON.stringify(error) },
      );
    }
  }

  private async rewardUsers(
    user: User,
    referralReward: UserReferralReward,
    flag: boolean,
  ): Promise<RewardUsersResponse> {
    // Determine reward amount - use default from referralReward or from user's specialty
    let amount = referralReward.amount;
    const res = {
      amount,
      isUserRewarded: false,
      isReferralUserRewarded: false,
    } as RewardUsersResponse;

    if (!amount) {
      if (!user.specialtyId) {
        res.amount = -1;
        return res;
      }
      const specialty = await this.specialtyService.getById(user.specialtyId);
      amount = specialty.referralValue;
    }

    // Find referral source (either user or public code)
    const [referralUser, publicReferralCode] = await Promise.all([
      referralReward.referredBy ? this.userRepository.findOneBy({ id: referralReward.referredBy }) : null,
      referralReward.publicReferralCodeId
        ? this.publicReferralCodeRepository.findOneBy({ id: referralReward.publicReferralCodeId })
        : null,
    ]);

    // Exit early if no valid referral source was found
    if (!referralUser && !publicReferralCode) {
      res.amount = -1;
      return res;
    }

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Set up referral metadata
      const referredBy = referralUser ? `${referralUser.firstName} ${referralUser.lastName}` : 'CTC';
      const code = publicReferralCode?.code || referralUser?.referralCode || null;
      const refId = referralReward.id.toString();

      // Reward the referred user if not already rewarded
      let isUserRewarded = false;
      if (!referralReward.isUserRewarded) {
        await queryRunner.manager
          .getRepository(UserTransaction)
          .createQueryBuilder()
          .insert()
          .values({
            userId: user.id,
            type: TransactionType.ReferralReward,
            title: 'Earned from Referral',
            amount,
            referredBy,
            code,
            refId,
          })
          .execute();

        await queryRunner.manager.update(User, { id: user.id }, { balance: () => `balance + ${amount}` });

        isUserRewarded = true;
      }

      // Reward the referring user if not already rewarded and flagged for reward
      let isReferralUserRewarded = false;
      if (!referralReward.isReferralUserRewarded && referralReward.referredBy && flag) {
        await queryRunner.manager
          .getRepository(UserTransaction)
          .createQueryBuilder()
          .insert()
          .values({
            userId: referralReward.referredBy,
            type: TransactionType.ReferralSuccess,
            title: 'Earned from Referral',
            amount,
            refId,
            referred: `${user.firstName} ${user.lastName}`,
          })
          .execute();

        await queryRunner.manager.update(
          User,
          { id: referralReward.referredBy },
          { balance: () => `balance + ${amount}` },
        );

        isReferralUserRewarded = true;
      }

      // Update reward record status
      const updateValues: Partial<UserReferralReward> = { amount };

      if (isUserRewarded) {
        updateValues.isUserRewarded = true;
        res.isUserRewarded = true;
      }

      if (isReferralUserRewarded) {
        updateValues.isReferralUserRewarded = true;
        res.isReferralUserRewarded = true;
      }

      await queryRunner.manager.update(UserReferralReward, { id: referralReward.id }, updateValues);

      await queryRunner.commitTransaction();
      return res;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      handleTransactionError(error, 'Failed to reward referral user');
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  public async verifyUser(id: number, data: VerifyUserPayload): Promise<void> {
    const user = await this.userRepository.findOneById(id);

    if (!user.hubspotContactId || !user.hubspotProductId) {
      throw badRequestError('Please wait for the user to be created in Hubspot');
    }

    if (user.verificationStatus === data.verificationStatus) {
      return;
    }

    let contactStatus = user.contactStatus;
    if (
      data.verificationStatus === VerificationStatus.Verified ||
      data.verificationStatus === VerificationStatus.Denied
    ) {
      if (user.isCompleteWS) {
        contactStatus = ContactStatus.Complete;
      }
    } else {
      if (user.isCompleteWS) {
        contactStatus = ContactStatus.ReviewInfo;
      } else {
        contactStatus = ContactStatus.WSPending;
      }
    }

    await this.hubspotService.syncVerificationComplete(
      user.hubspotContactId,
      user.hubspotProductId,
      data.verificationStatus,
      contactStatus!,
      user.isCompleteWS,
      data.note,
    );

    await this.userRepository.update(
      {
        id,
      },
      {
        verificationStatus: data.verificationStatus,
        isVerified: data.verificationStatus === VerificationStatus.Verified,
        note: data.verificationStatus === VerificationStatus.Denied ? data.note : null,
        contactStatus,
      },
    );

    // If unverified, notify silent to update user status
    if (data.verificationStatus === VerificationStatus.Verified) {
      waitUntil(
        (async (): Promise<void> => {
          await this.generateUserSurveys(user);
          await this.notificationService.notifyUserVerification(user.id);
        })(),
      );
    } else {
      await this.notificationService.notifyUserUnverification(user.id);
    }

    waitUntil(
      (async (): Promise<void> => {
        const referralReward = await this.userReferralRewardRepository.findOneBy({
          userId: user.id,
          isReferralUserRewarded: false,
        });

        if (referralReward && data.verificationStatus === VerificationStatus.Verified) {
          const reward = await this.rewardUsers(user, referralReward, true);

          if (referralReward.referredBy && reward.isReferralUserRewarded && reward.amount > 0) {
            await this.notificationService.notifyReferralBonus(referralReward.referredBy, user, reward.amount);
          }
        }
      })(),
    );

    waitUntil(this.syncAudiencesAndSpecialties(data.verificationStatus === VerificationStatus.Verified ? id : null));
  }

  public async requestUserInfo(id: number, data: RequestUserInfoPayload): Promise<void> {
    const user = await this.userRepository.findOneById(id);

    if (!user.hubspotContactId || !user.hubspotProductId) {
      throw badRequestError('Please wait for the user to be created in Hubspot');
    }

    if (
      user.verificationStatus !== VerificationStatus.Unverified ||
      (user.contactStatus !== ContactStatus.ReviewInfo && user.contactStatus !== ContactStatus.WaitForInfo) ||
      !user.isCompleteWS
    ) {
      throw badRequestError('User is not in review status');
    }

    if (user.contactStatus === data.contactStatus) {
      return;
    }

    await this.hubspotService.updateContact(user.hubspotContactId, {
      verification_status: this.hubspotService.mapVerificationStatusToHubspot(
        user.verificationStatus,
        user.isCompleteWS,
      ),
      industrii_contact_status:
        data.contactStatus === ContactStatus.WaitForInfo
          ? HubspotContactStatus.WaitForInfo
          : HubspotContactStatus.ReviewInfo,
    });

    await this.userRepository.update(
      {
        id,
      },
      {
        contactStatus: data.contactStatus,
      },
    );
  }

  public async updateNote(id: number, data: UpdateNotePayload): Promise<void> {
    const user = await this.userRepository.findOneById(id);

    if (!user.hubspotContactId || !user.hubspotProductId) {
      throw badRequestError('Please wait for the user to be created in Hubspot');
    }

    if (user.note !== data.note) {
      await this.hubspotService.updateContact(user.hubspotContactId, {
        industrii_note: data.note === null ? '' : data.note,
      });

      await this.userRepository.update(
        {
          id,
        },
        {
          note: data.note,
        },
      );
    }
  }

  public async checkEmailExist(email: string): Promise<boolean> {
    const [user, adminUser] = await Promise.all([
      this.userRepository.findOneBy({ email }),
      this.userAdminRepository.findOneBy({ email }),
    ]);

    if (user || adminUser) {
      return true;
    }

    return false;
  }

  private async checkUserProfile(
    user: User,
    email?: string,
    phone?: string | null,
  ): Promise<ValidateUserProfileResponse> {
    const response = {
      isEmailExist: false,
      isPhoneExist: false,
    };

    if (!email && !phone) {
      return response;
    }

    if (phone && user.phone !== phone) {
      const phoneExist = await this.checkPhoneExist(phone);
      response.isPhoneExist = phoneExist;
    }

    if (email && user.email !== email) {
      const [emailExist, hubspotContactExist] = await Promise.all([
        this.checkEmailExist(email),
        this.hubspotService.getContact(email),
      ]);

      response.isEmailExist = emailExist || Boolean(hubspotContactExist);
    }

    return response;
  }

  public async validateUserProfile(
    userId: number,
    data: ValidateUserProfilePayload,
  ): Promise<ValidateUserProfileResponse> {
    const user = await this.userRepository.findOneById(userId);
    return await this.checkUserProfile(user, data.email, data.phone);
  }

  public async updateUserProfile(userId: number, data: UpdateUserProfilePayload): Promise<User> {
    const user = await this.userRepository.findOneById(userId);

    if (data.phone && user.phone !== data.phone && !data.idToken) {
      throw badRequestError('Phone number cannot be changed without ID token');
    }

    const response = await this.checkUserProfile(user, data.email, data.phone);
    if (response.isEmailExist) {
      throw badRequestError('Email already taken', ErrorCode.AlreadyExists);
    }

    if (response.isPhoneExist) {
      throw badRequestError('Phone number already taken', ErrorCode.AlreadyExists);
    }

    let firebaseUserId = user.firebaseUserId;
    // Verify ID token if phone is provided and changed
    if (data.phone && user.phone !== data.phone) {
      const decoded = await this.verifyIdToken(data.idToken!, data.phone);
      firebaseUserId = decoded.uid;
    }

    const payload: Partial<HubspotContact> = {};

    if (data.email && user.email !== data.email) {
      payload.email = data.email;
    }

    if (data.phone && user.phone !== data.phone) {
      payload.phone = data.phone;
    }

    if (data.firstName && user.firstName !== data.firstName) {
      payload.firstname = data.firstName;
    }

    if (data.lastName && user.lastName !== data.lastName) {
      payload.lastname = data.lastName;
    }

    if (data.isEmailOptIn !== undefined && user.isEmailOptIn !== data.isEmailOptIn) {
      payload.industrii_email_opt_in = data.isEmailOptIn ? EmailOptIn.True : EmailOptIn.False;

      if (data.isEmailOptIn) {
        await this.hubspotService.updateEmailSubscription(user.email, StatusState.Subscribed);
      } else {
        await this.hubspotService.updateEmailSubscription(user.email, StatusState.Unsubscribed);
      }
    }

    // Only update if payload is not empty
    if (Object.keys(payload).length > 0) {
      await this.hubspotService.updateContact(user.hubspotContactId!, payload);
    }

    if (typeof data.notificationEnabled === 'boolean') {
      await this.userDeviceRepository.update(
        {
          userId,
        },
        {
          enabled: data.notificationEnabled,
        },
      );
    }

    // Update EFT email when update email profile
    if (data.email && data.email !== user.email) {
      await this.userPaymentMethodRepository.update(
        {
          userId: user.id,
          type: PaymentMethodType.Etransfer,
        },
        {
          email: data.email,
        },
      );
    }

    const updatedUser = await this.userRepository.save({
      ...user,
      firebaseUserId,
      ...data,
    });

    return updatedUser;
  }

  public async setUserBalance(userId: number, data: UpdateUserBalancePayload, refId?: string): Promise<void> {
    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Lock user row for update
      // Use pessimistic_write lock for subtracting balance
      const lockMode = data.type === TransactionType.Credit ? 'pessimistic_read' : 'pessimistic_write';
      const amount = data.type === TransactionType.Credit ? data.amount : -data.amount;

      const user = await queryRunner.manager
        .getRepository(User)
        .createQueryBuilder('users')
        .useTransaction(true)
        .setLock(lockMode)
        .where('users.id = :userId', { userId })
        .getOne();

      if (!user) {
        throw notFoundError('User not found');
      }

      if (data.type === TransactionType.Adjustment && user.balance + amount < 0) {
        throw badRequestError('Insufficient balance');
      }

      await queryRunner.manager
        .getRepository(UserTransaction)
        .createQueryBuilder()
        .insert()
        .values({
          userId: user.id,
          type: data.type,
          title: data.type,
          description: 'From Support team. If you believe this is an error, please reach out to us.',
          amount,
          refId,
        })
        .execute();

      await queryRunner.manager.update(
        User,
        {
          id: user.id,
        },
        {
          balance: () => `balance + ${amount}`,
        },
      );

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      handleTransactionError(error, 'Failed to update user balance');
    } finally {
      await queryRunner.release();
    }
  }

  public isSkipOnboardingSurvey(user: Omit<User, 'emailToLowerCase'>): boolean {
    if (
      user.gender &&
      user.birthday &&
      user.address &&
      user.city &&
      user.province &&
      user.country &&
      user.postalCode &&
      user.licenseNumber &&
      user.specialtyId &&
      user.practiceSetting &&
      user.employmentStatus
    ) {
      return true;
    }

    return false;
  }

  private sanitizeField(field: string | null | undefined): string | undefined {
    return field === null ? '' : field === undefined ? undefined : field;
  }

  public async syncAudiencesAndSpecialties(userId: number | null): Promise<void> {
    if (!userId) {
      await Promise.all([this.audienceService.audienceSelfUpdate(), this.specialtyService.specialtySelfUpdate()]);

      return;
    }

    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      return;
    }

    const [audiences, specialty] = await Promise.all([
      this.audienceService.getAudiencesByUser(user),
      user.specialtyId ? this.specialtyService.getById(user.specialtyId) : undefined,
    ]);

    await Promise.all([
      this.audienceService.audienceSelfUpdate(audiences),
      this.specialtyService.specialtySelfUpdate(specialty ? [specialty] : []),
    ]);
  }

  public async generateUserSurveys(user: User): Promise<void> {
    const audiences = await this.audienceService.getAudiencesByUser({
      ...user,
      verificationStatus: VerificationStatus.Verified,
    } as User);

    if (audiences.length) {
      const audienceIds = audiences.map(audience => audience.id);
      const surveys = await this.surveyService.getUnassignedSurveysForUser(user.id, audienceIds);

      if (surveys.length) {
        const surveyIds = surveys.map(survey => survey.id);
        await this.surveyService.createUserSurveys(user.id, surveyIds);
      }
    }
  }

  public async isUserPushTokensExist(userId: number): Promise<boolean> {
    const userDevice = await this.userDeviceRepository.findOneBy({ userId });
    return !!userDevice;
  }

  public async getAudiencesByUser(userId: number): Promise<Audience[]> {
    const user = await this.userRepository.findOneById(userId);
    const audiences = await this.audienceService.getAudiencesByUser(user, {
      isGetFilters: true,
      isVisibleOnly: true,
    });
    return audiences;
  }

  public async addUserToAudiences(userId: number, data: EditUserInAudiencesPayload): Promise<void> {
    await this.userRepository.findOneById(userId);
    await this.audienceService.addUserToAudiences(userId, data.audienceIds);

    waitUntil(this.syncAudiencesAndSpecialties(null));
  }

  public async removeUserFromAudiences(userId: number, data: EditUserInAudiencesPayload): Promise<void> {
    await this.userRepository.findOneById(userId);
    await this.audienceService.removeUserFromAudiences(userId, data.audienceIds);

    waitUntil(this.syncAudiencesAndSpecialties(null));
  }

  public async removeUsersFromAudience(audienceId: number, data: EditUsersInAudiencePayload): Promise<void> {
    await this.audienceService.removeUsersFromAudience(audienceId, data.userIds);

    waitUntil(this.syncAudiencesAndSpecialties(null));
  }

  async getUsersCompletedSurvey(
    surveyId: number,
    data: PaginationPayload<User>,
  ): Promise<PaginationResponseData<User>> {
    const { page, pageSize, search, sortBy, sortOrder } = data;

    const queryBuilder = this.userSubmissionRepository
      .createQueryBuilder('submission')
      .innerJoinAndSelect('submission.user', 'user')
      .where('submission.surveyId = :surveyId', { surveyId })
      .leftJoinAndMapOne('user.specialty', Specialty, 'specialty', 'user.specialtyId = specialty.id');

    if (search) {
      queryBuilder.andWhere(
        "(CONCAT(NULLIF(user.firstName, ''), ' ', NULLIF(user.lastName, '')) ILIKE :search OR " +
          'user.email ILIKE :search OR user.phone ILIKE :search OR user.province ILIKE :search)',
        { search: `%${search.toLowerCase()}%` },
      );
    }

    if (sortBy === 'completionDate') {
      queryBuilder.orderBy('submission.completionDate', sortOrder || 'DESC');
    } else if (sortBy && this.SORTABLE_FIELDS.includes(sortBy as keyof User)) {
      if (sortBy === 'specialty') {
        queryBuilder.orderBy('specialty.name', sortOrder || 'ASC');
      } else if (sortBy === 'lastLogin') {
        queryBuilder.orderBy('user.lastLogin', sortOrder || 'ASC', 'NULLS LAST');
      } else {
        queryBuilder.orderBy(`user.${sortBy}`, sortOrder || 'ASC');
      }
    } else {
      queryBuilder.orderBy('submission.completionDate', 'DESC');
    }

    const countDistinctQuery = this.userSubmissionRepository
      .createQueryBuilder('submission')
      .innerJoin('submission.user', 'user')
      .where('submission.surveyId = :surveyId', { surveyId })
      .select('COUNT(DISTINCT user.id)', 'count');

    const [res, countDistinct] = await Promise.all([
      withPagination(queryBuilder, page, pageSize),
      countDistinctQuery.getRawOne(),
    ]);

    const { data: submissions, total, totalPages } = res;

    // Extract user data from submissions
    const users = submissions.map((submission: UserSubmission) => {
      const user = submission.user;
      user.completionDate = submission.completionDate;

      return user;
    });

    return {
      data: users,
      total,
      totalPages,
      countDistinct: countDistinct ? Number(countDistinct.count) : null,
    };
  }

  public async setUserType(userId: number, data: UpdateUserTypePayload): Promise<void> {
    await this.userRepository.update({ id: userId }, { userType: data.userType });
  }
}
