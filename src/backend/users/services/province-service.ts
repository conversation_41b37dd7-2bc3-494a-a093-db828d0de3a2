import { ProvinceRepository } from '@/backend/users/repositories/province-repository';
import { dataSource } from '@/backend/database/data-source';
import { Province } from '@/backend/users/entities/Province';

export class ProvinceService {
  private provinceRepository = new ProvinceRepository(dataSource);

  constructor() {}

  public async getProvinces(): Promise<Province[]> {
    return await this.provinceRepository.find({
      order: {
        name: 'ASC',
      },
    });
  }
}
