import { CanadaPostService } from '@/backend/canadapost/services';
import { dataSource } from '@/backend/database/data-source';
import { badRequestError } from '@/backend/middlewares/capture-errors';
import { logger } from '@/backend/shared/utils/logger';
import { CountryRepository } from '@/backend/users/repositories/country-repository';
import { ILike } from 'typeorm';
import { City } from '../entities/City';
import { Province } from '../entities/Province';
import { CityRepository } from '../repositories/city-repository';
import { ProvinceRepository } from '../repositories/province-repository';

export class RegionService {
  private readonly countryRepository = new CountryRepository(dataSource);
  private readonly provinceRepository = new ProvinceRepository(dataSource);
  private readonly cityRepository = new CityRepository(dataSource);
  private readonly canadaPostService = new CanadaPostService(true);
  private readonly log = logger.child({ description: 'RegionService' });

  constructor() {}

  public async listProvinces(): Promise<Province[]> {
    return await this.provinceRepository.findAll();
  }

  public async listCities(): Promise<City[]> {
    return await this.cityRepository.findAll();
  }

  public async addLocation(canadaPostId: string): Promise<void> {
    const data = await this.canadaPostService.retrieveAddress({ Id: canadaPostId });
    const engAddress = data.Items.find(item => item.Language === 'ENG');

    if (!engAddress || engAddress.Error) {
      throw badRequestError(
        engAddress?.Description ? engAddress.Description : 'Failed to retrieve address from Canada Post',
      );
    }

    const country = await this.countryRepository.upsert(
      {
        name: engAddress.CountryName,
        code: engAddress.CountryIso2,
      },
      ['name'],
    );

    const province = await this.provinceRepository.upsert(
      {
        name: engAddress.ProvinceName,
        code: engAddress.ProvinceCode,
        countryId: country.raw[0].id,
      },
      ['name', 'countryId'],
    );

    await this.cityRepository.upsert(
      {
        name: engAddress.City,
        provinceId: province.raw[0].id,
      },
      ['name', 'provinceId'],
    );
  }

  public async getProvince(name: string | undefined | null): Promise<Province | null> {
    if (!name) {
      return null;
    }

    return await this.provinceRepository.findOne({
      where: {
        name: ILike(`%${name}%`),
      },
    });
  }

  public async addCity(name: string, provinceId?: number | null): Promise<void> {
    await this.cityRepository.upsert(
      {
        name,
        provinceId,
      },
      ['name', 'provinceId'],
    );
  }
}
