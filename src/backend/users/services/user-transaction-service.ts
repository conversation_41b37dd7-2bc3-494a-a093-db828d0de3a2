import { dataSource } from '@/backend/database/data-source';
import { Locale } from '@/backend/surveys/entities/Survey';
import { SurveyTranslation } from '@/backend/surveys/entities/SurveyTranslation';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import { TransactionType, UserTransaction } from '@/backend/users/entities/UserTransaction';
import { UserTransactionRepository } from '@/backend/users/repositories/user-transaction-repository';
import { TransactionTypeFilter } from '@/backend/users/types/transaction';
import { PaginationPayload, PaginationResponseData, withPagination } from '@/types/pagination';

export class UserTransactionService {
  private userTransactionRepository: UserTransactionRepository;

  constructor() {
    this.userTransactionRepository = new UserTransactionRepository(dataSource);
  }

  public async getUserTransactions(userId: number): Promise<UserTransaction[]> {
    return this.userTransactionRepository.find({ where: { userId }, order: { createdAt: 'DESC' } });
  }

  public async getTransactions(
    userId: number,
    data: PaginationPayload<UserTransaction>,
    locale: Locale | null,
    type: string | null,
  ): Promise<PaginationResponseData<UserTransaction>> {
    const { page, pageSize } = data;

    const queryBuilder = this.userTransactionRepository
      .createQueryBuilder('transactions')
      .where('transactions.userId = :userId', { userId })
      .orderBy('transactions.createdAt', 'DESC');

    if (locale) {
      queryBuilder
        .leftJoin(
          UserSurvey,
          'user_surveys',
          'user_surveys.id = CAST(transactions.refId AS INTEGER) AND transactions.type = :type',
          { type: TransactionType.Compensation },
        )
        .leftJoinAndMapOne(
          'transactions.surveyTranslation',
          SurveyTranslation,
          'survey_translation',
          'survey_translation.surveyId = user_surveys.surveyId AND survey_translation.locale = :locale',
          { locale },
        );
    }

    switch (type) {
      case TransactionTypeFilter.Earnings:
        queryBuilder.andWhere('transactions.type IN (:...types)', {
          types: [
            TransactionType.Compensation,
            TransactionType.ReferralReward,
            TransactionType.ReferralSuccess,
            TransactionType.Credit,
          ],
        });
        break;

      case TransactionTypeFilter.Withdrawals:
        queryBuilder.andWhere('transactions.type IN (:...types)', {
          types: [TransactionType.Withdrawal, TransactionType.Adjustment],
        });
        break;

      default:
        break;
    }

    const result = await withPagination(queryBuilder, page, pageSize);
    const mappedResult = result.data.map(item => {
      const tx = {
        ...item,
        surveyName: locale && item.surveyTranslation ? item.surveyTranslation.title : item.surveyName,
      };
      delete tx.surveyTranslation;
      return tx;
    });

    return { ...result, data: mappedResult };
  }
}
