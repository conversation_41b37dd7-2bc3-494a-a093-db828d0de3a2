import { Company } from '@/backend/companies/entities/Company';
import { dataSource } from '@/backend/database/data-source';
import { Survey } from '@/backend/surveys/entities/Survey';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import { UserSurveyRepository } from '@/backend/users/repositories/user-survey-repository';
import { Brackets } from 'typeorm';

export class UserSurveyService {
  private readonly userSurveyRepository: UserSurveyRepository;

  constructor() {
    this.userSurveyRepository = new UserSurveyRepository(dataSource);
  }

  public async getUserCompletedSurveys(userId: number): Promise<UserSurvey[]> {
    const data = await this.userSurveyRepository
      .createQueryBuilder('us')
      .leftJoinAndMapOne('us.survey', Survey, 's', 'us.surveyId = s.id')
      .leftJoinAndMapOne('s.company', Company, 'c', 'c.id = s.companyId')
      .where('us.userId = :userId', { userId })
      .andWhere(
        new Brackets(qb => {
          qb.where('us.isComplete = true').orWhere('us.totalSubmissions > 0');
        }),
      )
      .getMany();

    return data;
  }
}
