import { dataSource } from '@/backend/database/data-source';
import { UserDeviceRepository } from '@/backend/users/repositories/user-device-repository';
import { UserRepository } from '@/backend/users/repositories/user-repository';
import { CreateUserDevicePayload } from '@/backend/users/validations/create-user-device';
import { DeleteUserDevicePayload } from '@/backend/users/validations/delete-user-device';

export class UserDeviceService {
  private readonly userDeviceRepository = new UserDeviceRepository(dataSource);
  private readonly userRepository = new UserRepository(dataSource);

  constructor() {}

  public async create(userId: number, data: CreateUserDevicePayload): Promise<void> {
    const user = await this.userRepository.findOneById(userId);

    await this.userDeviceRepository.upsert(
      {
        userId: user.id,
        ...data,
        enabled: user.notificationEnabled,
      },
      ['userId', 'deviceId'],
    );
  }

  public async delete(userId: number, data: DeleteUserDevicePayload): Promise<void> {
    await this.userDeviceRepository.delete({ userId, token: data.token });
  }
}
