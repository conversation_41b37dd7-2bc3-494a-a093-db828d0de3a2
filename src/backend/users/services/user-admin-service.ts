import { dataSource } from '@/backend/database/data-source';
import { badRequestError, customHttpError, ErrorCode, notFoundError } from '@/backend/middlewares/capture-errors';
import { logger } from '@/backend/shared/utils/logger';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import { UserAdminRepository } from '@/backend/users/repositories/user-admin-repository';
import { AddAdminPayload, UpdateAdminPayload } from '@/backend/users/validations/add-update-admin';
import { firebaseClient } from '@/lib/firebase/client';
import { auth } from 'firebase-admin';
import { sendPasswordResetEmail } from 'firebase/auth';
import { IsNull } from 'typeorm';
import { UserRepository } from '../repositories/user-repository';

export class UserAdminService {
  private readonly userAdminRepository = new UserAdminRepository(dataSource);
  private readonly userRepository = new UserRepository(dataSource);
  private readonly log = logger.child({ description: 'UserAdminService' });

  constructor() {}

  public async getDashboardUsers(): Promise<UserAdmin[]> {
    return this.userAdminRepository.find({ where: { deletedAt: IsNull() } });
  }

  public async getDashboardUserById(userId: number): Promise<UserAdmin> {
    const user = await this.userAdminRepository.findOneBy({ id: userId });
    if (!user) {
      throw notFoundError('User not found');
    }
    return user;
  }

  public async addDashboardUser(data: AddAdminPayload): Promise<UserAdmin> {
    const [existingUserAdmin, existingUser] = await Promise.all([
      this.userAdminRepository.findOneBy({ email: data.email }),
      this.userRepository.findOneBy({ email: data.email }),
    ]);

    if (existingUserAdmin || existingUser) {
      throw customHttpError(400, 'Email already exists', ErrorCode.AlreadyExists);
    }

    let firebaseUser;
    try {
      firebaseUser = await auth().createUser({
        email: data.email,
        displayName: `${data.firstName} ${data.lastName}`,
      });
      await sendPasswordResetEmail(firebaseClient, data.email);
    } catch (error) {
      const msg = 'Failed to create dashboard user';
      this.log.warn(error, msg);
      throw customHttpError(500, msg, ErrorCode.InternalServerError);
    }
    const user = new UserAdmin();
    this.userAdminRepository.merge(user, { ...data, firebaseUserId: firebaseUser.uid });
    await this.userAdminRepository.save(user);
    return user;
  }

  public async updateDashboardUser(id: number, data: UpdateAdminPayload): Promise<UserAdmin> {
    let user = await this.userAdminRepository.findOneBy({ id });
    if (!user) {
      throw customHttpError(404, 'User not found', ErrorCode.NotFound);
    }
    user = await this.userAdminRepository.save({ ...user, ...data });
    return user;
  }

  public async deleteDashboardUser(id: number): Promise<void> {
    const user = await this.userAdminRepository.findOneBy({ id });
    if (!user) {
      throw customHttpError(404, 'User not found', ErrorCode.NotFound);
    }

    try {
      if (user.firebaseUserId) {
        await auth().deleteUser(user.firebaseUserId);
      }
      const deletedAt = new Date();
      await this.userAdminRepository.update(
        { id },
        { email: `deleted-${deletedAt.getTime()}-${user.email}`, deletedAt },
      );
    } catch (error) {
      const msg = 'Failed to delete dashboard user';
      this.log.warn(error, msg);
      throw badRequestError(msg);
    }
  }
}
