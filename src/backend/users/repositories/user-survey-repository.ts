import { User<PERSON>ur<PERSON> } from '@/backend/users/entities/UserSurvey';
import { DataSource, InsertResult, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

export class UserSurveyRepository extends Repository<UserSurvey> {
  constructor(private dataSource: DataSource) {
    super(UserSurvey, dataSource.createEntityManager());
  }

  public async insertWithQueryBuilder(
    values: QueryDeepPartialEntity<UserSurvey> | QueryDeepPartialEntity<UserSurvey>[],
  ): Promise<InsertResult> {
    const result = await this.createQueryBuilder().insert().values(values).execute();
    return result;
  }
}
