import { badRequestError, notFoundError } from '@/backend/middlewares/capture-errors';
import { User, VerificationStatus } from '@/backend/users/entities/User';
import { DataSource, IsNull, Repository } from 'typeorm';

export class UserRepository extends Repository<User> {
  constructor(private dataSource: DataSource) {
    super(User, dataSource.createEntityManager());
  }

  public async findOneById(id: number): Promise<User> {
    const user = await this.findOneBy({ id, deletedAt: IsNull() });
    if (!user) {
      throw notFoundError('User not found');
    }

    return user;
  }

  public async getVerifiedUser(id: number): Promise<User> {
    const user = await this.findOneById(id);
    if (user.verificationStatus !== VerificationStatus.Verified) {
      throw badRequestError('User not verified');
    }

    return user;
  }
}
