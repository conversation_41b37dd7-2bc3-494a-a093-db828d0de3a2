import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { EmploymentStatus } from '@/backend/users/entities/EmploymentStatus';
import { PracticeSetting } from '@/backend/users/entities/PracticeSetting';
import { User } from '@/backend/users/entities/User';
import { AdminRole } from '@/backend/users/entities/UserAdmin';

export enum UserRole {
  User = 'User',
}

export type Role = AdminRole | UserRole | '*';

export interface UserRegistrationInfo {
  isEmailExist: boolean;
  isPhoneExist: boolean;
  referralUser: User | null;
  publicReferralCode: PublicReferralCode | null;
}

export interface UserRegistrationResponse {
  accessToken: string;
  refreshToken: string;
  isReferral: boolean;
  refUserInfo: {
    firstName: string | null;
    lastName: string | null;
    refValue: number | null;
  } | null;
}

export interface IndustryInfo {
  employmentStatus: EmploymentStatus[];
  practiceSettings: PracticeSetting[];
  specialties: Specialty[];
}

export interface ValidateUserProfileResponse {
  isEmailExist: boolean;
  isPhoneExist: boolean;
}
