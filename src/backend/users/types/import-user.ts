import { User } from '@/backend/users/entities/User';
import { UserAdmin } from '@/backend/users/entities/UserAdmin';

export type ImportUser = {
  first_name: string;
  last_name: string;
  email: string;
  specialty: string;
  birthday: string;
  province: string;
  practice_setting: string;
  employment_status: string;
  user_role: string;
};

export type UserImportValidated = {
  user: Partial<User | UserAdmin> & {
    birthday?: Date | string | null;
  };
  errors: string[];
  existedUser: User | UserAdmin | null;
  selectedUser?: User | null;
};
