import { UserAdmin } from '@/backend/users/entities/UserAdmin';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type ObjectLiteral,
  type Relation,
} from 'typeorm';

@Entity('user_actions')
export class UserAction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  userId: number;

  @Column({ type: 'varchar', length: 500 })
  description: string;

  @Column({ type: 'varchar', length: 50 })
  entity: string;

  @Column({ type: 'jsonb', nullable: true })
  previousMetadata: ObjectLiteral | null;

  @Column({ type: 'jsonb', nullable: true })
  newMetadata: ObjectLiteral | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => UserAdmin)
  @JoinColumn({ name: 'userId' })
  user: Relation<UserAdmin>;
}
