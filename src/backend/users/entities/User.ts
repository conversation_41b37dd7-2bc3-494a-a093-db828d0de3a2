import { DecimalColumnTransformer } from '@/backend/shared/utils/data-transform';
import { Specialty } from '@/backend/specialties/entities/Specialty';
import { UserReferralReward } from '@/backend/users/entities/UserReferralReward';
import { UserSubmission } from '@/backend/users/entities/UserSubmission';
import {
  BeforeInsert,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

export enum Gender {
  Male = 'Male',
  Female = 'Female',
  Other = 'Other',
}

export enum VerificationStatus {
  Denied = 'Denied',
  Unverified = 'Unverified',
  Verified = 'Verified',
}

export enum ContactStatus {
  Complete = 'Complete',
  ReviewInfo = 'ReviewInfo',
  WaitForInfo = 'WaitForInfo',
  WSPending = 'WSPending',
}

export enum UserType {
  HCPUser = 'HCP User',
  Client = 'Client',
  ClientImported = 'Client - Imported',
  HCPUserImported = 'HCP User - Imported',
  Unverified = 'Unverified',
  Denied = 'Denied',
  Internal = 'Internal',
}

@Entity('users')
@Index(['email', 'deletedAt'], { where: `("deletedAt" IS NULL)`, unique: true })
@Index(['deletedAt', 'verificationStatus', 'city', 'practiceSetting', 'employmentStatus', 'province', 'specialtyId'], {
  where: `("deletedAt" IS NULL AND "verificationStatus" = 'Verified')`,
})
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  firstName: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  lastName: string | null;

  @Column({ type: 'varchar', length: 200, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  phone: string | null;

  @Index({ unique: true, where: `"firebaseUserId" IS NOT NULL` })
  @Column({ type: 'varchar', length: 150, nullable: true })
  firebaseUserId: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  licenseNumber: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  address: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  city: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  province: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  country: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  postalCode: string | null;

  @Column({ type: 'date', nullable: true })
  birthday: Date | null;

  @Column({
    type: 'enum',
    enum: Gender,
    default: null,
    nullable: true,
  })
  gender: Gender | null;

  @Column({
    type: 'enum',
    enum: UserType,
    default: null,
    nullable: true,
  })
  userType: UserType | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  practiceSetting: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  employmentStatus: string | null;

  @Column({ type: 'int', nullable: true })
  specialtyId: number | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    transformer: new DecimalColumnTransformer(),
  })
  balance: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  referralCode: string | null;

  @Column({ type: 'varchar', length: 150, nullable: true })
  referralLink: string | null;

  @Column({ type: 'varchar', length: 10, nullable: true })
  preferredLanguage: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  hubspotContactId: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  hubspotProductId: string | null;

  @Column({ type: 'boolean', default: false })
  notificationEnabled: boolean;

  @Column({ type: 'boolean', default: false })
  isEmailOptIn: boolean;

  @Column({ type: 'boolean', default: false })
  isVerified: boolean;

  @Column({
    type: 'enum',
    enum: VerificationStatus,
    default: null,
    nullable: true,
  })
  verificationStatus: VerificationStatus | null;

  @Column({ type: 'varchar', length: 500, nullable: true })
  note: string | null;

  @Column({
    type: 'enum',
    enum: ContactStatus,
    default: null,
    nullable: true,
  })
  contactStatus: ContactStatus | null;

  @Column({ type: 'boolean', default: false })
  isCompleteWS: boolean;

  @Column({ type: 'timestamp', nullable: true })
  lastLogin: Date | null;

  @Column({ type: 'boolean', default: false })
  isPublic: boolean;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => Specialty)
  @JoinColumn({ name: 'specialtyId' })
  specialty: Relation<Specialty>;

  // Referral reward received by this user
  @OneToOne(() => UserReferralReward, userReferralReward => userReferralReward.user)
  userReferralReward: Relation<UserReferralReward>;

  @OneToMany(() => UserSubmission, userSubmission => userSubmission.user)
  userSubmissions: Relation<UserSubmission[]>;

  @BeforeInsert()
  emailToLowerCase() {
    if (this.email) {
      this.email = this.email.toLowerCase();
    }
  }

  // Survey submission
  completionDate: Date;
}
