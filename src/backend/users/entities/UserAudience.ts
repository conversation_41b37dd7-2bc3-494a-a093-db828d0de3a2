import { Audience } from '@/backend/audiences/entities/Audience';
import { User } from '@/backend/users/entities/User';
import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, type Relation } from 'typeorm';

@Entity('user_audiences')
export class UserAudience {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  userId: number;

  @Index()
  @Column({ type: 'int' })
  audienceId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: Relation<User>;

  @ManyToOne(() => Audience)
  @JoinColumn({ name: 'audienceId' })
  audience: Relation<Audience>;
}
