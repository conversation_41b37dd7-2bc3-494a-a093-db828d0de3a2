import { User } from '@/backend/users/entities/User';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

export enum TokenProvider {
  FIREBASE = 'Firebase',
}

export enum Platform {
  ANDROID = 'Android',
  IOS = 'iOS',
}

@Unique(['userId', 'deviceId'])
@Entity('user_devices')
export class UserDevice {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  userId: number;

  @Column({ type: 'varchar', nullable: false })
  token: string;

  @Column({ type: 'varchar', nullable: false })
  deviceId: string;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({
    type: 'enum',
    enum: TokenProvider,
    default: TokenProvider.FIREBASE,
  })
  tokenProvider: TokenProvider;

  @Column({
    type: 'enum',
    enum: Platform,
  })
  platform: Platform;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: Relation<User>;
}
