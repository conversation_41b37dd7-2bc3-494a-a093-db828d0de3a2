import { SurveyQuestion } from '@/backend/surveys/entities/SurveyQuestion';
import { SurveyQuestionOption } from '@/backend/surveys/entities/SurveyQuestionOption';
import { UserSubmission } from '@/backend/users/entities/UserSubmission';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('user_survey_answers')
export class UserSurveyAnswer {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  userSurveyId: number;

  @Index()
  @Column({ type: 'int' })
  questionId: number;

  @Index()
  @Column({ type: 'int', nullable: true })
  questionOptionId: number | null;

  @Column({ type: 'text', nullable: true })
  value: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @Column({ type: 'int', nullable: true }) // Set to nullable for now, but will be required in the future
  submissionId: number;

  @ManyToOne(() => UserSurvey)
  @JoinColumn({ name: 'userSurveyId' })
  userSurvey: Relation<UserSurvey>;

  @ManyToOne(() => UserSubmission)
  @JoinColumn({ name: 'submissionId' })
  userSubmission: Relation<UserSubmission>;

  @ManyToOne(() => SurveyQuestion)
  @JoinColumn({ name: 'questionId' })
  surveyQuestion: Relation<SurveyQuestion>;

  @ManyToOne(() => SurveyQuestionOption)
  @JoinColumn({ name: 'questionOptionId' })
  surveyQuestionOption: Relation<SurveyQuestionOption>;
}
