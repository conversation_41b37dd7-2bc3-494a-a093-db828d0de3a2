import { Province } from '@/backend/users/entities/Province';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Unique, type Relation } from 'typeorm';

@Entity('cities')
@Unique(['name', 'provinceId'])
export class City {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: true })
  provinceId: number | null;

  @Column({ type: 'varchar', length: 50 })
  name: string;

  @ManyToOne(() => Province)
  @JoinColumn({ name: 'provinceId' })
  province: Relation<Province>;
}
