import { VopayTransactionStatus } from '@/backend/payments/types/vopay-response';
import { DecimalColumnTransformer } from '@/backend/shared/utils/data-transform';
import { SurveyTranslation } from '@/backend/surveys/entities/SurveyTranslation';
import { User } from '@/backend/users/entities/User';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

export enum TransactionType {
  Withdrawal = 'Withdrawal',
  Compensation = 'Compensation',
  ReferralSuccess = 'ReferralSuccess',
  ReferralReward = 'ReferralReward',
  Credit = 'Credit',
  Adjustment = 'Adjustment',
}

@Entity('user_transactions')
export class UserTransaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  userId: number;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  type: TransactionType;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  description: string | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    transformer: new DecimalColumnTransformer(),
  })
  amount: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  refId: string | null;

  @Column({ type: 'varchar', length: 500, nullable: true })
  surveyName: string;

  @Column({ type: 'timestamp', nullable: true })
  completionDate: Date;

  @Column({ type: 'varchar', length: 100, nullable: true })
  paidTo: string | null;

  @Column({
    type: 'enum',
    enum: VopayTransactionStatus,
    nullable: true,
  })
  vopayTransactionStatus: VopayTransactionStatus | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  referredBy: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  referred: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  code: string | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: Relation<User>;

  surveyTranslation?: SurveyTranslation;
}
