import { PublicReferralCode } from '@/backend/referral-codes/entities/PublicReferralCode';
import { DecimalColumnTransformer } from '@/backend/shared/utils/data-transform';
import { User } from '@/backend/users/entities/User';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('user_referral_rewards')
export class UserReferralReward {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int', nullable: true })
  publicReferralCodeId: number | null;

  @Column({ type: 'int', unique: true })
  userId: number;

  @Index()
  @Column({ type: 'int', nullable: true })
  referredBy: number | null;

  @Column({ type: 'boolean', default: false })
  isUserRewarded: boolean;

  @Column({ type: 'boolean', nullable: true })
  isReferralUserRewarded: boolean | null;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: new DecimalColumnTransformer(),
    nullable: true,
  })
  amount: number | null;

  @Column({ type: 'boolean', default: false })
  isCongratulationDisplayed: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => PublicReferralCode)
  @JoinColumn({ name: 'publicReferralCodeId' })
  publicReferralCode: Relation<PublicReferralCode>;

  @OneToOne(() => User, user => user.userReferralReward)
  @JoinColumn({ name: 'userId' })
  user: Relation<User>;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'referredBy' })
  referredByUser: Relation<User>;
}
