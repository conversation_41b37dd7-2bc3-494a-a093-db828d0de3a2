import { DecimalColumnTransformer } from '@/backend/shared/utils/data-transform';
import { Survey } from '@/backend/surveys/entities/Survey';
import { UserAnswer } from '@/backend/surveys/types/user-answer';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

export enum PublicSurveyProgress {
  Open = 'Open',
  InProgress = 'InProgress',
  Done = 'Done',
}

@Entity('public_survey_answers')
export class PublicSurveyAnswer {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 200, nullable: true })
  email: string | null;

  @Column({ type: 'int' })
  surveyId: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: new DecimalColumnTransformer(),
    default: 0,
  })
  compensation: number;

  @Column({ type: 'jsonb' })
  answers: UserAnswer[];

  @Index()
  @Column({
    type: 'enum',
    enum: PublicSurveyProgress,
    default: PublicSurveyProgress.Open,
  })
  status: PublicSurveyProgress;

  @Column({ type: 'text', nullable: true })
  error: string | null;

  @Column({ type: 'int', default: 0 })
  count: number;

  @Column({ type: 'int', nullable: true })
  userId: number | null;

  @Column({ type: 'timestamp', nullable: true })
  startDate: Date | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => Survey)
  @JoinColumn({ name: 'surveyId' })
  survey: Relation<Survey>;
}
