import { Country } from '@/backend/users/entities/Country';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Unique } from 'typeorm';

@Entity('provinces')
@Unique(['name', 'countryId'])
export class Province {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  countryId: number;

  @Column({ type: 'varchar', length: 50 })
  name: string;

  @Column({ type: 'varchar', length: 20 })
  code: string;

  @ManyToOne(() => Country)
  @JoinColumn({ name: 'countryId' })
  country: Country;
}
