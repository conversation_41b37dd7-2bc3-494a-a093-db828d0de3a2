import { Survey } from '@/backend/surveys/entities/Survey';
import { User } from '@/backend/users/entities/User';
import { UserSurvey } from '@/backend/users/entities/UserSurvey';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('user_submissions')
export class UserSubmission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  userSurveyId: number;

  @Column({ type: 'int' })
  surveyId: number;

  @Column({ type: 'int' })
  userId: number;

  @Column({ type: 'int' })
  responseId: number;

  @Column({ type: 'timestamp' })
  completionDate: Date;

  @Column({ type: 'varchar', length: 50, nullable: true })
  hubspotProgramId: string | null;

  @Column({ type: 'timestamp', nullable: true })
  startDate: Date | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => UserSurvey)
  @JoinColumn({ name: 'userSurveyId' })
  userSurvey: Relation<UserSurvey>;

  @ManyToOne(() => Survey)
  @JoinColumn({ name: 'surveyId' })
  survey: Relation<Survey>;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: Relation<User>;
}
