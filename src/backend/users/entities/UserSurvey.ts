import { Survey } from '@/backend/surveys/entities/Survey';
import { User } from '@/backend/users/entities/User';
import { UserSubmission } from '@/backend/users/entities/UserSubmission';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

@Entity('user_surveys')
@Index(['userId', 'surveyId'], { unique: true })
export class UserSurvey {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  userId: number;

  @Index()
  @Column({ type: 'int' })
  surveyId: number;

  @Column({ type: 'boolean', default: false })
  isComplete: boolean;

  @Column({ type: 'int', default: 0 })
  totalSubmissions: number;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @Column({ type: 'boolean', default: true })
  isPassScreening: boolean;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: Relation<User>;

  @ManyToOne(() => Survey, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'surveyId' })
  survey: Relation<Survey>;

  @OneToMany(() => UserSubmission, userSubmission => userSubmission.userSurvey)
  userSubmissions: UserSubmission[];
}
