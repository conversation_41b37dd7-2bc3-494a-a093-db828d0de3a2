import { User } from '@/backend/users/entities/User';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  type Relation,
} from 'typeorm';

export enum PaymentMethodType {
  Plaid = 'Plaid',
  Etransfer = 'Etransfer',
}

@Entity('user_payment_methods')
export class UserPaymentMethod {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'int' })
  userId: number;

  @Column({
    type: 'enum',
    enum: PaymentMethodType,
  })
  type: PaymentMethodType;

  @Column({ type: 'varchar', length: 150, nullable: true })
  accessToken: string;

  @Column({ type: 'varchar', length: 150, nullable: true })
  processorToken: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  email: string | null;

  // Last 4 digits of the account number
  @Column({ type: 'varchar', length: 4, nullable: true })
  mask: string | null;

  @Column({ type: 'varchar', length: 50, nullable: true })
  institutionId: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  bankName: string | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt: Date | null;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: Relation<User>;

  logo: string | null;
}
