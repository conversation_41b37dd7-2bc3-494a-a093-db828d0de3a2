import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum ImportStatus {
  Open = 'Open',
  InProgress = 'InProgress',
  Done = 'Done',
}

@Entity('user_import_tasks')
export class UserImportTask {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  userId: number;

  @Index()
  @Column({
    type: 'enum',
    enum: ImportStatus,
    default: ImportStatus.Open,
  })
  status: ImportStatus;

  @Column({ type: 'text', nullable: true })
  error: string | null;

  @Column({ type: 'int', default: 0 })
  count: number;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
