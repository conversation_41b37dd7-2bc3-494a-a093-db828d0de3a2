{"id": 1, "name": "Canada", "code": "CA", "provinces": [{"id": 1, "countryId": 1, "name": "Alberta", "code": "AB", "cities": [{"id": 1, "provinceId": 1, "name": "Edmonton"}, {"id": 2, "provinceId": 1, "name": "Cold Lake"}, {"id": 3, "provinceId": 1, "name": "Wetaskiwin"}, {"id": 4, "provinceId": 1, "name": "Grande Prairie"}, {"id": 5, "provinceId": 1, "name": "Red Deer"}, {"id": 6, "provinceId": 1, "name": "Lethbridge"}, {"id": 7, "provinceId": 1, "name": "Calgary"}, {"id": 8, "provinceId": 1, "name": "Spruce Grove"}, {"id": 9, "provinceId": 1, "name": "Airdrie"}, {"id": 10, "provinceId": 1, "name": "St. Albert"}, {"id": 11, "provinceId": 1, "name": "Lloydminster"}, {"id": 12, "provinceId": 1, "name": "<PERSON>"}, {"id": 13, "provinceId": 1, "name": "Fort Saskatchewan"}, {"id": 14, "provinceId": 1, "name": "<PERSON><PERSON><PERSON>"}, {"id": 15, "provinceId": 1, "name": "<PERSON><PERSON>"}, {"id": 16, "provinceId": 1, "name": "Medicine Hat"}, {"id": 17, "provinceId": 1, "name": "Beaumont"}, {"id": 18, "provinceId": 1, "name": "<PERSON><PERSON>"}]}, {"id": 2, "countryId": 1, "name": "British Columbia", "code": "BC", "cities": [{"id": 19, "provinceId": 2, "name": "Victoria"}, {"id": 20, "provinceId": 2, "name": "Fort St. John"}, {"id": 21, "provinceId": 2, "name": "<PERSON>"}, {"id": 22, "provinceId": 2, "name": "Cranbrook"}, {"id": 23, "provinceId": 2, "name": "Prince <PERSON>"}, {"id": 24, "provinceId": 2, "name": "Kamloops"}, {"id": 25, "provinceId": 2, "name": "Vancouver"}, {"id": 26, "provinceId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 27, "provinceId": 2, "name": "Williams <PERSON>"}, {"id": 28, "provinceId": 2, "name": "Castlegar"}, {"id": 29, "provinceId": 2, "name": "Pen<PERSON><PERSON>"}, {"id": 30, "provinceId": 2, "name": "Parksville"}, {"id": 31, "provinceId": 2, "name": "<PERSON>"}, {"id": 32, "provinceId": 2, "name": "Trail"}, {"id": 33, "provinceId": 2, "name": "Terrace"}, {"id": 34, "provinceId": 2, "name": "Quesnel"}, {"id": 35, "provinceId": 2, "name": "<PERSON>"}, {"id": 36, "provinceId": 2, "name": "<PERSON><PERSON>"}, {"id": 37, "provinceId": 2, "name": "Grand Forks"}, {"id": 38, "provinceId": 2, "name": "Dawson Creek"}, {"id": 39, "provinceId": 2, "name": "Powell River"}, {"id": 40, "provinceId": 2, "name": "Port Alberni"}, {"id": 41, "provinceId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 42, "provinceId": 2, "name": "Campbell River"}, {"id": 43, "provinceId": 2, "name": "<PERSON>"}, {"id": 44, "provinceId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 45, "provinceId": 2, "name": "Revelstoke"}, {"id": 46, "provinceId": 2, "name": "Kimberley"}, {"id": 47, "provinceId": 2, "name": "Delta"}, {"id": 48, "provinceId": 2, "name": "Langley"}, {"id": 49, "provinceId": 2, "name": "Rossland"}, {"id": 50, "provinceId": 2, "name": "<PERSON>"}, {"id": 51, "provinceId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 52, "provinceId": 2, "name": "New Westminster"}, {"id": 53, "provinceId": 2, "name": "<PERSON><PERSON><PERSON>"}, {"id": 54, "provinceId": 2, "name": "North Vancouver"}, {"id": 55, "provinceId": 2, "name": "<PERSON>"}, {"id": 56, "provinceId": 2, "name": "City of West Kelowna"}, {"id": 57, "provinceId": 2, "name": "Richmond"}, {"id": 58, "provinceId": 2, "name": "Coquitlam"}, {"id": 59, "provinceId": 2, "name": "Surrey"}, {"id": 60, "provinceId": 2, "name": "White Rock"}, {"id": 61, "provinceId": 2, "name": "Port Coquitlam"}, {"id": 62, "provinceId": 2, "name": "<PERSON><PERSON>"}, {"id": 63, "provinceId": 2, "name": "Port Moody"}, {"id": 64, "provinceId": 2, "name": "Langford"}, {"id": 65, "provinceId": 2, "name": "Maple Ridge"}, {"id": 66, "provinceId": 2, "name": "<PERSON><PERSON>"}, {"id": 67, "provinceId": 2, "name": "Pitt Meadows"}, {"id": 68, "provinceId": 2, "name": "Chilliwack"}, {"id": 69, "provinceId": 2, "name": "Abbotsford"}, {"id": 70, "provinceId": 2, "name": "Salmon Arm"}]}, {"id": 3, "countryId": 1, "name": "Manitoba", "code": "MB", "cities": [{"id": 71, "provinceId": 3, "name": "Winnipeg"}, {"id": 72, "provinceId": 3, "name": "<PERSON>"}, {"id": 73, "provinceId": 3, "name": "<PERSON>"}, {"id": 74, "provinceId": 3, "name": "<PERSON>lin Flon"}, {"id": 75, "provinceId": 3, "name": "<PERSON><PERSON><PERSON>"}, {"id": 76, "provinceId": 3, "name": "Selkirk"}, {"id": 77, "provinceId": 3, "name": "Steinbach"}, {"id": 78, "provinceId": 3, "name": "<PERSON>"}, {"id": 79, "provinceId": 3, "name": "Portage la Prairie"}]}, {"id": 4, "countryId": 1, "name": "New Brunswick", "code": "NB", "cities": [{"id": 80, "provinceId": 4, "name": "Fredericton"}, {"id": 81, "provinceId": 4, "name": "Saint <PERSON>"}, {"id": 82, "provinceId": 4, "name": "Parrtown"}, {"id": 83, "provinceId": 4, "name": "Carleton"}, {"id": 84, "provinceId": 4, "name": "Portland"}, {"id": 85, "provinceId": 4, "name": "Moncton"}, {"id": 86, "provinceId": 4, "name": "Bathurst"}, {"id": 87, "provinceId": 4, "name": "<PERSON><PERSON>"}, {"id": 88, "provinceId": 4, "name": "<PERSON><PERSON><PERSON>"}, {"id": 89, "provinceId": 4, "name": "<PERSON><PERSON>"}, {"id": 90, "provinceId": 4, "name": "<PERSON><PERSON>"}, {"id": 91, "provinceId": 4, "name": "Lancaster"}]}, {"id": 5, "countryId": 1, "name": "Newfoundland and Labrador", "code": "NL", "cities": [{"id": 92, "provinceId": 5, "name": "St. John's"}, {"id": 93, "provinceId": 5, "name": "Corner Brook"}, {"id": 94, "provinceId": 5, "name": "Mount Pearl"}]}, {"id": 6, "countryId": 1, "name": "Nova Scotia", "code": "NS", "cities": []}, {"id": 7, "countryId": 1, "name": "Ontario", "code": "ON", "cities": [{"id": 95, "provinceId": 7, "name": "Ottawa"}, {"id": 96, "provinceId": 7, "name": "Toronto"}, {"id": 97, "provinceId": 7, "name": "Windsor"}, {"id": 98, "provinceId": 7, "name": "Thunder Bay"}, {"id": 99, "provinceId": 7, "name": "Timmins-Porcupine"}, {"id": 100, "provinceId": 7, "name": "Sault Ste. Marie"}, {"id": 101, "provinceId": 7, "name": "<PERSON><PERSON>"}, {"id": 102, "provinceId": 7, "name": "London"}, {"id": 103, "provinceId": 7, "name": "St<PERSON>"}, {"id": 104, "provinceId": 7, "name": "Cornwall"}, {"id": 105, "provinceId": 7, "name": "Sarnia"}, {"id": 106, "provinceId": 7, "name": "Owen Sound"}, {"id": 107, "provinceId": 7, "name": "Mississauga"}, {"id": 108, "provinceId": 7, "name": "Niagara Falls"}, {"id": 109, "provinceId": 7, "name": "<PERSON>er"}, {"id": 110, "provinceId": 7, "name": "North Bay"}, {"id": 111, "provinceId": 7, "name": "Pembroke"}, {"id": 112, "provinceId": 7, "name": "Brockville"}, {"id": 113, "provinceId": 7, "name": "Quinte West"}, {"id": 114, "provinceId": 7, "name": "<PERSON>"}, {"id": 115, "provinceId": 7, "name": "Chatham-Kent"}, {"id": 116, "provinceId": 7, "name": "Woodstock"}, {"id": 117, "provinceId": 7, "name": "<PERSON><PERSON><PERSON>"}, {"id": 118, "provinceId": 7, "name": "Welland"}, {"id": 119, "provinceId": 7, "name": "Brantford"}, {"id": 120, "provinceId": 7, "name": "St. Catharines"}, {"id": 121, "provinceId": 7, "name": "Stratford"}, {"id": 122, "provinceId": 7, "name": "Peterborough"}, {"id": 123, "provinceId": 7, "name": "<PERSON><PERSON>"}, {"id": 124, "provinceId": 7, "name": "Oshawa"}, {"id": 125, "provinceId": 7, "name": "Guelph"}, {"id": 126, "provinceId": 7, "name": "Clarence-<PERSON><PERSON>"}, {"id": 127, "provinceId": 7, "name": "Kawartha Lakes"}, {"id": 128, "provinceId": 7, "name": "<PERSON>"}, {"id": 129, "provinceId": 7, "name": "Lambton Shores"}, {"id": 130, "provinceId": 7, "name": "County of Brant"}, {"id": 131, "provinceId": 7, "name": "Greater Sudbury"}, {"id": 132, "provinceId": 7, "name": "Nanticoke"}, {"id": 133, "provinceId": 7, "name": "Grand Sudbury"}, {"id": 134, "provinceId": 7, "name": "Cambridge"}, {"id": 135, "provinceId": 7, "name": "Waterloo"}, {"id": 136, "provinceId": 7, "name": "<PERSON><PERSON><PERSON>"}, {"id": 137, "provinceId": 7, "name": "Brampton"}, {"id": 138, "provinceId": 7, "name": "Prince <PERSON>"}, {"id": 139, "provinceId": 7, "name": "<PERSON><PERSON>"}, {"id": 140, "provinceId": 7, "name": "Port Colborne"}, {"id": 141, "provinceId": 7, "name": "Burlington"}, {"id": 142, "provinceId": 7, "name": "Port Arthur"}, {"id": 143, "provinceId": 7, "name": "<PERSON><PERSON><PERSON>"}, {"id": 144, "provinceId": 7, "name": "Nanticoke"}, {"id": 145, "provinceId": 7, "name": "Berlin"}, {"id": 146, "provinceId": 7, "name": "Hazeldean-March"}, {"id": 147, "provinceId": 7, "name": "Norfolk"}, {"id": 148, "provinceId": 7, "name": "Toronto Junction"}, {"id": 149, "provinceId": 7, "name": "Kingston"}, {"id": 150, "provinceId": 7, "name": "The Lakehead"}, {"id": 151, "provinceId": 7, "name": "Brant-on-the-Grand"}, {"id": 152, "provinceId": 7, "name": "Dryden"}, {"id": 153, "provinceId": 7, "name": "<PERSON>"}, {"id": 154, "provinceId": 7, "name": "Fort William"}, {"id": 155, "provinceId": 7, "name": "Quinte West"}, {"id": 156, "provinceId": 7, "name": "Sarnia-Clearwater"}, {"id": 157, "provinceId": 7, "name": "<PERSON><PERSON>"}, {"id": 158, "provinceId": 7, "name": "Belleville"}, {"id": 159, "provinceId": 7, "name": "Temiskaming Shores"}, {"id": 160, "provinceId": 7, "name": "Eastview"}]}, {"id": 8, "countryId": 1, "name": "Prince Edward Island", "code": "PE", "cities": [{"id": 161, "provinceId": 8, "name": "Summerside"}, {"id": 162, "provinceId": 8, "name": "Charlottetown"}]}, {"id": 9, "countryId": 1, "name": "Quebec", "code": "QC", "cities": [{"id": 163, "provinceId": 9, "name": "Charlesbourg"}, {"id": 173, "provinceId": 9, "name": "Québec"}, {"id": 187, "provinceId": 9, "name": "Anjou"}, {"id": 200, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 215, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 227, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 240, "provinceId": 9, "name": "Pa<PERSON><PERSON><PERSON><PERSON>"}, {"id": 246, "provinceId": 9, "name": "<PERSON><PERSON>"}, {"id": 256, "provinceId": 9, "name": "Donnacona"}, {"id": 164, "provinceId": 9, "name": "Loretteville"}, {"id": 176, "provinceId": 9, "name": "Pointe-au-Père"}, {"id": 185, "provinceId": 9, "name": "LaSalle"}, {"id": 194, "provinceId": 9, "name": "L'Île-Bizard"}, {"id": 202, "provinceId": 9, "name": "Knob-Lake"}, {"id": 210, "provinceId": 9, "name": "Témiscaming"}, {"id": 219, "provinceId": 9, "name": "Ville-Marie"}, {"id": 226, "provinceId": 9, "name": "Causapscal"}, {"id": 236, "provinceId": 9, "name": "Cap-Chat"}, {"id": 247, "provinceId": 9, "name": "Magog"}, {"id": 257, "provinceId": 9, "name": "Gatineau"}, {"id": 165, "provinceId": 9, "name": "Sillery"}, {"id": 178, "provinceId": 9, "name": "Sainte-Geneviève"}, {"id": 189, "provinceId": 9, "name": "Drummondville"}, {"id": 204, "provinceId": 9, "name": "Rivière-du-Loup"}, {"id": 211, "provinceId": 9, "name": "<PERSON><PERSON>"}, {"id": 222, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 229, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 241, "provinceId": 9, "name": "Saint-Louis-de-France"}, {"id": 251, "provinceId": 9, "name": "Lennoxville"}, {"id": 166, "provinceId": 9, "name": "Lac-Saint-Charles"}, {"id": 177, "provinceId": 9, "name": "Outremont"}, {"id": 186, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 195, "provinceId": 9, "name": "Moisie"}, {"id": 203, "provinceId": 9, "name": "Cabano"}, {"id": 212, "provinceId": 9, "name": "Baie-Saint-Paul"}, {"id": 223, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 234, "provinceId": 9, "name": "La Sarre"}, {"id": 242, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 252, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 259, "provinceId": 9, "name": "Alma"}, {"id": 167, "provinceId": 9, "name": "Saint-É<PERSON>"}, {"id": 174, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 183, "provinceId": 9, "name": "Montréal-Nord"}, {"id": 191, "provinceId": 9, "name": "Gas<PERSON>é"}, {"id": 198, "provinceId": 9, "name": "Lachine"}, {"id": 206, "provinceId": 9, "name": "Arthabaska"}, {"id": 214, "provinceId": 9, "name": "Témiscouata-sur-le-Lac"}, {"id": 221, "provinceId": 9, "name": "<PERSON>"}, {"id": 228, "provinceId": 9, "name": "Hull"}, {"id": 238, "provinceId": 9, "name": "Mont-Laurier"}, {"id": 248, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 262, "provinceId": 9, "name": "Saint-<PERSON><PERSON><PERSON>"}, {"id": 168, "provinceId": 9, "name": "<PERSON><PERSON>"}, {"id": 179, "provinceId": 9, "name": "Saint<PERSON><PERSON>"}, {"id": 188, "provinceId": 9, "name": "La Baie"}, {"id": 197, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 209, "provinceId": 9, "name": "Val-des-Sources"}, {"id": 217, "provinceId": 9, "name": "Buckingham"}, {"id": 231, "provinceId": 9, "name": "Iberville"}, {"id": 239, "provinceId": 9, "name": "La Tuque"}, {"id": 245, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 255, "provinceId": 9, "name": "Port-Cartier"}, {"id": 169, "provinceId": 9, "name": "Sainte-<PERSON><PERSON>"}, {"id": 181, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 190, "provinceId": 9, "name": "Pierrefonds"}, {"id": 199, "provinceId": 9, "name": "Saint-<PERSON><PERSON>"}, {"id": 213, "provinceId": 9, "name": "Salaberry-de-Valleyfield"}, {"id": 225, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 235, "provinceId": 9, "name": "Victoriaville"}, {"id": 243, "provinceId": 9, "name": "Notre-Dame-du-Lac"}, {"id": 253, "provinceId": 9, "name": "La Pocatière"}, {"id": 260, "provinceId": 9, "name": "Sainte-Anne-de-Beaupré"}, {"id": 170, "provinceId": 9, "name": "Val-Bélair"}, {"id": 182, "provinceId": 9, "name": "Roxboro"}, {"id": 196, "provinceId": 9, "name": "Montréal"}, {"id": 207, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 220, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 230, "provinceId": 9, "name": "Maniwaki"}, {"id": 237, "provinceId": 9, "name": "Saint<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 249, "provinceId": 9, "name": "<PERSON><PERSON>"}, {"id": 258, "provinceId": 9, "name": "Cadillac"}, {"id": 171, "provinceId": 9, "name": "Cap-Rouge"}, {"id": 180, "provinceId": 9, "name": "Val-d'Or"}, {"id": 192, "provinceId": 9, "name": "Saguenay"}, {"id": 205, "provinceId": 9, "name": "Rouyn-Noranda"}, {"id": 218, "provinceId": 9, "name": "Fermont"}, {"id": 233, "provinceId": 9, "name": "Sc<PERSON>fferville"}, {"id": 244, "provinceId": 9, "name": "Matagami"}, {"id": 254, "provinceId": 9, "name": "Saint-<PERSON><PERSON><PERSON><PERSON>"}, {"id": 261, "provinceId": 9, "name": "Bromptonville"}, {"id": 172, "provinceId": 9, "name": "Beauport"}, {"id": 175, "provinceId": 9, "name": "Chibougamau"}, {"id": 184, "provinceId": 9, "name": "Sept-Îles"}, {"id": 193, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 201, "provinceId": 9, "name": "La Malbaie–Pointe-au-Pic"}, {"id": 208, "provinceId": 9, "name": "Senneterre"}, {"id": 216, "provinceId": 9, "name": "Cap-de-la-Madeleine"}, {"id": 224, "provinceId": 9, "name": "<PERSON><PERSON><PERSON>"}, {"id": 232, "provinceId": 9, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 250, "provinceId": 9, "name": "Lebel-sur-Quévillon"}]}, {"id": 10, "countryId": 1, "name": "Saskatchewan", "code": "SK", "cities": [{"id": 268, "provinceId": 10, "name": "Swift Current"}, {"id": 269, "provinceId": 10, "name": "<PERSON><PERSON><PERSON>"}, {"id": 270, "provinceId": 10, "name": "Meadow Lake"}, {"id": 275, "provinceId": 10, "name": "North Battleford"}, {"id": 267, "provinceId": 10, "name": "Saskatoon"}, {"id": 276, "provinceId": 10, "name": "Humboldt"}, {"id": 264, "provinceId": 10, "name": "<PERSON><PERSON>"}, {"id": 277, "provinceId": 10, "name": "Martensville"}, {"id": 271, "provinceId": 10, "name": "<PERSON>"}, {"id": 278, "provinceId": 10, "name": "<PERSON><PERSON>"}, {"id": 266, "provinceId": 10, "name": "Lloydminster"}, {"id": 274, "provinceId": 10, "name": "<PERSON>"}, {"id": 263, "provinceId": 10, "name": "Regina"}, {"id": 273, "provinceId": 10, "name": "<PERSON>"}, {"id": 265, "provinceId": 10, "name": "Estevan"}, {"id": 272, "provinceId": 10, "name": "Mel<PERSON>"}]}, {"id": 11, "countryId": 1, "name": "Northwest Territories", "code": "NT", "cities": [{"id": 279, "provinceId": 11, "name": "Yellowknife"}]}, {"id": 12, "countryId": 1, "name": "Nunavut", "code": "NU", "cities": [{"id": 280, "provinceId": 12, "name": "Iqaluit"}]}, {"id": 13, "countryId": 1, "name": "Yukon", "code": "YT", "cities": [{"id": 281, "provinceId": 13, "name": "Whitehorse"}]}]}