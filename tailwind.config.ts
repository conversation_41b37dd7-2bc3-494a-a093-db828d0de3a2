import type { Config } from 'tailwindcss';

const config = {
  darkMode: ['class'],
  content: ['./pages/**/*.{ts,tsx}', './components/**/*.{ts,tsx}', './app/**/*.{ts,tsx}', './src/**/*.{ts,tsx}'],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      boxShadow: {
        custom: '0 20px 24px -4px rgba(16, 24, 32, 0.08)',
      },
      colors: {
        border: 'var(--border)',
        input: 'var(--input)',
        placeholder: 'var(--placeholder)',
        ring: 'var(--ring)',
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        primary: {
          DEFAULT: 'var(--primary)',
          bold: 'var(--primary-bold)',
          foreground: 'var(--primary-foreground)',
          brand: 'var(--primary-brand)',
        },
        secondary: {
          DEFAULT: 'var(--secondary)',
          foreground: 'var(--secondary-foreground)',
        },
        destructive: {
          DEFAULT: 'var(--destructive)',
          foreground: 'var(--destructive-foreground)',
        },
        muted: {
          DEFAULT: 'var(--muted)',
          foreground: 'var(--muted-foreground)',
        },
        accent: {
          DEFAULT: 'var(--accent)',
          foreground: 'var(--accent-foreground)',
        },
        popover: {
          DEFAULT: 'var(--popover)',
          foreground: 'var(--popover-foreground)',
        },
        card: {
          DEFAULT: 'var(--card)',
          foreground: 'var(--card-foreground)',
        },
        'gray-scale': {
          5: 'var(--gray-scale-5)',
        },
        'gray-text': {
          DEFAULT: 'var(--gray-text)',
        },
        error: {
          DEFAULT: 'var(--error)',
          light: 'var(--error-light)',
          bold: 'var(--error-bold)',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
      width: {
        '22.5': '22.5rem' /* 360px */,
        '25.5': '25.5rem' /* 408px */,
      },
      maxWidth: {
        '20': '20rem' /* 320px */,
        '25': '25rem' /* 400px */,
        '41.5': '41.5rem' /* 664px */,
        '94.5': '94.5rem' /* 1512px */,
      },
      minWidth: {
        '1.125': '1.125rem' /* 18px */,
        '12.813': '12.813rem' /* 205px */,
        '23.438': '23.438rem' /* 375px */,
      },
      padding: {
        '4.5': '1.125rem' /* 18px */,
      },
      transitionProperty: {
        width: 'width',
        height: 'height',
        'max-height': 'max-height',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;
