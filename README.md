This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Requirements

- Node.js version 18 or higher
- Yarn package manager

## Set up

1. Create a `.env` file similar to `.env.example` and fill out all variables.
2. Open terminal and run:

```bash
yarn # install all dependencies

yarn dev # start dev server
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Features

- Next.js with App Router
- Canada Post address search service
  - Note: If using a trial API key for Canada Post, it must be renewed every 14 days.

## UI Library

Using [Shadcn UI](https://ui.shadcn.com/), to install new UI component:

```bash
npx shadcn-ui@latest add 'component_name'

# Ex: npx shadcn-ui@latest add button
```

## Name conventions

- `hyphen-case` : file name
- `UpperCamelCase` : class / interface / type / enum / decorator / type parameters / component functions in TSX / JSXElement type parameter
- `lowerCamelCase` : variable / parameter / function / method / property / module alias
- `CONSTANT_CASE` : global constant values, including enum values

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Migration Commands

Apply all pending migrations to the database:

```bash
yarn build:babel && yarn migration:run
```

&nbsp;
Undo the most recent migration:

```bash
yarn build:babel && yarn migration:revert
```

&nbsp;
Create a new migration file based on changes in the entities:

```bash
yarn build:babel
NAME="migration_name" yarn migration:generate
```

&nbsp;
Generate an empty migration file for manual editing:

```bash
NAME="migration_name" yarn migration:create
```
